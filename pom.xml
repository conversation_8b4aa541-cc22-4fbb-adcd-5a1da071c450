<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>br.com.pacto</groupId>
    <artifactId>ZillyonWeb</artifactId>
    <packaging>war</packaging>
    <version>7.4.1853</version><!--USED-BY-BUMPVERION-->
    <name>ZillyonWeb</name>
    <url>http://www.pacto.vc</url>

    <repositories>
        <repository>
            <id>central</id>
            <name>Maven Central Repository</name>
            <url>https://repo.maven.apache.org/maven2</url>
            <layout>default</layout>
        </repository>
        <repository>
            <id>nexusLocal</id>
            <name>Pacto Maven Repository</name>
            <url>https://mussum.ath.cx/nexus/content/groups/public</url>
            <layout>default</layout>
        </repository>
        <repository>
            <id>nexusSolutioIn</id>
            <name>Solutio-in Maven Repository</name>
            <url>https://mussum.ath.cx/nexus/content/repositories/solutio</url>
            <layout>default</layout>
        </repository>
    </repositories>

    <distributionManagement>
        <snapshotRepository>
            <id>snapshots</id>
            <name>Internal Snapshots</name>
            <url>http://localhost/nexus/content/repositories/snapshots</url>
        </snapshotRepository>
        <repository>
            <id>releases</id>
            <name>Internal Releases</name>
            <url>http://localhost/nexus/content/repositories/releases</url>
        </repository>
    </distributionManagement>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.google.cloud</groupId>
                <artifactId>libraries-bom</artifactId>
                <version>  26.52.0</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>com.konghq</groupId>
            <artifactId>unirest-java</artifactId>
            <version>3.11.09</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.auth0/java-jwt -->
        <dependency>
            <groupId>com.auth0</groupId>
            <artifactId>java-jwt</artifactId>
            <version>3.8.3</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-xml</artifactId>
            <version>2.15.0</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>2.15.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.6</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
            <version>4.5.3</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.12</version>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.12</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.awaitility</groupId>
            <artifactId>awaitility</artifactId>
            <version>3.1.6</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcpg-jdk18on</artifactId>
            <version>1.72</version>
        </dependency>

        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk18on</artifactId>
            <version>1.72</version>
        </dependency>

        <dependency>
            <groupId>backport-util-concurrent</groupId>
            <artifactId>backport-util-concurrent</artifactId>
            <version>3.1</version>
        </dependency>
        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>barbecue</artifactId>
            <version>1.5-beta1</version>
        </dependency>
        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>zxing-core</artifactId>
            <version>1.7</version>
        </dependency>
        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>zxing-j2se</artifactId>
            <version>1.7</version>
        </dependency>
        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>qrgen</artifactId>
            <version>1.3</version>
        </dependency>
        <dependency>
            <groupId>net.sf.barcode4j</groupId>
            <artifactId>barcode4j</artifactId>
            <version>2.0</version>
        </dependency>

        <dependency>
            <groupId>batik</groupId>
            <artifactId>batik-util</artifactId>
            <version>1.6</version>
        </dependency>

        <dependency>
            <groupId>batik</groupId>
            <artifactId>batik-awt-util</artifactId>
            <version>1.6</version>
        </dependency>

        <dependency>
            <groupId>batik</groupId>
            <artifactId>batik-dom</artifactId>
            <version>1.6</version>
        </dependency>

        <dependency>
            <groupId>batik</groupId>
            <artifactId>batik-svggen</artifactId>
            <version>1.6</version>
        </dependency>

        <dependency>
            <groupId>batik</groupId>
            <artifactId>batik-xml</artifactId>
            <version>1.6</version>
        </dependency>

        <dependency>
            <groupId>cewolf</groupId>
            <artifactId>cewolf</artifactId>
            <version>1.0</version>
            <exclusions>
                <exclusion>
                    <groupId>gnujaxp</groupId>
                    <artifactId>gnujaxp</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>jfree</groupId>
                    <artifactId>jcommon</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>crimson</groupId>
                    <artifactId>crimson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>jstl</artifactId>
            <version>1.2</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib.richfaces</groupId>
            <artifactId>richfaces-api</artifactId>
            <version>3.3.4.Final</version>
        </dependency>

        <dependency>
            <groupId>commons-beanutils</groupId>
            <artifactId>commons-beanutils</artifactId>
            <version>1.7.0</version>
        </dependency>

        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>1.6</version>
        </dependency>

        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
            <version>3.2.2</version>
        </dependency>

        <dependency>
            <groupId>commons-dbcp</groupId>
            <artifactId>commons-dbcp</artifactId>
            <version>1.4</version>
        </dependency>

        <dependency>
            <groupId>commons-fileupload</groupId>
            <artifactId>commons-fileupload</artifactId>
            <version>1.1</version>
        </dependency>

        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.4</version>
        </dependency>

        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId>
            <version>3.9.0</version>
        </dependency>

        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy-all</artifactId>
            <version>1.5.5</version>
            <exclusions>
                <exclusion>
                    <artifactId>ant</artifactId>
                    <groupId>org.apache.ant</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>ant-launcher</artifactId>
                    <groupId>org.apache.ant</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib.ejb</groupId>
            <artifactId>javax.ejb</artifactId>
            <version>3.1.2.1</version>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>3.17</version>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>3.17</version>
        </dependency>

        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>42.2.18</version>
        </dependency>

        <dependency>
            <groupId>javax.xml</groupId>
            <artifactId>jsr173</artifactId>
            <version>1.0</version>
            <exclusions>
                <exclusion>
                    <groupId>crimson</groupId>
                    <artifactId>crimson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>net.sourceforge.jtds</groupId>
            <artifactId>jtds</artifactId>
            <version>1.2.2</version>
        </dependency>

        <dependency>
            <groupId>com.jcraft</groupId>
            <artifactId>jsch</artifactId>
            <version>0.1.53</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>chartcreator</artifactId>
            <version>1.2.0</version>
        </dependency>
        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>CNFe</artifactId>
            <version>2.9.4</version>
        </dependency>
        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>CFin</artifactId>
            <version>0.16</version>
        </dependency>
        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>commons-javaflow</artifactId>
            <version>20060411</version>
        </dependency>
        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>darkX</artifactId>
            <version>3.3.3.Final</version>
        </dependency>
        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>el-ri</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>jasperreports-javaflow</artifactId>
            <version>3.7.2</version>
        </dependency>
        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>jdt-compiler</artifactId>
            <version>3.1.1</version>
        </dependency>
        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>jersey-spring</artifactId>
            <version>1.1.5.1</version>
        </dependency>
        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>oauth-client-1.1.5.1</artifactId>
            <version>1.1.5.1</version>
        </dependency>
        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>oauth-signature</artifactId>
            <version>1.1.5.1</version>
        </dependency>
        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>themes</artifactId>
            <version>3.3.3.Final</version>
        </dependency>

        <dependency>
            <groupId>com.sun.xml.ws</groupId>
            <artifactId>jaxws-rt</artifactId>
            <version>2.2.8</version>
        </dependency>
        <dependency>
            <groupId>com.sun.xml.rpc</groupId>
            <artifactId>jaxrpc-impl</artifactId>
            <version>1.1.3_01</version>
        </dependency>
        <dependency>
            <groupId>javax.xml</groupId>
            <artifactId>jaxrpc-api</artifactId>
            <version>1.1</version>
        </dependency>

        <dependency>
            <groupId>net.spy</groupId>
            <artifactId>spymemcached</artifactId>
            <version>2.12.1</version>
        </dependency>

        <!-- FIM NOVAS -->

        <dependency>
            <groupId>org.jdom</groupId>
            <artifactId>jdom</artifactId>
            <version>1.1</version>
        </dependency>

        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>servlet-api</artifactId>
            <scope>provided</scope>
            <version>2.5</version>
        </dependency>

        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>jsp-api</artifactId>
            <scope>provided</scope>
            <version>2.0</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib.jms</groupId>
            <artifactId>javax.jms</artifactId>
            <version>3.1.2.1</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.sun.mail/javax.mail -->
        <dependency>
            <groupId>com.sun.mail</groupId>
            <artifactId>javax.mail</artifactId>
            <version>1.6.0</version>
        </dependency>

        <dependency>
            <groupId>org.simplejavamail</groupId>
            <artifactId>simple-java-mail</artifactId>
            <version>5.1.1</version>
        </dependency>

        <dependency>
            <groupId>javax.faces</groupId>
            <artifactId>jsf-api</artifactId>
            <version>1.2_15</version>
        </dependency>

        <dependency>
            <groupId>javax.faces</groupId>
            <artifactId>jsf-impl</artifactId>
            <version>1.2_15</version>
        </dependency>

        <dependency>
            <groupId>jfree</groupId>
            <artifactId>jfreechart</artifactId>
            <version>1.0.13</version>
            <exclusions>
                <exclusion>
                    <groupId>jfree</groupId>
                    <artifactId>jcommon</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>net.sf.jasperreports</groupId>
            <artifactId>jasperreports</artifactId>
            <version>3.7.2</version>
        </dependency>


        <dependency>
            <groupId>com.sun.jersey</groupId>
            <artifactId>jersey-server</artifactId>
            <version>1.1.5.2</version>
        </dependency>

        <dependency>
            <groupId>com.sun.jersey</groupId>
            <artifactId>jersey-client</artifactId>
            <version>1.1.5.2</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib.richfaces</groupId>
            <artifactId>richfaces-impl-pacto</artifactId>
            <version>3.3.5.RC1</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib.richfaces</groupId>
            <artifactId>richfaces-ui-pacto</artifactId>
            <version>3.3.4</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib.richfaces</groupId>
            <artifactId>richfaces-glassx-pacto</artifactId>
            <version>3.3.4</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib.fiji</groupId>
            <artifactId>fiji-api</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib.fiji</groupId>
            <artifactId>fiji-ui</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib.sms</groupId>
            <artifactId>sms</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.8.6</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.thymeleaf/thymeleaf -->
        <dependency>
            <groupId>org.thymeleaf</groupId>
            <artifactId>thymeleaf</artifactId>
            <version>3.0.11.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>iReport</artifactId>
            <version>3.0.0</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>iText</artifactId>
            <version>2.1.7</version>
        </dependency>

        <dependency>
            <groupId>org.glassfish.metro</groupId>
            <artifactId>webservices-rt</artifactId>
            <version>2.3</version>
            <scope>compile</scope>
        </dependency>

        <!-- AMAZON S3 CLIENT -->
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-s3</artifactId>
            <version>1.11.591</version>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-ec2</artifactId>
            <version>1.11.591</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>${jackson-version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <version>${jackson-version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>${jackson-version}</version>
        </dependency>
        <dependency>
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-mapper-asl</artifactId>
            <version>1.9.13</version>
        </dependency>
        <dependency>
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-core-asl</artifactId>
            <version>1.9.13</version>
        </dependency>
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
            <version>2.9.2</version>
        </dependency>
        <!--AMAZON S3 CLIENT END -->
        <dependency>
            <groupId>br.com.pacto.lib.imonitor</groupId>
            <artifactId>i-monitor</artifactId>
            <version>1.0</version>
        </dependency>

        <!-- BOLETOS INICIO -->
        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>JBarcodeBean</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>jboleto</artifactId>
            <version>1.3.26</version>
        </dependency>
        <!-- BOLETOS FINAL -->

        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>20140107</version>
        </dependency>

        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.9.2</version>
        </dependency>

        <!-- INTEGRACAO MAXIPAGO -->
        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>maxipago</artifactId>
            <version>1.0.1</version>
        </dependency>
        <!-- FINAL INTEGRACAO MAXIPAGO -->


        <!-- INTEGRACAO FITNESS CARD -->
        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>fitness-card</artifactId>
            <version>1.0</version>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>commons-lang</groupId>-->
        <!--            <artifactId>commons-lang</artifactId>-->
        <!--            <version>2.6</version>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>tzupdater</artifactId>
            <version>2.2.0</version>
        </dependency>

        <dependency>
            <groupId>net.sf.kxml</groupId>
            <artifactId>kxml2</artifactId>
            <version>2.3.0</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.google.cloud/google-cloud-errorreporting -->
        <dependency>
            <groupId>com.google.cloud</groupId>
            <artifactId>google-cloud-errorreporting</artifactId>
            <version>0.124.11-beta</version>
        </dependency>

        <dependency>
            <groupId>io.opencensus</groupId>
            <artifactId>opencensus-api</artifactId>
            <version>0.19.2</version>
        </dependency>
        <dependency>
            <groupId>io.opencensus</groupId>
            <artifactId>opencensus-exporter-trace-stackdriver</artifactId>
            <version>0.19.2</version>
        </dependency>
        <dependency>
            <groupId>io.opencensus</groupId>
            <artifactId>opencensus-impl</artifactId>
            <version>0.19.2</version>
            <scope>runtime</scope>
        </dependency>

        <dependency>
            <groupId>org.reflections</groupId>
            <artifactId>reflections</artifactId>
            <version>0.9.11</version>
        </dependency>

        <dependency>
            <groupId>pl.pragmatists</groupId>
            <artifactId>JUnitParams</artifactId>
            <version>1.1.1</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>bitly-api-client</artifactId>
            <version>0.8.0</version>
        </dependency>

        <dependency>
            <groupId>p6spy</groupId>
            <artifactId>p6spy</artifactId>
            <version>3.8.2</version>
        </dependency>

        <dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
            <version>1.4.11.1</version>
        </dependency>
        <!-- iText Core -->
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itext7-core</artifactId>
            <version>7.1.9</version>
            <type>pom</type>
        </dependency>

        <!-- iText pdfHTML add-on -->
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>html2pdf</artifactId>
            <version>2.1.6</version>
        </dependency>

        <dependency>
            <groupId>org.apache.parquet</groupId>
            <artifactId>parquet-hadoop</artifactId>
            <version>1.12.3</version>
            <exclusions>
                <exclusion>
                    <groupId>tomcat</groupId>
                    <artifactId>jasper-runtime</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>tomcat</groupId>
                    <artifactId>jasper-compiler</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.mortbay.jetty</groupId>
                    <artifactId>jsp-api-2.1</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.mortbay.jetty</groupId>
                    <artifactId>jsp-2.1</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.apache.hadoop/hadoop-core -->
        <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-core</artifactId>
            <version>1.2.1</version>
            <exclusions>
                <exclusion>
                    <groupId>tomcat</groupId>
                    <artifactId>jasper-runtime</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>tomcat</groupId>
                    <artifactId>jasper-compiler</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.mortbay.jetty</groupId>
                    <artifactId>jsp-api-2.1</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.mortbay.jetty</groupId>
                    <artifactId>jsp-2.1</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-net</groupId>
                    <artifactId>commons-net</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.apache.parquet/parquet-avro -->
        <dependency>
            <groupId>org.apache.parquet</groupId>
            <artifactId>parquet-avro</artifactId>
            <version>1.12.3</version>
            <exclusions>
                <exclusion>
                    <groupId>tomcat</groupId>
                    <artifactId>jasper-runtime</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>tomcat</groupId>
                    <artifactId>jasper-compiler</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.mortbay.jetty</groupId>
                    <artifactId>jsp-api-2.1</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.mortbay.jetty</groupId>
                    <artifactId>jsp-2.1</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.google.cloud</groupId>
            <artifactId>google-cloud-storage</artifactId>
            <version>2.12.0</version>
        </dependency>

        <dependency>
            <groupId>com.google.cloud</groupId>
            <artifactId>google-cloud-bigquery</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.javafaker</groupId>
            <artifactId>javafaker</artifactId>
            <version>1.0.2</version>
        </dependency>

    </dependencies>

    <properties>
        <project.build.sourceEncoding>ISO-8859-1</project.build.sourceEncoding>
        <sonar.qualitygate.wait>true</sonar.qualitygate.wait>

        <DISCOVERY_URL>https://discovery.ms.pactosolucoes.com.br</DISCOVERY_URL>
        <glassfish.host></glassfish.host>
        <servicos.host></servicos.host>
        <update-postgres>false</update-postgres>
        <url-servico-update-postgres></url-servico-update-postgres>
        <contexto></contexto>
        <sshUser>root</sshUser>
        <sshPort>22</sshPort>
        <rpwd></rpwd>
        <loadbalancer.host></loadbalancer.host>
        <urlNotificar></urlNotificar>
        <msgNotificar>O ZillyonWeb sera atualizado. Continue trabalhando. Voce sera desconectado em alguns minutos. Aproveite os Novos Recursos e Melhorias! A Pacto Solucoes agradece a sua compreensao.</msgNotificar>
        <flagSoftware>zw</flagSoftware>
        <HABILITAR_NICHO>false</HABILITAR_NICHO>
        <HABILITAR_CACHE_INIT_NICHO>true</HABILITAR_CACHE_INIT_NICHO>
        <VALIDADE_CACHE_NICHO_EM_MINUTOS>2880</VALIDADE_CACHE_NICHO_EM_MINUTOS>

        <HABILITAR_FUNCIONALIDADES_BETA>false</HABILITAR_FUNCIONALIDADES_BETA>

        <MAX_GPT>true</MAX_GPT>

        <!-- cfgBD.xml -->
        <nomeBD></nomeBD>
        <hostOAMD>localhost</hostOAMD>
        <urlDatabaseOAMD>jdbc:postgresql://${hostOAMD}:5432/OAMD</urlDatabaseOAMD>
        <userOAMD>postgres</userOAMD>
        <pwdOAMD>pactodb</pwdOAMD>
        <portaDB>5432</portaDB>
        <moduloNFE>false</moduloNFE>
        <roboApenasComoServico>true</roboApenasComoServico>
        <!-- -->

        <ZAW_DATASOURCE>http://app.pactosolucoes.com.br/ZAW_Datasource</ZAW_DATASOURCE>
        <ZAW_URL_NOTF_ACESSO>http://${loadbalancer.host}/${contexto}/UpdateServlet</ZAW_URL_NOTF_ACESSO>
        <URL_APLICACAO>http://${loadbalancer.host}/${contexto}</URL_APLICACAO>
        <ZAW_QUEUE>false</ZAW_QUEUE>
        <ZAW_QUEUE_CLUSTER>cluster_zaw</ZAW_QUEUE_CLUSTER>
        <ZW_INSTANCIAS></ZW_INSTANCIAS>
        <ZW_BANNERS>http://app.pactosolucoes.com.br/app/UpdateServlet</ZW_BANNERS>
        <URL_TREINO></URL_TREINO>
        <URL_OAMD>http://app.pactosolucoes.com.br/oamd</URL_OAMD>
        <URL_OAMD_SEGURA>https://app.pactosolucoes.com.br/oamd</URL_OAMD_SEGURA>
        <URL_IOF>https://app.pactosolucoes.com.br/ioamd</URL_IOF>
        <URL_LOGIN></URL_LOGIN>
        <URL_LOGIN_FRONT></URL_LOGIN_FRONT>
        <REDIRECT_LOGIN_SERV_LOCAL>false</REDIRECT_LOGIN_SERV_LOCAL>
        <URL_NFSE>http://suporte.pactosolucoes.com.br:8443/pactonfse/soap/IPactoNFSE</URL_NFSE>
        <URL_SUPORTE>https://pactosolucoes.com.br/ajuda/</URL_SUPORTE>
        <URL_NFSE_REST>http://suporte.pactosolucoes.com.br:6670/PactoNFSeWSRest/NFSeRESTService</URL_NFSE_REST>
        <URL_SERV_NOTA_FISCAL>http://naoTemHomologacao/servicoNotaFiscal</URL_SERV_NOTA_FISCAL>
        <URL_OAMD>http://app.pactosolucoes.com.br/oamd</URL_OAMD>
        <FOTOS_NUVEM>false</FOTOS_NUVEM>
        <TIPO_MIDIA>AWS_S3</TIPO_MIDIA>
        <URL_FOTOS_NUVEM>https://cdn1.pactorian.net</URL_FOTOS_NUVEM>
        <URL_NFE_NUVEM>https://cdn1.pactorian.net</URL_NFE_NUVEM>
        <DIR_ZAW_LOGS>/opt/ZAW_Logs</DIR_ZAW_LOGS>
        <URL_VENDAS_ONLINE>https://vendas.online.sistemapacto.com.br</URL_VENDAS_ONLINE>
        <URL_DOC_API>https://documenter.getpostman.com/view/8168625/2sAXjF7ubY</URL_DOC_API>
        <URL_SITE_APP>https://appmeubox.com.br</URL_SITE_APP>
        <DIR_ZAW_DATASOURCE>/opt/ZAW_Datasource</DIR_ZAW_DATASOURCE>
        <DIR_ZW_ARQ>/opt/ZW_ARQ/</DIR_ZW_ARQ>
        <DIR_ZAW_UTILS>/opt/ZAW_Utils</DIR_ZAW_UTILS>
        <tempoAguardar>300</tempoAguardar><!--segundos-->
        <tempoAguardarUpdatePG>0</tempoAguardarUpdatePG><!--segundos-->
        <COOKIE_FAIL>false</COOKIE_FAIL>
        <MY_FAQ_URL>https://app.pactosolucoes.com.br/FAQ</MY_FAQ_URL>
        <MY_FAQ_EMPRESAS></MY_FAQ_EMPRESAS>
        <SFTP_NOW>false</SFTP_NOW>
        <VALIDAR_USUARIO_OAMD>false</VALIDAR_USUARIO_OAMD>
        <VALIDAR_VERSAO_BD>true</VALIDAR_VERSAO_BD>
        <URL_OAMD_EMPRESAS>http://app.pactosolucoes.com.br/oamd/empresas</URL_OAMD_EMPRESAS>
        <URL_MODULO_NFSE>https://nfe2-web.pactosolucoes.com.br:9030/nfe</URL_MODULO_NFSE>
        <MY_URL_UP_BASE>https://app.pactosolucoes.com.br/myUCP</MY_URL_UP_BASE>
        <loadInstancesFromCloud>false</loadInstancesFromCloud>
        <prefixoInstanciasCloud>prod-zw-*</prefixoInstanciasCloud>
        <URL_FINANCEIRO_PACTO>https://suporte.pactosolucoes.com.br:9443/financeiroWS/soap/IFinanceiroWS</URL_FINANCEIRO_PACTO>
        <AWSAccessKeyId>********************</AWSAccessKeyId>
        <AWSSecretKey>B7FTWZU5K1p4gDeW13VeQSVT60MUb7Hi9zz61cRJ</AWSSecretKey>
        <AWSRegion>sa-east-1</AWSRegion>
        <INTEGRACAO_WIKI>false</INTEGRACAO_WIKI>
        <LIBERAR_ACESSO_FATOR_ZW>false</LIBERAR_ACESSO_FATOR_ZW>
        <URL_MEDIDOR>https://app.pactosolucoes.com.br/Medidor</URL_MEDIDOR>
        <HABILITAR_LEMBRETE_SOLICITACOES>true</HABILITAR_LEMBRETE_SOLICITACOES>
        <SERVIDOR_MEMCACHED>DISABLED</SERVIDOR_MEMCACHED>
        <BUSCAR_CONHECIMENTO_UCP>true</BUSCAR_CONHECIMENTO_UCP>
        <URL_BD_REMESSA>***************************************</URL_BD_REMESSA>
        <USER_BD_REMESSA>zillyonweb</USER_BD_REMESSA>
        <PWD_BD_REMESSA>pactodb</PWD_BD_REMESSA>
        <EMAIL_COMERCIAL_PACTO><EMAIL></EMAIL_COMERCIAL_PACTO>
        <SMTP_EMAIL_ROBO><EMAIL></SMTP_EMAIL_ROBO>
        <SMTP_EMAIL_NOREPLY><EMAIL></SMTP_EMAIL_NOREPLY>
        <SMTP_LOGIN_ROBO>pactosolucoes2017</SMTP_LOGIN_ROBO>
        <SMTP_SENHA_ROBO>RlOHgUGw8905</SMTP_SENHA_ROBO>
        <SMTP_CONEXAOSEGURA_ROBO>true</SMTP_CONEXAOSEGURA_ROBO>
        <SMTP_SERVER_ROBO>smtplw.com.br</SMTP_SERVER_ROBO>
        <INICIAR_TLS>false</INICIAR_TLS>
        <arrayCaixasPostaisSFTP></arrayCaixasPostaisSFTP>
        <arrayCaixasPostaisExtratoSFTP></arrayCaixasPostaisExtratoSFTP>
        <serverSFTPSlave></serverSFTPSlave>
        <urlDashBoard>http://suporte.pactosolucoes.com.br:9000/dash2</urlDashBoard>
        <useBounceService>true</useBounceService>
        <URL_INTEGRACOES_SERVICE>http://mock.pactosolucoes.com.br:8077</URL_INTEGRACOES_SERVICE>
        <URL_SERVICO_INTEGRACOES_MS>http://localhost:8199/integracoes-ms</URL_SERVICO_INTEGRACOES_MS>
        <CANCELAR_BOLETO_AO_CANCELAR_OU_ESTORNAR_CONTRATO>false</CANCELAR_BOLETO_AO_CANCELAR_OU_ESTORNAR_CONTRATO>
        <VALIDAR_TOKEN_API_ZW>false</VALIDAR_TOKEN_API_ZW>

        <validarInad>false</validarInad>
        <integraProtheus>false</integraProtheus>
        <urlInad></urlInad>
        <tokenInad></tokenInad>


        <sufixoInfra>30</sufixoInfra><!--Valores possiveis: 30,31,32,33,34,35... por enquanto-->
        <sufixoPorta>10</sufixoPorta><!--Valores possiveis: 10,11,12... por enquanto-->
        <APRESENTAR_HOTJAR>false</APRESENTAR_HOTJAR>
        <UTILIZAR_SINTETICO_MS>false</UTILIZAR_SINTETICO_MS>
        <URL_RECURSO_EMPRESA>https://ms1.pactosolucoes.com.br/oamd</URL_RECURSO_EMPRESA>
        <URL_API>https://app.pactosolucoes.com.br/api</URL_API>
        <URL_GOGOOD>https://api.gogood.com.br/prod</URL_GOGOOD>
        <GOGOOD_APP_ID>6wdNuWWpzwxECFLlp-cf7_4L4FNJr_UnoiwVs_7-hcM</GOGOOD_APP_ID>
        <GOGOOD_APP_SECRET>-yzVMBZgISas8lcuhSiQApQWY8dkcF3tFG_9pES8UzU</GOGOOD_APP_SECRET>
        <URL_ZW_AUTO>https://app.pactosolucoes.com.br/zw-auto</URL_ZW_AUTO>
        <URL_MIDIA_SOCIAL>http://squad-migracao.pactosolucoes.com.br:8120/midia-social-ms</URL_MIDIA_SOCIAL>
        <USAR_URL_RECURSO_EMPRESA>false</USAR_URL_RECURSO_EMPRESA>

        <!-- URLs para acessar o REST do servidor NFSe -->
        <nfse-rest-host>http://suporte.pactosolucoes.com.br</nfse-rest-host>
        <nfse-rest-port>6670</nfse-rest-port>
        <nfse-rest-url>${nfse-rest-host}:${nfse-rest-port}</nfse-rest-url>
        <nfse-rest-conf-path>${nfse-rest-url}/PactoNFSeConf</nfse-rest-conf-path>
        <nfse-rest-admin-path>${nfse-rest-conf-path}/AdminNFSe</nfse-rest-admin-path>
        <jackson-version>2.15.0</jackson-version>

        <GCLOUD_API_KEY></GCLOUD_API_KEY>
        <GERAR_PRINTS_ACOES_USUARIO>false</GERAR_PRINTS_ACOES_USUARIO>
        <LIMITE_PRINTS_ACOES_USUARIO>10</LIMITE_PRINTS_ACOES_USUARIO>

        <!--
            Porque as duas? Para que includes funcionem com ssl ou não
            O canal do cliente utiliza esse include: Veja em CanalPactoControle.java@prepararUrlIncludeCanalCliente
        -->
        <URL_HTTP_PLATAFORMA_PACTO>http://zw101.pactosolucoes.com.br:8011/ccl</URL_HTTP_PLATAFORMA_PACTO>
        <URL_HTTPS_PLATAFORMA_PACTO>https://zw101.pactosolucoes.com.br:9011/ccl/</URL_HTTPS_PLATAFORMA_PACTO>
        <debugJDBC>false</debugJDBC>
        <ENVIAR_EMAIL_VENDAS_ONLINE>false</ENVIAR_EMAIL_VENDAS_ONLINE>
        <STONE_OPEN_BANK_PRODUCAO>false</STONE_OPEN_BANK_PRODUCAO>
        <ATIVAR_GOOGLE_ANALYTICS>true</ATIVAR_GOOGLE_ANALYTICS>
        <ATIVAR_WEHELP>false</ATIVAR_WEHELP>
        <ATIVAR_ANUNCIO_VITIO>false</ATIVAR_ANUNCIO_VITIO>

        <URL_SERVICO_INTEGRACAO_SENDY>https://send.sistemapacto.com.br:8080/serviceSendy/</URL_SERVICO_INTEGRACAO_SENDY>

        <BUSCAR_REMOTO_EXTRATO_RETORNO_REMESSA>true</BUSCAR_REMOTO_EXTRATO_RETORNO_REMESSA>

        <URL_JENKINS>http://zw34.pactosolucoes.com.br/jk</URL_JENKINS>
        <URL_MAILING>http://zw34.pactosolucoes.com.br/app/ms</URL_MAILING>
        <AMBIENTE_DESENVOLVIMENTO_TESTE>false</AMBIENTE_DESENVOLVIMENTO_TESTE>
        <TOKEN_MAIL_GUN>**************************************************</TOKEN_MAIL_GUN>
        <HABILITA_MARKETING>true</HABILITA_MARKETING>
        <HABILITA_CLUBE_DE_BENEFICIOS>true</HABILITA_CLUBE_DE_BENEFICIOS>
        <TEMPO_SEGUNDOS_EXPIRAR_CACHE_BANNERS>432000</TEMPO_SEGUNDOS_EXPIRAR_CACHE_BANNERS>
        <URL_MARKETING_MS>https://ms1.pactosolucoes.com.br/marketing-ms</URL_MARKETING_MS>
        <DOMAIN_MAIL>wagi.com.br</DOMAIN_MAIL>
        <LOG_OUTPUT_MASCARADE>true</LOG_OUTPUT_MASCARADE>
        <LOG_OUTPUT_DEBUG>false</LOG_OUTPUT_DEBUG>
        <BI_MS>http://localhost:8299</BI_MS>
        <EMAIL_SQUAD_ADM_OPEN_TICKET></EMAIL_SQUAD_ADM_OPEN_TICKET>

        <BLOQUEAR_EMPRESAS_NAO_TIPIFICADAS>false</BLOQUEAR_EMPRESAS_NAO_TIPIFICADAS>
        <PRIVATE_KEY_TEMPLATE>${basedir}/target/${project.name}/WEB-INF/classes/propriedades/keys/private-key-kleopatra</PRIVATE_KEY_TEMPLATE>
        <ENABLE_LAST_ACTION_TIME>false</ENABLE_LAST_ACTION_TIME>
        <ENABLE_COUNT_DOWN>false</ENABLE_COUNT_DOWN>
        <URL_WEBHOOK_DISCORD_ENOTAS>https://discord.com/api/webhooks/958779210716053534/_JpyNtHb4yl32h3eGcCX6efL2sCgATDTf2LMXRdE3UPbLt4O04I4nMGYCWzVQpNm3Pg0</URL_WEBHOOK_DISCORD_ENOTAS>
        <URL_WEBHOOK_DISCORD_ENOTAS_HABILITAR>true</URL_WEBHOOK_DISCORD_ENOTAS_HABILITAR>

        <ENABLE_MENU_ZW_UI>true</ENABLE_MENU_ZW_UI>
        <TOKENS_ACESSO_API_CLIENTE>gP6pV2pS6lC8sY7nH6vG8tN4xT0vR9tU,teste,1</TOKENS_ACESSO_API_CLIENTE>
        <TOKENS_ACESSO_API_APPS>eH9hX9dC2cB2eC7xV8tL5zT2kR6uG8dX</TOKENS_ACESSO_API_APPS>
        <TOKEN_API_WAGI></TOKEN_API_WAGI>
        <PERMITIR_ENVIO_NOTIFICACAO_PUSH_AULA>true</PERMITIR_ENVIO_NOTIFICACAO_PUSH_AULA>
        <URL_APP_ALUNO_UNIFICADO>https://app-do-aluno-unificado.web.app</URL_APP_ALUNO_UNIFICADO>
        <TOKEN_PUSH_MOBILE_APP_ALUNO>sistemapacto</TOKEN_PUSH_MOBILE_APP_ALUNO>
        <UTEIS_EMAIL_SEND>true</UTEIS_EMAIL_SEND>
        <QTD_LIMITE_PACTOPAY>50</QTD_LIMITE_PACTOPAY>

        <AUTH_SECRET_ZW_PATH>/home/<USER>/.ssh/auth-secret-zw</AUTH_SECRET_ZW_PATH>
        <KEYWORD_PATH>/home/<USER>/.ssh/keyword</KEYWORD_PATH>
        <URL_TOTAPASS>https://api.totalpass.com/service/v1/track_usages</URL_TOTAPASS>
        <URL_TOTALPASS_VALIDATE>https://api.totalpass.com/service/v1/track_usages/validate</URL_TOTALPASS_VALIDATE>
        <URL_BASE_OPTIN></URL_BASE_OPTIN>
        <PASSWORD_MAIL_FACILITEPAY>xW7jU0fA4kH6iS5fI1oU9rF6kN8nI6qS</PASSWORD_MAIL_FACILITEPAY>
        <TEMPO_SEGUNDOS_EXPIRACAO_TOKEN_OPERACAO>60</TEMPO_SEGUNDOS_EXPIRACAO_TOKEN_OPERACAO>
        <PASSWORD_IMPORTADOR_TREINO>IBZvQqmBcMRuifIsjiOasGTR</PASSWORD_IMPORTADOR_TREINO>
        <ENABLE_COMPANY_WEBHOOK>false</ENABLE_COMPANY_WEBHOOK>
        <ENABLE_CONCILIADORA>true</ENABLE_CONCILIADORA>
        <VERIFY_CONTROLLERS_AFTER_PHASE>false</VERIFY_CONTROLLERS_AFTER_PHASE>

        <MINIMO_NOTAS_SOLICITACAO>100</MINIMO_NOTAS_SOLICITACAO>

        <URL_ARAGORN_MS></URL_ARAGORN_MS> <!--Em Produção sobrepõe-->
        <URL_AUTENTICACAO_MS></URL_AUTENTICACAO_MS> <!--Em Produção sobrepõe-->

        <REDIRECT_URI_CONNECT_PAGBANK></REDIRECT_URI_CONNECT_PAGBANK> <!--Em Produção sobrepõe-->
        <GOOGLE_APPLICATION_CREDENTIALS>/home/<USER>/Workspace/Pacto/Access-Keys/academia-de-verdade-a21ca50b8adb.json</GOOGLE_APPLICATION_CREDENTIALS>
        <MOCK_ARAGORN_CARDS>false</MOCK_ARAGORN_CARDS>
        <URL_ENVIO_ACESSO_INTEG_PRATIQUE>https://pratiquefitness.com.br/api/endpoints/pacto/index.php</URL_ENVIO_ACESSO_INTEG_PRATIQUE>
        <TOKEN_ENVIO_ACESSO_INTEG_PRATIQUE>kJ9pL2mQwX7vR4tY8nBcF5gHsD3aZ6eU</TOKEN_ENVIO_ACESSO_INTEG_PRATIQUE>
    </properties>

    <profiles>

        <profile>
            <id>develop</id>
            <properties>
                <URL_FINANCEIRO_PACTO>https://suporte.pactosolucoes.com.br:9443/financeiroWS/soap/IFinanceiroWS</URL_FINANCEIRO_PACTO>
                <URL_OAMD>http://homologacao.pactosolucoes.com.br:8066/oamd-tronco</URL_OAMD>
                <URL_OAMD_SEGURA>https://homologacao.pactosolucoes.com.br:9066/oamd-tronco</URL_OAMD_SEGURA>
                <FOTOS_NUVEM>true</FOTOS_NUVEM>
                <VALIDAR_VERSAO_BD>true</VALIDAR_VERSAO_BD>
                <TIPO_MIDIA>ZW_INTERNAL</TIPO_MIDIA>
                <URL_FOTOS_NUVEM>zw-photos</URL_FOTOS_NUVEM>
                <arrayCaixasPostaisExtratoSFTP>DCC:homologacao.pactosolucoes.com.br:2266:root:pac!@#br12:/home/<USER>/extrato-diario</arrayCaixasPostaisExtratoSFTP>
                <serverSFTPSlave>DCC,DCC_CIELO_ONLINE,DCC_BIN,DCC_E_REDE:homologacao.pactosolucoes.com.br:2266:root:pac!@#br12</serverSFTPSlave>
                <URL_NFSE>http://suporte.pactosolucoes.com.br:8443/pactonfse/soap/IPactoNFSE</URL_NFSE>
                <URL_NFSE_REST>http://suporte.pactosolucoes.com.br:6670/PactoNFSeWSR´est/NFSeRESTService</URL_NFSE_REST>
                <nfse-rest-host>http://suporte.pactosolucoes.com.br</nfse-rest-host>
                <nfse-rest-port>6670</nfse-rest-port>
                <hostOAMD>localhost</hostOAMD>
                <urlDatabaseOAMD>jdbc:postgresql://${hostOAMD}:5466/OAMD</urlDatabaseOAMD>
                <debugJDBC>true</debugJDBC>
                <APRESENTAR_HOTJAR>false</APRESENTAR_HOTJAR>
                <ATIVAR_GOOGLE_ANALYTICS>false</ATIVAR_GOOGLE_ANALYTICS>
                <ATIVAR_WEHELP>false</ATIVAR_WEHELP>
                <AMBIENTE_DESENVOLVIMENTO_TESTE>true</AMBIENTE_DESENVOLVIMENTO_TESTE>
                <URL_WEBHOOK_DISCORD_ENOTAS_HABILITAR>false</URL_WEBHOOK_DISCORD_ENOTAS_HABILITAR>
            </properties>
        </profile>

        <profile>
            <id>release</id>
            <properties>
                <URL_FINANCEIRO_PACTO>https://suporte.pactosolucoes.com.br:9443/financeiroWS/soap/IFinanceiroWS</URL_FINANCEIRO_PACTO>
                <URL_OAMD>http://homologacao.pactosolucoes.com.br:8066/oamd-versao</URL_OAMD>
                <URL_OAMD_SEGURA>https://homologacao.pactosolucoes.com.br:9066/oamd-versao</URL_OAMD_SEGURA>
                <FOTOS_NUVEM>true</FOTOS_NUVEM>
                <VALIDAR_VERSAO_BD>true</VALIDAR_VERSAO_BD>
                <TIPO_MIDIA>ZW_INTERNAL</TIPO_MIDIA>
                <URL_FOTOS_NUVEM>zw-photos</URL_FOTOS_NUVEM>
                <arrayCaixasPostaisExtratoSFTP>DCC:homologacao.pactosolucoes.com.br:2266:root:pac!@#br12:/home/<USER>/extrato-diario</arrayCaixasPostaisExtratoSFTP>
                <serverSFTPSlave>DCC,DCC_CIELO_ONLINE,DCC_BIN,DCC_E_REDE:homologacao.pactosolucoes.com.br:2266:root:pac!@#br12</serverSFTPSlave>
                <URL_NFSE>http://suporte.pactosolucoes.com.br:8443/pactonfse/soap/IPactoNFSE</URL_NFSE>
                <URL_NFSE_REST>http://suporte.pactosolucoes.com.br:6670/PactoNFSeWSRest/NFSeRESTService</URL_NFSE_REST>
                <nfse-rest-host>http://suporte.pactosolucoes.com.br</nfse-rest-host>
                <nfse-rest-port>6670</nfse-rest-port>
                <hostOAMD>localhost</hostOAMD>
                <urlDatabaseOAMD>jdbc:postgresql://${hostOAMD}:5466/OAMD</urlDatabaseOAMD>
                <debugJDBC>true</debugJDBC>
            </properties>
        </profile>

        <profile>
            <id>desenv</id>
            <properties>
                <URL_TOTALPASS>https://staging.totalpass.com/api/v1/track_usages</URL_TOTALPASS>
                <URL_TOTALPASS_VALIDATE>https://staging.totalpass.com/api/v1/track_usages/validate</URL_TOTALPASS_VALIDATE>
                <URL_API>http://localhost:8083/api</URL_API>
                <URL_VENDAS_ONLINE>http://localhost:4200</URL_VENDAS_ONLINE>
                <AUTH_SECRET_PATH>${basedir}/target/${project.name}/WEB-INF/classes/propriedades/keys/auth-secrect</AUTH_SECRET_PATH>
                <AUTH_SECRET_PERSONA_PATH>${basedir}/target/${project.name}/WEB-INF/classes/propriedades/keys/auth-secrect-persona</AUTH_SECRET_PERSONA_PATH>
                <AUTH_SECRET_ZW_PATH>${basedir}/target/${project.name}/WEB-INF/classes/propriedades/keys/auth-secrect</AUTH_SECRET_ZW_PATH>
                <DISCOVERY_URL>http://host.docker.internal:8101</DISCOVERY_URL>
                <URL_TREINO>http://host.docker.internal:8201/TreinoWeb</URL_TREINO>
                <URL_FINANCEIRO_PACTO>https://fin.pactosolucoes.com.br/financeiroWS/soap/IFinanceiroWS</URL_FINANCEIRO_PACTO>
                <loadbalancer.host>localhost:8081</loadbalancer.host>
                <contexto>ZillyonWeb</contexto>
                <ZW_INSTANCIAS>localhost:8081</ZW_INSTANCIAS>
                <URL_OAMD>http://host.docker.internal:8202/NewOAMD</URL_OAMD>
                <URL_OAMD_SEGURA>http://host.docker.internal:8202/NewOAMD</URL_OAMD_SEGURA>
                <ZW_BANNERS></ZW_BANNERS>
                <FOTOS_NUVEM>true</FOTOS_NUVEM>
                <VALIDAR_VERSAO_BD>true</VALIDAR_VERSAO_BD>
                <TIPO_MIDIA>AWS_S3</TIPO_MIDIA>
                <arrayCaixasPostaisSFTP>DCC_BIN:prod-gw-lac.firstdataclients.com:6522:LAGW-ECWCD001:rG~WY6fYR:/available,DCC:**************:22:lxv685:lxv685.123:/out</arrayCaixasPostaisSFTP>
                <arrayCaixasPostaisExtratoSFTP>DCC:sftp.pactosolucoes.com.br:2222:root:p4csf2121:/home/<USER>/extrato-diario</arrayCaixasPostaisExtratoSFTP>
                <serverSFTPSlave>DCC,DCC_CIELO_ONLINE,DCC_BIN,DCC_E_REDE:homologacao.pactosolucoes.com.br:2266:root:pac!@#br12</serverSFTPSlave>
                <URL_NFSE>http://suporte.pactosolucoes.com.br:8443/pactonfse/soap/IPactoNFSE</URL_NFSE>
                <URL_NFSE_REST>http://suporte.pactosolucoes.com.br:6670/PactoNFSeWSRest/NFSeRESTService</URL_NFSE_REST>
                <nfse-rest-host>http://suporte.pactosolucoes.com.br</nfse-rest-host>
                <nfse-rest-port>6670</nfse-rest-port>
                <hostOAMD>localhost</hostOAMD>
                <urlDatabaseOAMD>jdbc:postgresql://${hostOAMD}:5432/OAMD</urlDatabaseOAMD>
                <debugJDBC>false</debugJDBC>
                <URL_HTTP_ZW_HOMOLOGACAO>http://homologacao.pactosolucoes.com.br:8066/app-versao</URL_HTTP_ZW_HOMOLOGACAO>
                <URL_HTTPS_ZW_HOMOLOGACAO>https://homologacao.pactosolucoes.com.br:9066/app-versao</URL_HTTPS_ZW_HOMOLOGACAO>
                <APRESENTAR_HOTJAR>true</APRESENTAR_HOTJAR>
                <UTILIZAR_SINTETICO_MS>false</UTILIZAR_SINTETICO_MS>
                <EMAIL_COMERCIAL_PACTO><EMAIL></EMAIL_COMERCIAL_PACTO>
                <SMTP_EMAIL_ROBO><EMAIL></SMTP_EMAIL_ROBO>
                <SMTP_EMAIL_NOREPLY><EMAIL></SMTP_EMAIL_NOREPLY>
                <SMTP_LOGIN_ROBO><EMAIL></SMTP_LOGIN_ROBO>
                <SMTP_SENHA_ROBO>**************************************************</SMTP_SENHA_ROBO>
                <SMTP_CONEXAOSEGURA_ROBO>true</SMTP_CONEXAOSEGURA_ROBO>
                <SMTP_SERVER_ROBO>smtp.mailgun.org</SMTP_SERVER_ROBO>
                <URL_SERVICO_INTEGRACAO_SENDY>http://localhost:8090/serviceSendy/</URL_SERVICO_INTEGRACAO_SENDY>
                <URL_SERVICO_INTEGRACOES_MS>http://localhost:8199/integracoes-ms</URL_SERVICO_INTEGRACOES_MS>
                <URL_INTEGRACOES_SERVICE>http://localhost:8077</URL_INTEGRACOES_SERVICE>
                <ATIVAR_GOOGLE_ANALYTICS>false</ATIVAR_GOOGLE_ANALYTICS>
                <ATIVAR_WEHELP>false</ATIVAR_WEHELP>
                <AMBIENTE_DESENVOLVIMENTO_TESTE>true</AMBIENTE_DESENVOLVIMENTO_TESTE>
                <BLOQUEAR_EMPRESAS_NAO_TIPIFICADAS>false</BLOQUEAR_EMPRESAS_NAO_TIPIFICADAS>
                <PRIVATE_KEY_TEMPLATE>${basedir}/target/${project.name}/WEB-INF/classes/propriedades/keys/private-key-kleopatra</PRIVATE_KEY_TEMPLATE>
                <URL_WEBHOOK_DISCORD_ENOTAS_HABILITAR>false</URL_WEBHOOK_DISCORD_ENOTAS_HABILITAR>
                <PERMITIR_ENVIO_NOTIFICACAO_PUSH_AULA>false</PERMITIR_ENVIO_NOTIFICACAO_PUSH_AULA>
                <URL_MIDIA_SOCIAL>http://squad-migracao.pactosolucoes.com.br:8120/midia-social-ms</URL_MIDIA_SOCIAL>
                <TOKEN_MAIL_GUN>**************************************************</TOKEN_MAIL_GUN>
                <HABILITA_MARKETING>false</HABILITA_MARKETING>
                <HABILITA_CLUBE_DE_BENEFICIOS>false</HABILITA_CLUBE_DE_BENEFICIOS>
                <CANCELAR_BOLETO_AO_CANCELAR_OU_ESTORNAR_CONTRATO>false</CANCELAR_BOLETO_AO_CANCELAR_OU_ESTORNAR_CONTRATO>
                <VALIDAR_TOKEN_API_ZW>false</VALIDAR_TOKEN_API_ZW>
                <KEYWORD_PATH>${basedir}/target/${project.name}/WEB-INF/classes/propriedades/keys/auth-secrect</KEYWORD_PATH>
                <TEMPO_SEGUNDOS_EXPIRACAO_TOKEN_OPERACAO>60</TEMPO_SEGUNDOS_EXPIRACAO_TOKEN_OPERACAO>
                <REDIRECT_URI_CONNECT_PAGBANK>https://f448-170-82-79-185.ngrok-free.app/api/prest/pagbank/authRedirect</REDIRECT_URI_CONNECT_PAGBANK> <!--Em Produção sobrepõe-->
                <URL_GOGOOD>https://api.gogood.com.br/sand</URL_GOGOOD>
                <GOGOOD_APP_ID>iXlL9OZYvG_6gFb2z5KGb5LqiKZ-mz1CketMLfUaKZU</GOGOOD_APP_ID>
                <GOGOOD_APP_SECRET>Qlr_Qeoi5CtTyJUvtJKewIOyOeUKgJe2_Qj-5F967z8</GOGOOD_APP_SECRET>
            </properties>
        </profile>

        <profile>
            <id>chucknorris</id>
            <properties>
                <DISCOVERY_URL>http://devzw.pactosolucoes.com.br:8387</DISCOVERY_URL>
                <URL_API>http://zw.pactosolucoes.com.br:8384/API-ZillyonWeb</URL_API>
            </properties>
        </profile>

        <profile>
            <id>nfe-producao</id>
            <properties>
                <URL_NFSE_REST>http://nfe2.pactosolucoes.com.br:6670/PactoNFSeWSRest/NFSeRESTService</URL_NFSE_REST>
                <URL_SERV_NOTA_FISCAL>https://nfe.pactosolucoes.com.br/servico-nota-fiscal</URL_SERV_NOTA_FISCAL>
            </properties>
        </profile>

        <profile>
            <id>local</id>
            <properties>
                <DISCOVERY_URL>http://localhost:8101</DISCOVERY_URL>
                <URL_JENKINS>http://localhost:8205</URL_JENKINS>
                <AUTH_SECRET_PERSONA_PATH>${project.basedir}/docker/keys/auth-secrect-persona</AUTH_SECRET_PERSONA_PATH>
                <AUTH_SECRET_PATH>${project.basedir}/docker/keys/auth-secrect</AUTH_SECRET_PATH>
                <AUTH_SECRET_ZW_PATH>${project.basedir}/docker/keys/auth-secrect</AUTH_SECRET_ZW_PATH>
                <URL_OAMD>http://localhost:8202/NewOAMD</URL_OAMD>
                <URL_IOF>http://localhost:8200</URL_IOF>
                <BI_MS>http://localhost:8106</BI_MS>
                <URL_SERV_NOTA_FISCAL>http://localhost:8018</URL_SERV_NOTA_FISCAL>
                <debugJDBC>false</debugJDBC>
                <SMTP_EMAIL_ROBO><EMAIL></SMTP_EMAIL_ROBO>
                <SMTP_EMAIL_NOREPLY><EMAIL></SMTP_EMAIL_NOREPLY>
                <SMTP_LOGIN_ROBO><EMAIL></SMTP_LOGIN_ROBO>
                <SMTP_SENHA_ROBO>**************************************************</SMTP_SENHA_ROBO>
                <SMTP_SERVER_ROBO>smtp.mailgun.org</SMTP_SERVER_ROBO>
                <SMTP_SERVER_ROBO_INICIAR_TLS>true</SMTP_SERVER_ROBO_INICIAR_TLS>
                <SMTP_SERVER_ROBO_CONEXAO_SEGURA>false</SMTP_SERVER_ROBO_CONEXAO_SEGURA>
                <URL_MAILING>http://localhost:8200/ZillyonWeb/ms</URL_MAILING>
                <AMBIENTE_DESENVOLVIMENTO_TESTE>true</AMBIENTE_DESENVOLVIMENTO_TESTE>
                <ENABLE_MENU_ZW_UI>true</ENABLE_MENU_ZW_UI>
                <URL_API>http://localhost:8203/API-ZillyonWeb</URL_API>
                <URL_INTEGRACOES_SERVICE>http://localhost:8077</URL_INTEGRACOES_SERVICE>
                <MOCK_ARAGORN_CARDS>true</MOCK_ARAGORN_CARDS>
                <URL_AUTENTICACAO_MS>http://host.docker.internal:8100</URL_AUTENTICACAO_MS>
            </properties>
        </profile>

        <profile>
            <id>docker</id>
            <properties>
                <hostOAMD>postgres</hostOAMD>
                <DISCOVERY_URL>http://*********:8080/discovery</DISCOVERY_URL>
                <AUTH_SECRET_PERSONA_PATH>/root/.ssh/auth-secrect-persona</AUTH_SECRET_PERSONA_PATH>
                <AUTH_SECRET_PATH>/root/.ssh/auth-secrect</AUTH_SECRET_PATH>
                <AUTH_SECRET_ZW_PATH>/root/.ssh/auth-secrect</AUTH_SECRET_ZW_PATH>
                <PRIVATE_KEY_TEMPLATE>/root/.ssh/private-key-kleopatra</PRIVATE_KEY_TEMPLATE>
                <useBounceService>false</useBounceService>
                <FOTOS_NUVEM>true</FOTOS_NUVEM>
                <AMBIENTE_DESENVOLVIMENTO_TESTE>true</AMBIENTE_DESENVOLVIMENTO_TESTE>
                <APRESENTAR_HOTJAR>false</APRESENTAR_HOTJAR>
                <ATIVAR_GOOGLE_ANALYTICS>false</ATIVAR_GOOGLE_ANALYTICS>
                <ATIVAR_WEHELP>false</ATIVAR_WEHELP>
                <LOG_OUTPUT_MASCARADE>false</LOG_OUTPUT_MASCARADE>
                <LOG_OUTPUT_DEBUG>true</LOG_OUTPUT_DEBUG>
                <ENABLE_LAST_ACTION_TIME>false</ENABLE_LAST_ACTION_TIME>
                <PERMITIR_ENVIO_NOTIFICACAO_PUSH_AULA>false</PERMITIR_ENVIO_NOTIFICACAO_PUSH_AULA>
                <TOKEN_API_WAGI>ZFI5bUMxaUEycVU1cUM1bFk1ZEo3Z0g4YkI4dFcxcE4=</TOKEN_API_WAGI>
                <ZW_BANNERS></ZW_BANNERS>
                <TIPO_MIDIA>ZW_INTERNAL</TIPO_MIDIA>
                <URL_FOTOS_NUVEM>zw-photos</URL_FOTOS_NUVEM>
                <KEYWORD_PATH>/root/.ssh/auth-secrect</KEYWORD_PATH>
            </properties>
            <build>
                <finalName>ZillyonWeb</finalName>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-antrun-plugin</artifactId>
                        <version>1.8</version>
                        <executions>
                            <execution>
                                <phase>package</phase>
                                <goals>
                                    <goal>run</goal>
                                </goals>
                                <configuration>
                                    <target>
                                        <replace file="${project.build.directory}/${project.build.finalName}/WEB-INF/web.xml">
                                            <replacetoken><![CDATA[<session-timeout>120</session-timeout>]]></replacetoken>
                                            <replacevalue><![CDATA[<session-timeout>5</session-timeout>]]></replacevalue>
                                        </replace>
                                    </target>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>telemery</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>aspectj-maven-plugin</artifactId>
                        <version>1.15.0</version>
                        <configuration>
                            <complianceLevel>1.8</complianceLevel>
                            <source>1.8</source>
                            <target>1.8</target>
                            <aspectLibraries>
                                <aspectLibrary>
                                    <groupId>org.aspectj</groupId>
                                    <artifactId>aspectjrt</artifactId>
                                </aspectLibrary>
                            </aspectLibraries>
                        </configuration>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>compile</goal>
                                    <goal>test-compile</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>lw-no-port</id>
            <properties>
                <tempoAguardar>0</tempoAguardar>
                <tempoAguardarUpdatePG>0</tempoAguardarUpdatePG>
                <USAR_URL_RECURSO_EMPRESA>true</USAR_URL_RECURSO_EMPRESA>
                <ZAW_QUEUE>false</ZAW_QUEUE>
                <hostOAMD>localhost</hostOAMD>
                <portaDB>5432</portaDB>
                <urlDatabaseOAMD>jdbc:postgresql://${hostOAMD}:${portaDB}/OAMD</urlDatabaseOAMD>
                <userOAMD>postgres</userOAMD>
                <pwdOAMD>pactodb</pwdOAMD>
                <roboApenasComoServico>true</roboApenasComoServico>
                <APRESENTAR_HOTJAR>false</APRESENTAR_HOTJAR>
                <UTILIZAR_SINTETICO_MS>false</UTILIZAR_SINTETICO_MS>
                <flagSoftware>zw</flagSoftware>
                <contexto>app</contexto>
                <AUTH_SECRET_PERSONA_PATH>/home/<USER>/.ssh/auth-secret-persona-zw</AUTH_SECRET_PERSONA_PATH>
                <loadbalancer.host>zw${sufixoInfra}.pactosolucoes.com.br</loadbalancer.host>
                <glassfish.host>***********${sufixoInfra}</glassfish.host>
                <servicos.host>${glassfish.host}</servicos.host>
                <update-postgres>true</update-postgres>
                <url-servico-update-postgres>
                    http://${loadbalancer.host}/${contexto}/UpdateServlet?op=updatePG&amp;hostPG=${hostOAMD}&amp;portaPG=54${sufixoPorta}&amp;userPG=zillyonweb&amp;pwdPG=pactodb
                </url-servico-update-postgres>
                <urlNotificar>http://${loadbalancer.host}/${contexto}</urlNotificar>
                <msgNotificar>O ZillyonWeb sera atualizado. Continue trabalhando. Voce sera desconectado em alguns minutos. Aproveite os Novos Recursos e Melhorias! A Pacto Solucoes agradece a sua compreensao.</msgNotificar>

                <!-- SuperControle.properties -->
                <ZAW_DATASOURCE>http://${loadbalancer.host}/ZAW_Datasource</ZAW_DATASOURCE>
                <ZAW_URL_NOTF_ACESSO>http://${loadbalancer.host}/${contexto}/UpdateServlet</ZAW_URL_NOTF_ACESSO>
                <ZAW_QUEUE>true</ZAW_QUEUE>
                <ZW_INSTANCIAS>localhost:28080,localhost:28081,localhost:28082,localhost:28083</ZW_INSTANCIAS>
                <URL_TREINO></URL_TREINO>
                <URL_OAMD>http://app.pactosolucoes.com.br/oamd</URL_OAMD>
                <URL_LOGIN>http://app.pactosolucoes.com.br/login</URL_LOGIN>
                <URL_LOGIN_FRONT>https://lgn.pactosolucoes.com.br</URL_LOGIN_FRONT>
                <URL_NFSE>http://nfe2.pactosolucoes.com.br:8070/pactonfse/soap/IPactoNFSE</URL_NFSE>
                <URL_NFSE_REST>http://nfe2.pactosolucoes.com.br:6670/PactoNFSeWSRest/NFSeRESTService</URL_NFSE_REST>
                <URL_SERV_NOTA_FISCAL>https://nfe.pactosolucoes.com.br/servico-nota-fiscal</URL_SERV_NOTA_FISCAL>
                <nfse-rest-host>http://nfe2.pactosolucoes.com.br</nfse-rest-host>
                <nfse-rest-port>6670</nfse-rest-port>
                <URL_MEDIDOR>https://zw30.pactosolucoes.com.br:90${sufixoPorta}/Medidor</URL_MEDIDOR>
                <FOTOS_NUVEM>true</FOTOS_NUVEM>
                <TIPO_MIDIA>AWS_S3</TIPO_MIDIA>
                <URL_FOTOS_NUVEM>https://cdn1.pactorian.net</URL_FOTOS_NUVEM>
                <URL_NFE_NUVEM>https://cdn1.pactorian.net</URL_NFE_NUVEM>
                <SFTP_NOW>false</SFTP_NOW>
                <VALIDAR_USUARIO_OAMD>true</VALIDAR_USUARIO_OAMD>
                <VALIDAR_VERSAO_BD>false</VALIDAR_VERSAO_BD>
                <URL_OAMD_EMPRESAS>https://app.pactosolucoes.com.br/oamd/empresas</URL_OAMD_EMPRESAS>
                <SERVIDOR_MEMCACHED>localhost:11211</SERVIDOR_MEMCACHED>
                <BUSCAR_CONHECIMENTO_UCP>true</BUSCAR_CONHECIMENTO_UCP>
                <URL_BD_REMESSA>****************************************</URL_BD_REMESSA>
                <USER_BD_REMESSA>zillyonweb</USER_BD_REMESSA>
                <PWD_BD_REMESSA>pactodb</PWD_BD_REMESSA>
                <serverSFTPSlave>DCC,DCC_CIELO_ONLINE,DCC_BIN,DCC_E_REDE:sftp.pactosolucoes.com.br:2222:root:p4csf2121</serverSFTPSlave>
                <USAR_URL_RECURSO_EMPRESA>true</USAR_URL_RECURSO_EMPRESA>
                <ENVIAR_EMAIL_VENDAS_ONLINE>true</ENVIAR_EMAIL_VENDAS_ONLINE>
                <DVALIDAR_USUARIO_OAMD>true</DVALIDAR_USUARIO_OAMD>
                <STONE_OPEN_BANK_PRODUCAO>false</STONE_OPEN_BANK_PRODUCAO>
                <URL_JENKINS>http://${loadbalancer.host}/jk</URL_JENKINS>
                <URL_MAILING>http://${loadbalancer.host}/${contexto}/ms</URL_MAILING>
                <BI_MS>http://localhost:28091/bi-ms</BI_MS>
                <EMAIL_SQUAD_ADM_OPEN_TICKET><EMAIL></EMAIL_SQUAD_ADM_OPEN_TICKET>
                <URL_SERVICO_INTEGRACOES_MS>https://${loadbalancer.host}/integracoes-ms</URL_SERVICO_INTEGRACOES_MS>
                <CANCELAR_BOLETO_AO_CANCELAR_OU_ESTORNAR_CONTRATO>true</CANCELAR_BOLETO_AO_CANCELAR_OU_ESTORNAR_CONTRATO>
                <URL_ARAGORN_MS>https://aragorn.ms.pactosolucoes.com.br</URL_ARAGORN_MS>
                <URL_AUTENTICACAO_MS>https://auth.ms.pactosolucoes.com.br</URL_AUTENTICACAO_MS>
                <REDIRECT_URI_CONNECT_PAGBANK>https://app.pactosolucoes.com.br/api/prest/pagbank/authRedirect</REDIRECT_URI_CONNECT_PAGBANK>
            </properties>
        </profile>

        <profile>
            <id>app-just</id>
            <properties>
                <validarInad>false</validarInad>
                <integraProtheus>true</integraProtheus>
                <protheus>http://***********:8083</protheus>
                <urlInad>http://*************:8012/rest/WSPCADALUNO</urlInad>
                <tokenInad>24dc2bdf1702e22cef943bb8367eb495</tokenInad>
                <hostOAMD>prod-zw-just.c33scets5izf.us-east-1.rds.amazonaws.com</hostOAMD>
                <userOAMD>zillyonweb</userOAMD>
                <glassfish.host>aws.justfit.pactosolucoes.com.br</glassfish.host>
                <loadbalancer.host>justfit2.pactosolucoes.com.br</loadbalancer.host>
                <url-servico-update-postgres>
                    http://${loadbalancer.host}/${contexto}/UpdateServlet?op=updatePG&amp;hostPG=${hostOAMD}&amp;portaPG=5432&amp;userPG=zillyonweb&amp;pwdPG=${pwdOAMD}
                </url-servico-update-postgres>
                <BUSCAR_CONHECIMENTO_UCP>true</BUSCAR_CONHECIMENTO_UCP>
                <TIPO_MIDIA>AWS_S3</TIPO_MIDIA>
                <URL_FOTOS_NUVEM>https://cdn1.pactorian.net</URL_FOTOS_NUVEM>
                <ZAW_DATASOURCE>http://aws.justfit.pactosolucoes.com.br/ZAW_Datasource</ZAW_DATASOURCE>
                <DIR_ZAW_DATASOURCE>/opt/NFS/ZAW_Datasource</DIR_ZAW_DATASOURCE>
                <DIR_ZW_ARQ>/opt/NFS/ZW_ARQ</DIR_ZW_ARQ>
                <REDIRECT_LOGIN_SERV_LOCAL>true</REDIRECT_LOGIN_SERV_LOCAL>
                <URL_LOGIN>https://justfit2.pactosolucoes.com.br/login</URL_LOGIN>
            </properties>
        </profile>

        <profile>
            <id>lw-9002</id>
            <properties>
                <!-- cfgBD.xml -->
                <hostOAMD>localhost</hostOAMD>
                <urlDatabaseOAMD>jdbc:postgresql://${hostOAMD}:54${sufixoInfra}/OAMD</urlDatabaseOAMD>
                <userOAMD>postgres</userOAMD>
                <pwdOAMD>pactodb</pwdOAMD>
                <portaDB>54${sufixoInfra}</portaDB>
                <roboApenasComoServico>true</roboApenasComoServico>
                <APRESENTAR_HOTJAR>false</APRESENTAR_HOTJAR>
                <UTILIZAR_SINTETICO_MS>false</UTILIZAR_SINTETICO_MS>
                <flagSoftware>zw</flagSoftware>
                <contexto>app</contexto>
                <AUTH_SECRET_PATH>/home/<USER>/.ssh/auth-secret</AUTH_SECRET_PATH>
                <AUTH_SECRET_PERSONA_PATH>/home/<USER>/.ssh/auth-secret-persona-zw</AUTH_SECRET_PERSONA_PATH>
                <loadbalancer.host>zw${sufixoInfra}.pactosolucoes.com.br:80${sufixoInfra}</loadbalancer.host>
                <glassfish.host>10.33.205.${sufixoInfra}</glassfish.host>
                <servicos.host>${glassfish.host}</servicos.host>
                <update-postgres>true</update-postgres>
                <url-servico-update-postgres>
                    http://${loadbalancer.host}/${contexto}/UpdateServlet?op=updatePG&amp;hostPG=${hostOAMD}&amp;portaPG=54${sufixoInfra}&amp;userPG=zillyonweb&amp;pwdPG=pactodb
                </url-servico-update-postgres>
                <urlNotificar>http://${loadbalancer.host}/${contexto}</urlNotificar>
                <msgNotificar>O ZillyonWeb sera atualizado. Continue trabalhando. Voce sera desconectado em alguns minutos. Aproveite os Novos Recursos e Melhorias! A Pacto Solucoes agradece a sua compreensao.</msgNotificar>
                <!-- SuperControle.properties -->
                <ZAW_DATASOURCE>http://${loadbalancer.host}/ZAW_Datasource</ZAW_DATASOURCE>
                <ZAW_URL_NOTF_ACESSO>http://${loadbalancer.host}/${contexto}/UpdateServlet</ZAW_URL_NOTF_ACESSO>
                <ZAW_QUEUE>true</ZAW_QUEUE>
                <ZW_INSTANCIAS>localhost:28080,localhost:28081,localhost:28082,localhost:28083</ZW_INSTANCIAS>
                <URL_TREINO></URL_TREINO>
                <URL_OAMD>http://app.pactosolucoes.com.br/oamd</URL_OAMD>
                <URL_LOGIN>http://app.pactosolucoes.com.br/login</URL_LOGIN>
                <URL_LOGIN_FRONT>https://lgn.pactosolucoes.com.br</URL_LOGIN_FRONT>
                <URL_NFSE>http://nfe2.pactosolucoes.com.br:8070/pactonfse/soap/IPactoNFSE</URL_NFSE>
                <URL_NFSE_REST>http://nfe2.pactosolucoes.com.br:6670/PactoNFSeWSRest/NFSeRESTService</URL_NFSE_REST>
                <URL_SERV_NOTA_FISCAL>https://nfe.pactosolucoes.com.br/servico-nota-fiscal</URL_SERV_NOTA_FISCAL>
                <nfse-rest-host>http://nfe2.pactosolucoes.com.br</nfse-rest-host>
                <nfse-rest-port>6670</nfse-rest-port>
                <URL_MEDIDOR>https://zw30.pactosolucoes.com.br:90${sufixoInfra}/Medidor</URL_MEDIDOR>
                <FOTOS_NUVEM>true</FOTOS_NUVEM>
                <TIPO_MIDIA>AWS_S3</TIPO_MIDIA>
                <URL_FOTOS_NUVEM>https://cdn1.pactorian.net</URL_FOTOS_NUVEM>
                <URL_NFE_NUVEM>https://cdn1.pactorian.net</URL_NFE_NUVEM>
                <SFTP_NOW>false</SFTP_NOW>
                <VALIDAR_USUARIO_OAMD>true</VALIDAR_USUARIO_OAMD>
                <VALIDAR_VERSAO_BD>false</VALIDAR_VERSAO_BD>
                <URL_OAMD_EMPRESAS>https://app.pactosolucoes.com.br/oamd/empresas</URL_OAMD_EMPRESAS>
                <SERVIDOR_MEMCACHED>localhost:11211</SERVIDOR_MEMCACHED>
                <BUSCAR_CONHECIMENTO_UCP>true</BUSCAR_CONHECIMENTO_UCP>
                <URL_BD_REMESSA>******************************${sufixoInfra}/remessa</URL_BD_REMESSA>
                <USER_BD_REMESSA>zillyonweb</USER_BD_REMESSA>
                <PWD_BD_REMESSA>pactodb</PWD_BD_REMESSA>
                <serverSFTPSlave>DCC,DCC_CIELO_ONLINE,DCC_BIN,DCC_E_REDE:sftp.pactosolucoes.com.br:2222:root:p4csf2121</serverSFTPSlave>
                <USAR_URL_RECURSO_EMPRESA>true</USAR_URL_RECURSO_EMPRESA>
                <ENVIAR_EMAIL_VENDAS_ONLINE>true</ENVIAR_EMAIL_VENDAS_ONLINE>
                <STONE_OPEN_BANK_PRODUCAO>false</STONE_OPEN_BANK_PRODUCAO>
                <URL_JENKINS>http://${loadbalancer.host}/jk</URL_JENKINS>
                <URL_MAILING>http://${loadbalancer.host}/${contexto}/ms</URL_MAILING>
                <BI_MS>http://localhost:28091/bi-ms</BI_MS>
                <EMAIL_SQUAD_ADM_OPEN_TICKET><EMAIL></EMAIL_SQUAD_ADM_OPEN_TICKET>
                <URL_SERVICO_INTEGRACOES_MS>https://${loadbalancer.host}/integracoes-ms</URL_SERVICO_INTEGRACOES_MS>
                <CANCELAR_BOLETO_AO_CANCELAR_OU_ESTORNAR_CONTRATO>true</CANCELAR_BOLETO_AO_CANCELAR_OU_ESTORNAR_CONTRATO>
                <URL_ARAGORN_MS>https://aragorn.ms.pactosolucoes.com.br</URL_ARAGORN_MS>
                <URL_AUTENTICACAO_MS>https://auth.ms.pactosolucoes.com.br</URL_AUTENTICACAO_MS>
                <REDIRECT_URI_CONNECT_PAGBANK>https://app.pactosolucoes.com.br/api/prest/pagbank/authRedirect</REDIRECT_URI_CONNECT_PAGBANK>
            </properties>
        </profile>

        <profile>
            <id>lw-9009</id>
            <properties>
                <!-- cfgBD.xml -->
                <hostOAMD>localhost</hostOAMD>
                <urlDatabaseOAMD>jdbc:postgresql://${hostOAMD}:54${sufixoPorta}/OAMD</urlDatabaseOAMD>
                <userOAMD>postgres</userOAMD>
                <pwdOAMD>pactodb</pwdOAMD>
                <portaDB>54${sufixoPorta}</portaDB>
                <roboApenasComoServico>true</roboApenasComoServico>
                <APRESENTAR_HOTJAR>false</APRESENTAR_HOTJAR>
                <UTILIZAR_SINTETICO_MS>false</UTILIZAR_SINTETICO_MS>
                <AUTH_SECRET_PATH>/home/<USER>/.ssh/auth-secret</AUTH_SECRET_PATH>
                <AUTH_SECRET_PERSONA_PATH>/home/<USER>/.ssh/auth-secret-persona-zw</AUTH_SECRET_PERSONA_PATH>
                <flagSoftware>zw</flagSoftware>
                <contexto>app</contexto>
                <loadbalancer.host>zw${sufixoInfra}.pactosolucoes.com.br:80${sufixoPorta}</loadbalancer.host>
                <glassfish.host>10.33.205.${sufixoInfra}</glassfish.host>
                <servicos.host>${glassfish.host}</servicos.host>
                <update-postgres>true</update-postgres>
                <url-servico-update-postgres>
                    http://${loadbalancer.host}/${contexto}/UpdateServlet?op=updatePG&amp;hostPG=${hostOAMD}&amp;portaPG=54${sufixoPorta}&amp;userPG=zillyonweb&amp;pwdPG=pactodb
                </url-servico-update-postgres>
                <urlNotificar>http://${loadbalancer.host}/${contexto}</urlNotificar>
                <msgNotificar>O ZillyonWeb sera atualizado. Continue trabalhando. Voce sera desconectado em alguns minutos. Aproveite os Novos Recursos e Melhorias! A Pacto Solucoes agradece a sua compreensao.</msgNotificar>

                <!-- SuperControle.properties -->
                <ZAW_DATASOURCE>http://${loadbalancer.host}/ZAW_Datasource</ZAW_DATASOURCE>
                <ZAW_URL_NOTF_ACESSO>http://${loadbalancer.host}/${contexto}/UpdateServlet</ZAW_URL_NOTF_ACESSO>
                <ZAW_QUEUE>true</ZAW_QUEUE>
                <ZW_INSTANCIAS>localhost:28080,localhost:28081,localhost:28082,localhost:28083</ZW_INSTANCIAS>
                <URL_TREINO></URL_TREINO>
                <URL_OAMD>http://app.pactosolucoes.com.br/oamd</URL_OAMD>
                <URL_LOGIN>http://app.pactosolucoes.com.br/login</URL_LOGIN>
                <URL_LOGIN_FRONT>https://lgn.pactosolucoes.com.br</URL_LOGIN_FRONT>
                <URL_NFSE>http://nfe2.pactosolucoes.com.br:8070/pactonfse/soap/IPactoNFSE</URL_NFSE>
                <URL_NFSE_REST>http://nfe2.pactosolucoes.com.br:6670/PactoNFSeWSRest/NFSeRESTService</URL_NFSE_REST>
                <URL_SERV_NOTA_FISCAL>https://nfe.pactosolucoes.com.br/servico-nota-fiscal</URL_SERV_NOTA_FISCAL>
                <nfse-rest-host>http://nfe2.pactosolucoes.com.br</nfse-rest-host>
                <nfse-rest-port>6670</nfse-rest-port>
                <URL_MEDIDOR>https://zw30.pactosolucoes.com.br:90${sufixoPorta}/Medidor</URL_MEDIDOR>
                <FOTOS_NUVEM>true</FOTOS_NUVEM>
                <TIPO_MIDIA>AWS_S3</TIPO_MIDIA>
                <URL_FOTOS_NUVEM>https://cdn1.pactorian.net</URL_FOTOS_NUVEM>
                <URL_NFE_NUVEM>https://cdn1.pactorian.net</URL_NFE_NUVEM>
                <SFTP_NOW>false</SFTP_NOW>
                <VALIDAR_USUARIO_OAMD>true</VALIDAR_USUARIO_OAMD>
                <VALIDAR_VERSAO_BD>false</VALIDAR_VERSAO_BD>
                <URL_OAMD_EMPRESAS>https://app.pactosolucoes.com.br/oamd/empresas</URL_OAMD_EMPRESAS>
                <SERVIDOR_MEMCACHED>localhost:11211</SERVIDOR_MEMCACHED>
                <BUSCAR_CONHECIMENTO_UCP>true</BUSCAR_CONHECIMENTO_UCP>
                <URL_BD_REMESSA>******************************${sufixoPorta}/remessa</URL_BD_REMESSA>
                <USER_BD_REMESSA>zillyonweb</USER_BD_REMESSA>
                <PWD_BD_REMESSA>pactodb</PWD_BD_REMESSA>
                <serverSFTPSlave>DCC,DCC_CIELO_ONLINE,DCC_BIN,DCC_E_REDE:sftp.pactosolucoes.com.br:2222:root:p4csf2121</serverSFTPSlave>
                <USAR_URL_RECURSO_EMPRESA>true</USAR_URL_RECURSO_EMPRESA>
                <ENVIAR_EMAIL_VENDAS_ONLINE>true</ENVIAR_EMAIL_VENDAS_ONLINE>
                <STONE_OPEN_BANK_PRODUCAO>false</STONE_OPEN_BANK_PRODUCAO>
                <URL_JENKINS>http://${loadbalancer.host}/jk</URL_JENKINS>
                <URL_MAILING>http://${loadbalancer.host}/${contexto}/ms</URL_MAILING>
                <BI_MS>http://localhost:28091/bi-ms</BI_MS>
                <EMAIL_SQUAD_ADM_OPEN_TICKET><EMAIL></EMAIL_SQUAD_ADM_OPEN_TICKET>
                <URL_SERVICO_INTEGRACOES_MS>https://${loadbalancer.host}:90${sufixoPorta}/integracoes-ms</URL_SERVICO_INTEGRACOES_MS>
                <CANCELAR_BOLETO_AO_CANCELAR_OU_ESTORNAR_CONTRATO>true</CANCELAR_BOLETO_AO_CANCELAR_OU_ESTORNAR_CONTRATO>
                <URL_ARAGORN_MS>https://aragorn.ms.pactosolucoes.com.br</URL_ARAGORN_MS>
                <URL_AUTENTICACAO_MS>https://auth.ms.pactosolucoes.com.br</URL_AUTENTICACAO_MS>
                <REDIRECT_URI_CONNECT_PAGBANK>https://app.pactosolucoes.com.br/api/prest/pagbank/authRedirect</REDIRECT_URI_CONNECT_PAGBANK>
            </properties>
        </profile>

        <profile>
            <id>homologacao</id>
            <properties>

                <!-- cfgBD.xml -->
                <hostOAMD>localhost</hostOAMD>
                <urlDatabaseOAMD>jdbc:postgresql://${hostOAMD}:5466/OAMD</urlDatabaseOAMD>
                <userOAMD>postgres</userOAMD>
                <pwdOAMD>pactodb</pwdOAMD>
                <roboApenasComoServico>true</roboApenasComoServico>
                <!-- -->

                <!-- parametros exclusivo para deployment -->
                <flagSoftware>zw</flagSoftware>
                <contexto>app</contexto>
                <glassfish.host>homologacao.pactosolucoes.com.br1</glassfish.host>
                <loadbalancer.host>${glassfish.host}:8066</loadbalancer.host>
                <servicos.host>${glassfish.host}</servicos.host>
                <update-postgres>true</update-postgres>
                <url-servico-update-postgres>
                    http://${loadbalancer.host}/${contexto}/UpdateServlet?op=updatePG&amp;hostPG=${hostOAMD}&amp;portaPG=5432&amp;userPG=zillyonweb&amp;pwdPG=pactodb
                </url-servico-update-postgres>
                <urlNotificar>http://${loadbalancer.host}/${contexto}</urlNotificar>
                <msgNotificar>O ZillyonWeb sera atualizado. Continue trabalhando. Voce sera desconectado em alguns minutos. Aproveite os Novos Recursos e Melhorias! A Pacto Solucoes agradece a sua compreensao.</msgNotificar>
                <tempoAguardar>10</tempoAguardar><!--segundos-->
                <tempoAguardarUpdatePG>0</tempoAguardarUpdatePG><!--segundos-->

                <!-- SuperControle.properties -->
                <ZAW_DATASOURCE>http://homologacao.pactosolucoes.com.br:8066/ZAW_Datasource</ZAW_DATASOURCE>
                <ZAW_URL_NOTF_ACESSO>http://${loadbalancer.host}/${contexto}/UpdateServlet</ZAW_URL_NOTF_ACESSO>
                <ZAW_QUEUE>false</ZAW_QUEUE>
                <!--<ZAW_QUEUE_CLUSTER>cluster_zaw</ZAW_QUEUE_CLUSTER>-->
                <ZW_INSTANCIAS>
                    localhost:28080,localhost:28081
                </ZW_INSTANCIAS>
                <URL_TREINO></URL_TREINO>
                <URL_OAMD>http://homologacao.pactosolucoes.com.br:8066/oamd</URL_OAMD>
                <URL_LOGIN></URL_LOGIN>
                <URL_LOGIN_FRONT></URL_LOGIN_FRONT>
                <URL_NFSE>http://suporte.pactosolucoes.com.br:8443/pactonfse/soap/IPactoNFSE</URL_NFSE>
                <URL_NFSE_REST>http://suporte.pactosolucoes.com.br:6670/PactoNFSeWSRest/NFSeRESTService</URL_NFSE_REST>
                <nfse-rest-host>http://suporte.pactosolucoes.com.br</nfse-rest-host>
                <nfse-rest-port>6670</nfse-rest-port>
                <FOTOS_NUVEM>true</FOTOS_NUVEM>
                <TIPO_MIDIA>AWS_S3</TIPO_MIDIA>
                <URL_FOTOS_NUVEM>https://cdn1.pactorian.net</URL_FOTOS_NUVEM>
                <DIR_ZAW_LOGS>/opt/ZAW_Logs</DIR_ZAW_LOGS>
                <DIR_ZAW_DATASOURCE>/opt/ZAW_Datasource</DIR_ZAW_DATASOURCE>
                <DIR_ZAW_UTILS>/opt/ZAW_Utils</DIR_ZAW_UTILS>
                <VALIDAR_VERSAO_BD>true</VALIDAR_VERSAO_BD>
                <URL_OAMD_EMPRESAS>http://homologacao.pactosolucoes.com.br/oamd/empresas</URL_OAMD_EMPRESAS>
                <URL_MODULO_NFSE>http://${loadbalancer.host}/nfe</URL_MODULO_NFSE>
                <URL_FINANCEIRO_PACTO>https://suporte.pactosolucoes.com.br:9443/financeiroWS/soap/IFinanceiroWS</URL_FINANCEIRO_PACTO>

                <SFTP_NOW>false</SFTP_NOW>
                <COOKIE_FAIL>true</COOKIE_FAIL>
                <SERVIDOR_MEMCACHED>localhost:11211</SERVIDOR_MEMCACHED>
                <VALIDAR_USUARIO_OAMD>false</VALIDAR_USUARIO_OAMD>
                <MY_URL_UP_BASE>http://homologacao.pactosolucoes.com.br:8066/ucp</MY_URL_UP_BASE>
                <BUSCAR_CONHECIMENTO_UCP>true</BUSCAR_CONHECIMENTO_UCP>
                <URL_BD_REMESSA>****************************************</URL_BD_REMESSA>
                <URL_JENKINS>http://${loadbalancer.host}/jk</URL_JENKINS>
                <URL_MAILING>http://${loadbalancer.host}/${contexto}/ms</URL_MAILING>
                <BI_MS>http://localhost:28091/bi-ms</BI_MS>
                <URL_SERVICO_INTEGRACOES_MS>https://${loadbalancer.host}/integracoes-ms</URL_SERVICO_INTEGRACOES_MS>
            </properties>
        </profile>

        <profile>
            <id>gqs</id>
            <properties>

                <!-- cfgBD.xml -->
                <hostOAMD>localhost</hostOAMD>
                <urlDatabaseOAMD>jdbc:postgresql://${hostOAMD}:5432/OAMD</urlDatabaseOAMD>
                <userOAMD>postgres</userOAMD>
                <pwdOAMD>pactodb</pwdOAMD>
                <roboApenasComoServico>true</roboApenasComoServico>
                <!-- -->

                <!-- parametros exclusivo para deployment -->
                <flagSoftware>zw</flagSoftware>
                <contexto>app</contexto>

                <servicos.host>${glassfish.host}</servicos.host>
                <update-postgres>true</update-postgres>
                <url-servico-update-postgres>
                    http://${loadbalancer.host}/${contexto}/UpdateServlet?op=updatePG&amp;hostPG=${hostOAMD}&amp;portaPG=5432&amp;userPG=zillyonweb&amp;pwdPG=pactodb
                </url-servico-update-postgres>
                <urlNotificar>http://${loadbalancer.host}/${contexto}</urlNotificar>
                <msgNotificar>O ZillyonWeb sera atualizado. Continue trabalhando. Voce sera desconectado em alguns minutos. Aproveite os Novos Recursos e Melhorias! A Pacto Solucoes agradece a sua compreensao.</msgNotificar>
                <tempoAguardar>10</tempoAguardar><!--segundos-->
                <tempoAguardarUpdatePG>0</tempoAguardarUpdatePG><!--segundos-->

                <!-- SuperControle.properties -->
                <ZAW_DATASOURCE>http://${loadbalancer.host}/ZAW_Datasource</ZAW_DATASOURCE>
                <ZAW_URL_NOTF_ACESSO>http://${loadbalancer.host}/${contexto}/UpdateServlet</ZAW_URL_NOTF_ACESSO>
                <ZAW_QUEUE>false</ZAW_QUEUE>
                <!--<ZAW_QUEUE_CLUSTER>cluster_zaw</ZAW_QUEUE_CLUSTER>-->
                <ZW_INSTANCIAS></ZW_INSTANCIAS>
                <URL_TREINO></URL_TREINO>
                <URL_OAMD>http://${loadbalancer.host}/oamd</URL_OAMD>
                <URL_LOGIN></URL_LOGIN>
                <URL_LOGIN_FRONT></URL_LOGIN_FRONT>
                <URL_NFSE>http://suporte.pactosolucoes.com.br:8443/pactonfse/soap/IPactoNFSE</URL_NFSE>
                <URL_NFSE_REST>http://suporte.pactosolucoes.com.br:6670/PactoNFSeWSRest/NFSeRESTService</URL_NFSE_REST>
                <nfse-rest-host>http://suporte.pactosolucoes.com.br</nfse-rest-host>
                <nfse-rest-port>6670</nfse-rest-port>
                <FOTOS_NUVEM>true</FOTOS_NUVEM>
                <TIPO_MIDIA>ZW_INTERNAL</TIPO_MIDIA>
                <URL_FOTOS_NUVEM>zw-photos</URL_FOTOS_NUVEM>
                <DIR_ZAW_LOGS>/opt/ZAW_Logs</DIR_ZAW_LOGS>
                <DIR_ZAW_DATASOURCE>/opt/ZAW_Datasource</DIR_ZAW_DATASOURCE>
                <DIR_ZAW_UTILS>/opt/ZAW_Utils</DIR_ZAW_UTILS>
                <VALIDAR_VERSAO_BD>true</VALIDAR_VERSAO_BD>
                <URL_OAMD_EMPRESAS>http://${loadbalancer.host}/oamd/empresas</URL_OAMD_EMPRESAS>
                <URL_MODULO_NFSE>http://${loadbalancer.host}/nfe</URL_MODULO_NFSE>
                <URL_FINANCEIRO_PACTO>http://desenv-luiz:8443/financeiroWS/soap/IFinanceiroWS</URL_FINANCEIRO_PACTO>

                <SFTP_NOW>false</SFTP_NOW>
                <COOKIE_FAIL>false</COOKIE_FAIL>
                <SERVIDOR_MEMCACHED></SERVIDOR_MEMCACHED>
                <INTEGRACAO_WIKI>true</INTEGRACAO_WIKI>
                <VALIDAR_USUARIO_OAMD>false</VALIDAR_USUARIO_OAMD>
                <MY_URL_UP_BASE>http://${loadbalancer.host}/ucp</MY_URL_UP_BASE>
                <BUSCAR_CONHECIMENTO_UCP>true</BUSCAR_CONHECIMENTO_UCP>
                <serverSFTPSlave>DCC,DCC_CIELO_ONLINE,DCC_BIN,DCC_E_REDE:sftp.pactosolucoes.com.br:2222:root:p4csf2121</serverSFTPSlave>
            </properties>
        </profile>

        <profile>
            <id>prod-cliente-cluster</id>
            <properties>
                <!-- cfgBD.xml -->
                <hostOAMD>localhost</hostOAMD>
                <urlDatabaseOAMD>jdbc:postgresql://${hostOAMD}:5432/OAMD</urlDatabaseOAMD>
                <userOAMD>postgres</userOAMD>
                <pwdOAMD>pactodb</pwdOAMD>
                <roboApenasComoServico>true</roboApenasComoServico>
                <!-- -->

                <!-- parametros exclusivo para deployment -->
                <flagSoftware>zw</flagSoftware>
                <!--<contexto>app</contexto>-->
                <!--<loadbalancer.host>app.pactosolucoes.com.br</loadbalancer.host>
                <glassfish.host>********</glassfish.host>-->
                <servicos.host>${glassfish.host}</servicos.host>
                <update-postgres>true</update-postgres>
                <url-servico-update-postgres>
                    http://${loadbalancer.host}/${contexto}/UpdateServlet?op=updatePG&amp;hostPG=${hostOAMD}&amp;portaPG=5432&amp;userPG=zillyonweb&amp;pwdPG=pactodb
                </url-servico-update-postgres>
                <urlNotificar>http://${loadbalancer.host}/${contexto}</urlNotificar>
                <msgNotificar>O ZillyonWeb sera atualizado. Continue trabalhando. Voce sera desconectado em alguns minutos. Aproveite os Novos Recursos e Melhorias! A Pacto Solucoes agradece a sua compreensao.</msgNotificar>

                <!-- SuperControle.properties -->
                <ZAW_DATASOURCE>http://${loadbalancer.host}/ZAW_Datasource</ZAW_DATASOURCE>
                <ZAW_URL_NOTF_ACESSO>http://${loadbalancer.host}/${contexto}/UpdateServlet</ZAW_URL_NOTF_ACESSO>
                <!--<ZAW_QUEUE_CLUSTER>cluster_zaw</ZAW_QUEUE_CLUSTER>-->
                <!--<ZW_INSTANCIAS></ZW_INSTANCIAS>-->
                <URL_OAMD>http://app.pactosolucoes.com.br/oamd</URL_OAMD>
                <URL_LOGIN>http://app.pactosolucoes.com.br/login</URL_LOGIN>
                <URL_LOGIN_FRONT>https://lgn.pactosolucoes.com.br</URL_LOGIN_FRONT>
                <URL_NFSE>http://nfe2.pactosolucoes.com.br:8070/pactonfse/soap/IPactoNFSE</URL_NFSE>
                <URL_NFSE_REST>http://nfe2.pactosolucoes.com.br:6670/PactoNFSeWSRest/NFSeRESTService</URL_NFSE_REST>
                <URL_SERV_NOTA_FISCAL>https://nfe.pactosolucoes.com.br/servico-nota-fiscal</URL_SERV_NOTA_FISCAL>
                <nfse-rest-host>http://nfe2.pactosolucoes.com.br</nfse-rest-host>
                <nfse-rest-port>6670</nfse-rest-port>
                <FOTOS_NUVEM>true</FOTOS_NUVEM>
                <TIPO_MIDIA>AWS_S3</TIPO_MIDIA>
                <URL_FOTOS_NUVEM>https://cdn1.pactorian.net</URL_FOTOS_NUVEM>
                <URL_NFE_NUVEM>https://cdn1.pactorian.net</URL_NFE_NUVEM>
                <DIR_ZAW_LOGS>/opt/ZAW_Logs</DIR_ZAW_LOGS>
                <DIR_ZAW_DATASOURCE>/opt/ZAW_Datasource</DIR_ZAW_DATASOURCE>
                <DIR_ZAW_UTILS>/opt/ZAW_Utils</DIR_ZAW_UTILS>
                <VALIDAR_USUARIO_OAMD>true</VALIDAR_USUARIO_OAMD>
                <VALIDAR_VERSAO_BD>false</VALIDAR_VERSAO_BD>
                <URL_OAMD_EMPRESAS>https://app.pactosolucoes.com.br/oamd/empresas</URL_OAMD_EMPRESAS>

                <SFTP_NOW>false</SFTP_NOW>
                <COOKIE_FAIL>false</COOKIE_FAIL>
                <USAR_URL_RECURSO_EMPRESA>true</USAR_URL_RECURSO_EMPRESA>
                <ENVIAR_EMAIL_VENDAS_ONLINE>true</ENVIAR_EMAIL_VENDAS_ONLINE>
                <STONE_OPEN_BANK_PRODUCAO>false</STONE_OPEN_BANK_PRODUCAO>
                <AUTH_SECRET_PATH>/root/.ssh/auth-secret</AUTH_SECRET_PATH>
                <AUTH_SECRET_PERSONA_PATH>/root/.ssh/auth-secret-persona-zw</AUTH_SECRET_PERSONA_PATH>
                <URL_JENKINS>http://localhost:8080/jk</URL_JENKINS>
                <URL_MAILING>http://localhost:8080/${contexto}/ms</URL_MAILING>
                <EMAIL_SQUAD_ADM_OPEN_TICKET><EMAIL></EMAIL_SQUAD_ADM_OPEN_TICKET>
                <URL_SERVICO_INTEGRACOES_MS>https://${loadbalancer.host}/integracoes-ms</URL_SERVICO_INTEGRACOES_MS>
                <CANCELAR_BOLETO_AO_CANCELAR_OU_ESTORNAR_CONTRATO>true</CANCELAR_BOLETO_AO_CANCELAR_OU_ESTORNAR_CONTRATO>
            </properties>
        </profile>

        <profile>
            <id>prod-cliente-no-cluster</id>
            <properties>
                <!-- cfgBD.xml -->
                <hostOAMD>localhost</hostOAMD>
                <urlDatabaseOAMD>jdbc:postgresql://${hostOAMD}:5432/OAMD</urlDatabaseOAMD>
                <userOAMD>postgres</userOAMD>
                <pwdOAMD>pactodb</pwdOAMD>
                <roboApenasComoServico>true</roboApenasComoServico>
                <!-- -->

                <!-- parametros exclusivo para deployment -->
                <flagSoftware>zw</flagSoftware>
                <!--<contexto>app</contexto>-->
                <!--<loadbalancer.host>app.pactosolucoes.com.br</loadbalancer.host>
                <glassfish.host>********</glassfish.host>-->
                <servicos.host>${glassfish.host}</servicos.host>
                <update-postgres>true</update-postgres>
                <url-servico-update-postgres>
                    http://${loadbalancer.host}/${contexto}/UpdateServlet?op=updatePG&amp;hostPG=${hostOAMD}&amp;portaPG=5432&amp;userPG=zillyonweb&amp;pwdPG=pactodb
                </url-servico-update-postgres>
                <urlNotificar>http://${loadbalancer.host}/${contexto}</urlNotificar>

                <!-- SuperControle.properties -->
                <ZAW_DATASOURCE>http://${loadbalancer.host}/ZAW_Datasource</ZAW_DATASOURCE>
                <!--<ZAW_URL_NOTF_ACESSO>http://${loadbalancer.host}/${contexto}/UpdateServlet</ZAW_URL_NOTF_ACESSO>-->
                <!--<ZAW_QUEUE>false</ZAW_QUEUE>-->
                <!--<ZAW_QUEUE_CLUSTER></ZAW_QUEUE_CLUSTER>-->
                <!--<ZW_INSTANCIAS></ZW_INSTANCIAS>-->
                <URL_OAMD>http://app.pactosolucoes.com.br/oamd</URL_OAMD>
                <!--<URL_LOGIN>http://app.pactosolucoes.com.br/login</URL_LOGIN>-->
                <REDIRECT_LOGIN_SERV_LOCAL>true</REDIRECT_LOGIN_SERV_LOCAL>
                <URL_NFSE>http://nfe2.pactosolucoes.com.br:8070/pactonfse/soap/IPactoNFSE</URL_NFSE>
                <URL_NFSE_REST>http://nfe2.pactosolucoes.com.br:6670/PactoNFSeWSRest/NFSeRESTService</URL_NFSE_REST>
                <URL_SERV_NOTA_FISCAL>https://nfe.pactosolucoes.com.br/servico-nota-fiscal</URL_SERV_NOTA_FISCAL>
                <nfse-rest-host>http://nfe2.pactosolucoes.com.br</nfse-rest-host>
                <nfse-rest-port>6670</nfse-rest-port>
                <FOTOS_NUVEM>true</FOTOS_NUVEM>
                <TIPO_MIDIA>ZW_INTERNAL</TIPO_MIDIA>
                <URL_FOTOS_NUVEM>zw-photos</URL_FOTOS_NUVEM>
                <DIR_ZAW_LOGS>/opt/ZAW_Logs</DIR_ZAW_LOGS>
                <DIR_ZAW_DATASOURCE>/opt/ZAW_Datasource</DIR_ZAW_DATASOURCE>
                <DIR_ZAW_UTILS>/opt/ZAW_Utils</DIR_ZAW_UTILS>
                <VALIDAR_USUARIO_OAMD>true</VALIDAR_USUARIO_OAMD>
                <VALIDAR_VERSAO_BD>false</VALIDAR_VERSAO_BD>
                <URL_OAMD_EMPRESAS>https://app.pactosolucoes.com.br/oamd/empresas</URL_OAMD_EMPRESAS>

                <SFTP_NOW>false</SFTP_NOW>
                <COOKIE_FAIL>false</COOKIE_FAIL>
                <serverSFTPSlave>DCC,DCC_CIELO_ONLINE,DCC_BIN,DCC_E_REDE:sftp.pactosolucoes.com.br:2222:root:p4csf2121</serverSFTPSlave>
                <USAR_URL_RECURSO_EMPRESA>true</USAR_URL_RECURSO_EMPRESA>
                <ENVIAR_EMAIL_VENDAS_ONLINE>true</ENVIAR_EMAIL_VENDAS_ONLINE>
                <STONE_OPEN_BANK_PRODUCAO>false</STONE_OPEN_BANK_PRODUCAO>
                <AUTH_SECRET_PATH>/root/.ssh/auth-secret</AUTH_SECRET_PATH>
                <AUTH_SECRET_PERSONA_PATH>/root/.ssh/auth-secret-persona-zw</AUTH_SECRET_PERSONA_PATH>
                <URL_JENKINS>http://localhost:8080/jk</URL_JENKINS>
                <URL_MAILING>http://localhost:8080/${contexto}/ms</URL_MAILING>
                <BI_MS>http://localhost:28091/bi-ms</BI_MS>
                <EMAIL_SQUAD_ADM_OPEN_TICKET><EMAIL></EMAIL_SQUAD_ADM_OPEN_TICKET>
                <URL_SERVICO_INTEGRACOES_MS>http://${loadbalancer.host}/integracoes-ms</URL_SERVICO_INTEGRACOES_MS>
                <CANCELAR_BOLETO_AO_CANCELAR_OU_ESTORNAR_CONTRATO>true</CANCELAR_BOLETO_AO_CANCELAR_OU_ESTORNAR_CONTRATO>
            </properties>
        </profile>

        <profile>
            <id>glassfish-local</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-antrun-plugin</artifactId>
                        <version>1.8</version>
                        <dependencies>
                            <dependency>
                                <groupId>br.com.pacto.lib</groupId>
                                <artifactId>xmltask</artifactId>
                                <version>1.16</version>
                            </dependency>
                        </dependencies>
                        <executions>
                            <execution>
                                <id>exclude-jaxws</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>run</goal>
                                </goals>
                                <configuration>
                                    <target name="task-remover-jax">
                                        <echo message="REMOVENDO JAX-WS..."/>
                                        <delete failonerror="true">
                                            <fileset dir="${basedir}/target/${project.name}/WEB-INF/lib"
                                                     includes="jax*.jar"/>
                                        </delete>
                                    </target>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>generate-ws</id>
            <repositories>
                <repository>
                    <id>nexusLocal</id>
                    <name>Pacto Maven Repository</name>
                    <url>https://mussum.ath.cx/nexus/content/groups/public</url>
                    <layout>default</layout>
                </repository>
                <repository>
                    <id>nexusSolutioIn</id>
                    <name>Solutio-in Maven Repository</name>
                    <url>https://mussum.ath.cx/nexus/content/repositories/solutio</url>
                    <layout>default</layout>
                </repository>
            </repositories>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.jvnet.jax-ws-commons</groupId>
                        <artifactId>jaxws-maven-plugin</artifactId>
                        <version>2.2</version>
                        <executions>
                            <execution>
                                <id>generate-ws-acesso</id>
                                <phase>process-classes</phase>
                                <goals>
                                    <goal>wsgen</goal>
                                </goals>
                                <configuration>
                                    <sei>acesso.webservice.ValidacaoAcessoWS</sei>
                                    <genWsdl>false</genWsdl>
                                    <keep>false</keep>

                                    <resourceDestDir>${basedir}/src/main/webapp/WEB-INF/wsdl</resourceDestDir>
                                </configuration>
                            </execution>
                            <execution>
                                <id>generate-ws-registraracesso</id>
                                <phase>process-classes</phase>
                                <goals>
                                    <goal>wsgen</goal>
                                </goals>
                                <configuration>
                                    <sei>acesso.webservice.RegistrarAcessoWS</sei>
                                    <genWsdl>false</genWsdl>
                                    <keep>false</keep>
                                    <resourceDestDir>${basedir}/src/main/webapp/WEB-INF/wsdl</resourceDestDir>
                                </configuration>
                            </execution>
                            <execution>
                                <id>generate-ws-cad</id>
                                <phase>compile</phase>
                                <goals>
                                    <goal>wsgen</goal>
                                </goals>
                                <configuration>
                                    <sei>negocio.comuns.basico.webservice.IntegracaoCadastrosWS</sei>
                                    <genWsdl>false</genWsdl>
                                    <keep>false</keep>
                                    <resourceDestDir>${basedir}/src/main/webapp/WEB-INF/wsdl</resourceDestDir>
                                </configuration>
                            </execution>
                            <execution>
                                <id>generate-ws-ecf</id>
                                <phase>process-classes</phase>
                                <goals>
                                    <goal>wsgen</goal>
                                </goals>
                                <configuration>
                                    <sei>br.com.pactosolucoes.ecf.cupomfiscal.ws.EmissorCupomWS</sei>
                                    <genWsdl>false</genWsdl>
                                    <keep>false</keep>
                                    <resourceDestDir>${basedir}/src/main/webapp/WEB-INF/wsdl</resourceDestDir>
                                </configuration>
                            </execution>
                            <execution>
                                <id>generate-ws-adm</id>
                                <phase>process-classes</phase>
                                <goals>
                                    <goal>wsgen</goal>
                                </goals>
                                <configuration>
                                    <sei>servicos.integracao.adm.AdmWS</sei>
                                    <genWsdl>false</genWsdl>
                                    <keep>false</keep>
                                    <resourceDestDir>${basedir}/src/main/webapp/WEB-INF/wsdl</resourceDestDir>
                                </configuration>
                            </execution>
                            <execution>
                                <id>generate-ws-negociacao</id>
                                <phase>process-classes</phase>
                                <goals>
                                    <goal>wsgen</goal>
                                </goals>
                                <configuration>
                                    <sei>negocio.comuns.plano.webservice.NegociacaoWS</sei>
                                    <genWsdl>false</genWsdl>
                                    <keep>false</keep>
                                    <resourceDestDir>${basedir}/src/main/webapp/WEB-INF/wsdl</resourceDestDir>
                                </configuration>
                            </execution>
                            <execution>
                                <id>generate-ws-financeiro</id>
                                <phase>process-classes</phase>
                                <goals>
                                    <goal>wsgen</goal>
                                </goals>
                                <configuration>
                                    <sei>negocio.comuns.financeiro.webservice.FinanceiroWS</sei>
                                    <genWsdl>false</genWsdl>
                                    <keep>false</keep>
                                    <resourceDestDir>${basedir}/src/main/webapp/WEB-INF/wsdl</resourceDestDir>
                                </configuration>
                            </execution>
                        </executions>

                        <dependencies>
                            <dependency>
                                <groupId>com.sun.xml.ws</groupId>
                                <artifactId>jaxws-ri</artifactId>
                                <version>2.2.6</version>
                                <type>pom</type>
                            </dependency>
                            <dependency>
                                <groupId>javax.activation</groupId>
                                <artifactId>activation</artifactId>
                                <version>1.1.1</version>
                            </dependency>
                            <dependency>
                                <groupId>commons-beanutils</groupId>
                                <artifactId>commons-beanutils</artifactId>
                                <version>1.7.0</version>
                            </dependency>
                            <dependency>
                                <groupId>backport-util-concurrent</groupId>
                                <artifactId>backport-util-concurrent</artifactId>
                                <version>3.1</version>
                            </dependency>

                        </dependencies>

                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>scp</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-antrun-plugin</artifactId>
                        <version>1.8</version>
                        <dependencies>
                            <dependency>
                                <groupId>ant</groupId>
                                <artifactId>ant-jsch</artifactId>
                                <version>1.6.5</version>
                            </dependency>
                            <dependency>
                                <groupId>com.jcraft</groupId>
                                <artifactId>jsch</artifactId>
                                <version>0.1.55</version>
                            </dependency>
                        </dependencies>
                        <executions>
                            <execution>
                                <id>scp-exec</id>
                                <phase>pre-integration-test</phase>
                                <goals>
                                    <goal>run</goal>
                                </goals>
                                <configuration>
                                    <target name="task-scp">
                                        <tstamp>
                                            <format property="now" pattern="yyyy-MM-dd HH:mm:ss"/>
                                        </tstamp>
                                        <echo message="${now} Transferindo ${sshUser}@${glassfish.host}:/opt/${contexto}.war..."/>
                                        <scp trust="true" file="${basedir}/target/${project.name}.war"
                                             port="${sshPort}"
                                             verbose="false"
                                             keyfile="${user.home}/.ssh/id_rsa"
                                             passphrase=""
                                             todir="${sshUser}@${glassfish.host}:/opt/${contexto}.war"/>
                                        <tstamp>
                                            <format property="end" pattern="yyyy-MM-dd HH:mm:ss"/>
                                        </tstamp>
                                        <echo message="${end} Transferencia concluida!"/>
                                    </target>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>notificar</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-antrun-plugin</artifactId>
                        <version>1.8</version>
                        <dependencies>
                            <dependency>
                                <groupId>ant-contrib</groupId>
                                <artifactId>ant-contrib</artifactId>
                                <version>1.0b3</version>
                                <exclusions>
                                    <exclusion>
                                        <groupId>ant</groupId>
                                        <artifactId>ant</artifactId>
                                    </exclusion>
                                </exclusions>
                            </dependency>
                        </dependencies>
                        <executions>
                            <execution>
                                <id>notificar-exec</id>
                                <phase>pre-integration-test</phase>
                                <goals>
                                    <goal>run</goal>
                                </goals>
                                <configuration>
                                    <target xmlns:ac="antlib:net.sf.antcontrib">
                                        <tstamp>
                                            <format property="dti" pattern="yyyyMMddHHmmss"/>
                                        </tstamp>

                                        <tstamp>
                                            <format property="dtf" pattern="yyyyMMddHHmmss" unit="second" offset="${tempoAguardar}"/>
                                        </tstamp>

                                        <echo message="Notificar usuarios de que o sistema sera atualizado em: ${tempoAguardar} segundos ..."/>

                                        <ac:urlencode name="requestNotificar" property="msgparam" value="${msgNotificar}" xmlns:ac="antlib:net.sf.antcontrib"/>

                                        <ac:trycatch property="foo" reference="bar">
                                            <ac:try>
                                                <get src="${urlNotificar}/UpdateServlet?op=createNOTF&amp;desc=${msgparam}&amp;dti=${dti}&amp;dtf=${dtf}&amp;propagable=s"
                                                     dest="notificacao"
                                                     verbose="false"/>
                                            </ac:try>

                                            <ac:catch></ac:catch>

                                            <ac:finally></ac:finally>
                                        </ac:trycatch>

                                        <echo message="Esperar o usuario: ${tempoAguardar} segundos para realizar o Deployment..."/>
                                        <sleep seconds="${tempoAguardar}"/>
                                    </target>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>redeploy</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-antrun-plugin</artifactId>
                        <version>1.8</version>
                        <dependencies>
                            <dependency>
                                <groupId>ant</groupId>
                                <artifactId>ant-jsch</artifactId>
                                <version>1.6.5</version>
                            </dependency>
                            <dependency>
                                <groupId>com.jcraft</groupId>
                                <artifactId>jsch</artifactId>
                                <version>0.1.55</version>
                            </dependency>
                            <dependency>
                                <groupId>ant-contrib</groupId>
                                <artifactId>ant-contrib</artifactId>
                                <version>1.0b3</version>
                                <exclusions>
                                    <exclusion>
                                        <artifactId>ant</artifactId>
                                        <groupId>ant</groupId>
                                    </exclusion>
                                </exclusions>
                            </dependency>
                        </dependencies>
                        <executions>
                            <execution>
                                <id>redeploy-exec</id>
                                <phase>pre-integration-test</phase>
                                <goals>
                                    <goal>run</goal>
                                </goals>
                                <configuration>
                                    <target name="task-redeploy">
                                        <tstamp>
                                            <format property="now" pattern="yyyy-MM-dd HH:mm:ss"/>
                                        </tstamp>
                                        <echo message="${now} Implantar em Glassfish..."/>
                                        <sshexec port="${sshPort}" verbose="false"
                                                 trust="true" host="${glassfish.host}"
                                                 username="${sshUser}"
                                                 keyfile="${user.home}/.ssh/id_rsa"
                                                 passphrase=""
                                                 command="sh /opt/redeploy.sh ${contexto}"/>
                                        <tstamp>
                                            <format property="end" pattern="yyyy-MM-dd HH:mm:ss"/>
                                        </tstamp>
                                        <echo message="${end} OK!"/>
                                    </target>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>atualizarPG</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-antrun-plugin</artifactId>
                        <version>1.8</version>
                        <dependencies>
                            <dependency>
                                <groupId>ant</groupId>
                                <artifactId>ant-jsch</artifactId>
                                <version>1.6.5</version>
                            </dependency>
                            <dependency>
                                <groupId>com.jcraft</groupId>
                                <artifactId>jsch</artifactId>
                                <version>0.1.55</version>
                            </dependency>
                            <dependency>
                                <groupId>ant-contrib</groupId>
                                <artifactId>ant-contrib</artifactId>
                                <version>1.0b3</version>
                                <exclusions>
                                    <exclusion>
                                        <artifactId>ant</artifactId>
                                        <groupId>ant</groupId>
                                    </exclusion>
                                </exclusions>
                            </dependency>
                        </dependencies>
                        <executions>
                            <execution>
                                <id>atualizarPG-exec</id>
                                <phase>install</phase>
                                <goals>
                                    <goal>run</goal>
                                </goals>
                                <configuration>
                                    <target>
                                        <echo message="Preparar para Executar atualizacao PostgreSQL... esperando ${tempoAguardarUpdatePG}s (ate que a aplicacao esteja pronta)..."/>
                                        <sleep seconds="${tempoAguardarUpdatePG}"/>
                                        <tstamp>
                                            <format property="now" pattern="yyyy-MM-dd HH:mm:ss"/>
                                        </tstamp>
                                        <sshexec port="${sshPort}" verbose="true"
                                                 trust="true" host="${servicos.host}"
                                                 username="${sshUser}"
                                                 keyfile="${user.home}/.ssh/id_rsa"
                                                 passphrase=""
                                                 command="nohup /opt/RobotRunner/ATUALIZABD-WORKERS.sh >/dev/null 2>&amp;1"/>
                                        <tstamp>
                                            <format property="end" pattern="yyyy-MM-dd HH:mm:ss"/>
                                        </tstamp>
                                    </target>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>restart-glassfish-full</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-antrun-plugin</artifactId>
                        <version>1.8</version>
                        <dependencies>
                            <dependency>
                                <groupId>ant</groupId>
                                <artifactId>ant-jsch</artifactId>
                                <version>1.6.5</version>
                            </dependency>
                            <dependency>
                                <groupId>com.jcraft</groupId>
                                <artifactId>jsch</artifactId>
                                <version>0.1.55</version>
                            </dependency>
                        </dependencies>
                        <executions>
                            <execution>
                                <id>restart-glassfish-full-exec</id>
                                <phase>install</phase>
                                <goals>
                                    <goal>run</goal>
                                </goals>
                                <configuration>
                                    <target name="task-restart-glassfish">
                                        <tstamp>
                                            <format property="now" pattern="yyyy-MM-dd HH:mm:ss"/>
                                        </tstamp>
                                        <echo message="${now} Reiniciando Glassfish..."/>
                                        <sshexec port="${sshPort}" verbose="false"
                                                 trust="true" host="${glassfish.host}"
                                                 username="${sshUser}"
                                                 keyfile="${user.home}/.ssh/id_rsa"
                                                 passphrase=""
                                                 command="sh /opt/restart-glassfish.sh"/>
                                        <tstamp>
                                            <format property="end" pattern="yyyy-MM-dd HH:mm:ss"/>
                                        </tstamp>
                                        <echo message="${end} OK!"/>
                                    </target>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>restart-glassfish-single-instance</id>
            <properties>
                <instanceName>zw-01</instanceName>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-antrun-plugin</artifactId>
                        <version>1.8</version>
                        <dependencies>
                            <dependency>
                                <groupId>ant</groupId>
                                <artifactId>ant-jsch</artifactId>
                                <version>1.6.5</version>
                            </dependency>
                            <dependency>
                                <groupId>com.jcraft</groupId>
                                <artifactId>jsch</artifactId>
                                <version>0.1.55</version>
                            </dependency>
                        </dependencies>
                        <executions>
                            <execution>
                                <id>restart-glassfish-single-instance-exec</id>
                                <phase>install</phase>
                                <goals>
                                    <goal>run</goal>
                                </goals>
                                <configuration>
                                    <target name="task-restart-glassfish">
                                        <tstamp>
                                            <format property="now" pattern="yyyy-MM-dd HH:mm:ss"/>
                                        </tstamp>
                                        <echo message="${now} Reiniciando Instância ${instanceName}"/>
                                        <sshexec port="${sshPort}" verbose="false"
                                                 trust="true" host="${glassfish.host}"
                                                 username="${sshUser}"
                                                 keyfile="${user.home}/.ssh/id_rsa"
                                                 passphrase=""
                                                 command="sh /root/fi ${instanceName}"/>
                                        <tstamp>
                                            <format property="end" pattern="yyyy-MM-dd HH:mm:ss"/>
                                        </tstamp>
                                        <echo message="${end} OK!"/>
                                    </target>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>restart-glassfish-all-instances</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-antrun-plugin</artifactId>
                        <version>1.8</version>
                        <dependencies>
                            <dependency>
                                <groupId>ant</groupId>
                                <artifactId>ant-jsch</artifactId>
                                <version>1.6.5</version>
                            </dependency>
                            <dependency>
                                <groupId>com.jcraft</groupId>
                                <artifactId>jsch</artifactId>
                                <version>0.1.55</version>
                            </dependency>
                        </dependencies>
                        <executions>
                            <execution>
                                <id>restart-glassfish-all-instances-exec</id>
                                <phase>install</phase>
                                <goals>
                                    <goal>run</goal>
                                </goals>
                                <configuration>
                                    <target name="task-restart-glassfish-all-instances">
                                        <tstamp>
                                            <format property="now" pattern="yyyy-MM-dd HH:mm:ss"/>
                                        </tstamp>
                                        <echo message="${now} Reiniciando instâncias Glassfish..."/>
                                        <sshexec trust="true"
                                                 port="${sshPort}"
                                                 verbose="false"
                                                 host="${glassfish.host}"
                                                 username="${sshUser}"
                                                 keyfile="${user.home}/.ssh/id_rsa"
                                                 passphrase=""
                                                 command="sh /opt/restart-glassfish-parallel.sh"/>
                                        <tstamp>
                                            <format property="end" pattern="yyyy-MM-dd HH:mm:ss"/>
                                        </tstamp>
                                        <echo message="${end} all instances restarted!"/>
                                    </target>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>create-jms-queue</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-antrun-plugin</artifactId>
                        <version>1.8</version>
                        <dependencies>
                            <dependency>
                                <groupId>ant</groupId>
                                <artifactId>ant-jsch</artifactId>
                                <version>1.6.5</version>
                            </dependency>
                            <dependency>
                                <groupId>com.jcraft</groupId>
                                <artifactId>jsch</artifactId>
                                <version>0.1.55</version>
                            </dependency>
                            <dependency>
                                <groupId>ant-contrib</groupId>
                                <artifactId>ant-contrib</artifactId>
                                <version>1.0b3</version>
                                <exclusions>
                                    <exclusion>
                                        <artifactId>ant</artifactId>
                                        <groupId>ant</groupId>
                                    </exclusion>
                                </exclusions>
                            </dependency>
                        </dependencies>
                        <executions>
                            <execution>
                                <id>create-jms-queue-exec</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>run</goal>
                                </goals>
                                <configuration>
                                    <target name="task-create-jms-queue">
                                        <echo message="Tentar criar JMS QUEUE ZAW_QUEUE..."/>
                                        <taskdef resource="net/sf/antcontrib/antcontrib.properties"/>
                                        <trycatch property="exc" reference="bar">
                                            <try>
                                                <sshexec port="${sshPort}" verbose="false" trust="true" host="${glassfish.host}"
                                                         username="root"
                                                         keyfile="${user.home}/.ssh/id_rsa"
                                                         passphrase=""
                                                         command="sh /opt/glassfish3/bin/asadmin create-jms-resource --target ${ZAW_QUEUE_CLUSTER} --restype javax.jms.QueueConnectionFactory --property ClientId=JMS_ZAW_QueueFactory JMS_ZAW_QueueFactory"/>
                                                <echo message="OK!"/>
                                            </try>
                                            <catch>
                                                <echo message="Erro: ${exc}"/>
                                            </catch>
                                        </trycatch>
                                    </target>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

    </profiles>


    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.0</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <compilerArgument>-XDignore.symbol.file</compilerArgument>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>2.2</version>
                <configuration>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.samaxes.maven</groupId>
                <artifactId>minify-maven-plugin</artifactId>
                <version>1.6.2</version>
                <dependencies>
                    <dependency>
                        <groupId>org.codehaus.plexus</groupId>
                        <artifactId>plexus-utils</artifactId>
                        <version>3.3.0</version>
                    </dependency>
                </dependencies>
                <executions>
                    <execution>
                        <id>default-minify</id>
                        <phase>prepare-package</phase>
                        <configuration>
                            <cssSourceDir>css</cssSourceDir>
                            <cssTargetDir>css</cssTargetDir>
                            <cssFinalFile>pacto_flat_2.16.css</cssFinalFile>
                            <cssSourceFiles>
                                <cssSourceFile>pacto_flat.css</cssSourceFile>
                            </cssSourceFiles>
                        </configuration>
                        <goals>
                            <goal>minify</goal>
                        </goals>
                    </execution>

                    <execution>
                        <id>default-minify-pacto-icon-font</id>
                        <phase>prepare-package</phase>
                        <configuration>
                            <cssSourceDir>beta/css</cssSourceDir>
                            <cssTargetDir>beta/css</cssTargetDir>
                            <cssFinalFile>pacto-icon-font4.0.css</cssFinalFile>
                            <cssSourceFiles>
                                <cssSourceFile>pacto-icon-font.css</cssSourceFile>
                            </cssSourceFiles>
                        </configuration>
                        <goals>
                            <goal>minify</goal>
                        </goals>
                    </execution>

                    <execution>
                        <id>crm-minify</id>
                        <phase>prepare-package</phase>
                        <configuration>
                            <cssSourceDir>css</cssSourceDir>
                            <cssTargetDir>css</cssTargetDir>
                            <cssFinalFile>crm_1.0.css</cssFinalFile>
                            <cssSourceFiles>
                                <cssSourceFile>crm.css</cssSourceFile>
                            </cssSourceFiles>
                        </configuration>
                        <goals>
                            <goal>minify</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>pacto-minify</id>
                        <phase>prepare-package</phase>
                        <configuration>
                            <cssSourceDir>/</cssSourceDir>
                            <cssTargetDir>css</cssTargetDir>
                            <cssFinalFile>pacto_css_1.3.css</cssFinalFile>
                            <cssSourceFiles>
                                <cssSourceFile>css_pacto.css</cssSourceFile>
                            </cssSourceFiles>
                        </configuration>
                        <goals>
                            <goal>minify</goal>
                        </goals>
                    </execution>

                    <execution>
                        <id>default-minify-otimize</id>
                        <phase>prepare-package</phase>
                        <configuration>
                            <cssSourceDir>css</cssSourceDir>
                            <cssTargetDir>css</cssTargetDir>
                            <cssFinalFile>otimizeOt.css</cssFinalFile>
                            <cssSourceFiles>
                                <cssSourceFile>otimize.css</cssSourceFile>
                            </cssSourceFiles>
                        </configuration>
                        <goals>
                            <goal>minify</goal>
                        </goals>
                    </execution>

                    <execution>
                        <id>default-minify-packcss</id>
                        <phase>prepare-package</phase>
                        <configuration>
                            <cssSourceDir>beta/css</cssSourceDir>
                            <cssTargetDir>css</cssTargetDir>
                            <cssFinalFile>packcss1.0.css</cssFinalFile>
                            <cssSourceFiles>
                                <cssSourceFile>pure-ext.css</cssSourceFile>
                                <cssSourceFile>bootstrap-tabs.css</cssSourceFile>
                                <cssSourceFile>pure-min.css</cssSourceFile>
                            </cssSourceFiles>
                        </configuration>
                        <goals>
                            <goal>minify</goal>
                        </goals>
                    </execution>

                    <execution>
                        <id>default-minify-telaCliente</id>
                        <phase>prepare-package</phase>
                        <configuration>
                            <cssSourceDir>css</cssSourceDir>
                            <cssTargetDir>css</cssTargetDir>
                            <cssFinalFile>gestaoRecebiveis1.0.css</cssFinalFile>
                            <cssSourceFiles>
                                <cssSourceFile>telaCliente.css</cssSourceFile>
                                <cssSourceFile>gestaoRecebiveis.css</cssSourceFile>
                                <cssSourceFile>tooltipster/tooltipster.css</cssSourceFile>
                                <cssSourceFile>tooltipster/tooltipster-light.css</cssSourceFile>
                                <cssSourceFile>faqtoid.css</cssSourceFile>
                            </cssSourceFiles>
                        </configuration>
                        <goals>
                            <goal>minify</goal>
                        </goals>
                    </execution>

                    <execution>
                        <id>default-minify-tooltipster</id>
                        <phase>prepare-package</phase>
                        <configuration>
                            <cssSourceDir>css</cssSourceDir>
                            <cssTargetDir>css</cssTargetDir>
                            <cssFinalFile>tooltipster1.0.css</cssFinalFile>
                            <cssSourceFiles>
                                <cssSourceFile>tooltipster/tooltipster.css</cssSourceFile>
                                <cssSourceFile>tooltipster/tooltipster-light.css</cssSourceFile>
                            </cssSourceFiles>
                        </configuration>
                        <goals>
                            <goal>minify</goal>
                        </goals>
                    </execution>

                    <execution>
                        <id>default-minify-pure</id>
                        <phase>prepare-package</phase>
                        <configuration>
                            <cssSourceDir>beta/css</cssSourceDir>
                            <cssTargetDir>css</cssTargetDir>
                            <cssFinalFile>pure1.0.css</cssFinalFile>
                            <cssSourceFiles>
                                <cssSourceFile>pure-ext.css</cssSourceFile>
                            </cssSourceFiles>
                        </configuration>
                        <goals>
                            <goal>minify</goal>
                        </goals>
                    </execution>

                    <execution>
                        <id>default-minify-font-awesome</id>
                        <phase>prepare-package</phase>
                        <configuration>
                            <cssSourceDir>beta/css</cssSourceDir>
                            <cssTargetDir>beta/css</cssTargetDir>
                            <cssFinalFile>font-awesome_2.0.css</cssFinalFile>
                            <cssSourceFiles>
                                <cssSourceFile>font-awesome.css</cssSourceFile>
                            </cssSourceFiles>
                        </configuration>
                        <goals>
                            <goal>minify</goal>
                        </goals>
                    </execution>

                    <execution>
                        <id>default-minify-bootstrap</id>
                        <phase>prepare-package</phase>
                        <configuration>
                            <cssSourceDir>bootstrap</cssSourceDir>
                            <cssTargetDir>bootstrap</cssTargetDir>
                            <cssFinalFile>bootplus.css</cssFinalFile>
                            <cssSourceFiles>
                                <cssSourceFile>bootplus.css</cssSourceFile>
                            </cssSourceFiles>
                        </configuration>
                        <goals>
                            <goal>minify</goal>
                        </goals>
                    </execution>

                    <execution>
                        <id>default-minify-js</id>
                        <phase>prepare-package</phase>
                        <configuration>
                            <charset>ISO-8859-1</charset>
                            <jsSourceDir>script</jsSourceDir>
                            <jsTargetDir>script</jsTargetDir>
                            <jsFinalFile>all_script_3.4.js</jsFinalFile>
                            <jsSourceFiles>
                                <jsSourceFile>bancos/validador-contas.js</jsSourceFile>
                                <jsSourceFile>utf8.js</jsSourceFile>
                                <!--<jsSourceFile>bancos/bank-account-validator.min.js</jsSourceFile> Removido porque estava gerando erro-->
                                <jsSourceFile>script.js</jsSourceFile>
                                <jsSourceFile>Notifier.js</jsSourceFile>
                            </jsSourceFiles>
                        </configuration>
                        <goals>
                            <goal>minify</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>faqtoid-minify-js</id>
                        <phase>prepare-package</phase>
                        <configuration>
                            <jsSourceDir>script</jsSourceDir>
                            <jsTargetDir>script</jsTargetDir>
                            <jsFinalFile>faqtoid_1.1.js</jsFinalFile>
                            <jsSourceFiles>
                                <jsSourceFile>faqtoid_0.1.js</jsSourceFile>
                            </jsSourceFiles>
                        </configuration>
                        <goals>
                            <goal>minify</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>bi-minify-js</id>
                        <phase>prepare-package</phase>
                        <configuration>
                            <charset>ISO-8859-1</charset>
                            <jsSourceDir>script</jsSourceDir>
                            <jsTargetDir>script</jsTargetDir>
                            <jsFinalFile>zw_bi_v1.14.js</jsFinalFile>
                            <jsSourceFiles>
                                <jsSourceFile>zw_bi.js</jsSourceFile>
                            </jsSourceFiles>
                        </configuration>
                        <goals>
                            <goal>minify</goal>
                        </goals>
                    </execution>

                    <execution>
                        <id>googleAnalytics-minify-js</id>
                        <phase>prepare-package</phase>
                        <configuration>
                            <charset>ISO-8859-1</charset>
                            <jsSourceDir>script</jsSourceDir>
                            <jsTargetDir>script</jsTargetDir>
                            <jsFinalFile>googleAnalytics_v1.0.js</jsFinalFile>
                            <jsSourceFiles>
                                <jsSourceFile>googleAnalytics.js</jsSourceFile>
                            </jsSourceFiles>
                        </configuration>
                        <goals>
                            <goal>minify</goal>
                        </goals>
                    </execution>

                    <execution>
                        <id>sortable-minify-js</id>
                        <phase>prepare-package</phase>
                        <configuration>
                            <jsSourceDir>script</jsSourceDir>
                            <jsTargetDir>script</jsTargetDir>
                            <jsFinalFile>jquery-sortable.js</jsFinalFile>
                            <jsSourceFiles>
                                <jsSourceFile>jquery-sortable.js</jsSourceFile>
                            </jsSourceFiles>
                        </configuration>
                        <goals>
                            <goal>minify</goal>
                        </goals>
                    </execution>

                    <execution>
                        <id>default-minify-gr</id>
                        <phase>prepare-package</phase>
                        <configuration>
                            <jsSourceDir>/</jsSourceDir>
                            <jsTargetDir>script</jsTargetDir>
                            <jsFinalFile>packJQueryPlugins.js</jsFinalFile>
                            <jsSourceFiles>
                                <jsSourceFile>bootstrap/jquery.js</jsSourceFile>
                                <jsSourceFile>script/amcharts_v2.js</jsSourceFile>
                                <jsSourceFile>script/amcharts_serial_v2.js</jsSourceFile>
                                <jsSourceFile>script/tooltipster/jquery.tooltipster.min.js</jsSourceFile>
                                <jsSourceFile>script/jquery.maskedinput-1.2.2.js</jsSourceFile>
                                <jsSourceFile>script/faqtoid_0.1.js</jsSourceFile>
                                <jsSourceFile>beta/js/DT_bootstrap.js</jsSourceFile>
                                <jsSourceFile>beta/js/bootstrap-tab.js</jsSourceFile>
                            </jsSourceFiles>
                        </configuration>
                        <goals>
                            <goal>minify</goal>
                        </goals>
                    </execution>

                    <execution>
                        <id>default-minify-cubomagico</id>
                        <phase>prepare-package</phase>
                        <configuration>
                            <charset>UTF-8</charset>
                            <jsSourceDir>script</jsSourceDir>
                            <jsTargetDir>script</jsTargetDir>
                            <jsFinalFile>cubomagico.min.js</jsFinalFile>
                            <jsSourceFiles>
                                <jsSourceFile>cubomagico.js</jsSourceFile>
                            </jsSourceFiles>
                        </configuration>
                        <goals>
                            <goal>minify</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>default-minify-linhaTempo</id>
                        <phase>prepare-package</phase>
                        <configuration>
                            <cssSourceDir>css</cssSourceDir>
                            <cssTargetDir>css</cssTargetDir>
                            <cssFinalFile>linhaTempoContrato_1.0.css</cssFinalFile>
                            <cssSourceFiles>
                                <cssSourceFile>linhaTempoContrato.css</cssSourceFile>
                            </cssSourceFiles>
                        </configuration>
                        <goals>
                            <goal>minify</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>default-minify-cssbi</id>
                        <phase>prepare-package</phase>
                        <configuration>
                            <cssSourceDir>css</cssSourceDir>
                            <cssTargetDir>css</cssTargetDir>
                            <cssFinalFile>css_bi_1.5.css</cssFinalFile>
                            <cssSourceFiles>
                                <cssSourceFile>css_bi.css</cssSourceFile>
                            </cssSourceFiles>
                        </configuration>
                        <goals>
                            <goal>minify</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>default-minify-negociacaoContrato</id>
                        <phase>prepare-package</phase>
                        <configuration>
                            <charset>UTF-8</charset>
                            <jsSourceDir>script</jsSourceDir>
                            <jsTargetDir>script</jsTargetDir>
                            <jsFinalFile>negociacaoContrato_1.0.js</jsFinalFile>
                            <jsSourceFiles>
                                <jsSourceFile>negociacaoContrato.js</jsSourceFile>
                            </jsSourceFiles>
                        </configuration>
                        <goals>
                            <goal>minify</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>1.8</version>
                <dependencies>
                    <dependency>
                        <groupId>br.com.pacto.lib</groupId>
                        <artifactId>xmltask</artifactId>
                        <version>1.16</version>
                    </dependency>
                </dependencies>

                <executions>
                    <execution>
                        <id>exclude-any-sources</id>
                        <phase>process-classes</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <configuration>
                            <target unless="${ZAW_QUEUE}">
                                <echo message="Desabilitando ZAW_QUEUE..."/>
                                <delete file="${basedir}/target/classes/servicos/jms/zaw/beans/MessageBean.class" failonerror="false"/>
                            </target>
                        </configuration>
                    </execution>
                    <execution>
                        <id>compilar</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <configuration>
                            <target xmlns:ac="antlib:net.sf.antcontrib">
                                <echo message="Setar propriedades da Aplicacao ${basedir}"/>

                                <taskdef name="xmltask" classname="com.oopsconsultancy.xmltask.ant.XmlTask"/>

                                <copy verbose="false" file="${basedir}/src/main/resources/servicos/propriedades/SuperControle.properties"
                                      tofile="${basedir}/src/main/resources/servicos/propriedades/SuperControle-temp.properties">
                                    <filterchain>
                                        <replacetokens>
                                            <token key="SFTP_NOW" value="${SFTP_NOW}"/>
                                            <token key="COOKIE_FAIL" value="${COOKIE_FAIL}"/>
                                            <token key="ZAW_DATASOURCE" value="${ZAW_DATASOURCE}"/>
                                            <token key="DIR_ZW_ARQ" value="${DIR_ZW_ARQ}"/>
                                            <token key="ZAW_URL_NOTF_ACESSO" value="${ZAW_URL_NOTF_ACESSO}"/>
                                            <token key="URL_APLICACAO" value="${URL_APLICACAO}"/>
                                            <token key="ZAW_QUEUE" value="${ZAW_QUEUE}"/>
                                            <token key="ZW_INSTANCIAS" value="${ZW_INSTANCIAS}"/>
                                            <token key="ZW_BANNERS" value="${ZW_BANNERS}"/>
                                            <token key="URL_TREINO" value="${URL_TREINO}"/>
                                            <token key="URL_OAMD" value="${URL_OAMD}"/>
                                            <token key="URL_OAMD_SEGURA" value="${URL_OAMD_SEGURA}"/>
                                            <token key="URL_IOF" value="${URL_IOF}"/>
                                            <token key="URL_LOGIN" value="${URL_LOGIN}"/>
                                            <token key="URL_LOGIN_FRONT" value="${URL_LOGIN_FRONT}"/>
                                            <token key="REDIRECT_LOGIN_SERV_LOCAL" value="${REDIRECT_LOGIN_SERV_LOCAL}"/>
                                            <token key="URL_NFSE" value="${URL_NFSE}"/>
                                            <token key="URL_NFSE_REST" value="${URL_NFSE_REST}"/>
                                            <token key="URL_SERV_NOTA_FISCAL" value="${URL_SERV_NOTA_FISCAL}"/>
                                            <token key="nfse-rest-admin-path" value="${nfse-rest-admin-path}"/>
                                            <token key="FOTOS_NUVEM" value="${FOTOS_NUVEM}"/>
                                            <token key="URL_FOTOS_NUVEM" value="${URL_FOTOS_NUVEM}"/>
                                            <token key="URL_NFE_NUVEM" value="${URL_NFE_NUVEM}"/>
                                            <token key="TIPO_MIDIA" value="${TIPO_MIDIA}"/>
                                            <token key="DIR_ZAW_LOGS" value="${DIR_ZAW_LOGS}"/>
                                            <token key="URL_VENDAS_ONLINE" value="${URL_VENDAS_ONLINE}"/>
                                            <token key="URL_DOC_API" value="${URL_DOC_API}"/>
                                            <token key="DIR_ZAW_DATASOURCE" value="${DIR_ZAW_DATASOURCE}"/>
                                            <token key="DIR_ZAW_UTILS" value="${DIR_ZAW_UTILS}"/>
                                            <token key="MY_FAQ_URL" value="${MY_FAQ_URL}"/>
                                            <token key="MY_FAQ_EMPRESAS" value="${MY_FAQ_EMPRESAS}"/>
                                            <token key="VALIDAR_USUARIO_OAMD" value="${VALIDAR_USUARIO_OAMD}"/>
                                            <token key="VALIDAR_VERSAO_BD" value="${VALIDAR_VERSAO_BD}"/>
                                            <token key="VALIDAR_BLOQUEIO_OUTROS" value="${VALIDAR_BLOQUEIO_OUTROS}"/>
                                            <token key="URL_OAMD_EMPRESAS" value="${URL_OAMD_EMPRESAS}"/>
                                            <token key="URL_MODULO_NFSE" value="${URL_MODULO_NFSE}"/>
                                            <token key="MY_URL_UP_BASE" value="${MY_URL_UP_BASE}"/>
                                            <token key="URL_FINANCEIRO_PACTO" value="${URL_FINANCEIRO_PACTO}"/>
                                            <token key="LOAD_INSTANCES_CLOUD" value="${loadInstancesFromCloud}"/>
                                            <token key="PREFIXO_INSTANCIAS_CLOUD" value="${prefixoInstanciasCloud}"/>
                                            <token key="AWSAccessKeyId" value="${AWSAccessKeyId}"/>
                                            <token key="AWSSecretKey" value="${AWSSecretKey}"/>
                                            <token key="AWSRegion" value="${AWSRegion}"/>
                                            <token key="LIBERAR_ACESSO_FATOR_ZW" value="${LIBERAR_ACESSO_FATOR_ZW}"/>
                                            <token key="HABILITAR_LEMBRETE_SOLICITACOES" value="${HABILITAR_LEMBRETE_SOLICITACOES}"/>
                                            <token key="URL_MEDIDOR" value="${URL_MEDIDOR}"/>
                                            <token key="BUSCAR_CONHECIMENTO_UCP" value="${BUSCAR_CONHECIMENTO_UCP}"/>
                                            <token key="EMAIL_COMERCIAL_PACTO" value="${EMAIL_COMERCIAL_PACTO}"/>
                                            <token key="SMTP_EMAIL_ROBO" value="${SMTP_EMAIL_ROBO}"/>
                                            <token key="SMTP_EMAIL_NOREPLY" value="${SMTP_EMAIL_NOREPLY}"/>
                                            <token key="SMTP_LOGIN_ROBO" value="${SMTP_LOGIN_ROBO}"/>
                                            <token key="SMTP_SENHA_ROBO" value="${SMTP_SENHA_ROBO}"/>
                                            <token key="SMTP_CONEXAOSEGURA_ROBO" value="${SMTP_CONEXAOSEGURA_ROBO}"/>
                                            <token key="SMTP_SERVER_ROBO" value="${SMTP_SERVER_ROBO}"/>
                                            <token key="INICIAR_TLS" value="${INICIAR_TLS}"/>

                                            <token key="ARRAY_CAIXAS_POSTAIS_SFTP" value="${arrayCaixasPostaisSFTP}"/>
                                            <token key="SERVER_SFTP_SLAVE" value="${serverSFTPSlave}"/>
                                            <token key="URL_DASHBOARD" value="${urlDashBoard}"/>

                                            <token key="VALIDAR_INAD" value="${validarInad}"/>
                                            <token key="INTEGRA_PROTHEUS" value="${integraProtheus}"/>
                                            <token key="URL_PROTHEUS" value="${protheus}"/>
                                            <token key="URL_INAD" value="${urlInad}"/>
                                            <token key="TOKEN_INAD" value="${tokenInad}"/>
                                            <token key="DISCOVERY_URL" value="${DISCOVERY_URL}"/>
                                            <token key="AUTH_SECRET_PERSONA_PATH" value="${AUTH_SECRET_PERSONA_PATH}"/>
                                            <token key="AUTH_SECRET_PATH" value="${AUTH_SECRET_PATH}"/>
                                            <token key="AUTH_SECRET_ZW_PATH" value="${AUTH_SECRET_ZW_PATH}"/>

                                            <token key="APRESENTAR_HOTJAR" value="${APRESENTAR_HOTJAR}"/>
                                            <token key="UTILIZAR_SINTETICO_MS" value="${UTILIZAR_SINTETICO_MS}"/>
                                            <token key="URL_RECURSO_EMPRESA" value="${URL_RECURSO_EMPRESA}"/>
                                            <token key="URL_ZW_AUTO" value="${URL_ZW_AUTO}"/>
                                            <token key="URL_MIDIA_SOCIAL" value="${URL_MIDIA_SOCIAL}"/>
                                            <token key="URL_API" value="${URL_API}"/>
                                            <token key="USAR_URL_RECURSO_EMPRESA" value="${USAR_URL_RECURSO_EMPRESA}"/>
                                            <token key="ENVIAR_EMAIL_VENDAS_ONLINE" value="${ENVIAR_EMAIL_VENDAS_ONLINE}"/>
                                            <token key="USE_BOUNCE_SERVICE" value="${useBounceService}"/>
                                            <token key="GCLOUD_API_KEY" value="${GCLOUD_API_KEY}"/>
                                            <token key="GERAR_PRINTS_ACOES_USUARIO" value="${GERAR_PRINTS_ACOES_USUARIO}"/>
                                            <token key="LIMITE_PRINTS_ACOES_USUARIO" value="${LIMITE_PRINTS_ACOES_USUARIO}" />
                                            <token key="URL_SUPORTE" value="${URL_SUPORTE}" />
                                            <token key="URL_HTTP_PLATAFORMA_PACTO" value="${URL_HTTP_PLATAFORMA_PACTO}"/>
                                            <token key="URL_HTTPS_PLATAFORMA_PACTO" value="${URL_HTTPS_PLATAFORMA_PACTO}"/>
                                            <token key="URL_HTTP_ZW_HOMOLOGACAO" value="${URL_HTTP_ZW_HOMOLOGACAO}"/>
                                            <token key="URL_HTTPS_ZW_HOMOLOGACAO" value="${URL_HTTPS_ZW_HOMOLOGACAO}"/>
                                            <token key="DEBUG_JDBC" value="${debugJDBC}"/>
                                            <token key="STONE_OPEN_BANK_PRODUCAO" value="${STONE_OPEN_BANK_PRODUCAO}"/>
                                            <token key="VERSAO_SISTEMA" value="${project.version}"/>
                                            <token key="ENVIRONMENT" value="${ENVIRONMENT}"/>
                                            <token key="ATIVAR_GOOGLE_ANALYTICS" value="${ATIVAR_GOOGLE_ANALYTICS}"/>
                                            <token key="ATIVAR_WEHELP" value="${ATIVAR_WEHELP}"/>
                                            <token key="BUSCAR_REMOTO_EXTRATO_RETORNO_REMESSA" value="${BUSCAR_REMOTO_EXTRATO_RETORNO_REMESSA}"/>
                                            <token key="URL_INTEGRACOES_SERVICE" value="${URL_INTEGRACOES_SERVICE}"/>
                                            <token key="URL_SERVICO_INTEGRACOES_MS" value="${URL_SERVICO_INTEGRACOES_MS}"/>
                                            <token key="URL_JENKINS" value="${URL_JENKINS}"/>
                                            <token key="URL_MAILING" value="${URL_MAILING}"/>
                                            <token key="AMBIENTE_DESENVOLVIMENTO_TESTE" value="${AMBIENTE_DESENVOLVIMENTO_TESTE}"/>
                                            <token key="TOKEN_MAIL_GUN" value="${TOKEN_MAIL_GUN}"/>
                                            <token key="HABILITA_MARKETING" value="${HABILITA_MARKETING}"/>
                                            <token key="HABILITA_CLUBE_DE_BENEFICIOS" value="${HABILITA_CLUBE_DE_BENEFICIOS}"/>
                                            <token key="TEMPO_SEGUNDOS_EXPIRAR_CACHE_BANNERS" value="${TEMPO_SEGUNDOS_EXPIRAR_CACHE_BANNERS}"/>
                                            <token key="SERVIDOR_MEMCACHED" value="${SERVIDOR_MEMCACHED}"/>
                                            <token key="URL_MARKETING_MS" value="${URL_MARKETING_MS}"/>
                                            <token key="DOMAIN_MAIL" value="${DOMAIN_MAIL}"/>
                                            <token key="LOG_OUTPUT_MASCARADE" value="${LOG_OUTPUT_MASCARADE}"/>
                                            <token key="LOG_OUTPUT_DEBUG" value="${LOG_OUTPUT_DEBUG}"/>
                                            <token key="BI_MS" value="${BI_MS}"/>
                                            <token key="EMAIL_SQUAD_ADM_OPEN_TICKET" value="${EMAIL_SQUAD_ADM_OPEN_TICKET}"/>
                                            <token key="BLOQUEAR_EMPRESAS_NAO_TIPIFICADAS" value="${BLOQUEAR_EMPRESAS_NAO_TIPIFICADAS}"/>
                                            <token key="PRIVATE_KEY_TEMPLATE" value="${PRIVATE_KEY_TEMPLATE}"/>
                                            <token key="ENABLE_LAST_ACTION_TIME" value="${ENABLE_LAST_ACTION_TIME}"/>
                                            <token key="ENABLE_COUNT_DOWN" value="${ENABLE_COUNT_DOWN}"/>
                                            <token key="URL_WEBHOOK_DISCORD_ENOTAS" value="${URL_WEBHOOK_DISCORD_ENOTAS}"/>
                                            <token key="URL_WEBHOOK_DISCORD_ENOTAS_HABILITAR" value="${URL_WEBHOOK_DISCORD_ENOTAS_HABILITAR}"/>
                                            <token key="ENABLE_MENU_ZW_UI" value="${ENABLE_MENU_ZW_UI}"/>
                                            <token key="TOKENS_ACESSO_API_CLIENTE" value="${TOKENS_ACESSO_API_CLIENTE}"/>
                                            <token key="TOKENS_ACESSO_API_APPS" value="${TOKENS_ACESSO_API_APPS}"/>
                                            <token key="TOKEN_API_WAGI" value="${TOKEN_API_WAGI}"/>
                                            <token key="PERMITIR_ENVIO_NOTIFICACAO_PUSH_AULA" value="${PERMITIR_ENVIO_NOTIFICACAO_PUSH_AULA}"/>
                                            <token key="UTEIS_EMAIL_SEND" value="${UTEIS_EMAIL_SEND}"/>
                                            <token key="QTD_LIMITE_PACTOPAY" value="${QTD_LIMITE_PACTOPAY}"/>
                                            <token key="CANCELAR_BOLETO_AO_CANCELAR_OU_ESTORNAR_CONTRATO" value="${CANCELAR_BOLETO_AO_CANCELAR_OU_ESTORNAR_CONTRATO}"/>
                                            <token key="VALIDAR_TOKEN_API_ZW" value="${VALIDAR_TOKEN_API_ZW}"/>
                                            <token key="KEYWORD_PATH" value="${KEYWORD_PATH}"/>
                                            <token key="URL_BASE_OPTIN" value="${URL_BASE_OPTIN}"/>
                                            <token key="PASSWORD_MAIL_FACILITEPAY" value="${PASSWORD_MAIL_FACILITEPAY}"/>
                                            <token key="TEMPO_SEGUNDOS_EXPIRACAO_TOKEN_OPERACAO" value="${TEMPO_SEGUNDOS_EXPIRACAO_TOKEN_OPERACAO}"/>
                                            <token key="HABILITAR_NICHO" value="${HABILITAR_NICHO}"/>
                                            <token key="HABILITAR_CACHE_INIT_NICHO" value="${HABILITAR_CACHE_INIT_NICHO}"/>
                                            <token key="VALIDADE_CACHE_NICHO_EM_MINUTOS" value="${VALIDADE_CACHE_NICHO_EM_MINUTOS}"/>
                                            <token key="PASSWORD_IMPORTADOR_TREINO" value="${PASSWORD_IMPORTADOR_TREINO}"/>
                                            <token key="ENABLE_COMPANY_WEBHOOK" value="${ENABLE_COMPANY_WEBHOOK}"/>
                                            <token key="ENABLE_CONCILIADORA" value="${ENABLE_CONCILIADORA}"/>
                                            <token key="VERIFY_CONTROLLERS_AFTER_PHASE" value="${VERIFY_CONTROLLERS_AFTER_PHASE}"/>
                                            <token key="HABILITAR_FUNCIONALIDADES_BETA" value="${HABILITAR_FUNCIONALIDADES_BETA}"/>
                                            <token key="MINIMO_NOTAS_SOLICITACAO" value="${MINIMO_NOTAS_SOLICITACAO}"/>
                                            <token key="TOKEN_PUSH_MOBILE_APP_ALUNO" value="${TOKEN_PUSH_MOBILE_APP_ALUNO}"/>
                                            <token key="URL_APP_ALUNO_UNIFICADO" value="${URL_APP_ALUNO_UNIFICADO}"/>
                                            <token key="MAX_GPT" value="${MAX_GPT}"/>
                                            <token key="URL_ARAGORN_MS" value="${URL_ARAGORN_MS}"/>
                                            <token key="URL_AUTENTICACAO_MS" value="${URL_AUTENTICACAO_MS}"/>
                                            <token key="REDIRECT_URI_CONNECT_PAGBANK" value="${REDIRECT_URI_CONNECT_PAGBANK}"/>
                                            <token key="URL_GOGOOD" value="${URL_GOGOOD}"/>
                                            <token key="GOGOOD_APP_ID" value="${GOGOOD_APP_ID}"/>
                                            <token key="GOGOOD_APP_SECRET" value="${GOGOOD_APP_SECRET}"/>
                                            <token key="GOOGLE_APPLICATION_CREDENTIALS" value="${GOOGLE_APPLICATION_CREDENTIALS}"/>
                                            <token key="URL_ENVIO_ACESSO_INTEG_PRATIQUE" value="${URL_ENVIO_ACESSO_INTEG_PRATIQUE}"/>
                                            <token key="TOKEN_ENVIO_ACESSO_INTEG_PRATIQUE" value="${TOKEN_ENVIO_ACESSO_INTEG_PRATIQUE}"/>
                                            <token key="MOCK_ARAGORN_CARDS" value="${MOCK_ARAGORN_CARDS}"/>
                                        </replacetokens>
                                    </filterchain>
                                </copy>

                                <move verbose="false" file="${basedir}/src/main/resources/servicos/propriedades/SuperControle-temp.properties"
                                      tofile="${basedir}/target/classes/servicos/propriedades/SuperControle.properties"/>

                                <echo message="Configurando URL do OAMD para ${urlDatabaseOAMD}..." />

                                <xmltask outputter="simple"
                                         source="${basedir}/target/classes/negocio/facade/jdbc/utilitarias/cfgBD.xml"
                                         dest="${basedir}/target/classes/negocio/facade/jdbc/utilitarias/cfgBD.xml">

                                    <replace path="/aplicacao/bd/nomeBD/text()"
                                             withText="${nomeBD}"/>

                                    <replace path="/aplicacao/bd/servidor/text()"
                                             withText="${hostOAMD}"/>

                                    <replace path="/aplicacao/bd/url-oamd/text()"
                                             withText="${urlDatabaseOAMD}"/>

                                    <replace path="/aplicacao/bd/username/text()"
                                             withText="${userOAMD}"/>

                                    <replace path="/aplicacao/bd/senha/text()"
                                             withText="${pwdOAMD}"/>

                                    <replace path="/aplicacao/bd/porta/text()"
                                             withText="${portaDB}"/>

                                    <replace path="/aplicacao/bd/roboApenasComoServico/text()"
                                             withText="${roboApenasComoServico}"/>

                                    <replace path="/aplicacao/bd/NFE/text()"
                                             withText="${moduloNFE}"/>

                                    <replace path="/aplicacao/bd/ipServidoresMemCached/text()"
                                             withText="${SERVIDOR_MEMCACHED}"/>

                                    <replace path="/aplicacao/bd/urlBdRemessa/text()"
                                             withText="${URL_BD_REMESSA}"/>

                                    <replace path="/aplicacao/bd/userBdRemessa/text()"
                                             withText="${USER_BD_REMESSA}"/>

                                    <replace path="/aplicacao/bd/pwdBdRemessa/text()"
                                             withText="${PWD_BD_REMESSA}"/>

                                </xmltask>

                            </target>
                        </configuration>
                    </execution>
                    <!--<execution>-->
                    <!--<id>corrigirMinify</id>-->
                    <!--<phase>prepare-package</phase>-->
                    <!--<goals>-->
                    <!--<goal>run</goal>-->
                    <!--</goals>-->
                    <!--<configuration>-->
                    <!--<target>-->
                    <!--<echo message="Corrigindo all_1.5.min.css" />-->
                    <!--<replaceregexp file="${basedir}/target/${project.name}/css/all_1.0.min.css"-->
                    <!--match="%\+"-->
                    <!--replace="% + "-->
                    <!--flags="g"-->
                    <!--byline="true"/>-->
                    <!--</target>-->
                    <!--</configuration>-->
                    <!--</execution>-->

                </executions>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
                <version>1.0</version>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>2.4</version>
                <configuration>
                    <packagingExcludes>
                        WEB-INF/lib/ant-1.7.0.jar,
                        WEB-INF/lib/ant-launcher-1.7.0.jar,
                        WEB-INF/lib/avalon-framework-impl-4.2.0.jar,
                        WEB-INF/lib/bcmail-jdk14-1.38.jar,
                        WEB-INF/lib/bcmail-jdk14-138.jar,
                        WEB-INF/lib/bcprov-jdk14-1.38.jar,
                        WEB-INF/lib/bcprov-jdk14-138.jar,
                        WEB-INF/lib/bctsp-jdk14-1.38.jar,
                        WEB-INF/lib/commons-beanutils-core-1.8.0.jar,
                        WEB-INF/lib/commons-dbcp-1.4.jar,
                        WEB-INF/lib/commons-el-1.0.jar,
                        WEB-INF/lib/commons-pool-1.5.4.jar,
                        WEB-INF/lib/el-ri-1.0.jar,
                        WEB-INF/lib/FastInfoset-1.2.12.jar,
                        WEB-INF/lib/gmbal-api-only-3.1.0-b001.jar,
                        WEB-INF/lib/google-api-client-1.14.1-beta.jar,
                        WEB-INF/lib/google-oauth-client-1.14.1-beta.jar,
                        WEB-INF/lib/ha-api-3.1.9.jar,
                        WEB-INF/lib/istack-commons-runtime-2.16.jar,
                        WEB-INF/lib/jasperreports-3.7.2.jar,
                        WEB-INF/lib/javax.annotation-api-1.2-b03.jar,
                        WEB-INF/lib/javax.xml.soap-api-1.3.5.jar,
                        WEB-INF/lib/jaxb-core-2.2.7.jar,
                        WEB-INF/lib/jaxb-impl-2.2.7.jar,
                        WEB-INF/lib/jaxrpc-api-1.1.jar,
                        WEB-INF/lib/jaxrpc-impl-1.1.3_01.jar,
                        WEB-INF/lib/jaxrpc-spi-1.1.3_01.jar,
                        WEB-INF/lib/jaxws-rt-2.2.8.jar,
                        WEB-INF/lib/jdtcore-3.1.0.jar,
                        WEB-INF/lib/jline-0.9.94.jar,
                        WEB-INF/lib/jsf-facelets-1.1.14.jar,
                        WEB-INF/lib/jsp-2.1-6.1.14.jar,
                        WEB-INF/lib/jsp-api-2.1-6.1.14.jar,
                        WEB-INF/lib/jsr173-1.0.jar,
                        WEB-INF/lib/jsr181-api-1.0-MR1.jar,
                        WEB-INF/lib/jsr305-1.3.9.jar,
                        WEB-INF/lib/jstl-1.2.jar,
                        WEB-INF/lib/log4j-1.2.12.jar,
                        WEB-INF/lib/management-api-3.0.0-b012.jar,
                        WEB-INF/lib/mimepull-1.9.1.jar,
                        WEB-INF/lib/nekohtml-1.9.6.jar,
                        WEB-INF/lib/policy-2.3.1.jar,
                        WEB-INF/lib/resolver-20050927.jar,
                        WEB-INF/lib/saaj-api-1.3.4.jar,
                        WEB-INF/lib/saaj-impl-1.3.20.jar,
                        WEB-INF/lib/stax2-api-3.1.1.jar,
                        WEB-INF/lib/stax-api-1.0.1.jar,
                        WEB-INF/lib/stax-ex-1.7.1.jar,
                        WEB-INF/lib/streambuffer-1.5.1.jar,
                        WEB-INF/lib/woodstox-core-asl-4.1.2.jar,
                        WEB-INF/lib/xalan-2.6.0.jar,
                        WEB-INF/lib/xercesImpl-2.6.2.jar,
                        WEB-INF/lib/xml-apis-1.0.b2.jar,
                        WEB-INF/lib/xml-apis-1.3.04.jar,
                        WEB-INF/lib/xmlParserAPIs-2.6.2.jar,
                        WEB-INF/lib/xpp3-1.1.4c.jar
                    </packagingExcludes>
                    <webXml>src/main/webapp/WEB-INF/web.xml</webXml>
                    <warSourceExcludes>**/.svn/**</warSourceExcludes>
                </configuration>
            </plugin>
        </plugins>
        <finalName>${project.artifactId}</finalName>
    </build>
</project>

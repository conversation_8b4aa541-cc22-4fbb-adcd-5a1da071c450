package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.comuns.json.JSONMapper;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.UsuarioEmailVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioTelefoneVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.enumerador.negocio.comuns.basico.enumerador.cadastrodinamico.LocaleEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.arquitetura.UsuarioEmail;
import negocio.facade.jdbc.arquitetura.UsuarioPerfilAcesso;
import negocio.facade.jdbc.arquitetura.UsuarioTelefone;
import negocio.facade.jdbc.basico.*;
import negocio.modulos.integracao.usuariomovel.UsuarioMovel;
import org.apache.http.HttpStatus;
import org.json.JSONObject;
import servicos.discovery.ClientDiscoveryDataDTO;
import servicos.discovery.DiscoveryMsService;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.impl.login.DadosAcessoDTO;
import servicos.impl.login.DesvincularUsuarioDTO;
import servicos.impl.login.EmpresaAcessoDTO;
import servicos.impl.login.TokenDTO;
import servicos.impl.login.TokenSolicitacaoDTO;
import servicos.impl.login.TrocaEmpresaDTO;
import servicos.impl.login.UsuarioGeralDTO;
import servicos.impl.login.UsuarioGeralPendenteDTO;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SincronizarUsuarioNovoLogin {

    private static final String ENDPOINT_LOGIN_USUARIO_NOVO = "/prest/login/v2/usuario/novo";
    private static final String ENDPOINT_LOGIN_USUARIO_ATUALIZAR = "/prest/login/v2/usuario/atualizar";
    private static final String ENDPOINT_LOGIN_NOVA_SENHA = "/prest/login/v2/senha/nova";
    private static final String ENDPOINT_LOGIN_NOVO_EMAIL = "/prest/login/v2/email/novo";
    private static final String ENDPOINT_LOGIN_NOVO_TELEFONE = "/prest/login/v2/telefone/novo";
    private static final String ENDPOINT_LOGIN_PROCESSAR_TOKEN = "/prest/login/v2/token";
    private static final String ENDPOINT_LOGIN_EMPRESAS = "/prest/login/v2/empresas";
    private static final String ENDPOINT_LOGIN_EMPRESAS_VALIDAR = "/prest/login/v2/empresas/validar/";
    private static final String ENDPOINT_LOGIN_EMPRESAS_REDIRECT = "/prest/login/v2/empresas/redirect/";
    private static final String ENDPOINT_LOGIN_DESVINCULAR_USUARIO = "/prest/login/v2/usuario/desvincular";
    private static final String ENDPOINT_LOGIN_PROCESSAR_USUARIO_REDE = "/prest/login/v2/usuario/rede/";
    private static final String ENDPOINT_LOGIN_USUARIO_MESMO_EMAIL = "/prest/login/v2/find-user-same-email";
    private static final String ENDPOINT_LOGIN_OBTER_DADOS_USUARIO = "/prest/login/v2/obter";


    private static void preencherDadosUsuario(UsuarioVO usuarioVO, Connection con) throws Exception {
        Colaborador colaboradorDAO;
        Email emailDAO;
        UsuarioPerfilAcesso usuarioPerfilAcessoDAO;
        Empresa empresaDAO;
        try {
            colaboradorDAO = new Colaborador(con);
            emailDAO = new Email(con);
            usuarioPerfilAcessoDAO = new UsuarioPerfilAcesso(con);
            empresaDAO = new Empresa(con);

            usuarioVO.setColaboradorVO(colaboradorDAO.consultarPorChavePrimaria(usuarioVO.getColaboradorVO().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
            usuarioVO.getColaboradorVO().getPessoa().setEmailVOs(emailDAO.consultarEmails(usuarioVO.getColaboradorVO().getPessoa().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            usuarioVO.setUsuarioPerfilAcessoVOs(usuarioPerfilAcessoDAO.consultarUsuarioPerfilAcesso(usuarioVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));

            Map<Integer, EmpresaVO> mapaEmpresa = new HashMap<>();
            for (UsuarioPerfilAcessoVO perfilAcessoVO : usuarioVO.getUsuarioPerfilAcessoVOs()) {
                EmpresaVO empresaVO = mapaEmpresa.get(perfilAcessoVO.getEmpresa().getCodigo());
                if (empresaVO == null) {
                    empresaVO = empresaDAO.consultarPorChavePrimaria(perfilAcessoVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
                    mapaEmpresa.put(empresaVO.getCodigo(), empresaVO);
                }
                perfilAcessoVO.setEmpresa(empresaVO);
            }
        } finally {
            colaboradorDAO = null;
            emailDAO = null;
            usuarioPerfilAcessoDAO = null;
            empresaDAO = null;
        }
    }

    public static void atualizarUsuarioGeral(Integer usuario, Connection con, Integer empresaSolicitante,
                                             UsuarioVO usuarioResponsavelVO, String ipCliente, boolean novoLogin) throws Exception {
        atualizarUsuarioGeral(usuario, con, empresaSolicitante, usuarioResponsavelVO, ipCliente, novoLogin, null);
    }

    public static void atualizarUsuarioGeral(Integer usuario, Connection con, Integer empresaSolicitante,
                                             UsuarioVO usuarioResponsavelVO, String ipCliente, boolean novoLogin, String key) throws Exception {
        Usuario usuarioDAO;
        try {
            usuarioDAO = new Usuario(con);
            novoLogin = verificarForcarNovoLogin(novoLogin, con);

            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(usuario, Uteis.NIVELMONTARDADOS_TODOS);
            if (UteisValidacao.emptyString(usuarioVO.getUsuarioGeral())) {
                throw new Exception("ID UsuarioGeral não encontrado");
            }
            atualizarUsuarioGeral(usuarioVO, con, empresaSolicitante, usuarioResponsavelVO, ipCliente, novoLogin, key);
        } finally {
            usuarioDAO = null;
        }
    }

    private static void atualizarUsuarioGeral(UsuarioVO usuarioVO, Connection con, Integer empresaSolicitante,
                                              UsuarioVO usuarioResponsavelVO, String ipCliente, boolean novoLogin, String key) throws Exception {
        Usuario usuarioDAO = null;
        try {
            usuarioDAO = new Usuario(con);
            novoLogin = verificarForcarNovoLogin(novoLogin, con);
            final String chave = key == null ? DAO.resolveKeyFromConnection(con) : key;

            UsuarioGeralDTO usuarioGeralDTO = obterUsuarioGeralDTO(usuarioVO, chave, empresaSolicitante, usuarioResponsavelVO, ipCliente, con, novoLogin);
            usuarioGeralDTO = atualizarUsuarioGeral(usuarioGeralDTO);

            if (UteisValidacao.emptyString(usuarioGeralDTO.getId())) {
                throw new Exception("Usuário não sincronizado");
            }

            if (UteisValidacao.emptyString(usuarioVO.getUsuarioGeral())) {
                usuarioVO.setUsuarioGeral(usuarioGeralDTO.getId());
                usuarioDAO.alterarUsuarioGeral(usuarioVO);
            }

            usuarioDAO.incluirHistoricoSincronizacao(usuarioVO.getCodigo(), true, new JSONObject(usuarioGeralDTO).toString(), "SAVE_USUARIO");
        } catch (Exception ex) {
            try {
                if (usuarioDAO == null) {
                    usuarioDAO = new Usuario(con);
                }
                usuarioDAO.incluirHistoricoSincronizacao(usuarioVO.getCodigo(), false, ex.getMessage(), "SAVE_USUARIO");
            } catch (Exception ex1) {
                ex1.printStackTrace();
            }
            throw ex;
        } finally {
            usuarioDAO = null;
        }
    }

    private static UsuarioEmailVO obterUsuarioEmail(Integer usuario, Connection con) throws Exception {
        UsuarioEmail usuarioEmailDAO = null;
        try {
            usuarioEmailDAO = new UsuarioEmail(con);
            UsuarioEmailVO usuarioEmailVO = usuarioEmailDAO.consultarPorUsuario(usuario);
            if (UteisValidacao.emptyNumber(usuarioEmailVO.getCodigo()) ||
                    UteisValidacao.emptyString(usuarioEmailVO.getEmail())) {
                throw new Exception("E-mail do usuário não encontrado");
            }
            if (!UteisValidacao.validaEmail(usuarioEmailVO.getEmail())) {
                throw new Exception("E-mail do usuário inválido");
            }
            return usuarioEmailVO;
        } finally {
            usuarioEmailDAO = null;
        }
    }

    private static UsuarioTelefoneVO obterUsuarioTelefone(Integer usuario, Connection con) throws Exception {
        UsuarioTelefone usuarioTelefoneDAO = null;
        try {
            usuarioTelefoneDAO = new UsuarioTelefone(con);
            UsuarioTelefoneVO usuarioTelefoneVO = usuarioTelefoneDAO.consultarPorUsuario(usuario);
            if (UteisValidacao.emptyNumber(usuarioTelefoneVO.getCodigo()) ||
                    UteisValidacao.emptyString(usuarioTelefoneVO.getNumero())) {
                return null;
            }
            if (!UteisValidacao.validaTelefone(usuarioTelefoneVO.getNumero())) {
                throw new Exception("Telefone do usuário inválido");
            }
            return usuarioTelefoneVO;
        } finally {
            usuarioTelefoneDAO = null;
        }
    }

    public static String enviarEmailNovoUsuario(Integer usuario, boolean definirSenha, boolean enviarCodigo, String emailEnviar, Connection con,
                                                Integer empresaSolicitante, UsuarioVO usuarioResponsavelVO, String ipCliente, boolean novoLogin) throws Exception {
        Usuario usuarioDAO = null;
        UsuarioEmail usuarioEmailDAO = null;
        try {
            usuarioDAO = new Usuario(con);
            usuarioEmailDAO = new UsuarioEmail(con);
            novoLogin = verificarForcarNovoLogin(novoLogin, con);
            final String chave = DAO.resolveKeyFromConnection(con);

            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(usuario, Uteis.NIVELMONTARDADOS_TODOS);
            if (!UteisValidacao.emptyString(usuarioVO.getUsuarioGeral())) {
                throw new Exception("Usuário já tem um id geral");
            }

            UsuarioGeralPendenteDTO usuarioGeralPendenteDTO = new UsuarioGeralPendenteDTO();
            usuarioGeralPendenteDTO.setChave(chave);

            //caso marcado será enviado um email que ao clicar ele irá validar o email e após ira definir a senha
            usuarioGeralPendenteDTO.setDefinirSenha(definirSenha);

            if (UteisValidacao.emptyString(emailEnviar)) {
                UsuarioEmailVO usuarioEmailVO = obterUsuarioEmail(usuarioVO.getCodigo(), con);
                usuarioGeralPendenteDTO.setEmail(usuarioEmailVO.getEmail());
            } else {
                UsuarioEmailVO usuarioEmailVO = usuarioEmailDAO.consultarPorUsuario(usuarioVO.getCodigo());
                if (UteisValidacao.emptyNumber(usuarioEmailVO.getCodigo()) && UteisValidacao.emptyString(usuarioEmailVO.getEmail())) {
                    usuarioEmailVO.setUsuario(usuarioVO.getCodigo());
                    usuarioEmailVO.setEmail(emailEnviar);
                    usuarioEmailVO.setVerificado(false);
                    usuarioEmailDAO.incluir(usuarioEmailVO);
                }else{
                    usuarioEmailVO.setUsuario(usuarioVO.getCodigo());
                    usuarioEmailVO.setEmail(emailEnviar);
                    usuarioEmailVO.setVerificado(false);
                    usuarioEmailDAO.alterar(usuarioEmailVO);
                }
                usuarioGeralPendenteDTO.setEmail(emailEnviar);
            }

            UsuarioTelefoneVO usuarioTelefoneVO = obterUsuarioTelefone(usuarioVO.getCodigo(), con);
            if (usuarioTelefoneVO != null &&
                    !UteisValidacao.emptyString(usuarioTelefoneVO.getNumero()) &&
                    usuarioTelefoneVO.isVerificado()) {
                String telefone = usuarioTelefoneVO.getNumero().replaceAll("[^0-9]", "");
                telefone = (usuarioTelefoneVO.getDdi() + "|" + telefone);
                usuarioGeralPendenteDTO.setTelefone(telefone);
            }

            usuarioGeralPendenteDTO.setUsuarioGeral(obterUsuarioGeralDTO(usuarioVO, chave, empresaSolicitante, usuarioResponsavelVO, ipCliente, con, novoLogin));
            usuarioGeralPendenteDTO.getUsuarioGeral().getSolicitacao().setCodigoViaEmail(enviarCodigo);

            String tokenUsuarioPendente = novoUsuarioGeral(usuarioGeralPendenteDTO);
            if (UteisValidacao.emptyString(tokenUsuarioPendente)) {
                throw new Exception("Usuário não sincronizado");
            }
            usuarioDAO.incluirHistoricoSincronizacao(usuarioVO.getCodigo(), true, tokenUsuarioPendente, "NOVO_USUARIO_PENDENTE");
            return tokenUsuarioPendente;
        } catch (Exception ex) {
            try {
                if (usuarioDAO == null) {
                    usuarioDAO = new Usuario(con);
                }
                usuarioDAO.incluirHistoricoSincronizacao(usuario, false, ex.getMessage(), "NOVO_USUARIO_PENDENTE");
            } catch (Exception ex1) {
                ex1.printStackTrace();
            }
            throw ex;
        } finally {
            usuarioDAO = null;
        }
    }

    private static UsuarioGeralDTO obterUsuarioGeralDTO(UsuarioVO usuarioVO, String chaveSolicitante,
                                                        Integer empresaSolicitante, UsuarioVO usuarioResponsavelVO,
                                                        String ipCliente, Connection con, boolean novoLogin) throws Exception {
        novoLogin = verificarForcarNovoLogin(novoLogin, con);

        //usuário da pacto não pode sincronizar
        if (usuarioVO.getUsuarioPactoSolucoes()) {
            throw new Exception("Usuário interno Sistema Pacto não pode ser sincronizado");
        }

        //preencher todas as informações necessárias do usuario/colaborador para realizar o envio
        preencherDadosUsuario(usuarioVO, con);
        UsuarioGeralDTO usuarioGeralDTO = toUsuarioGeralDTO(usuarioVO, chaveSolicitante, con);
        TokenSolicitacaoDTO dto = new TokenSolicitacaoDTO(usuarioVO, chaveSolicitante, empresaSolicitante, usuarioResponsavelVO, ipCliente, novoLogin);
        usuarioGeralDTO.setSolicitacao(dto);
        return usuarioGeralDTO;
    }

    private static UsuarioGeralDTO atualizarUsuarioGeral(UsuarioGeralDTO usuarioGeralDTO) throws Exception {
        String url = (obterURLLoginBack() + ENDPOINT_LOGIN_USUARIO_ATUALIZAR);
        String response = ExecuteRequestHttpService.post(url, new JSONObject(usuarioGeralDTO).toString(), new HashMap<>());
        if (!new JSONObject(response).has("content")) {
            throw new Exception(response);
        }
        return JSONMapper.getObject(new JSONObject(response).getJSONObject("content"), UsuarioGeralDTO.class);
    }

    private static String novoUsuarioGeral(UsuarioGeralPendenteDTO usuarioGeralPendenteDTO) throws Exception {
        String url = (obterURLLoginBack() + ENDPOINT_LOGIN_USUARIO_NOVO);
        String response = ExecuteRequestHttpService.post(url, new JSONObject(usuarioGeralPendenteDTO).toString(), new HashMap<>());
        if (!new JSONObject(response).has("content")) {
            throw new Exception(response);
        }
        return new JSONObject(response).getString("content");
    }

    public static void processarUsuarioRede(Integer empresa, Integer usuario, String ipCliente, String usuarioGeral, String chave, boolean integracaoNovoLogin) throws Exception {
        ClientDiscoveryDataDTO clientDiscoveryDataDTO = DiscoveryMsService.urlsChave(chave);
        String zwUrl = clientDiscoveryDataDTO.getServiceUrls().getZwUrl();
        String paramSinc = "chave=" + chave + "&empresa=" + empresa.toString() + "&usuario=" + usuario + "&ipCliente=" + ipCliente + "&integNovoLogin=" + integracaoNovoLogin;
        String responseOK = ExecuteRequestHttpService.post(zwUrl + "/prest/auth/v2/usuario/sincronizarNovoLogin?" + paramSinc, "", new HashMap<>());
        if (responseOK.equals("OK")) {
            String urlLogin = (obterURLLoginBack() + ENDPOINT_LOGIN_PROCESSAR_USUARIO_REDE + usuarioGeral);
            String response = ExecuteRequestHttpService.post(urlLogin, "", new HashMap<>());
            if (!response.equals("{}")) {
                throw new Exception("Algo inesperado ocorreu ao sincronizar usuario geral rede.");
            }
        }
    }

    private static String obterURLLoginBack() {
       return PropsService.getPropertyValue(PropsService.urlLogin);
    }

    public static String obterUrlBackObterDadosUsuario() {
        return obterURLLoginBack() + ENDPOINT_LOGIN_OBTER_DADOS_USUARIO;
    }

    private static UsuarioGeralDTO toUsuarioGeralDTO(UsuarioVO usuarioVO, String chave, Connection con) {
        UsuarioGeralDTO usuarioGeralDTO = new UsuarioGeralDTO();
        usuarioGeralDTO.setId(UteisValidacao.emptyString(usuarioVO.getUsuarioGeral()) ? null : usuarioVO.getUsuarioGeral());
        usuarioGeralDTO.setNome(usuarioVO.getNome());
        try {
            usuarioGeralDTO.setFotokey(Uteis.getPaintFotoDaNuvem(usuarioVO.getColaboradorVO().getPessoa().getFotoKey()));
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        try {
            LocaleEnum localeEnum = LocaleEnum.obterLocale(usuarioVO.getLinguagem());
            usuarioGeralDTO.setIdioma(localeEnum.getLocalize());
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        usuarioGeralDTO.setSenha(usuarioVO.getSenha());

        final String dataAtualizacao = UteisValidacao.emptyString(usuarioGeralDTO.getId())
                ? null
                : Uteis.getDataComHora(Calendario.hoje());

        usuarioGeralDTO.setDadosAcesso(new ArrayList<>());
        for (UsuarioPerfilAcessoVO usuarioPerfilAcessoVO : usuarioVO.getUsuarioPerfilAcessoVOs()) {
            DadosAcessoDTO dadosAcessoDTO = new DadosAcessoDTO();
            dadosAcessoDTO.setChave(chave);
            dadosAcessoDTO.setTipoUsuario("CO"); //colaborador
            dadosAcessoDTO.setCodigoEmpresa(usuarioPerfilAcessoVO.getEmpresa().getCodigo());
            dadosAcessoDTO.setCodigoUsuario(usuarioVO.getCodigo());
            dadosAcessoDTO.setUsername(usuarioVO.getUsername());
            dadosAcessoDTO.setCodigoPessoa(usuarioVO.getColaboradorVO().getPessoa().getCodigo());
            dadosAcessoDTO.setAtivo(usuarioVO.getColaboradorVO().getSituacao().equalsIgnoreCase("AT"));

            if (!UteisValidacao.emptyString(dataAtualizacao)) {
                dadosAcessoDTO.setDataAtualizacao(dataAtualizacao);
            }

            //usuário do TreinoWeb
            UsuarioMovel usuarioMovelDAO;
            try {
                usuarioMovelDAO = new UsuarioMovel(con);
                dadosAcessoDTO.setCodigoUsuarioTW(usuarioMovelDAO.obterUsuarioTW(chave, usuarioVO.getCodigo(), usuarioPerfilAcessoVO.getEmpresa().getCodigo()));
            } catch (Exception ex) {
                ex.printStackTrace();
            } finally {
                usuarioMovelDAO = null;
            }

            //idioma
            if (UteisValidacao.emptyString(usuarioVO.getLinguagem())) {
                try {
                    LocaleEnum localeEnum = LocaleEnum.obterLocale(usuarioPerfilAcessoVO.getEmpresa().getLocaleTexto());
                    dadosAcessoDTO.setIdioma(localeEnum.getLocalize());
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            } else {
                try {
                    LocaleEnum localeEnum = LocaleEnum.obterLocale(usuarioVO.getLinguagem());
                    dadosAcessoDTO.setIdioma(localeEnum.getLocalize());
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }

            usuarioGeralDTO.getDadosAcesso().add(dadosAcessoDTO);
        }
        return usuarioGeralDTO;
    }

    public static void solicitarTrocaSenha(Integer usuario, Connection con, Integer empresaSolicitante,
                                           UsuarioVO usuarioResponsavelVO, String ipCliente, boolean novoLogin) throws Exception {
        Usuario usuarioDAO = null;
        try {
            usuarioDAO = new Usuario(con);
            novoLogin = verificarForcarNovoLogin(novoLogin, con);
            final String chave = DAO.resolveKeyFromConnection(con);

            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(usuario, Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);
            validarUsuarioGeralPreenchido(usuarioVO);

            TokenSolicitacaoDTO dto = new TokenSolicitacaoDTO(usuarioVO, chave, empresaSolicitante, usuarioResponsavelVO, ipCliente, novoLogin);

            String urlAtivar = obterURLLoginBack() + ENDPOINT_LOGIN_NOVA_SENHA;
            String response = ExecuteRequestHttpService.post(urlAtivar, new JSONObject(dto).toString(), new HashMap<>());
            if (!new JSONObject(response).has("content")) {
                throw new Exception(response);
            }
            usuarioDAO.incluirHistoricoSincronizacao(usuarioVO.getCodigo(), true, response, "TROCA_SENHA");
        } catch (Exception ex) {
            try {
                if (usuarioDAO == null) {
                    usuarioDAO = new Usuario(con);
                }
                usuarioDAO.incluirHistoricoSincronizacao(usuario, false, ex.getMessage(), "TROCA_SENHA");
            } catch (Exception ex1) {
                ex1.printStackTrace();
            }
            throw ex;
        } finally {
            usuarioDAO = null;
        }
    }

    public static String solicitarTrocaEmail(Integer usuario, boolean viaCodigoEmail, boolean enviarLinkEmail,
                                             UsuarioEmailVO usuarioEmailNovo, Connection con, Integer empresaSolicitante,
                                             UsuarioVO usuarioResponsavelVO, String ipCliente, boolean novoLogin) throws Exception {
        return solicitarTrocaEmail(usuario, viaCodigoEmail, enviarLinkEmail, usuarioEmailNovo, con, empresaSolicitante, usuarioResponsavelVO, ipCliente, novoLogin, false);
    }

    public static String solicitarTrocaEmail(Integer usuario, boolean viaCodigoEmail, boolean enviarLinkEmail,
                                             UsuarioEmailVO usuarioEmailNovo, Connection con, Integer empresaSolicitante,
                                             UsuarioVO usuarioResponsavelVO, String ipCliente, boolean novoLogin, boolean trocaEmailVincularDados) throws Exception {
        Usuario usuarioDAO = null;
        try {
            if (!UteisValidacao.validaEmail(usuarioEmailNovo.getEmail())) {
                throw new Exception("E-mail inválido");
            }

            usuarioDAO = new Usuario(con);
            novoLogin = verificarForcarNovoLogin(novoLogin, con);
            final String chave = DAO.resolveKeyFromConnection(con);

            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(usuario, Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);

            TokenSolicitacaoDTO dto = new TokenSolicitacaoDTO(usuarioVO, chave, empresaSolicitante, usuarioResponsavelVO, ipCliente, novoLogin, trocaEmailVincularDados);
            dto.setEmail(usuarioEmailNovo.getEmail().trim());
            dto.setCodigoViaEmail(viaCodigoEmail);

            String urlAtivar = obterURLLoginBack() + ENDPOINT_LOGIN_NOVO_EMAIL;

            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            RequestHttpService service = new RequestHttpService();
            RespostaHttpDTO respostaHttpDTO = service.executeRequest(urlAtivar, headers, null, new JSONObject(dto).toString(), MetodoHttpEnum.POST);
            JSONObject jsonResponse = new JSONObject(respostaHttpDTO.getResponse());
            if (!jsonResponse.has("content")) {
                if (jsonResponse.has("meta") && jsonResponse.getJSONObject("meta")
                        .optString("error").equals("email-vinculado-a-outro-usuario-geral")) {
                    throw new Exception("Este e-mail já está vinculado a outro usuário geral! Tente outro, ou crie um novo usuário para utilizá-lo.");
                }
                throw new Exception(respostaHttpDTO.getResponse());
            }
            usuarioDAO.incluirHistoricoSincronizacao(usuario, true, respostaHttpDTO.getResponse(), "TROCA_EMAIL");
            return new JSONObject(respostaHttpDTO.getResponse()).getString("content");
        } catch (Exception ex) {
            try {
                if (usuarioDAO == null) {
                    usuarioDAO = new Usuario(con);
                }
                usuarioDAO.incluirHistoricoSincronizacao(usuario, false, ex.getMessage(), "TROCA_EMAIL");
            } catch (Exception ex1) {
                ex1.printStackTrace();
            }
            throw ex;
        } finally {
            usuarioDAO = null;
        }
    }

    public static String solicitarTrocaTelefone(Integer usuario, UsuarioTelefoneVO usuarioTelefoneNovo, Connection con,
                                                Integer empresaSolicitante, UsuarioVO usuarioResponsavelVO, String ipCliente,
                                                boolean novoLogin) throws Exception {
        Usuario usuarioDAO = null;
        try {
            if (!Uteis.validarTelefoneCelular(usuarioTelefoneNovo.getNumero())) {
                throw new Exception("Número celular inválido");
            }

            usuarioDAO = new Usuario(con);
            novoLogin = verificarForcarNovoLogin(novoLogin, con);
            final String chave = DAO.resolveKeyFromConnection(con);

            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(usuario, Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);
            validarUsuarioGeralPreenchido(usuarioVO);

            String ddi = usuarioTelefoneNovo.getDdi();
            String numero = usuarioTelefoneNovo.getNumero();
            if (UteisValidacao.emptyString(ddi)) {
                ddi = "55";
            }

            //salvar sempre no padrão "DDI|NUMERO_TELEFONE" somente números
            String numeroFormatado = (ddi.replaceAll("[^0-9]", "") + "|" + numero.replaceAll("[^0-9]", ""));

            TokenSolicitacaoDTO dto = new TokenSolicitacaoDTO(usuarioVO, chave, empresaSolicitante, usuarioResponsavelVO, ipCliente, novoLogin);
            dto.setTelefone(numeroFormatado);

            String urlAtivar = obterURLLoginBack() + ENDPOINT_LOGIN_NOVO_TELEFONE;


            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");

            RequestHttpService service = new RequestHttpService();
            RespostaHttpDTO respostaHttpDTO = service.executeRequest(urlAtivar, headers, null, new JSONObject(dto).toString(), MetodoHttpEnum.POST);
            JSONObject jsonResponse = new JSONObject(respostaHttpDTO.getResponse());
            if (!jsonResponse.has("content")) {
                if (jsonResponse.has("meta")) {
                    if (jsonResponse.getJSONObject("meta")
                            .optString("error").equals("telefone-vinculado-a-outro-usuario-geral")) {
                        throw new Exception("Este telefone já está vinculado a outro usuário geral! Tente outro, ou crie um novo usuário para utilizá-lo.");
                    } else if (!UteisValidacao.emptyString(jsonResponse.getJSONObject("meta")
                            .optString("message"))) {
                        throw new Exception(jsonResponse.getJSONObject("meta")
                                .optString("message"));
                    }
                }
                throw new Exception(respostaHttpDTO.getResponse());
            }
            usuarioDAO.incluirHistoricoSincronizacao(usuario, true, respostaHttpDTO.getResponse(), "TROCA_TELEFONE");
            return new JSONObject(respostaHttpDTO.getResponse()).getString("content");
        } catch (Exception ex) {
            try {
                if (usuarioDAO == null) {
                    usuarioDAO = new Usuario(con);
                }
                usuarioDAO.incluirHistoricoSincronizacao(usuario, false, ex.getMessage(), "TROCA_TELEFONE");
            } catch (Exception ex1) {
                ex1.printStackTrace();
            }
            throw ex;
        } finally {
            usuarioDAO = null;
        }
    }

    public static void validarToken(Integer usuario, TokenDTO tokenDTO, Connection con, Integer empresaSolicitante,
                                    UsuarioVO usuarioResponsavelVO, String ipCliente, String operacao, boolean novoLogin) throws Exception {
        Usuario usuarioDAO = null;
        try {
            usuarioDAO = new Usuario(con);
            novoLogin = verificarForcarNovoLogin(novoLogin, con);
            final String chave = DAO.resolveKeyFromConnection(con);

            tokenDTO.setIp(ipCliente);
            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(usuario, Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);
            TokenSolicitacaoDTO dto = new TokenSolicitacaoDTO(usuarioVO, chave, empresaSolicitante, usuarioResponsavelVO, ipCliente, novoLogin);
            tokenDTO.setOrigem(new JSONObject(dto).toString());

            String urlAtivar = obterURLLoginBack() + ENDPOINT_LOGIN_PROCESSAR_TOKEN;

            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");

            RequestHttpService service = new RequestHttpService();
            RespostaHttpDTO respostaHttpDTO = service.executeRequest(urlAtivar, headers, null, new JSONObject(tokenDTO).toString(), MetodoHttpEnum.POST);
            if (!respostaHttpDTO.getHttpStatus().equals(HttpStatus.SC_OK)) {
                String msgErro = "";
                try {
                    JSONObject jsonObject = new JSONObject(respostaHttpDTO.getResponse());
                    msgErro = jsonObject.getJSONObject("meta").optString("message");
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                if (!UteisValidacao.emptyString(msgErro)) {
                    throw new Exception(msgErro);
                } else {
                    throw new Exception(respostaHttpDTO.getResponse());
                }
            }
            usuarioDAO.incluirHistoricoSincronizacao(usuario, true, respostaHttpDTO.getResponse(), operacao);
        } catch (Exception ex) {
            try {
                if (usuarioDAO == null) {
                    usuarioDAO = new Usuario(con);
                }
                usuarioDAO.incluirHistoricoSincronizacao(usuario, false, ex.getMessage(), operacao);
            } catch (Exception ex1) {
                ex1.printStackTrace();
            }
            throw ex;
        } finally {
            usuarioDAO = null;
        }
    }

    public static void desvincularUsuario(Integer idUsuario, Connection con, UsuarioVO usuarioResponsavel, Integer empresaSolicitante, String ipCliente, Boolean novoLogin) throws Exception {
        Usuario usuarioDao = null;
        try {
            usuarioDao = new Usuario(con);

            UsuarioVO usuarioVO = usuarioDao.consultarPorChavePrimaria(idUsuario, Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);
            if (UteisValidacao.emptyString(usuarioVO.getUsuarioGeral())) {
                throw new Exception("Usuário já está desvinculado da academia!");
            }
            desvincularUsuario(usuarioVO, con, usuarioResponsavel, empresaSolicitante, ipCliente, novoLogin);
        } finally {
            usuarioDao = null;
        }
    }

    public static void desvincularUsuario(UsuarioVO usuarioVO, Connection con, UsuarioVO usuarioResponsavel, Integer empresaSolicitante, String ipCliente, Boolean novoLogin) throws Exception {
        Usuario usuarioDao = null;
        try {
            usuarioDao = new Usuario(con);
            novoLogin = verificarForcarNovoLogin(novoLogin, con);
            String chave = DAO.resolveKeyFromConnection(con);

            String urlAtivar = obterURLLoginBack() + ENDPOINT_LOGIN_DESVINCULAR_USUARIO;

            TokenSolicitacaoDTO tokenSolicitacaoDTO = new TokenSolicitacaoDTO(usuarioVO, chave, empresaSolicitante, usuarioResponsavel, ipCliente, novoLogin);

            DesvincularUsuarioDTO desvincularUsuarioDTO = new DesvincularUsuarioDTO();
            desvincularUsuarioDTO.setIdUsuarioResponsavel(usuarioResponsavel.getCodigo());
            desvincularUsuarioDTO.setTokenSolicitacaoDTO(tokenSolicitacaoDTO);
            desvincularUsuarioDTO.setChaveDesvincular(chave);
            desvincularUsuarioDTO.setIdUsarioGeral(usuarioVO.getUsuarioGeral());
            desvincularUsuarioDTO.setCodigoZw(usuarioVO.getCodigo());

            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");

            RequestHttpService service = new RequestHttpService();
            RespostaHttpDTO respostaHttpDTO = service.executeRequest(urlAtivar, headers, null, new JSONObject(desvincularUsuarioDTO).toString(), MetodoHttpEnum.POST);
            if (!respostaHttpDTO.getHttpStatus().equals(HttpStatus.SC_OK)) {
                String msgErro = "";
                try {
                    JSONObject jsonObject = new JSONObject(respostaHttpDTO.getResponse());
                    msgErro = jsonObject.getJSONObject("meta").optString("message");
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                if (!UteisValidacao.emptyString(msgErro)) {
                    throw new Exception(msgErro);
                } else {
                    throw new Exception(respostaHttpDTO.getResponse());
                }
            }

            usuarioDao.incluirHistoricoSincronizacao(usuarioVO.getCodigo(), true, new JSONObject(tokenSolicitacaoDTO).toString(), "DESVINCULAR_USUARIO_NOVO_LOGIN");
        } catch (Exception ex) {
            try {
                if (usuarioDao == null) {
                    usuarioDao = new Usuario(con);
                }
                usuarioDao.incluirHistoricoSincronizacao(usuarioVO.getCodigo(), false, ex.getMessage(), "DESVINCULAR_USUARIO_NOVO_LOGIN");
            } catch (Exception ex1) {
                ex1.printStackTrace();
            }
            throw ex;
        } finally {
            usuarioDao = null;
        }
    }

    private static void validarUsuarioGeralPreenchido(UsuarioVO usuarioVO) throws Exception {
        if (UteisValidacao.emptyString(usuarioVO.getUsuarioGeral())) {
            throw new Exception("Necessário validar o e-mail.");
        }
    }

    public static List<EmpresaAcessoDTO> obterEmpresasUsuarioGeral(String usuarioGeral) throws Exception {
        if (UteisValidacao.emptyString(usuarioGeral)) {
            return null;
        }
        String url = PropsService.getPropertyValue(PropsService.urlLogin) + ENDPOINT_LOGIN_EMPRESAS;

        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");

        Map<String, String> params = new HashMap<>();
        params.put("ug", usuarioGeral);

        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO respostaHttpDTO = service.executeRequest(url, headers, params, null, MetodoHttpEnum.POST);
        Uteis.logarDebug("obterEmpresasUsuarioGeral | " + usuarioGeral + " | " + respostaHttpDTO.getResponse());
        if (!respostaHttpDTO.getHttpStatus().equals(HttpStatus.SC_OK)) {
            String msgErro = "";
            try {
                JSONObject jsonObject = new JSONObject(respostaHttpDTO.getResponse());
                msgErro = jsonObject.getJSONObject("meta").optString("message");
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            if (!UteisValidacao.emptyString(msgErro)) {
                throw new Exception(msgErro);
            } else {
                throw new Exception(respostaHttpDTO.getResponse());
            }
        }
        return JSONMapper.getList(new JSONObject(respostaHttpDTO.getResponse()).getJSONArray("content"), EmpresaAcessoDTO.class);
    }

    public static String validarObterUrlTrocaEmpresa(String usuarioGeral, TrocaEmpresaDTO dto) throws Exception {
        if (UteisValidacao.emptyString(usuarioGeral)) {
            throw new Exception("UsuarioGeral não informado");
        }

        String url = obterURLLoginBack() + ENDPOINT_LOGIN_EMPRESAS_VALIDAR + usuarioGeral + "/" + dto.getEmpresasAcesso().getTk();

        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");

        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO respostaHttpDTO = service.executeRequest(url, headers, null, null, MetodoHttpEnum.GET);
        if (!respostaHttpDTO.getHttpStatus().equals(HttpStatus.SC_OK)) {
            String msgErro = "";
            try {
                JSONObject jsonObject = new JSONObject(respostaHttpDTO.getResponse());
                msgErro = jsonObject.getJSONObject("meta").optString("message");
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            if (!UteisValidacao.emptyString(msgErro)) {
                throw new Exception(msgErro);
            } else {
                throw new Exception(respostaHttpDTO.getResponse());
            }
        }
        try {
            return new JSONObject(respostaHttpDTO.getResponse()).optString("content");
        } catch (Exception ex) {
            ex.printStackTrace();
            return "";
        }
    }

    private static boolean verificarForcarNovoLogin(boolean novoLogin, Connection con) {
        ConfiguracaoSistema configDAO;
        try {
            //se estiver vindo como false verificar se a config está para forçar usar o novo login
            if (!novoLogin) {
                configDAO = new ConfiguracaoSistema(con);
                return configDAO.isForcarNovoLogin();
            }
            return novoLogin;
        } catch (Exception ex) {
            ex.printStackTrace();
            return novoLogin;
        } finally {
            configDAO = null;
        }
    }

    public static List<UsuarioGeralDTO> buscaUsuarioMesmoEmail(String chave, String idUsuarioGeralAtual, String novoEmail) throws Exception {
        String url = obterURLLoginBack() + ENDPOINT_LOGIN_USUARIO_MESMO_EMAIL;
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");

        Map<String, String> params = new HashMap<>();
        params.put("usuarioGeralId", idUsuarioGeralAtual);
        params.put("email", novoEmail);

        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO respostaHttpDTO = service.executeRequest(url, headers, params, null, MetodoHttpEnum.GET);

        Uteis.logarDebug("buscaUsuarioMesmoEmail | " + idUsuarioGeralAtual + " | " + novoEmail + " | " + respostaHttpDTO.getResponse());
        if (!respostaHttpDTO.getHttpStatus().equals(HttpStatus.SC_OK)) {
            String msgErro = "";
            try {
                JSONObject jsonObject = new JSONObject(respostaHttpDTO.getResponse());
                msgErro = jsonObject.getJSONObject("meta").optString("message");
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            if (!UteisValidacao.emptyString(msgErro)) {
                throw new Exception(msgErro);
            } else {
                throw new Exception(respostaHttpDTO.getResponse());
            }
        }
        return JSONMapper.getList(new JSONObject(respostaHttpDTO.getResponse()).getJSONArray("content"), UsuarioGeralDTO.class);
    }

    public static String getUrlLoginEmpresasRedirect(final String usuarioGeral, final String token) {
        return obterURLLoginBack() + SincronizarUsuarioNovoLogin.ENDPOINT_LOGIN_EMPRESAS_REDIRECT + usuarioGeral + "/" + token;
    }

}

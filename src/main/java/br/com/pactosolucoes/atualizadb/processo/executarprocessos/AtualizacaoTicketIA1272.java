package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Luis Antônio de Melo Gomes",
        data = "20/05/2025",
        descricao = "Implementar campo para desabilitar agendamento de aulas experimentais",
        motivacao = "IA-1272")
public class AtualizacaoTicketIA1272 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "ALTER TABLE plano ADD COLUMN habilitarIa BOOLEAN DEFAULT false NULL;", c);

            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "ALTER TABLE produto " +
                            "ADD COLUMN habilitarIa BOOLEAN DEFAULT false NULL;" , c);

            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "ALTER TABLE turma " +
                            "ADD COLUMN habilitarIa BOOLEAN DEFAULT false NULL;" , c);

            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "ALTER TABLE configuracaocrmia " +
                            "ADD COLUMN desabilitarLigacao BOOLEAN DEFAULT false NULL,"  +
                            "ADD COLUMN desabilitarPlanos BOOLEAN DEFAULT false NULL, "  +
                            "ADD COLUMN desabilitarProdutos BOOLEAN DEFAULT false NULL, " +
                            "ADD COLUMN desabilitarTurmas BOOLEAN DEFAULT false NULL, "  +
                            "ADD COLUMN desabilitarVisita BOOLEAN DEFAULT false NULL, " +
                            "ADD COLUMN unificarDF BOOLEAN DEFAULT false NULL;", c);
        }
    }
}

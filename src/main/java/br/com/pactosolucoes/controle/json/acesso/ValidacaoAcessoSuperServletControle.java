package br.com.pactosolucoes.controle.json.acesso;

import acesso.webservice.AcessoControle;
import acesso.webservice.BaseAcessoWS;
import acesso.webservice.DaoAuxiliar;
import acesso.webservice.ValidacaoAcessoWS;
import acesso.webservice.Validador;
import acesso.webservice.retorno.ResultadoWSEnum;
import acesso.webservice.retorno.RetornoRequisicaoBuscarCodigoAcesso;
import acesso.webservice.retorno.RetornoRequisicaoBuscarLocais;
import acesso.webservice.retorno.RetornoRequisicaoConsultarClientes;
import acesso.webservice.retorno.RetornoRequisicaoConsultarColaboradores;
import acesso.webservice.retorno.RetornoRequisicaoDadosOffline;
import acesso.webservice.retorno.RetornoRequisicaoLiberacaoAcesso;
import acesso.webservice.retorno.RetornoRequisicaoRegistrarAcesso;
import acesso.webservice.retorno.RetornoRequisicaoValidacaoAcesso;
import acesso.webservice.retorno.RetornoRequisicaoValidarPermissaoUsuario;
import acesso.webservice.retorno.RetornoRequisicaoWS;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.controle.json.SuperServletControle;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.acesso.AutorizacaoAcessoGrupoEmpresarialVO;
import negocio.comuns.acesso.ClienteExclusaoAcessoDTO;
import negocio.comuns.acesso.IntegracaoAcessoGrupoEmpresarialVO;
import negocio.comuns.acesso.LocalAcessoVO;
import negocio.comuns.acesso.PessoaManterAcessoDTO;
import negocio.comuns.acesso.ServidorFacialVO;
import negocio.comuns.acesso.enumerador.DirecaoAcessoEnum;
import negocio.comuns.acesso.enumerador.MeioIdentificacaoEnum;
import negocio.comuns.acesso.enumerador.MetodoAcessoEnum;
import negocio.comuns.acesso.enumerador.SituacaoAcessoEnum;
import negocio.comuns.acesso.enumerador.TipoAcessoEnum;
import negocio.comuns.acesso.enumerador.TipoLiberacaoEnum;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.xmlgen.ClienteXML;
import negocio.comuns.basico.xmlgen.ColaboradorXML;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.oamd.RedeEmpresaVO;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.impl.microsservice.acessosistema.AcessoSistemaMSService;
import servicos.integracao.ValidacaoAcessoWSConsumer;
import servicos.notificador.NotificacaoTO;
import servicos.notificador.NotificadorServiceControle;
import servicos.oamd.RedeEmpresaService;
import servicos.propriedades.PropsService;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URLDecoder;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static java.util.Objects.isNull;

public abstract class ValidacaoAcessoSuperServletControle extends SuperServletControle {

    protected static final String PARAM_EMPRESA = "empresa";
    protected static final String PARAM_CODIGO_ACESSO = "codigoAcesso";
    protected static final String PARAM_LOCAL_ACESSO = "localAcesso";
    protected static final String PARAM_TERMINAL = "terminal";
    protected static final String PARAM_DIRECAO = "direcao";
    protected static final String PARAM_MEIO_IDENTIFICACAO = "meioIdentificacao";
    protected static final String PARAM_MATRICULA = "matricula";
    protected static final String PARAM_FORCAR_LIB = "forcarLib";
    protected static final String PARAM_LOCAL = "local";
    protected static final String PARAM_PESSOA = "pessoa";
    protected static final String PARAM_ASSINATURA = "assinatura";
    protected static final String PARAM_SENHA = "senha";
    protected static final String PARAM_USUARIO = "usuario";
    protected static final String PARAM_DATA_ACESSO = "dataAcesso";
    protected static final String PARAM_TIPO = "tipo";
    protected static final String PARAM_PUBLICAR_AUTORIZACAO = "publicarAutorizacao";

    public void tratarException(HttpServletResponse response, JSONObject jsonRetorno, Exception e) {
        e.printStackTrace();
        if (e.getMessage().contains("JSONObject")) {
            jsonRetorno.put(STATUS_ERRO, e.getMessage());
            response.setStatus(400);
        } else {
            jsonRetorno.put(STATUS_ERRO, e.getMessage());
            response.setStatus(500);
        }
    }

    public String getNomeCpfPesquisar(HttpServletRequest request) {
        return isCPFParamValue(request.getQueryString()) ? Uteis.aplicarMascara(Uteis.removerMascara(obterParamDecode("nomeCpfPesquisar=", request.getQueryString())), "999.999.999-99") :
                obterParamDecode("nomeCpfPesquisar=", request.getQueryString());
    }

    protected boolean isCPFParamValue(String param) {
        return Uteis.isNumeroValido(Uteis.removerMascara(param)) && (param.length() == 11 || param.length() == 14);
    }

    public JSONObject consultarPessoasSemTemplateBiometrico(String key) throws Exception {
        AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);

        JSONArray arrayClientes = acessoControle.getAcessoClienteDao().consultarClientesSemTemplateBiometrico();
        JSONArray arrayColaboradores = acessoControle.getAcessoClienteDao().consultarColaboradoresSemTemplateBiometrico();

        JSONObject objReturn = new JSONObject();
        objReturn.put("clientes", arrayClientes);
        objReturn.put("colaboradores", arrayColaboradores);
        return objReturn;
    }

    public JSONObject obterServidorFacial(String key, String nomePC) throws Exception {
        ServidorFacialVO servidorFacialVO = DaoAuxiliar.retornarAcessoControle(key).getServidorFacialDao().consultarPorNomePC(nomePC);
        if (servidorFacialVO == null) {
            throw new Exception("Não foi encontrado Servidor Facial para este PC");
        }
        return servidorFacialVO.toJSON();
    }

    /*
     * Valida o acesso do cliente pela Matrícula.
     */

    public RetornoRequisicaoValidacaoAcesso validarAcessoPeloCodigoAcesso(
            String codigoCartao,
            Integer empresa,
            Boolean forcarLib,
            String key,
            Integer localAcesso,
            MeioIdentificacaoEnum meioIdentificacao,
            DirecaoAcessoEnum direcao,
            String terminal,
            boolean acessoOutraEmpresa,
            boolean publicarAutorizacao) throws Exception {
        if (acessoOutraEmpresa) {
            String identificadorOutraEmpresa = codigoCartao.substring(0, 4);
            String codAcesso = codigoCartao.substring(4);
            IntegracaoAcessoGrupoEmpresarialVO integracao = DaoAuxiliar.retornarAcessoControle(key).getIntegracaoDao().consultarPorCodigoChaveIdentificacao(identificadorOutraEmpresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

            return ValidacaoAcessoWSConsumer.validarAcesso(new AutorizacaoAcessoGrupoEmpresarialVO(),
                    codAcesso, integracao.getEmpresaRemota().getCodigo(),
                    integracao.getLocalAcesso(),
                    integracao.getTerminal().toString(),
                    direcao, forcarLib, meioIdentificacao,
                    integracao.getChave(),
                    integracao.getUrlZillyonWeb(),
                    MetodoAcessoEnum.PELOCODIGOACESSO,
                    terminal,
                    true,
                    codigoCartao);
        } else {

            if (codigoCartao.startsWith("AUT")) {
                AutorizacaoAcessoGrupoEmpresarialVO autorizacao;
                RedeEmpresaVO redeEmpresaVO = RedeEmpresaService.obterRedePorChave(key);
                if (redeEmpresaVO != null && redeEmpresaVO.getGestaoRedes()) {
                    autorizacao = AcessoSistemaMSService.findByCodigoAutorizacao(codigoCartao, redeEmpresaVO);
                    if (publicarAutorizacao && autorizacao != null) {
                        AcessoSistemaMSService.publish(autorizacao, false, redeEmpresaVO);
                    }
                } else {
                    autorizacao = DaoAuxiliar.retornarAcessoControle(key).getAutorizacaoDao().consultarPorCodigo(null, null, codigoCartao, null, null, null, null, null, Uteis.NIVELMONTARDADOS_DADOSBASICOS, null, null);
                }

                if ((meioIdentificacao.getCodigo().equals(MeioIdentificacaoEnum.CODIGOACESSOLEITORSERIAL.getCodigo())) ||
                        (meioIdentificacao == MeioIdentificacaoEnum.CODIGOBARRACOMPUTADOR)) {
                    meioIdentificacao = MeioIdentificacaoEnum.MATRICULATECLADOCOMPUTADOR;
                }
                autorizacao.setCodAcesso(autorizacao.getCodAcesso().replaceFirst("NU", ""));
                return ValidacaoAcessoWSConsumer.validarAcesso(autorizacao,
                        autorizacao.getCodAcesso(), autorizacao.getEmpresaRemota().getCodigo(),
                        autorizacao.getIntegracao().getLocalAcesso(),
                        autorizacao.getIntegracao().getTerminal().toString(),
                        direcao, forcarLib, meioIdentificacao,
                        autorizacao.getIntegracao().getChave(),
                        autorizacao.getIntegracao().getUrlZillyonWeb(),
                        MetodoAcessoEnum.PELOCODIGOACESSO,
                        terminal,
                        false,
                        codigoCartao,
                        key,
                        empresa);

            } else {
                return Validador.validarAcesso(codigoCartao, empresa, localAcesso, terminal, direcao, forcarLib, meioIdentificacao, key);
            }
        }
    }

    public RetornoRequisicaoValidacaoAcesso validarAcessoClientePelaMatricula(
            Integer empresa,
            Boolean forcarLib,
            String key,
            Integer localAcesso,
            String matricula,
            MeioIdentificacaoEnum meioIdentificacao,
            DirecaoAcessoEnum direcao,
            String terminal) throws Exception {
        AutorizacaoAcessoGrupoEmpresarialVO autorizacao = new AutorizacaoAcessoGrupoEmpresarialVO();
        if (UteisValidacao.emptyNumber(autorizacao.getCodigo())) {
            String codigoAcesso = "";
            RetornoRequisicaoValidacaoAcesso retorno = null;
            ClienteVO cliente = null;
            try {
                if (Integer.parseInt(matricula) != 0) {
                    AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
                    cliente = acessoControle.getClienteDao().consultarPorMatricula(matricula, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    if (cliente != null) {
                        codigoAcesso = cliente.getCodAcesso();
                    }
                }
                if ((cliente == null) || (Integer.parseInt(matricula) == 0)) {
                    RetornoRequisicaoValidacaoAcesso validarAcessoWS = RetornoRequisicaoWS.montarRetornoValidacaoAcesso(
                            SituacaoAcessoEnum.RV_BLOQALUNOMATNAOCADASTRADO);
                    validarAcessoWS.setTerminal(terminal);
                    return validarAcessoWS;
                }
                retorno = Validador.validarAcesso(codigoAcesso, empresa, localAcesso,
                        terminal, direcao, forcarLib, meioIdentificacao, key);
            } catch (Exception e) {
                e.printStackTrace();

                retorno = new RetornoRequisicaoValidacaoAcesso();
                retorno.setResultado(ResultadoWSEnum.ERRO);
                retorno.setTerminal(terminal);
                retorno.setMsgErro("Método que ocorreu o erro: "
                        + "\"public RetornoRequisicaoValidacaoAcesso validarAcessoClientePelaMatricula\""
                        + " Classe do Erro: " + e.getClass()
                        + " Mensagem Erro: " + e.getMessage());

            }
            return retorno;
        } else {
            return ValidacaoAcessoWSConsumer.validarAcesso(autorizacao, matricula,
                    autorizacao.getEmpresaRemota().getCodigo(),
                    autorizacao.getIntegracao().getLocalAcesso(),
                    autorizacao.getIntegracao().getTerminal().toString(),
                    direcao,
                    forcarLib, meioIdentificacao, autorizacao.getIntegracao().getChave(), autorizacao.getIntegracao().getUrlZillyonWeb(), MetodoAcessoEnum.PELAMATRICULA, terminal, false, null);
        }
    }

    /*Author: Ulisses
     *Data: 02/06/11
     *Objetivo: Validar o acesso de Cliente e Colaborador por senha.
     * Obs.:Inicialmente a senha serÃ¡ digitada no teclado da catraca.
     */
    public RetornoRequisicaoValidacaoAcesso validarAcessoPessoaPorSenha(
            Integer empresa,
            Boolean forcarLib,
            String key,
            Integer localAcesso,
            MeioIdentificacaoEnum meioIdentificacao,
            String senha,
            DirecaoAcessoEnum direcao,
            String terminal) throws Exception {

        AutorizacaoAcessoGrupoEmpresarialVO autorizacao = DaoAuxiliar.retornarAcessoControle(key).getAutorizacaoDao().consultarPorCodigo(null, null, null, null, senha, null, null, null, Uteis.NIVELMONTARDADOS_DADOSBASICOS, null, null);
        EmpresaVO empresaVO = DaoAuxiliar.retornarAcessoControle(key).getEmpresaDao().consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
        if (UteisValidacao.emptyNumber(autorizacao.getCodigo())) {
            RetornoRequisicaoValidacaoAcesso retorno = null;
            RetornoRequisicaoValidacaoAcesso retornoCliente = null;
            RetornoRequisicaoValidacaoAcesso retornoColab = null;
            boolean senhaInvalida = false;
            ResultSet codigos = null;
            try {
                senhaInvalida = (senha.length() != (empresaVO.isSenhaAcessoOnzeDigitos() ? 11 : 5));
                AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
                if (!senhaInvalida) {
                    String senhaEncriptada = Uteis.encriptar(senha);
                    codigos = acessoControle.obterCodigoAcessoPorSenhaEmpresa(senhaEncriptada);
                }
                int qtdCodigos = 0;
                if (codigos != null && codigos.next()) {
                    codigos.last();
                    qtdCodigos = codigos.getRow();
                    codigos.beforeFirst();

                    while (codigos.next()) {
                        if (!UteisValidacao.emptyString(codigos.getString("codcliente")) && (UteisValidacao.emptyString(codigos.getString("codcolaborador")) || UteisValidacao.emptyNumber(codigos.getInt("acessocolaborador")))) {
                            retornoCliente = Validador.validarAcesso(codigos.getString("codcliente"), empresa, localAcesso,
                                    terminal, direcao, forcarLib, meioIdentificacao, key);
                            retorno = retornoCliente;
                            if (retornoCliente.getBloqueadoLiberado().equals("B") && !UteisValidacao.emptyString(codigos.getString("codcolaborador"))) {
                                retornoColab = Validador.validarAcesso(codigos.getString("codcolaborador"), empresa, localAcesso,
                                        terminal, direcao, forcarLib, meioIdentificacao, key);
                                retorno = retornoColab;
                            }
                            if (retornoCliente.getSituacaoAcesso().name().equals("RV_BLOQALUNOPARCELAABERTA") && (retornoColab != null && retornoColab.getSituacaoAcesso().name().equals("RV_BLOQCOLABORADORINATIVO"))) {
                                retorno = retornoCliente;
                            }
                        } else {
                            if (qtdCodigos > 1) {
                                ColaboradorVO colaborador = acessoControle.getColaboradorDao().consultarPorCodAcesso(codigos.getString("codcolaborador"), 0, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                                if (colaborador == null) {
                                    continue;
                                }
                            }

                            retorno = Validador.validarAcesso(codigos.getString("codcolaborador"), empresa, localAcesso,
                                    terminal, direcao, forcarLib, meioIdentificacao, key);
                            if (retorno.getBloqueadoLiberado().equals("B") && !UteisValidacao.emptyString(codigos.getString("codcliente"))) {
                                retorno = Validador.validarAcesso(codigos.getString("codcliente"), empresa, localAcesso,
                                        terminal, direcao, forcarLib, meioIdentificacao, key);
                            }
                        }
                    }
                } else {
                    RetornoRequisicaoValidacaoAcesso validarAcessoWS = RetornoRequisicaoWS.montarRetornoValidacaoAcesso(
                            SituacaoAcessoEnum.RV_BLOQPESSOASENHAINVALIDA);
                    validarAcessoWS.setTerminal(terminal);
                    return validarAcessoWS;
                }
            } catch (Exception e) {
                if (retorno == null) {
                    retorno = new RetornoRequisicaoValidacaoAcesso();
                }
                retorno.setResultado(ResultadoWSEnum.ERRO);
                retorno.setTerminal(terminal);
                retorno.setMsgErro("Método que ocorreu o erro: "
                        + "\"public RetornoRequisicaoValidacaoAcesso validarAcessoPessoaPorSenha\""
                        + " Classe do Erro: " + e.getClass()
                        + " Mensagem Erro: " + e.getMessage());

            }
            return retorno;
        } else {
            if (autorizacao.getTipoPessoa().equals("CL")) {
                if (UteisValidacao.emptyNumber(autorizacao.getCodigoMatricula())) {
                    String codAcesso = autorizacao.getCodAcesso().startsWith("NU") ? autorizacao.getCodAcesso().substring(2) : autorizacao.getCodAcesso();
                    return ValidacaoAcessoWSConsumer.validarAcesso(autorizacao, codAcesso,
                            autorizacao.getEmpresaRemota().getCodigo(),
                            autorizacao.getIntegracao().getLocalAcesso(),
                            autorizacao.getIntegracao().getTerminal().toString(),
                            direcao,
                            forcarLib, meioIdentificacao, autorizacao.getIntegracao().getChave(), autorizacao.getIntegracao().getUrlZillyonWeb(), MetodoAcessoEnum.PELOCODIGOACESSO, terminal, false, null);
                } else {
                    return ValidacaoAcessoWSConsumer.validarAcesso(autorizacao, autorizacao.getCodigoMatricula().toString(),
                            autorizacao.getEmpresaRemota().getCodigo(),
                            autorizacao.getIntegracao().getLocalAcesso(),
                            autorizacao.getIntegracao().getTerminal().toString(),
                            direcao,
                            forcarLib, meioIdentificacao, autorizacao.getIntegracao().getChave(), autorizacao.getIntegracao().getUrlZillyonWeb(), MetodoAcessoEnum.PELAMATRICULA, terminal, false, null);
                }

            } else {
                return ValidacaoAcessoWSConsumer.validarAcesso(autorizacao, autorizacao.getCodigoGenerico().toString(),
                        autorizacao.getEmpresaRemota().getCodigo(),
                        autorizacao.getIntegracao().getLocalAcesso(),
                        autorizacao.getIntegracao().getTerminal().toString(),
                        direcao,
                        forcarLib, meioIdentificacao, autorizacao.getIntegracao().getChave(), autorizacao.getIntegracao().getUrlZillyonWeb(), MetodoAcessoEnum.PELOCODIGOCOLABORADOR, terminal, false, null);
            }
        }

    }

    /*
     * Valida o acesso do Colaborador pelo cÃ³digo
     */
    public RetornoRequisicaoValidacaoAcesso validarAcessoColaboradorPeloCodigo(
            String codigo,
            Integer empresa,
            Boolean forcarLib,
            String key,
            Integer localAcesso,
            MeioIdentificacaoEnum meioIdentificacao,
            DirecaoAcessoEnum direcao,
            String terminal) throws Exception {

        String codigoAcesso = "";
        RetornoRequisicaoValidacaoAcesso retorno = null;
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            ColaboradorVO colaborador = acessoControle.getColaboradorDao().consultarPorCodigo(Integer.valueOf(codigo), 0, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            if ((colaborador != null) && (colaborador.getCodigo().intValue() != 0)) {
                codigoAcesso = colaborador.getCodAcesso();
                retorno = Validador.validarAcesso(codigoAcesso, empresa, localAcesso,
                        terminal, direcao, forcarLib, meioIdentificacao, key);
            } else {
                RetornoRequisicaoValidacaoAcesso validarAcessoWS = RetornoRequisicaoWS.montarRetornoValidacaoAcesso(
                        SituacaoAcessoEnum.RV_BLOQCOLABORADORCODNAOCADASTRADO);
                validarAcessoWS.setTerminal(terminal);
                retorno = validarAcessoWS;
            }
        } catch (Exception e) {
            retorno = new RetornoRequisicaoValidacaoAcesso();
            retorno.setTerminal(terminal);
            retorno.setResultado(ResultadoWSEnum.ERRO);
            retorno.setMsgErro("Método que ocorreu o erro: "
                    + "\"public RetornoRequisicaoValidacaoAcesso validarAcessoColaboradorPeloCodigo\""
                    + " Classe do Erro: " + e.getClass()
                    + " Mensagem Erro: " + e.getMessage());
        }
        return retorno;

    }

    /**
     * Operação de serviço web Objetivo: Retornar as informações do Local de Acesso, bem como seus coletores.
     */

    public RetornoRequisicaoBuscarLocais buscarConfigLocalAcesso(
            Integer empresa,
            String key,
            String nomeComputador) throws Exception {
        RetornoRequisicaoBuscarLocais retorno = null;
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            LocalAcessoVO localAcessoVo = acessoControle.getLocalAcessoDao().consultarPorComputador(nomeComputador, empresa);
            if ((localAcessoVo.getCodigo() == null) || (localAcessoVo.getCodigo() <= 0)) {
                retorno = new RetornoRequisicaoBuscarLocais();
                retorno.setResultado(ResultadoWSEnum.ERRO);
                retorno.setMsgErro("Nenhum local cadastrado para o computador \"" + nomeComputador + "\"");
            } else {
                localAcessoVo.setListaColetores(acessoControle.getColetorDao().consultarColetores(localAcessoVo.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                EmpresaVO empresaVO = acessoControle.getEmpresaDao().consultarPorChavePrimaria(localAcessoVo.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
                localAcessoVo.setEmpresa(empresaVO);
                retorno = RetornoRequisicaoWS.montarRetornoLocalAcesso(localAcessoVo, empresaVO.isSenhaAcessoOnzeDigitos(), empresaVO.isRegistrarTentativasAcesso());
            }

        } catch (Exception e) {
            if (retorno == null) {
                retorno = new RetornoRequisicaoBuscarLocais();
            }
            retorno.setResultado(ResultadoWSEnum.ERRO);
            retorno.setMsgErro("Método que ocorreu o erro: "
                    + "\"public RetornoRequisicaoBuscarLocais buscarConfigLocalAcesso\""
                    + " Classe do Erro: " + e.getClass()
                    + " Mensagem Erro: " + e.getMessage());

        }
        return retorno;
    }

    public RetornoRequisicaoValidarPermissaoUsuario validarPemissaoUsuario(
            Integer empresa,
            String key,
            String recurso,
            String senha,
            Integer terminal,
            String usuario) {
        RetornoRequisicaoValidarPermissaoUsuario retorno = new RetornoRequisicaoValidarPermissaoUsuario();
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            try {
                Integer usuarioCodigo;
                if (Uteis.isInteger(usuario)) {
                    usuarioCodigo = Integer.parseInt(usuario);
                } else {
                    UsuarioVO usuarioVO = acessoControle.getUsuarioDao()
                            .consultarPorUsername(usuario, Uteis.NIVELMONTARDADOS_MINIMOS);
                    usuarioCodigo = usuarioVO.getCodigo();
                }
                retorno = acessoControle.validarPermissaoUsuario(usuarioCodigo, senha, empresa, recurso);
            } catch (Exception e) {
                /*
                 * Na validaÃ§Ã£o do usuÃ¡rio, se o usuÃ¡rio nÃ£o tem permissÃ£o Ã© lanÃ§ada uma exceÃ§Ã£o
                 */
                retorno.setTemPemissao(false);
                retorno.setMensagem(e.getMessage());
            }
        } catch (Exception e) {
            retorno.setResultado(ResultadoWSEnum.ERRO);
            retorno.setMsgErro("Método que ocorreu o erro: "
                    + "\"public RetornoRequisicaoLiberarForcado permiteLiberarForcado\""
                    + " Classe do Erro: " + e.getClass()
                    + " Mensagem Erro: " + e.getMessage());
        }
        return retorno;
    }

    /**
     * OperaÃ§Ã£o de serviÃ§o web
     */

    public RetornoRequisicaoRegistrarAcesso registrarAcesso(
            Integer codigo,
            Date dataAcesso,
            DirecaoAcessoEnum direcao,
            Integer empresa,
            String key,
            Integer local,
            MeioIdentificacaoEnum meioIdentificacao,
            SituacaoAcessoEnum situacao,
            Integer terminal,
            String tipo,
            Integer usuario) throws Exception {
        BaseAcessoWS baseAcesso = new BaseAcessoWS();
        return baseAcesso.registrarAcessoInternal(codigo, dataAcesso, direcao, empresa, key,
                local, meioIdentificacao, situacao, terminal, tipo, usuario, null, "", null);

    }

    public RetornoRequisicaoBuscarCodigoAcesso buscarPorCodigoAcesso(
            Integer empresa,
            String key,
            Integer local,
            String codigoAcesso,
            Integer terminal,
            String tipo) throws Exception {

        AutorizacaoAcessoGrupoEmpresarialVO autorizacao = DaoAuxiliar.retornarAcessoControle(key).getAutorizacaoDao().consultarPorCodigo(null, null, null, null, null, codigoAcesso, null, null, Uteis.NIVELMONTARDADOS_DADOSBASICOS, null, null);
        RetornoRequisicaoBuscarCodigoAcesso retorno = new RetornoRequisicaoBuscarCodigoAcesso();
        retorno.setTerminal(terminal.toString());
        AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
        try {
            if (!UteisValidacao.emptyNumber(autorizacao.getCodigo())) {
                retorno.setCodigoAcesso(autorizacao.getCodigoAutorizacao());
                retorno.setNomeCliente(autorizacao.getNomePessoa());
            } else if (TipoAcessoEnum.TA_ALUNO.getId().equals(tipo)) {
                ClienteVO cliente = acessoControle.getClienteDao().consultarPorCodAcesso(codigoAcesso, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                if (cliente != null) {
                    cliente.setPessoa(acessoControle.getPessoaDao().consultarPorCodigo(cliente.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                    retorno.setCodigoAcesso(cliente.getCodAcesso());
                    retorno.setNomeCliente(cliente.getPessoa().getNome());
                    retorno.setUrlFotoCliente(cliente.getPessoa().getUrlFoto());
                }
            } else if (TipoAcessoEnum.TA_COLABORADOR.getId().equals(tipo)) {
                ColaboradorVO colaborador = acessoControle.getColaboradorDao().consultarPorCodAcesso(codigoAcesso, 0, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                if (colaborador != null) {
                    colaborador.setPessoa(acessoControle.getPessoaDao().consultarPorCodigo(colaborador.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                    retorno.setCodigoAcesso(colaborador.getCodAcesso());
                    retorno.setNomeCliente(colaborador.getPessoa().getNome());
                    retorno.setUrlFotoCliente(colaborador.getPessoa().getUrlFoto());
                }
            }
        } catch (Exception e) {
            retorno.setResultado(ResultadoWSEnum.ERRO);
            retorno.setMsgErro("Método que ocorreu o erro: "
                    + "\"public RetornoRequisicaoBuscarCodigoAcesso buscarCodigoAcesso\""
                    + " Classe do Erro: " + e.getClass()
                    + " Mensagem Erro: " + e.getMessage());

        }
        return retorno;
    }

    public RetornoRequisicaoBuscarCodigoAcesso buscarCodigoAcesso(
            Integer empresa,
            String key,
            Integer local,
            String matricula,
            Integer terminal,
            String tipo) throws Exception {

        AutorizacaoAcessoGrupoEmpresarialVO autorizacao;
        RedeEmpresaVO redeEmpresa = RedeEmpresaService.obterRedePorChave(key);
        if (redeEmpresa != null && redeEmpresa.getGestaoRedes()) {
            autorizacao = AcessoSistemaMSService.findByCodigoAutorizacao(matricula, redeEmpresa);
        } else {
            autorizacao = DaoAuxiliar.retornarAcessoControle(key).getAutorizacaoDao().consultarPorCodigo(null, null, null, null, null, matricula, null, null, Uteis.NIVELMONTARDADOS_DADOSBASICOS, null, null);
        }

        RetornoRequisicaoBuscarCodigoAcesso retorno = new RetornoRequisicaoBuscarCodigoAcesso();
        retorno.setTerminal(terminal.toString());
        AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
        try {
            if (!UteisValidacao.emptyNumber(autorizacao.getCodigo())) {
                retorno.setCodigoAcesso(autorizacao.getCodigoAutorizacao());
                retorno.setNomeCliente(autorizacao.getNomePessoa());
            } else if (TipoAcessoEnum.TA_ALUNO.getId().equals(tipo)) {
                ClienteVO cliente = acessoControle.getClienteDao().consultarPorMatricula(matricula, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                if (cliente != null) {
                    cliente.setPessoa(acessoControle.getPessoaDao().consultarPorCodigo(cliente.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                    retorno.setCodigoAcesso(cliente.getCodAcesso());
                    retorno.setNomeCliente(cliente.getPessoa().getNome());
                }
            } else if (TipoAcessoEnum.TA_COLABORADOR.getId().equals(tipo)) {
                ColaboradorVO colaborador = acessoControle.getColaboradorDao().consultarPorCodigo(Integer.valueOf(matricula), 0, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                if (colaborador != null) {
                    colaborador.setPessoa(acessoControle.getPessoaDao().consultarPorCodigo(colaborador.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                    retorno.setCodigoAcesso(colaborador.getCodAcesso());
                    retorno.setNomeCliente(colaborador.getPessoa().getNome());
                }
            }
        } catch (Exception e) {
            retorno.setResultado(ResultadoWSEnum.ERRO);
            retorno.setMsgErro("Método que ocorreu o erro: "
                    + "\"public RetornoRequisicaoBuscarCodigoAcesso buscarCodigoAcesso\""
                    + " Classe do Erro: " + e.getClass()
                    + " Mensagem Erro: " + e.getMessage());

        }
        return retorno;
    }

    /**
     * @author: Ulisses Data: 22/12/10 Objetivo do método: Pesquisar clientes
     * pelo nome. Tipo de Retorno : Retorna uma "String", cujo conteÃºdo Ã© um
     * arquivo XML no formato DataPacket, com a relaÃ§Ã£o dos clientes
     * consultados.
     */

    public RetornoRequisicaoConsultarClientes consultarClientesPeloNome(
            Integer idEmpresa,
            String key,
            String nomePesquisar) throws Exception {
        RetornoRequisicaoConsultarClientes retorno = new RetornoRequisicaoConsultarClientes();
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            List<ClienteVO> listaClientes = acessoControle.consultarClientesPeloNome(nomePesquisar, 0, 50);
            listaClientes.addAll(acessoControle.getAutorizacaoDao().consultarPorNomeMock(nomePesquisar));
            retorno.setClientesXML((new ClienteXML()).criarArquivoXMLDataPacket(listaClientes));
        } catch (Exception e) {
            retorno.setResultado(ResultadoWSEnum.ERRO);
            retorno.setMsgErro("Método que ocorreu o erro: "
                    + "\"public RetornoRequisicaoConsultarClientes consultarClientesPeloNome\""
                    + " Classe do Erro: " + e.getClass()
                    + " Mensagem Erro: " + e.getMessage());

        }
        return retorno;

    }

    public RetornoRequisicaoConsultarClientes consultarClientesPeloNomeCpf(
            String key,
            String nomeCpfPesquisar) throws Exception {
        RetornoRequisicaoConsultarClientes retorno = new RetornoRequisicaoConsultarClientes();
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            List<ClienteVO> listaClientes = acessoControle.consultarClientesPeloNome(nomeCpfPesquisar, 0, 50);

            RedeEmpresaVO redeEmpresa = RedeEmpresaService.obterRedePorChave(key);
            if (redeEmpresa != null && redeEmpresa.getGestaoRedes()) {
                List<AutorizacaoAcessoGrupoEmpresarialVO> autorizacoes = AcessoSistemaMSService.findAllByNameOrCPF(nomeCpfPesquisar, redeEmpresa);
                for (AutorizacaoAcessoGrupoEmpresarialVO aut : autorizacoes) {
                    listaClientes.add(aut.toClienteVO());
                }
            } else {
                listaClientes.addAll(acessoControle.getAutorizacaoDao().consultarPorNomeMock(nomeCpfPesquisar));
            }

            if (isNull(listaClientes)) {
                listaClientes = new ArrayList<>();
            }

            listaClientes.addAll(acessoControle.consultarClientesPeloCpf(nomeCpfPesquisar, (50 - listaClientes.size())));
            listaClientes.addAll(acessoControle.getAutorizacaoDao().consultarPorCpfMock(nomeCpfPesquisar));

            retorno.setClientesXML((new ClienteXML()).criarArquivoXMLDataPacket(listaClientes));
        } catch (Exception e) {
            retorno.setResultado(ResultadoWSEnum.ERRO);
            retorno.setMsgErro("Método que ocorreu o erro: "
                    + "\"public RetornoRequisicaoConsultarClientes consultarClientesPeloNomeCpf\""
                    + " Classe do Erro: " + e.getClass()
                    + " Mensagem Erro: " + e.getMessage());

        }
        return retorno;

    }

    public RetornoRequisicaoConsultarClientes consultarClientesPeloNomeAutorizacaoCobranca(
            Integer idEmpresa,
            String key,
            String nomePesquisar) throws Exception {
        RetornoRequisicaoConsultarClientes retorno = new RetornoRequisicaoConsultarClientes();
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            List<ClienteVO> listaClientes = acessoControle.getClienteDao().consultarPorNomeClienteComAutorizacao(nomePesquisar, 0, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, 50);
            retorno.setClientesXML((new ClienteXML()).criarArquivoXMLDataPacket(listaClientes, true));
        } catch (Exception e) {
            retorno.setResultado(ResultadoWSEnum.ERRO);
            retorno.setMsgErro("Método que ocorreu o erro: "
                    + "\"public RetornoRequisicaoConsultarClientes consultarClientesPeloNome\""
                    + " Classe do Erro: " + e.getClass()
                    + " Mensagem Erro: " + e.getMessage());

        }
        return retorno;
    }

    public RetornoRequisicaoConsultarClientes consultarClientesPeloNomeCpfAutorizacaoCobranca(
            Integer idEmpresa,
            String key,
            String nomePesquisar) throws Exception {
        RetornoRequisicaoConsultarClientes retorno = new RetornoRequisicaoConsultarClientes();
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            List<ClienteVO> listaClientes = acessoControle.getClienteDao().consultarPorNomeClienteComAutorizacao(nomePesquisar, 0, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, 50);
            if (isNull(listaClientes) || listaClientes.isEmpty()) {
                listaClientes = acessoControle.getClienteDao().consultarPorCpfClienteComAutorizacao(nomePesquisar, 0, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, 50);
            }
            retorno.setClientesXML((new ClienteXML()).criarArquivoXMLDataPacket(listaClientes, true));
        } catch (Exception e) {
            retorno.setResultado(ResultadoWSEnum.ERRO);
            retorno.setMsgErro("Método que ocorreu o erro: "
                    + "\"public RetornoRequisicaoConsultarClientes consultarClientesPeloNome\""
                    + " Classe do Erro: " + e.getClass()
                    + " Mensagem Erro: " + e.getMessage());

        }
        return retorno;
    }

    /**
     * @author: Ulisses Data: 29/12/10 Objetivo do método: Registrar o acesso
     * rápido de visitantes, terceiros, alunos que esqueceram o número de sua
     * matrícula etc... Tipo de Retorno : RetornoRequisicaoLiberacaoAcesso
     */

    public RetornoRequisicaoLiberacaoAcesso registrarLiberacaoAcesso(
            Date dataAcesso,
            DirecaoAcessoEnum direcao,
            Integer empresa,
            String key,
            Integer local,
            Integer terminal,
            TipoLiberacaoEnum tipoLiberacao,
            Integer usuario,
            String codAcesso,
            String justificativa,
            String nomeGenerico) throws Exception {
        RetornoRequisicaoLiberacaoAcesso retorno = new RetornoRequisicaoLiberacaoAcesso();
        retorno.setTerminal(terminal.toString());
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            acessoControle.registrarLiberacaoAcesso(dataAcesso, tipoLiberacao, empresa, local, terminal, direcao, usuario, codAcesso, justificativa, nomeGenerico);
            retorno.setAcessoLiberado(true);
        } catch (Exception e) {
            retorno.setResultado(ResultadoWSEnum.ERRO);
            retorno.setMsgErro("Método que ocorreu o erro: "
                    + "\"public RetornoRequisicaoLiberacaoAcesso registrarLiberacaoAcesso\""
                    + " Classe do Erro: " + e.getClass()
                    + " Mensagem Erro: " + e.getMessage());

        }
        return retorno;
    }

    public String getVersaoSistema() {
        return PropsService.getPropertyValue(PropsService.VERSAO_SISTEMA);
    }

    /*Author: XiquiN
     *Data: 02/12/11
     *Objetivo: Limpar a lista de locais de acesso para onde a foto do cliente jÃ¡ foi enviada.
     Dessa forma, a foto Ã© enviada novamente.
     */

    /**
     * @author: Waller Maciel
     * @since 14/02/11 Resetar o HashMap de controladores AcessoControle, para
     * que novas configuracoes sejam aceitas.
     */

    public void resetMapaControladores() {
        DaoAuxiliar.resetMapaControladores();
    }

    /*Author: XiquiN
     *Data: 17/12/11
     *Objetivo: Enviar a lista das pessoas que acessaram nos Ãºltimos 3 meses e que tÃªm foto.
     */

    public void excluirPessoaFotoLocalAcesso(
            String key,
            Integer localAcesso,
            Integer pessoa,
            String codigoAutorizacao) throws Exception {

        if (codigoAutorizacao == null || codigoAutorizacao.equals("")) {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            acessoControle.getPessoaFotoLocalAcessoDao().excluirFotoPessoaLocalAcesso(localAcesso, pessoa);
        } else {
            AutorizacaoAcessoGrupoEmpresarialVO autorizacao = DaoAuxiliar.retornarAcessoControle(key).getAutorizacaoDao().consultarPorCodigo(null, null, null, null, null, codigoAutorizacao, null, null, Uteis.NIVELMONTARDADOS_DADOSBASICOS, null, null);
            if (!UteisValidacao.emptyNumber(autorizacao.getCodigo())) {
                ValidacaoAcessoWSConsumer.excluirPessoaFotoLocalAcesso(autorizacao, autorizacao.getIntegracao().getLocalAcesso(), autorizacao.getIntegracao().getChave(), autorizacao.getIntegracao().getUrlZillyonWeb());
            }
        }

    }

    /*Author: XiquiN
     *Data: 17/12/11
     *Objetivo: Enviar a foto da pessoa informada. Ao enviar, Ã© gravado um registro na tabela pessoaFotoLocalAcesso
     */

    public String[] montarListaAcessosPessoasComFoto(
            String key,
            Integer localAcesso) throws Exception {
        AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
        return acessoControle.montarListaAcessosPessoasComFoto(localAcesso);
    }

    /*Author: XiquiN
     *Data: 02/05/12
     *Objetivo: Gravar em arquivo o que for recebido
     */

    public byte[] pegarFotoPessoa(
            String key,
            Integer localAcesso,
            Integer pessoa) throws Exception {
        AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
        return acessoControle.pegarFotoPessoa(localAcesso, pessoa);
    }

    public void gravarArquivoLocal(
            byte[] conteudo,
            String key,
            String nome) throws Exception {

        try {
            String dir = PropsService.getPropertyValue(PropsService.pathLogsZAcesso);
            nome = dir + '/' + key + '_' + nome;

            File temp = new File(dir);
            if (!temp.exists()) {
                temp.mkdirs();
            }
            temp = new File(nome);
            if (temp.exists()) {
                temp.delete();
            }

            FileOutputStream file = new FileOutputStream(nome, false);
            file.write(conteudo);
            file.close();

            Uteis.logar(null, "Arquivo gravado: " + nome + " - "
                    + Formatador.formatarValorNumerico(Double.valueOf(conteudo.length) / 1024, ",##0.00")
                    + " kb");
        } catch (Exception erro) {
            Uteis.logar(null, "Erro gravando arquivo "
                    + nome + ": " + erro.getMessage());
        }
    }

    public void registrarDownloadBaseOffline(String key,
                                             Integer localAcesso) {
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            acessoControle.getLocalAcessoDao().alterarDataDownloadBase(localAcesso, Calendario.hoje());
        } catch (Exception e) {
            Uteis.logar(null, e.toString());
        }
    }

    public void registrarVersaoAcesso(String key,
                                      Integer localAcesso,
                                      String versao,
                                      String parametros) {
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            acessoControle.getLocalAcessoDao().alterarVersaoAcesso(localAcesso, versao, parametros);
        } catch (Exception e) {
            Uteis.logar(null, e.toString());
        }
    }

    public Boolean temDadosOfflineGerados(
            String key,
            Integer localAcesso) {
        try {
            AcessoControle ac = DaoAuxiliar.retornarAcessoControle(key);
            //verifica se o Local possui "UtilizarModoOffLine" marcado
            if (ac.getLocalAcessoDao().utilizaModoOffLine(localAcesso)) {
                boolean temDadosOffLineHoje = ac.getLocalAcessoDao().temDadosOffLine(localAcesso);
                Uteis.logar(null, "Tem aquivo novo off-line para " + key + " -> " + temDadosOffLineHoje);
                return temDadosOffLineHoje;
            } else {
                return false;
            }
        } catch (Exception ex) {
            Logger.getLogger(ValidacaoAcessoWS.class.getName()).log(Level.SEVERE, null, ex);
        }
        return false;
    }

    public String ultimoDadosOfflineGerado(
            String key,
            Integer localAcesso) {
        try {
            AcessoControle ac = DaoAuxiliar.retornarAcessoControle(key);
            //verifica se o Local possui "UtilizarModoOffLine" marcado
            if (ac.getLocalAcessoDao().utilizaModoOffLine(localAcesso)) {
                Date ultimaAtualizacao = ac.getLocalAcessoDao().ultimoDadosOffLineGerado(localAcesso);
                if (ultimaAtualizacao != null) {
                    final String data = Calendario.getData(ultimaAtualizacao, "yyyy-MM-dd HH:mm:ss");
                    final String msg = String.format("Tem aquivo novo off-line para %s com data %s ", key, data);
                    Uteis.logar(null, msg);
                    return data;
                }
            }
        } catch (Exception ex) {
            Logger.getLogger(ValidacaoAcessoWS.class.getName()).log(Level.SEVERE, null, ex);
        }
        return "";
    }

    public String solicitarUtilitario(String nome) throws Exception {

        StringBuilder ret = new StringBuilder();

        // Por algum motivo se nÃ£o criptogravar no ZAW o nome nÃ£o chega direito
        // EntÃ£o por enquanto estamos usando a criptografia
        nome = Uteis.desencriptarRetornoZAWOffline(nome);
        String dir = PropsService.getPropertyValue(PropsService.pathUtilsZAcesso) + "/";
        File io = new File(dir + nome);
        if (!io.exists()) {
            throw new IOException("Utilitário não encontrado");
        }

        ret.append("Usuario=").append(PropsService.getPropertyValue(PropsService.usuarioHTTPAcesso)).append("\n");
        ret.append("Senha=").append(PropsService.getPropertyValue(PropsService.senhaHTTPAcesso)).append("\n");
        ret.append("URL=").append(PropsService.getPropertyValue(PropsService.urlUtilsZAcesso)).
                append("/").append(nome);
        return Uteis.encriptarRetornoZAWOffline(ret.toString());
    }

    public String consultarOperacoesPendentes(
            String key,
            Integer localAcesso) {
        //Tratar notificaÃ§Ãµes agendadas para os Concentradores Desktops
        List<NotificacaoTO> notfs = NotificadorServiceControle.obterListaNotificacoesPorTipo("WS");
        String operacoes = "";
        for (NotificacaoTO notf : notfs) {
            if (notf.getChave() != null && !notf.getChave().isEmpty() && notf.getChave().equals(key)) {//chave especifica
                if (notf.getLocalAcesso() != null && !notf.getLocalAcesso().isEmpty() && notf.getLocalAcesso().equals(
                        localAcesso.toString())) {//local de acesso especifico, dessa chave, dessa empresa
                    operacoes += notf.getDescricaoPlusID();
                    notf.setLida(true);
                } else if (notf.getLocalAcesso() == null || notf.getLocalAcesso().isEmpty()) {//qualquer local de acesso dessa chave, dessa empresa
                    operacoes += notf.getDescricaoPlusID();
                    notf.setLida(true);
                }
            } else if (notf.getChave() == null || notf.getChave().isEmpty()) {//qualquer chave, qualquer empresa
                operacoes += notf.getDescricaoPlusID();
                notf.setLida(true);
            }
        }
        String data = "";
        return data.isEmpty() ? operacoes : "ts:" + data + ";" + operacoes;
    }

    public void registrarCodigoPossuiMaisDeumaDigital(String key, String codigoAcesso) {

        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            acessoControle.getClienteDao().alterarSomenteTemMaisDeUmaDigital(codigoAcesso, true);
        } catch (Exception e) {
            Uteis.logar(null, e.toString());
        }
    }

    public void registrarCodigoAcessoAlternativo(String key, String codigoAcesso, String codigoAcessoAlternativo) {
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            acessoControle.getClienteDao().alterarCodigoAcessoAlternativo(codigoAcesso, codigoAcessoAlternativo);
        } catch (Exception e) {
            Uteis.logar(null, e.toString());
        }
    }

    public void registrarCodigoNaoPossuiMaisDeumaDigital(String key, String codigoAcesso) {
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            acessoControle.getClienteDao().alterarSomenteTemMaisDeUmaDigital(codigoAcesso, false);
        } catch (Exception e) {
            Uteis.logar(null, e.toString());
        }
    }

    public JSONArray obterIpLocalAcesso(String key, String categoria) throws Exception {
        return new JSONArray(DaoAuxiliar.retornarAcessoControle(key).getLocalAcessoDao().obterIp(categoria));
    }

    public JSONArray registrarTicket(String key, String nomeOuMatriculaOuCodigoAcesso, String ticket, String dataHora) throws Exception {
        return new JSONArray(DaoAuxiliar.retornarAcessoControle(key).getAcessoClienteDao().registrarTicket(nomeOuMatriculaOuCodigoAcesso, ticket, dataHora));
    }

    public RetornoRequisicaoConsultarColaboradores consultarColaboradoresPeloNome(
            Integer idEmpresa,
            String key,
            String nomePesquisar,
            Boolean consultarTerceirizado) throws Exception {
        RetornoRequisicaoConsultarColaboradores retorno = new RetornoRequisicaoConsultarColaboradores();
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);

            List<ColaboradorVO> listaColaboradores;
            if (consultarTerceirizado) {
                listaColaboradores = acessoControle.getColaboradorDao().consultarPorNomeTipoColaborador(nomePesquisar,
                        idEmpresa, false, false, Uteis.NIVELMONTARDADOS_CONSULTA_WS,
                        TipoColaboradorEnum.TERCEIRIZADO, TipoColaboradorEnum.FORNECEDOR);
            } else {
                listaColaboradores = acessoControle.getColaboradorDao().consultarPorNomeTipoColaborador(nomePesquisar,
                        idEmpresa, false, false, Uteis.NIVELMONTARDADOS_CONSULTA_WS,
                        TipoColaboradorEnum.PROFESSOR, TipoColaboradorEnum.PROFESSOR_TREINO, TipoColaboradorEnum.PERSONAL_TRAINER,
                        TipoColaboradorEnum.ORIENTADOR, TipoColaboradorEnum.CONSULTOR, TipoColaboradorEnum.PERSONAL_INTERNO,
                        TipoColaboradorEnum.PERSONAL_EXTERNO, TipoColaboradorEnum.ESTUDIO, TipoColaboradorEnum.COORDENADOR,
                        TipoColaboradorEnum.MEDICO, TipoColaboradorEnum.FUNCIONARIO, TipoColaboradorEnum.ADMINISTRADOR);
            }

            List<String> codAcessoIncluidos = new ArrayList<>();
            List<ColaboradorVO> listaRetornar = new ArrayList<>();
            for (ColaboradorVO colaboradorVO : listaColaboradores) {
                if (!codAcessoIncluidos.contains(colaboradorVO.getCodAcesso())) {
                    codAcessoIncluidos.add(colaboradorVO.getCodAcesso());
                    listaRetornar.add(colaboradorVO);
                }
            }

            retorno.setColaboradoresXML((new ColaboradorXML()).criarArquivoXMLDataPacket(listaRetornar));
        } catch (Exception e) {
            retorno.setResultado(ResultadoWSEnum.ERRO);
            retorno.setMsgErro("Método que ocorreu o erro: "
                    + "\"public RetornoRequisicaoConsultarColaboradores consultarColaboradoresPeloNome\""
                    + " Classe do Erro: " + e.getClass()
                    + " Mensagem Erro: " + e.getMessage());

        }
        return retorno;

    }

    public RetornoRequisicaoConsultarColaboradores consultarColaboradoresPeloNomeCpf(
            Integer idEmpresa,
            String key,
            String nomeCpfPesquisar,
            Boolean consultarTerceirizado) throws Exception {
        RetornoRequisicaoConsultarColaboradores retorno = new RetornoRequisicaoConsultarColaboradores();
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);

            List<ColaboradorVO> listaColaboradores;
            if (consultarTerceirizado) {
                listaColaboradores = acessoControle.getColaboradorDao().consultarPorNomeTipoColaborador(nomeCpfPesquisar,
                        idEmpresa, false, false, Uteis.NIVELMONTARDADOS_CONSULTA_WS,
                        TipoColaboradorEnum.TERCEIRIZADO, TipoColaboradorEnum.FORNECEDOR);
            } else {
                listaColaboradores = acessoControle.getColaboradorDao().consultarPorNomeTipoColaborador(nomeCpfPesquisar,
                        idEmpresa, false, false, Uteis.NIVELMONTARDADOS_CONSULTA_WS,
                        TipoColaboradorEnum.PROFESSOR, TipoColaboradorEnum.PROFESSOR_TREINO, TipoColaboradorEnum.PERSONAL_TRAINER,
                        TipoColaboradorEnum.ORIENTADOR, TipoColaboradorEnum.CONSULTOR, TipoColaboradorEnum.PERSONAL_INTERNO,
                        TipoColaboradorEnum.PERSONAL_EXTERNO, TipoColaboradorEnum.ESTUDIO, TipoColaboradorEnum.COORDENADOR,
                        TipoColaboradorEnum.MEDICO, TipoColaboradorEnum.FUNCIONARIO, TipoColaboradorEnum.ADMINISTRADOR);
            }
            if (isNull(listaColaboradores) || listaColaboradores.isEmpty()) {
                if (consultarTerceirizado) {
                    listaColaboradores = acessoControle.getColaboradorDao().consultarPorCpfTipoColaborador(nomeCpfPesquisar,
                            idEmpresa, false, false, Uteis.NIVELMONTARDADOS_CONSULTA_WS,
                            TipoColaboradorEnum.TERCEIRIZADO, TipoColaboradorEnum.FORNECEDOR);
                } else {
                    listaColaboradores = acessoControle.getColaboradorDao().consultarPorCpfTipoColaborador(nomeCpfPesquisar,
                            idEmpresa, false, false, Uteis.NIVELMONTARDADOS_CONSULTA_WS,
                            TipoColaboradorEnum.PROFESSOR, TipoColaboradorEnum.PROFESSOR_TREINO, TipoColaboradorEnum.PERSONAL_TRAINER,
                            TipoColaboradorEnum.ORIENTADOR, TipoColaboradorEnum.CONSULTOR, TipoColaboradorEnum.PERSONAL_INTERNO,
                            TipoColaboradorEnum.PERSONAL_EXTERNO, TipoColaboradorEnum.ESTUDIO, TipoColaboradorEnum.COORDENADOR,
                            TipoColaboradorEnum.MEDICO, TipoColaboradorEnum.FUNCIONARIO, TipoColaboradorEnum.ADMINISTRADOR);
                }
            }

            List<String> codAcessoIncluidos = new ArrayList<>();
            List<ColaboradorVO> listaRetornar = new ArrayList<>();
            for (ColaboradorVO colaboradorVO : listaColaboradores) {
                if (!codAcessoIncluidos.contains(colaboradorVO.getCodAcesso())) {
                    codAcessoIncluidos.add(colaboradorVO.getCodAcesso());
                    listaRetornar.add(colaboradorVO);
                }
            }

            retorno.setColaboradoresXML((new ColaboradorXML()).criarArquivoXMLDataPacket(listaRetornar));
        } catch (Exception e) {
            retorno.setResultado(ResultadoWSEnum.ERRO);
            retorno.setMsgErro("Método que ocorreu o erro: "
                    + "\"public RetornoRequisicaoConsultarColaboradores consultarColaboradoresPeloNome\""
                    + " Classe do Erro: " + e.getClass()
                    + " Mensagem Erro: " + e.getMessage());

        }
        return retorno;

    }

    public RetornoRequisicaoDadosOffline montarDadosOffline(String key, int localAcesso) throws Exception {

        RetornoRequisicaoDadosOffline ret = new RetornoRequisicaoDadosOffline();
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            ret = acessoControle.gerarArquivoOffline(key, localAcesso, null);

            // Para manter compatibilidade com ValidacaoAcessoWS, o formato de retorno foi mantido
        } catch (Exception e) {
            ret.setResultado(ResultadoWSEnum.ERRO);
            ret.setMsgErro(e.getMessage());
            throw e;
        }

        return ret;
    }

    public JSONObject consultarAlunosNaAcademia(String key, Integer codEmpresa) throws Exception {
        AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
        return acessoControle.getAcessoClienteDao().consultarAlunosNaAcademia(codEmpresa);
    }

    public String obterParamDecode(String param, String query) {
        try {
            return URLDecoder.decode(query.split(param)[1], "ISO-8859-1");
        } catch (Exception e) {
            return " ";
        }
    }

    public JSONObject registrarLog(String key, String codAcesso, String operacao, String entidade, String nomeCampo, String mensagem) throws Exception {
        AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);

        PessoaVO pessoaAcesso = null;
        ClienteVO clienteAcesso = acessoControle.getClienteDao().consultarPorCodAcesso(codAcesso, false, Uteis.NIVELMONTARDADOS_MINIMOS);
        if (clienteAcesso != null) {
            pessoaAcesso = clienteAcesso.getPessoa();
        }
        if (pessoaAcesso == null) {
            ColaboradorVO colaboradorAcesso = acessoControle.getColaboradorDao().consultarPorCodAcesso(codAcesso, 0, Uteis.NIVELMONTARDADOS_MINIMOS);
            pessoaAcesso = colaboradorAcesso.getPessoa();
        }

        if (pessoaAcesso == null) {
            throw new Exception("Não foi encontrado pessoa com o código de acesso " + codAcesso);
        }

        if (UteisValidacao.emptyString(operacao)) {
            throw new Exception("Operação deve ser informada");
        }

        if (UteisValidacao.emptyString(entidade)) {
            throw new Exception("Entidade deve ser informada");
        }

        LogVO obj = new LogVO();
        obj.setPessoa(pessoaAcesso.getCodigo());

        obj.setChavePrimaria("");
        obj.setNomeEntidade(entidade.toUpperCase());
        obj.setNomeEntidadeDescricao(entidade);
        obj.setOperacao(operacao.toUpperCase());
        obj.setResponsavelAlteracao("ZillyonAcessoWeb");
        obj.setNomeCampo(nomeCampo);
        obj.setDataAlteracao(Calendario.hoje());
        obj.setValorCampoAnterior("");
        obj.setValorCampoAlterado(mensagem);

        acessoControle.getLogDao().incluirSemCommit(obj);

        JSONObject objReturn = new JSONObject();
        objReturn.put("codigo", obj.getCodigo());
        return objReturn;
    }

    public List<ClienteExclusaoAcessoDTO> listaAlunosExclusaoAcesso(String key, Integer empresa) throws Exception {
//        Manter no equipamento se o aluno possuir autorização de acesso ou período de acesso vigente.
//        Manter no equipamento se o cliente possuir pelo menos um registro de acesso nos últimos 60 dias.
//        Manter no equipamento se a situação do aluno for "Ativo".
//        Manter no equipamento se a situação do contrato for diferente de cancelado ou desistente.
//        Manter no sistema se a data de cadastro do cliente ou visitante for inferior a 60 dias

        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("cl.empresa, \n");
        sql.append("cl.matricula, \n");
        sql.append("cl.pessoa, \n");
        sql.append("cl.codacesso, \n");
        sql.append("cl.codacessoalternativo \n");
        sql.append("from cliente cl \n");
        sql.append("inner join pessoa p on p.codigo = cl.pessoa \n");
        sql.append("where cl.situacao not in ('AT') \n");
        sql.append("and not exists(select codigo from acessocliente where cliente = cl.codigo and dthrentrada >= (CURRENT_DATE - interval '60' day)) \n");
        sql.append("and not exists(select codigo from autorizacaoacessocliente where cliente = cl.codigo and CURRENT_DATE between vigenciainicial::date and vigenciafinal::date) \n");
        sql.append("and not exists(select codigo from periodoacessocliente where pessoa = cl.pessoa and CURRENT_DATE between datainicioacesso and datafinalacesso and tipoacesso in ('CA','BO','TO','TD','PL','AA','DI','RT','RR','RA','RC')) \n");
        sql.append("and not exists(select codigo from contrato where pessoa = cl.pessoa and situacao = 'AT') \n");
        sql.append("and not (cl.situacao = 'VI' and p.datacadastro::date >= (CURRENT_DATE - interval '60' day)) \n");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append("and cl.empresa = ").append(empresa).append(" \n");
        }

        List<ClienteExclusaoAcessoDTO> lista = new ArrayList<>();
        try (Connection con = new DAO().obterConexaoEspecifica(key)) {
            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
            while (rs.next()) {
                ClienteExclusaoAcessoDTO dto = new ClienteExclusaoAcessoDTO();
                dto.setEmpresa(rs.getInt("empresa"));
                dto.setMatricula(rs.getString("matricula"));
                dto.setPessoa(rs.getInt("pessoa"));
                dto.setCodacesso(rs.getString("codacesso"));
                dto.setCodacessoalternativo(rs.getString("codacessoalternativo"));
                lista.add(dto);
            }
        }
        return lista;
    }

    public List<PessoaManterAcessoDTO> listaPessoasManterAcesso(String key, Integer empresa) throws Exception {
        if (UteisValidacao.emptyNumber(empresa)) {
            throw new Exception("Empresa deve ser informada");
        }
        // Traz colaboradores que estão ativos
        // Alunos que possuem algum periodo de acesso futuro, ou algum período de acesso que se encerrou nos últimos 60 dias
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT\n");
        sql.append("\tDISTINCT pes.codigo AS codPessoa,\n");
        sql.append("\tCASE\n");
        sql.append("\t\tWHEN col.codigo IS NOT NULL AND col.situacao = 'AT'\n");
        sql.append("\t\tTHEN NULL\n");
        sql.append("\t\tWHEN col.codigo IS NOT NULL AND col.situacao = 'NA' AND cli.codigo IS NOT NULL\n");
        sql.append("\t\tTHEN cli.codigomatricula\n");
        sql.append("\t\tWHEN col.codigo IS NULL\n");
        sql.append("\t\tTHEN cli.codigomatricula\n");
        sql.append("\tEND AS matricula,\n");
        sql.append("\tCASE\n");
        sql.append("\t\tWHEN col.codigo IS NOT NULL AND col.situacao = 'AT'\n");
        sql.append("\t\tTHEN col.empresa\n");
        sql.append("\t\tWHEN col.codigo IS NOT NULL AND col.situacao = 'NA' AND cli.codigo IS NOT NULL\n");
        sql.append("\t\tTHEN cli.empresa\n");
        sql.append("\t\tWHEN col.codigo IS NULL\n");
        sql.append("\t\tTHEN cli.empresa\n");
        sql.append("\tEND AS empresa,\n");
        sql.append("\tCASE\n");
        sql.append("\t\tWHEN col.codigo IS NOT NULL AND col.situacao = 'AT'\n");
        sql.append("\t\tTHEN col.codacesso\n");
        sql.append("\t\tWHEN col.codigo IS NOT NULL AND col.situacao = 'NA' AND cli.codigo IS NOT NULL\n");
        sql.append("\t\tTHEN cli.codacesso\n");
        sql.append("\t\tWHEN col.codigo IS NULL\n");
        sql.append("\t\tTHEN cli.codacesso\n");
        sql.append("\tEND AS codAcesso,\n");
        sql.append("\tCASE\n");
        sql.append("\t\tWHEN col.codigo IS NOT NULL AND col.situacao = 'AT'\n");
        sql.append("\t\tTHEN col.codacessoalternativo\n");
        sql.append("\t\tWHEN col.codigo IS NOT NULL AND col.situacao = 'NA' AND cli.codigo IS NOT NULL\n");
        sql.append("\t\tTHEN cli.codacessoalternativo\n");
        sql.append("\t\tWHEN col.codigo IS NULL\n");
        sql.append("\t\tTHEN cli.codacessoalternativo\n");
        sql.append("\tEND AS codAcessoAlternativo,\n");
        sql.append("\tpes.fotokey AS fotokey\n");
        sql.append("FROM pessoa pes\n");
        sql.append("LEFT JOIN colaborador col ON col.pessoa = pes.codigo\n");
        sql.append("LEFT JOIN cliente cli ON cli.pessoa = pes.codigo\n");
        sql.append("WHERE (((col.codigo IS NOT NULL AND col.situacao = 'AT') OR (col.codigo IS NOT NULL AND col.situacao = 'NA' AND cli.codigo IS NOT NULL)) OR (col.codigo IS NULL))\n");
        sql.append("AND (((EXISTS (SELECT ac.codigo\n");
        sql.append("\t\tFROM acessocliente ac\n");
        sql.append("\t\tINNER JOIN localacesso lac ON lac.codigo = ac.localacesso\n");
        sql.append("\t\tWHERE ac.cliente = cli.codigo\n");
        sql.append("\t\tAND dthrentrada::date > NOW ()::date - INTERVAL '60 days'\n");
        sql.append("\t\tAND lac.empresa = ").append(empresa).append("))\n");
        sql.append("\tOR (col.situacao = 'AT' AND col.empresa = ").append(empresa).append("))\n");
        sql.append("OR ((cli.codigo IS NULL AND col.situacao = 'AT' AND col.empresa = ").append(empresa).append(") OR (cli.codigo IS NOT NULL AND cli.empresa = ").append(empresa).append(" AND\n");
        sql.append("\t(EXISTS\n");
        sql.append("\t\t(SELECT codigo\n");
        sql.append("\t\tFROM periodoacessocliente pac\n");
        sql.append("\t\tWHERE pessoa = pes.codigo\n");
        sql.append("\t\tAND datafinalacesso::date > NOW ()::date - INTERVAL '60 days')))))\n");
        sql.append("AND (cli.codigo IS NOT NULL OR col.codigo IS NOT NULL)");

        List<PessoaManterAcessoDTO> lista = new ArrayList<>();
        try (Connection con = new DAO().obterConexaoEspecifica(key)) {
            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
            while (rs.next()) {
                PessoaManterAcessoDTO dto = new PessoaManterAcessoDTO();
                dto.setPessoa(rs.getInt("codPessoa"));
                dto.setMatricula(rs.getString("matricula"));
                dto.setEmpresa(rs.getInt("empresa"));
                dto.setCodAcesso(rs.getString("codAcesso"));
                dto.setCodAcessoAlternativo(rs.getString("codAcessoAlternativo"));
                dto.setUrlFoto(Uteis.getPaintFotoDaNuvem(rs.getString("fotokey")));
                lista.add(dto);
            }
        }
        return lista;
    }

}

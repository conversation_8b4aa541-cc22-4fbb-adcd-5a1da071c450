package br.com.pactosolucoes.controle.json.cadastro;

import acesso.webservice.AcessoControle;
import acesso.webservice.DaoAuxiliar;
import br.com.pactosolucoes.controle.json.SuperServletControle;
import br.com.pactosolucoes.encrypt.gpg.PgpEncryption;
import br.com.pactosolucoes.enumeradores.TipoTelefoneEnum;
import br.com.pactosolucoes.integracao.amigoFit.ClienteAmigoFitJSON;
import controle.arquitetura.SuperControle;
import negocio.comuns.acesso.AcessoClienteVO;
import negocio.comuns.acesso.DadosAcessoOfflineVO;
import negocio.comuns.acesso.PessoaConsultaTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.*;
import negocio.comuns.basico.enumerador.TiposMensagensEnum;
import negocio.comuns.basico.webservice.IntegracaoCadastrosWS;
import negocio.comuns.contrato.*;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.MemCachedManager;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.LogIntegracoes;
import negocio.facade.jdbc.contrato.ContratoRecorrencia;
import negocio.facade.jdbc.memcached.ObjetoCacheEnum;
import negocio.facade.jdbc.plano.Modalidade;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.interfaces.acesso.AcessoClienteInterfaceFacade;
import negocio.interfaces.basico.ClienteInterfaceFacade;
import negocio.interfaces.basico.ClienteMensagemInterfaceFacade;
import negocio.interfaces.basico.PessoaInterfaceFacade;
import negocio.interfaces.contrato.PeriodoAcessoClienteInterfaceFacade;
import negocio.interfaces.financeiro.MovParcelaInterfaceFacade;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.negocio.comuns.basico.SGPAvaliacaoFisicaTO;
import relatorio.negocio.comuns.basico.SGPModalidadeComTurmaTO;
import relatorio.negocio.comuns.basico.SGPModalidadeSemTurmaTO;
import relatorio.negocio.jdbc.basico.SGPAvaliacaoFisicaRel;
import relatorio.negocio.jdbc.basico.SGPModalidadeComTurmaRel;
import relatorio.negocio.jdbc.basico.SGPModalidadeSemTurmaRel;
import servicos.propriedades.PropsService;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.nio.charset.StandardCharsets;
import java.security.Security;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class IntegracaoCadastroServletControle extends SuperServletControle {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        super.processRequest(request, response);
        PrintWriter out = response.getWriter();
        JSONObject jsonRetorno = new JSONObject();
        try {
            String key = obterParametroString(request, "key");
            Integer empresa = obterParametroInt(request, "empresa");
            String operacao = obterParametroString(request, "operacao");
            OperacoesIntegracaoCadastroEnum operacaoEnum = OperacoesIntegracaoCadastroEnum.obterOperacao(operacao);
            if (operacaoEnum == null) {
                throw new Exception("Não foi possível identificar qual operação deve ser realizada.");
            }
            switch (operacaoEnum) {
                case obterClientePorCPF: {
                    String cpf = obterParametroString(request, "cpf");
                    String retorno;
                    if (cpf.isEmpty() || key.isEmpty()) {
                        throw new Exception("Não foi possível Realizar a operação. Favor informar o CPF e Chave da empresa.");
                    } else {
                        String cpfcons = Uteis.formatarCpfCnpj(cpf, false);
                        retorno = obterClientePorCPF(key, cpfcons, Uteis.NIVELMONTARDADOS_ROBO);
                        jsonRetorno.put(STATUS_SUCESSO, retorno);
                        out.println(jsonRetorno);
                    }
                }
                break;
                case gravarUtilizacaoAvaliacaoFisica: {
                    Integer codCliente = obterParametro(request, "codigoCliente");
                    Integer codAvaliacaoFisica = obterParametro(request, "codigoAvaliacaoFisica");
                    Date dataAvaliacao = obterParametroDate(request, "dataAvaliacao");
                    String retorno = gravarUtilizacaoAvaliacaoFisica(key, codCliente, codAvaliacaoFisica, dataAvaliacao);
                    jsonRetorno.put(STATUS_SUCESSO, retorno);
                }
                break;
                case removerUtilizacaoAvaliacaoFisica: {
                    Integer codAvaliacaoFisica = obterParametro(request, "codigoAvaliacaoFisica");
                    String retorno = removerUtilizacaoAvaliacaoFisica(key, codAvaliacaoFisica);
                    jsonRetorno.put(STATUS_SUCESSO, retorno);
                }
                case obterClienteAmigoFitPorCPF: {
                    String cpf = obterParametroString(request, "cpf");
                    String retorno;
                    if (cpf.isEmpty() || key.isEmpty()) {
                        throw new Exception("Não foi possível Realizar a operação. Favor informar o CPF e Chave da empresa.");
                    } else {
                        String cpfcons = Uteis.formatarCpfCnpj(cpf, false);
                        retorno = obterClienteAmigoFitPorCPF(key, cpfcons);
                        jsonRetorno.put(STATUS_SUCESSO, retorno);
                        out.println(jsonRetorno);
                    }
                }
                break;
                case consultarParcelas: {
                    String matriculaCpf =  obterParametroString(request, "matriculaCpf");
                    MovParcelaInterfaceFacade movparcelaDao = DaoAuxiliar.retornarAcessoControle(key).getMovParcelaDao();
                    List<MovParcelaVO> list = movparcelaDao.consultarParcelasPorMatriculaOuCpf(matriculaCpf, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, false);
                    JSONArray movParcelas = new JSONArray();
                    for (MovParcelaVO obj : list) {
                        JSONObject movParcelaObj = new JSONObject(obj.toJSON("v2"));
                        movParcelas.put(movParcelaObj);
                    }
                    out.println(movParcelas);
                }
                break;
                case consultarParcelasVencidas: {
                    String matricula =  obterParametroString(request, "matricula");

                    ClienteInterfaceFacade clienteDao = DaoAuxiliar.retornarAcessoControle(key).getClienteDao();
                    ClienteVO clienteVO = clienteDao.consultarPorMatricula(matricula, false, Uteis.NIVELMONTARDADOS_MINIMOS);
                    if (clienteVO == null) {
                        throw new Exception("Matrícula não encontrada");
                    }

                    if (clienteVO.getSituacao().equals("IN")) {
                        throw new Exception("Cliente se encontra inativo");
                    }

                    MovParcelaInterfaceFacade movparcelaDao = DaoAuxiliar.retornarAcessoControle(key).getMovParcelaDao();
                    List<MovParcelaVO> list = movparcelaDao.consultarParcelasVencidasPorMatricula(matricula, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    JSONArray movParcelas = new JSONArray();
                    for (MovParcelaVO obj : list) {
                        JSONObject movParcelaObj = new JSONObject(obj.toWS());
                        movParcelas.put(movParcelaObj);
                    }
                    out.println(movParcelas);
                }
                break;
                case consultarParcelasVencidasOuUltimaRenovacao: {
                    Integer pessoa =  obterParametroInt(request, "pessoa");
                    MovParcelaInterfaceFacade movparcelaDao = DaoAuxiliar.retornarAcessoControle(key).getMovParcelaDao();
                    List<MovParcelaVO> list = movparcelaDao.consultarParcelasVencidasOuUltimaRenovacaoPessoa(pessoa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    JSONArray movParcelas = new JSONArray();
                    for (MovParcelaVO obj : list) {
                        JSONObject movParcelaObj = new JSONObject(obj.toWS());
                        movParcelas.put(movParcelaObj);
                    }
                    out.println(movParcelas);
                }
                break;
                case obterPessoaPorIdExternoIntegracao: {
                    String idExternoIntegracao = obterParametroString(request, "idExternoIntegracao");
                    String userAgent = obterParametroString(request, "userAgent");
                    String retorno;
                    if (UteisValidacao.emptyString(idExternoIntegracao) || UteisValidacao.emptyString(key)) {
                        throw new Exception("Não foi possível realizar a operação. Favor informar o idExternoIntegracao e chave da empresa.");
                    } else {
                        retorno = obterClientePorIdExternoIntegracao(key, idExternoIntegracao, userAgent);
                        jsonRetorno.put(STATUS_SUCESSO, retorno);
                        out.println(jsonRetorno);
                    }
                }
                break;
                case atualizarTemplateFacial:{
                    Integer codigoPessoa = obterParametroInt(request, "codigoPessoa");
                    JSONObject jsonBodyRequest = new JSONObject(Uteis.convertStreamToStringBuffer(request.getInputStream(), "UTF-8").toString());
                    String templateBase64 = jsonBodyRequest.getString("templateBase64");
                    String privateKeyKleopatra = PropsService.getPropertyValue(PropsService.pathPrivateKeyTemplate);

                    if(UteisValidacao.emptyString(Uteis.readLineByLineJava8(privateKeyKleopatra))){
                        throw new Exception("Não foi possível realizar a operação. A chave não está presente para essa operação, entre em contato com o suporte.");
                    }

                    if(UteisValidacao.emptyString(key)){
                        throw new Exception("Não foi possível realizar a operação. Favor informar a chave da empresa.");
                    }
                    if(UteisValidacao.emptyNumber(codigoPessoa) || UteisValidacao.emptyString(templateBase64)){
                        throw new Exception("Não foi possível realizar a operação. Favor informar código e template facial da pessoa.");
                    }

                    Security.addProvider(new BouncyCastleProvider());

                    try (InputStream chave = new ByteArrayInputStream(Uteis.readLineByLineJava8(privateKeyKleopatra).getBytes())) {
                        byte[] decrypt = PgpEncryption.decryptNoPassword(templateBase64.getBytes(), chave);
                        String templateBase64Decrypt = new StringBuilder(new String(decrypt, StandardCharsets.UTF_8)).toString();
                        jsonRetorno.put(STATUS_SUCESSO, atualizarTemplateFacial(key, codigoPessoa, templateBase64Decrypt));
                        out.println(jsonRetorno);
                    }

                }
                break;
                case sgpAvaliacaoFisica: {
                    String dataInicial = obterParametroString(request, "dataInicial");
                    String dataFinal = obterParametroString(request, "dataFinal");
                    String retorno = consultarSpgAvaliacaoFisica(key, empresa, dataInicial, dataFinal);
                    jsonRetorno.put(STATUS_SUCESSO, retorno);
                    out.println(jsonRetorno);
                }
                break;
                case sgpModalidadesComTurma: {
                    String dataInicial = obterParametroString(request, "dataInicial");
                    String dataFinal = obterParametroString(request, "dataFinal");
                    String retorno = consultarSgpModalidadesComTurma(key, empresa, dataInicial, dataFinal);
                    jsonRetorno.put(STATUS_SUCESSO, retorno);
                    out.println(jsonRetorno);
                }
                break;
                case sgpModalidadesSemTurma: {
                    String dataInicial = obterParametroString(request, "dataInicial");
                    String dataFinal = obterParametroString(request, "dataFinal");
                    String retorno = consultarSgpModalidadesSemTurma(key, empresa, dataInicial, dataFinal);
                    jsonRetorno.put(STATUS_SUCESSO, retorno);
                    out.println(jsonRetorno);
                }
                break;
                case atualizaCodigoAfiliadoVitio: {
                    String codigoAfiliado = obterParametroString(request, "codigoAfiliado");
                    Integer idEmpresa = Integer.parseInt(obterParametroString(request, "idEmpresa"));
                    String telefoneColaborador = obterParametroString(request, "telefoneColaborador");
                    jsonRetorno = obtemJsonRetornoAtualizacaoCodigoVitio(jsonRetorno, key, codigoAfiliado, idEmpresa, telefoneColaborador);
                    out.println(jsonRetorno);
                }
                break;
                case consultarClientePelaMatriculaSESC: {
                    String matriculaPesquisar = obterParametroString(request, "matriculaPesquisar");
                    String retorno = obterClienteJsonPorMatriculaSesc(matriculaPesquisar, empresa, key);
                    jsonRetorno.put(STATUS_SUCESSO, retorno);
                    out.println(jsonRetorno);
                }
                break;
                case atualizaAlunoPelaMatriculaSESC: {
                    String matriculaPesquisar = obterParametroString(request, "matriculaPesquisar");
                    List<Integer> codigosMatriculas = obterClientesPorMatriculaSesc(matriculaPesquisar, empresa, key);
                    String dadosAluno = new JSONObject(Uteis.convertStreamToStringBuffer(request.getInputStream(), "UTF-8").toString()).toString();
                    LogIntegracoesVO logIntegracoesVO  = inserirLogIntegracao(dadosAluno, operacao, key);
                    try {
                        if (codigosMatriculas.size() == 1) {
                            String resultado = DaoAuxiliar.retornarAcessoControle(key).getIntegracaoImportacaoDao().atualizaInformacoesAluno(dadosAluno, codigosMatriculas.get(0), empresa);
                            if (resultado.equals("Dados atualizados com sucesso!")) {
                                jsonRetorno.put(STATUS_SUCESSO, resultado);
                            } else {
                                jsonRetorno.put(STATUS_ERRO, resultado);
                            }
                        } else if (codigosMatriculas.size() > 1) {
                            JSONObject jsonMatriculas = new JSONObject();
                            jsonMatriculas.put("message", "Matrícula Sesc duplicada!");
                            jsonMatriculas.put("matriculaszw", codigosMatriculas);
                            jsonRetorno.put(STATUS_ERRO, jsonMatriculas.toString());
                        } else {
                            jsonRetorno.put(STATUS_ERRO, "Cliente não encontrado!");
                        }
                        alterarLogIntegracao(logIntegracoesVO, jsonRetorno.toString(), key);
                    }catch (Exception e) {
                        alterarLogIntegracao(logIntegracoesVO, "Erro: "+ e.getMessage(), key);
                        throw e;
                    }
                    out.println(jsonRetorno);
                }
                break;
                case atualizaAlunoPeloCPF: {
                    String cpf = obterParametroString(request, "cpf");
                    List<Integer> codigosMatriculas = obterClientesPorCpf(cpf, empresa, key);
                    String dadosAluno = new JSONObject(Uteis.convertStreamToStringBuffer(request.getInputStream(), "UTF-8").toString()).toString();
                    LogIntegracoesVO logIntegracoesVO  = inserirLogIntegracao(dadosAluno, operacao, key);
                    try {
                        if (codigosMatriculas.size() == 1) {
                            String resultado = DaoAuxiliar.retornarAcessoControle(key).getIntegracaoImportacaoDao().atualizaInformacoesAluno(dadosAluno, codigosMatriculas.get(0), empresa);
                            if (resultado.equals("Dados atualizados com sucesso!")) {
                                jsonRetorno.put(STATUS_SUCESSO, resultado);
                            } else {
                                jsonRetorno.put(STATUS_ERRO, resultado);
                            }
                        } else if (codigosMatriculas.size() > 1) {
                            JSONObject jsonMatriculas = new JSONObject();
                            jsonMatriculas.put("message", "CPF duplicado!");
                            jsonMatriculas.put("matriculaszw", codigosMatriculas);
                            jsonRetorno.put(STATUS_ERRO, jsonMatriculas.toString());
                        } else {
                            jsonRetorno.put(STATUS_ERRO, "Cliente não encontrado!");
                        }
                        alterarLogIntegracao(logIntegracoesVO, jsonRetorno.toString(), key);
                    } catch (Exception e) {
                        alterarLogIntegracao(logIntegracoesVO, "Erro: "+ e.getMessage(), key);
                        throw e;
                    }

                    out.println(jsonRetorno);
                }
                break;
                case OBTER_CLIENTE_CODACESSO: {
                    findClientByAccessCode(request, jsonRetorno, key);
                    out.println(jsonRetorno);
                }
                break;
                case OBTER_COLABORADOR_CODACESSO: {
                    findCollaboratorByAccessCode(request, jsonRetorno, key);
                    out.println(jsonRetorno);
                }
                break;
                case atualizarFotoPerfilAlunoCPFMatricula: {
                    String matricula = obterParametroString(request, "matricula");
                    String cpf = obterParametroString(request, "cpf");
                    String foto = obterParametroRaw(request);
                    String retorno = atualizarFotoClienteCpfMatriculaSesc(key, cpf, foto, matricula);
                    jsonRetorno.put(STATUS_SUCESSO, retorno);
                    out.println(jsonRetorno);
                }
                break;
                case LIMPAR_SINCRONIZADO_EM_POR_CODACESSO: {
                    String codAcesso = obterParametroString(request, "codAcesso");
                    String retorno = limparSincronizadoEmPorCodAcesso(key, codAcesso);
                    jsonRetorno.put(STATUS_SUCESSO, retorno);
                    out.println(jsonRetorno);
                }
                break;
                case RESET_CACHE_MAP_BANNERS: {
                    if (MemCachedManager.getInstance().getMemcachedOn()) {
                        String retorno = MemCachedManager.getInstance().resetCacheMap(ObjetoCacheEnum.MAPA_CAMPANHA_BANNERS, Uteis.getTempoSegundosExpiracaoCacheBanners());
                        jsonRetorno.put(STATUS_SUCESSO, retorno);
                    } else {
                        jsonRetorno.put(STATUS_ERRO, "MemCache nao esta ativo");
                    }
                    out.println(jsonRetorno);
                }
                break;
                case STATUS_CACHE_MAP_BANNERS: {
                    if (MemCachedManager.getInstance().getMemcachedOn()) {
                        JSONObject retorno = new JSONObject();
                        Map<String, String> mapaCampanha = MemCachedManager.getInstance().obterMapa(ObjetoCacheEnum.MAPA_CAMPANHA_BANNERS);
                        retorno.put("QTD_MAPA_CAMPANHA", mapaCampanha != null ? mapaCampanha.size() : 0);
                        jsonRetorno.put(STATUS_SUCESSO, retorno);
                    } else {
                        jsonRetorno.put(STATUS_ERRO, "MemCache nao esta ativo");
                    }
                    out.println(jsonRetorno);
                }
                break;
                case RESET_CACHE_MAP_CLUBE_DE_BENEFICIOS: {
                    if (MemCachedManager.getInstance().getMemcachedOn()) {
                        String retorno = MemCachedManager.getInstance().resetCacheMap(ObjetoCacheEnum.MAPA_CLUBE_DE_BENEFICIOS, 432000);
                        jsonRetorno.put(STATUS_SUCESSO, retorno);
                    } else {
                        jsonRetorno.put(STATUS_ERRO, "MemCache nao esta ativo");
                    }
                    out.println(jsonRetorno);
                }
                break;
                case STATUS_CACHE_MAP_CLUBE_DE_BENEFICIOS: {
                    if (MemCachedManager.getInstance().getMemcachedOn()) {
                        JSONObject retorno = new JSONObject();
                        Map<String, String> mapaCampanha = MemCachedManager.getInstance().obterMapa(ObjetoCacheEnum.MAPA_CLUBE_DE_BENEFICIOS);
                        retorno.put("QTD_MAPA_CLUBE_DE_BENEFICIOS", mapaCampanha != null ? mapaCampanha.size() : 0);
                        jsonRetorno.put(STATUS_SUCESSO, retorno);
                    } else {
                        jsonRetorno.put(STATUS_ERRO, "MemCache nao esta ativo");
                    }
                    out.println(jsonRetorno);
                }
                break;
                case consultarContratosRecorrenciaTotemRenovar: {
                    int codigoCliente = obterParametroInt(request, "cliente");
                    String retorno = consultarContratosRecorrenciaTotemRenovar(key, codigoCliente);
                    jsonRetorno.put(STATUS_SUCESSO, retorno);
                    out.println(jsonRetorno);
                }
                break;
                case consultarClienteGymnamic: {
                    String cpf = obterParametroString(request, "cpf");
                    String email =  obterParametroString(request, "email");
                    Integer codigoEmpresa = obterParametroInt(request, "codigoEmpresa");
                    String retorno = consultarClienteGymnamic(key, cpf, email, codigoEmpresa);
                    jsonRetorno.put(STATUS_SUCESSO, retorno);
                    out.println(jsonRetorno);
                }
                break;
                case ATUALIZAR_STATUS_SINC_FOTO_PESSOA_AUTORIZACAO_ACESSO: {
                    Integer codAutorizacao = obterParametroInt(request, "codAutorizacao");
                    Integer status = obterParametroInt(request, "status");
                    String retorno = atualizarStatusSincFotoPessoa(key, codAutorizacao, status);
                    jsonRetorno.put(STATUS_SUCESSO, retorno);
                    out.println(jsonRetorno);
                }
                break;
                case atualizarAcessoCatraca: {
                    JSONObject body = new JSONObject();
                    body.put("empresa", obterParametroInt(request, "empresa"));
                    body.put("email", obterParametroString(request, "email"));
                    body.put("cpf", obterParametroString(request, "cpf"));
                    body.put("matricula", obterParametroString(request, "matricula"));
                    body.put("aviso", obterParametroString(request, "aviso"));
                    body.put("mensagem", obterParametroString(request, "mensagem"));
                    body.put("bloqueado", obterParametroBoolean(request, "bloqueado"));
                    String retorno = atualizarAcessoCatraca(key, body);
                    if(retorno.contains("ERRO")) {
                        jsonRetorno.put(STATUS_ERRO, retorno);
                    }else {
                        jsonRetorno.put(STATUS_SUCESSO, retorno);
                    }
                    out.println(jsonRetorno);
                }
                break;
                case consultarAcessosCatraca: {
                    JSONObject body = new JSONObject();
                    body.put("empresa", obterParametroInt(request, "empresa"));
                    body.put("email", obterParametroString(request, "email"));
                    body.put("cpf", obterParametroString(request, "cpf"));
                    body.put("matricula", obterParametroString(request, "matricula"));
                    String retorno = consultarAcessosCatraca(key, body);
                    if(retorno.contains("ERRO")) {
                        jsonRetorno.put(STATUS_ERRO, retorno);
                    }else {
                        jsonRetorno.put(STATUS_SUCESSO, retorno);
                    }
                    out.println(jsonRetorno);
                }
                break;
                case consultarContratoConcomitante: {
                    Integer codigoEmpresa = obterParametroInt(request, "empresa");
                    String retorno = consultarContratoConcomitante(key, codigoEmpresa);
                    jsonRetorno.put(STATUS_SUCESSO, retorno);
                    out.println(jsonRetorno);
                }
                break;
            }
        } catch (Exception e) {
            jsonRetorno.put(STATUS_ERRO, e.getMessage());
            out.println(jsonRetorno);
            out.println("ERRO:" + e.getMessage());
        }
    }

    private void findCollaboratorByAccessCode(HttpServletRequest request, JSONObject jsonRetorno, String key) throws Exception {
        String codAcesso = obterParametroString(request, "codAcesso");
        ColaboradorVO colaboradorConsultado = DaoAuxiliar.retornarAcessoControle(key).getColaboradorDao().consultarPorCodAcesso(codAcesso, 0, Uteis.NIVELMONTARDADOS_CONSULTA_WS_COM_BIOMETRIAS);
        if (colaboradorConsultado != null) {
            PessoaConsultaTO pessoaConsultaTO = new PessoaConsultaTO(colaboradorConsultado);
            jsonRetorno.put(STATUS_SUCESSO, pessoaConsultaTO.toJSON());
        } else {
            jsonRetorno.put(STATUS_ERRO, "Colaborador não encontrado!");
        }
    }

    private void findClientByAccessCode(HttpServletRequest request, JSONObject jsonRetorno, String key) throws Exception {
        String codAcesso = obterParametroString(request, "codAcesso");
        ClienteVO clienteConsultado = DaoAuxiliar.retornarAcessoControle(key).getClienteDao().consultarPorCodAcesso(codAcesso, false, Uteis.NIVELMONTARDADOS_CONSULTA_WS_COM_BIOMETRIAS);
        if (clienteConsultado != null) {
            PessoaConsultaTO pessoaConsultaTO = new PessoaConsultaTO(clienteConsultado);
            jsonRetorno.put(STATUS_SUCESSO, pessoaConsultaTO.toJSON());
        } else {
            jsonRetorno.put(STATUS_ERRO, "Cliente não encontrado!");
        }
    }

    private List<Integer> obterClientesPorMatriculaSesc(String matriculaPesquisar, Integer empresa, String key) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT codigomatricula FROM cliente \n");
        sql.append(" WHERE matriculaSesc = '").append(matriculaPesquisar).append("' \n");
        sql.append(" AND empresa = ").append(empresa);
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), DaoAuxiliar.retornarAcessoControle(key).getCon());
        List<Integer> matriculas = new ArrayList<>();
        while (rs.next()) {
            matriculas.add(rs.getInt("codigomatricula"));
        }
        return matriculas;
    }

    private String atualizarStatusSincFotoPessoa(String ctx, Integer codAutorizacao, Integer status) throws ServletException {
        try {
            Connection con = DaoAuxiliar.retornarAcessoControle(ctx).getCon();
            SuperFacadeJDBC.executarConsulta("update autorizacaoacessogrupoempresarial set statusSincronizacaoFotoPessoa = " + status + " where codigo = " + codAutorizacao, con);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServletException(ex.getMessage());
        }
        return "OK";
    }

    private List<Integer> obterClientesPorCpf(String cpf, Integer empresa, String key) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT cli.codigomatricula FROM Cliente cli \n");
        sql.append(" INNER JOIN pessoa pes ON pes.codigo = cli.pessoa \n");
        sql.append("WHERE (trim(pes.cfp) = '").append(cpf).append("' or trim(pes.cfp) = '").append(Uteis.removerMascara(cpf)).append("') \n");
        sql.append(" AND cli.empresa = ").append(empresa).append(" \n");
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), DaoAuxiliar.retornarAcessoControle(key).getCon());
        List<Integer> codigosMatriculas = new ArrayList<>();
        while (rs.next()) {
            codigosMatriculas.add(rs.getInt("codigomatricula"));
        }
        return codigosMatriculas;
    }

    private String getMensagemClientesEncontradosPorMatricula (String matriculaPesquisar, Integer empresa, String key) {
        try {
            return "Quantidade de clientes encontrados com a matrícula SESC: " + obterClientesPorMatriculaSesc(matriculaPesquisar, empresa, key).size();
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    private String obterClienteJsonPorMatriculaSesc(String matriculaSesc, Integer empresa, String key) throws Exception {
        ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT codigo FROM cliente WHERE matriculaSesc = '" + matriculaSesc + "' AND empresa = " + empresa, DaoAuxiliar.retornarAcessoControle(key).getCon());
        JSONArray jsonArray = new JSONArray();
        Connection con = DaoAuxiliar.retornarAcessoControle(key).getCon();
        Cliente clienteDao = new Cliente(con);
        Empresa empresaDao = new Empresa(con);
        while (rs.next()) {
            ClienteVO clienteVO = clienteDao.consultarPorChavePrimaria(rs.getInt("codigo"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            JSONObject jsonCliente = new JSONObject();
            jsonCliente.put("codigo", clienteVO.getCodigo());
            jsonCliente.put("situacao", clienteVO.getSituacao());
            jsonCliente.put("codigoPessoa", clienteVO.getPessoa().getCodigo());
            jsonCliente.put("nome", clienteVO.getPessoa().getNome());
            jsonCliente.put("matricula", clienteVO.getMatricula());
            jsonCliente.put("cpf", clienteVO.getPessoa().getCfp());
            jsonCliente.put("responsavelFinanceiro", clienteVO.getPessoa().getNomeMae());
            jsonCliente.put("cpfResponsavel", clienteVO.getPessoa().getCpfMae());
            String email = null;
            if (!clienteVO.getPessoa().getEmailVOs().isEmpty()) {
                email = clienteVO.getPessoa().getEmailVOs().get(0).getEmail();
            }
            jsonCliente.put("email", email);
            jsonCliente.put("sexo", clienteVO.getPessoa().getSexo());
            String telResidencial = null;
            String telCelular = null;
            if (!clienteVO.getPessoa().getTelefoneVOs().isEmpty()) {
                for (TelefoneVO tel : clienteVO.getPessoa().getTelefoneVOs()) {
                    if (tel.getTipoTelefone().equals(TipoTelefoneEnum.RESIDENCIAL.getCodigo())) {
                        telResidencial = tel.getNumero();
                    } else if (tel.getTipoTelefone().equals(TipoTelefoneEnum.CELULAR.getCodigo())) {
                        telCelular = tel.getNumero();
                    }
                }
            }
            jsonCliente.put("telResidencial", telResidencial);
            jsonCliente.put("telCelular", telCelular);
            EmpresaVO empresaVO = empresaDao.consultarPorChavePrimaria(clienteVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
            jsonCliente.put("nomeEmpresa", empresaVO.getNome());
            jsonCliente.put("dataNascimento", clienteVO.getPessoa().getDataNasc() != null ? Uteis.getData(clienteVO.getPessoa().getDataNasc(), "br") : "");
            jsonCliente.put("dataCadastro", clienteVO.getPessoa().getDataCadastro() != null ? Uteis.getData(clienteVO.getPessoa().getDataCadastro(), "br") : "");
            if (!clienteVO.getPessoa().getEnderecoVOs().isEmpty()) {
                EnderecoVO enderecoVO = clienteVO.getPessoa().getEnderecoVOs().get(0);
                jsonCliente.put("endereco", enderecoVO.getEndereco());
                jsonCliente.put("numero", enderecoVO.getNumero());
                jsonCliente.put("complemento", enderecoVO.getComplemento());
                jsonCliente.put("bairro", enderecoVO.getBairro());
                jsonCliente.put("cep", enderecoVO.getCep());
            }

            if (!clienteVO.getVinculoVOs().isEmpty()) {
                for (VinculoVO v : clienteVO.getVinculoVOs()) {
                    if (v.getTipoVinculo().equals("CO")) {
                        jsonCliente.put("consultor", v.getColaborador().getCodigo());
                    }
                }
            }
            jsonArray.put(jsonCliente);
        }
        clienteDao = null;
        empresaDao = null;
        if (jsonArray.length() > 0) {
            return jsonArray.toString();
        } else {
            throw new Exception("Nenhum cliente encontrado");
        }
    }

    private JSONObject obtemJsonRetornoAtualizacaoCodigoVitio(JSONObject jsonRetorno, String key, String codigoAfiliado, Integer idEmpresa, String telefoneColaborador) throws Exception {
        if(codigoAfiliado.split("").length != 6) {
            jsonRetorno.put(STATUS_SUCESSO, "false");
            jsonRetorno.put("mensagem", "O código Vitio deve ter obrigatóriamente 6 dígitos!");
            return jsonRetorno;
        }
        ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT c.pessoa AS codigo FROM colaborador c JOIN telefone t ON t.pessoa = c.pessoa WHERE t.numero = '" + telefoneColaborador + "' AND empresa = " + idEmpresa + " GROUP BY c.pessoa;", DaoAuxiliar.retornarAcessoControle(key).getCon());
        Integer qtdColaborador = 0;
        Integer codPessoa = 0;
        boolean maisDeUmColaboradorPorTelefone = false;
        while(rs.next()) {
            qtdColaborador++;
            if(qtdColaborador > 1) {
                maisDeUmColaboradorPorTelefone = true;
                break;
            }
            codPessoa = rs.getInt("codigo");
        }
        if(!maisDeUmColaboradorPorTelefone && codPessoa > 0) {
            DaoAuxiliar.retornarAcessoControle(key).getIntegracaoImportacaoDao().atualizarCodigoAfiliadoVitio(codPessoa, codigoAfiliado, idEmpresa);
            jsonRetorno.put(STATUS_SUCESSO, "true");
            jsonRetorno.put("mensagem", "");
        } else {
            jsonRetorno.put(STATUS_SUCESSO, "false");
            jsonRetorno.put("mensagem", "Colaborador com telefone duplicado ou não encontrado!");
        }
        return jsonRetorno;
    }

    private String obterClientePorCPF(String key, String cpf, Integer nivelMontarDados) {
        try {
            ClienteInterfaceFacade clienteInterfaceFacade = DaoAuxiliar.retornarAcessoControle(key).getClienteDao();
            ClienteVO clienteVO = clienteInterfaceFacade.consultarPorCfp(cpf, nivelMontarDados);
            return clienteVO.toJSONAPI().toString();
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    private String obterClienteAmigoFitPorCPF(String key, String cpf) {
        try {
            ClienteInterfaceFacade clienteInterfaceFacade = DaoAuxiliar.retornarAcessoControle(key).getClienteDao();
            ClienteAmigoFitJSON cliente = clienteInterfaceFacade.consultarIntegracaoAmigoFit(cpf);
            if (UteisValidacao.emptyNumber(cliente.getCodigoclienteIndicou())) {
                throw new Exception("Nenhum cliente encontrado");
            }
            ClienteAmigoFitJSON teste = cliente;
            JSONObject obj = ClienteAmigoFitJSON.toJsonConsulta(teste);
            return obj.toString();
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    private String obterClientePorIdExternoIntegracao(String key, String idExternoIntegracao, String userAgent) throws Exception {
        PessoaInterfaceFacade pessoaDao = DaoAuxiliar.retornarAcessoControle(key).getPessoaDao();
        int codigoPessoa = pessoaDao.obterPessoaPorIdExternoIntegracao(idExternoIntegracao);

        JSONObject jsonRetorno = new JSONObject();
        jsonRetorno.put("codigoPessoa", codigoPessoa);
        jsonRetorno.put("key", key);
        jsonRetorno.put("user-agent", userAgent);
        jsonRetorno.put("timevld", Calendario.getInstance(Calendario.hoje().getTime() + ((60 * 3) * 1000)).getTimeInMillis());

        return jsonRetorno.toString();
    }

    public String atualizarTemplateFacial(String key, Integer codigoPessoa, String templateBase64) throws Exception{
        PessoaInterfaceFacade pessoaDao = DaoAuxiliar.retornarAcessoControle(key).getPessoaDao();
        String retorno = pessoaDao.uploadTemplateFotoPessoa(key, codigoPessoa, templateBase64);
        SuperControle.notificarOuvintes(DadosAcessoOfflineVO.CHAVE_NOTIFICAR_ATUALIZAR_TEMPLATE_FACIAL_PESSOA + "(" + codigoPessoa + ")", PropsService.getPropertyValue(PropsService.urlNotificacaoAcesso), key);
        return retorno;
    }

    private String gravarUtilizacaoAvaliacaoFisica(final String key, final Integer codCliente, final Integer codAvaliacaoFisica, final Date dataAvaliacao) throws Exception {
        AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
        MovProdutoVO movProdutoAvalicao = acessoControle.getMovProdutoDao().consultarAvaliacaoVigente(codCliente, dataAvaliacao, Uteis.NIVELMONTARDADOS_MINIMOS);
        if (movProdutoAvalicao != null) {
            UtilizacaoAvaliacaoFisicaVO utilizacao = new UtilizacaoAvaliacaoFisicaVO();
            utilizacao.setMovProdutoVO(movProdutoAvalicao);
            utilizacao.setCodAvaliacaoFisica(codAvaliacaoFisica);
            utilizacao.setDataAvaliacaoFisica(dataAvaliacao);
            utilizacao.setPrimeiraavaliacao(acessoControle.getUtilizacaoAvaliacaoFisicaDao().findByMovproduto(movProdutoAvalicao.getCodigo()).isEmpty());
            acessoControle.getUtilizacaoAvaliacaoFisicaDao().gravar(utilizacao);
            return "ok";
        }
        return "ok-semproduto";
    }

    private String removerUtilizacaoAvaliacaoFisica(final String key, final Integer codAvaliacaoFisica) throws Exception {
        AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
        acessoControle.getUtilizacaoAvaliacaoFisicaDao().excluirPorCodAvaliacaoFisica(codAvaliacaoFisica);
        return "ok";
    }

    private String consultarSpgAvaliacaoFisica(String key, Integer empresa, String dataInicial, String dataFinal) throws Exception {
        try {
            Date dtInicial = Uteis.getDate(dataInicial, "dd/MM/yyyy");
            Date dtFinal = Uteis.getDate(dataFinal, "dd/MM/yyyy");

            SGPAvaliacaoFisicaRel rel = new SGPAvaliacaoFisicaRel(DaoAuxiliar.retornarAcessoControle(key).getCon());

            EmpresaVO empresaVO = new EmpresaVO();
            empresaVO.setCodigo(empresa);

            rel.setarParametrosConsulta(dtInicial, dtFinal, empresaVO);
            rel.validarDados();
            SGPAvaliacaoFisicaTO sgpAvaliacaoFisicaTO = rel.consultar().get(0);

            JSONObject jsonObj = new JSONObject();
            jsonObj.put("comerciarios", sgpAvaliacaoFisicaTO.getQtdeComerciario());
            jsonObj.put("dependentes", sgpAvaliacaoFisicaTO.getQtdeDependente());
            jsonObj.put("usuarios", sgpAvaliacaoFisicaTO.getQtdeUsuario());
            jsonObj.put("total", sgpAvaliacaoFisicaTO.getTotalCategorias());

            JSONObject jsonRetornar = new JSONObject();
            jsonRetornar.put("avaliacoes_fisicas", jsonObj);
            return jsonRetornar.toString();
        } catch (Exception e) {
            throw new ConsistirException(e.getMessage());
        }
    }

    private String consultarSgpModalidadesComTurma(String key, Integer empresa, String dataInicial, String dataFinal) throws Exception {
        try {
            Connection con = DaoAuxiliar.retornarAcessoControle(key).getCon();

            Date dtInicial = Uteis.getDate(dataInicial, "dd/MM/yyyy");
            Date dtFinal = Uteis.getDate(dataFinal, "dd/MM/yyyy");

            SGPModalidadeComTurmaRel rel = new SGPModalidadeComTurmaRel(con);

            EmpresaVO empresaVO = new EmpresaVO();
            empresaVO.setCodigo(empresa);

            Modalidade modalidadeDao = new Modalidade(con);
            List<ModalidadeVO> modalidades = modalidadeDao.consultarTodasModalidades(empresa, true, true);

            rel.setarParametrosConsulta(dtInicial, dtFinal, empresaVO, modalidades);
            rel.setModalidades(modalidades);
            rel.validarDados();
            List<SGPModalidadeComTurmaTO> sgpModalidadeComTurmaTO = rel.consultar();

            JSONArray jsonArray = new JSONArray();
            for (SGPModalidadeComTurmaTO sgp: sgpModalidadeComTurmaTO) {
                if (sgp.getNomeModalidade().equals("TOTAIS")) {
                    continue;
                }
                JSONObject jsonObj = new JSONObject();
                jsonObj.put("modalidade", sgp.getNomeModalidade());
                jsonObj.put("comerciarios", sgp.getQtdeComerciario());
                jsonObj.put("dependentes", sgp.getQtdeDependente());
                jsonObj.put("usuarios", sgp.getQtdeUsuario());
                jsonObj.put("total", sgp.getTotalCategorias());
                jsonObj.put("evasoes", sgp.getListaCanceladosDesistentes().size());
                jsonObj.put("turmas", sgp.getQtdTurmasCriadasPeriodo());
                jsonObj.put("horasAula", sgp.getQtdAulasPeriodo());
                jsonObj.put("frequencia_registradas", sgp.getFrequenciaPeriodo());
                jsonObj.put("frequencia_possiveis", sgp.getFrequenciaPossivel());
                jsonObj.put("vagas_disponiveis", sgp.getTotalVagasDisponiveis());
                jsonArray.put(jsonObj);
            }
            JSONObject jsonRetornar = new JSONObject();
            jsonRetornar.put("modalidades_com_turmas", jsonArray);
            return jsonRetornar.toString();
        } catch (Exception e) {
            throw new ConsistirException(e.getMessage());
        }
    }

    public String atualizarFotoClienteCpfMatriculaSesc(String key, String cpf, String foto, String matricula) {
        try {
            String fotoKey;
            JSONObject json = new JSONObject();
            JSONArray jsons = new JSONArray();
            List<Integer> codigos = DaoAuxiliar.retornarAcessoControle(key).getIntegracaoImportacaoDao().codigoPessoaCpfMatriculaSesc(cpf, matricula);

            for (Integer codigo : codigos) {
                fotoKey = DaoAuxiliar.retornarAcessoControle(key).getContratoAssinaturaDigitalService().alterarFotoAluno(
                        key, null, codigo, foto, 0, null);

                jsons.put("Codigo: " + codigo);
                jsons.put("Foto: " + fotoKey);
                json.put("Lista", jsons);
            }

            return json.toString();
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }

    public String limparSincronizadoEmPorCodAcesso(String key, String codAcesso) throws Exception {
        AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
        acessoControle.getClienteDao().removerSincronizadoRedeEmpresa(codAcesso);
        acessoControle.getColaboradorDao().removerSincronizadoRedeEmpresa(codAcesso);
        return "ok";
    }

    private String consultarSgpModalidadesSemTurma(String key, Integer empresa, String dataInicial, String dataFinal) throws Exception {
        try {
            Connection con = DaoAuxiliar.retornarAcessoControle(key).getCon();

            Date dtInicial = Uteis.getDate(dataInicial, "dd/MM/yyyy");
            Date dtFinal = Uteis.getDate(dataFinal, "dd/MM/yyyy");

            SGPModalidadeSemTurmaRel rel = new SGPModalidadeSemTurmaRel(con);

            EmpresaVO empresaVO = new EmpresaVO();
            empresaVO.setCodigo(empresa);

            Modalidade modalidadeDao = new Modalidade(con);
            List<ModalidadeVO> modalidades = modalidadeDao.consultarModalidadeAtivaSemTurma(empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            rel.setarParametrosConsulta(dtInicial, dtFinal, empresaVO, modalidades);
            rel.setModalidades(modalidades);
            rel.validarDados();
            List<SGPModalidadeSemTurmaTO> sgpModalidadeSemTurmaTO = rel.consultar();

            JSONArray jsonArray = new JSONArray();
            for (SGPModalidadeSemTurmaTO sgp: sgpModalidadeSemTurmaTO) {
                if (sgp.getNomeModalidade().equals("TOTAIS")) {
                    continue;
                }
                JSONObject jsonObj = new JSONObject();
                jsonObj.put("modalidade", sgp.getNomeModalidade());
                jsonObj.put("comerciarios", sgp.getQtdeComerciario());
                jsonObj.put("dependentes", sgp.getQtdeDependente());
                jsonObj.put("usuarios", sgp.getQtdeUsuario());
                jsonObj.put("total", sgp.getTotalCategorias());
                jsonObj.put("evasoes", sgp.getListaCanceladosDesistentes().size());
                jsonObj.put("dias", sgp.getQtdDias());
                jsonObj.put("frequencia_registradas", sgp.getFrequenciaPeriodo());
                jsonObj.put("frequencia_possiveis", sgp.getFrequenciaPossivel());
                jsonObj.put("vagas_disponiveis", sgp.getVagasDisponiveis());
                jsonArray.put(jsonObj);
            }
            JSONObject jsonRetornar = new JSONObject();
            jsonRetornar.put("modalidades_sem_turmas", jsonArray);
            return jsonRetornar.toString();
        } catch (Exception e) {
            throw new ConsistirException(e.getMessage());
        }
    }

    private LogIntegracoesVO inserirLogIntegracao(String json, String operacao, String key) {
        LogIntegracoesVO logIntegracoesVO = new LogIntegracoesVO();
        try {
            logIntegracoesVO.setDadosRecebidos(json);
            logIntegracoesVO.setDataLancamento(Calendario.hoje());
            logIntegracoesVO.setServico("IntegracaoCadastro."+operacao);
            DaoAuxiliar.retornarAcessoControle(key).getLogintegracoesDao().incluir(logIntegracoesVO);

        } catch (Exception e){
            Uteis.logar("Erro ao inserir log integracao  "+operacao +": "+ e.getMessage() + " # Dados recebido: "+json);
        }
        return logIntegracoesVO;
    }

    private void alterarLogIntegracao(LogIntegracoesVO logIntegracoesVO, String resultado ,String key) {
        try {
            logIntegracoesVO.setResultado(resultado);
            DaoAuxiliar.retornarAcessoControle(key).getLogintegracoesDao().alterar(logIntegracoesVO);
        } catch (Exception e){
            Uteis.logar("Erro ao alterar log integracao  "+logIntegracoesVO.getCodigo() +": "+ e.getMessage());
        }
    }

    public String consultarContratosRecorrenciaTotemRenovar(final String key, int cliente) {

        try {
            Conexao.guardarConexaoForJ2SE(key, DaoAuxiliar.retornarAcessoControle(key).getContratoDao().getCon());
            ClienteVO clienteVO = DaoAuxiliar.retornarAcessoControle(key).getClienteDao().consultarPorCodigo(cliente, false, Uteis.NIVELMONTARDADOS_METAAGENDAMENTODETALHADA);

            List list = DaoAuxiliar.retornarAcessoControle(key).getContratoDao().consultarContratosRecorrenciaRenovar(clienteVO.getPessoa().getCodigo(), clienteVO.getEmpresa().getCarenciaRenovacao(), true, Uteis.NIVELMONTARDADOS_MINIMOS);

            JSONArray contratos = new JSONArray();
            for (Object obj : list) {
                ContratoVO contrato = (ContratoVO) obj;
                contrato.setPlano(DaoAuxiliar.retornarAcessoControle(key).getPlanoDao().consultarPorChavePrimaria(contrato.getPlano().getCodigo(), Uteis.NIVELMONTARDADOS_CONSULTA_WS));
                contrato.setContratoDuracao(new ContratoDuracaoVO());
                contrato.getContratoDuracao().setNumeroMeses(DaoAuxiliar.retornarAcessoControle(key).getContratoDuracaoDao().consultarDuracaoMeses(contrato.getCodigo()));
                contrato.setContratoModalidadeVOs(DaoAuxiliar.retornarAcessoControle(key).getContratoModalidadeDao().consultarContratoModalidades(contrato.getCodigo(), false, Uteis.NIVELMONTARDADOS_ROBO));
                contrato.setContratoCondicaoPagamento(DaoAuxiliar.retornarAcessoControle(key).getContratoCondicaoPagamentoDao().consultarContratoCondicaoPagamentos(contrato.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
                contrato.setContratoHorario(DaoAuxiliar.retornarAcessoControle(key).getContratoHorarioDao().consultarContratoHorarios(contrato.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));

                String situacaoSub = "";
                if (contrato.getSituacao().equals("AT")) {
                    situacaoSub = DaoAuxiliar.retornarAcessoControle(key).getZwFacade().obterSituacaoClienteAtivo(contrato, clienteVO);

                }
                if (contrato.getSituacao().equals("TR")) {
                    situacaoSub = DaoAuxiliar.retornarAcessoControle(key).getZwFacade().obterSituacaoClienteTrancado(contrato, clienteVO);

                }
                if (contrato.getSituacao().equals("IN")) {
                    situacaoSub = DaoAuxiliar.retornarAcessoControle(key).getZwFacade().obterSituacaoClienteInativo(contrato, clienteVO);
                }
                if (contrato.getSituacao().equals("CA")) {
                    situacaoSub = "CA";
                }
                contrato.setSituacaoSubordinada(situacaoSub);
                ContratoRecorrencia contratoRecorrenciaDAO = new ContratoRecorrencia(DaoAuxiliar.retornarAcessoControle(key).getCon());
                ContratoRecorrenciaVO contratoRecorrenciaVO = contratoRecorrenciaDAO.consultarPorContrato(contrato.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                contratoRecorrenciaDAO = null;
                if (contratoRecorrenciaVO == null) {
                    contratoRecorrenciaVO = new ContratoRecorrenciaVO();
                }
                contrato.setContratoRecorrenciaVO(contratoRecorrenciaVO);
                contrato.verificarQualBotaoReferenteASituacaoContratoSeraApresentado(clienteVO, list);
                JSONObject contratoObj = new JSONObject(contrato.toWS(false));
                contratos.put(contratoObj);
            }

            return contratos.toString();
        } catch (Exception ex) {
            return ex.getMessage();
        }
    }


    public String consultarClienteGymnamic(final String key, String cpf, String email, Integer empresa) throws Exception {

        List<ConsultaClienteGymnamic> clientes = DaoAuxiliar.retornarAcessoControle(key).getClienteDao().consultarClienteEmailOuCPFGymnamic(email, cpf, empresa);
        if (UteisValidacao.emptyList(clientes)){
            throw new Exception("Nenhum cliente encontrado");
        }
        JSONArray jsonArray = new JSONArray();
        for (ConsultaClienteGymnamic consultaClienteGymnamic : clientes) {
            jsonArray.put(new JSONObject(consultaClienteGymnamic));
        }

        return jsonArray.toString();
    }

    public String atualizarAcessoCatraca(String chave, JSONObject requestBody) throws Exception {
        try {
            int empresa = requestBody.optInt("empresa");
            String cpf = requestBody.optString("cpf");
            int matricula =  requestBody.optInt("matricula");
            String email = requestBody.optString("email");

            String aviso = requestBody.optString("aviso");
            String mensagem = requestBody.optString("mensagem");
            boolean bloqueado = requestBody.optBoolean("bloqueado");

            ClienteInterfaceFacade clienteDao = DaoAuxiliar.retornarAcessoControle(chave).getClienteDao();
            ClienteMensagemInterfaceFacade mensagemDao = DaoAuxiliar.retornarAcessoControle(chave).getClienteMsgDao();

            List<ClienteVO> clientes = new ArrayList();
            if(!email.isEmpty()) {
                ClienteVO cliente = clienteDao.consultarPorEmail(email, empresa, Uteis.NIVELMONTARDADOS_MINIMOS);
                if(cliente != null) {
                    clientes.add(cliente);
                }
            }

            if(clientes.isEmpty() && !cpf.isEmpty()) {
                clientes = clienteDao.consultarPorCPF(cpf, empresa, Uteis.NIVELMONTARDADOS_MINIMOS);
            }

            if(clientes.isEmpty() && matricula > 0) {
                clientes = clienteDao.consultarPorMatricula(String.valueOf(matricula), empresa, false, Uteis.NIVELMONTARDADOS_MINIMOS);
            }

            if (clientes.isEmpty()){
                throw new Exception("Nenhum cliente encontrado");
            }

            for(ClienteVO cliente : clientes) {
                ClienteMensagemVO clienteMensagemVO = mensagemDao.consultarPorCodigoTipoMensagemECliente(
                        cliente.getCodigo(), TiposMensagensEnum.AVISO_CONSULTOR.getSigla(), false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA
                );

                if (clienteMensagemVO != null && !clienteMensagemVO.getCodigo().equals(0)) {
                    mensagemDao.excluir(clienteMensagemVO);
                }

                if (bloqueado) {
                    if (!aviso.isEmpty()) {
                        clienteMensagemVO = new ClienteMensagemVO();
                        clienteMensagemVO.setMensagem(aviso);
                        clienteMensagemVO.setCliente(cliente);
                        clienteMensagemVO.setTipomensagem(TiposMensagensEnum.AVISO_CONSULTOR);
                        clienteMensagemVO.setUsuario(new UsuarioVO());
                        clienteMensagemVO.getUsuario().setCodigo(1);
                        mensagemDao.incluir(clienteMensagemVO);
                    }

                    mensagemDao.lancarBloqueioCatraca(mensagem.isEmpty() ? "Procure a recepção." : mensagem, cliente.getPessoa().getCodigo(), new UsuarioVO());
                } else {
                    mensagemDao.lancarDesbloqueioCatraca(cliente.getPessoa().getCodigo());
                }
            }

            JSONObject json = new JSONObject();
            json.put("mensagem", "Acesso do cliente alterado com sucesso");
            return json.toString();

        } catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
            return "ERRO:" + ex.getMessage();
        }
    }

    public String consultarAcessosCatraca(String chave, JSONObject requestBody) throws Exception {
        try {
            int empresa = requestBody.optInt("empresa");
            String cpf = requestBody.optString("cpf");
            int matricula =  requestBody.optInt("matricula");
            String email = requestBody.optString("email");

            ClienteInterfaceFacade clienteDao = DaoAuxiliar.retornarAcessoControle(chave).getClienteDao();

            List<ClienteVO> clientes = new ArrayList();
            if(!email.isEmpty()) {
                ClienteVO cliente = clienteDao.consultarPorEmail(email, empresa, Uteis.NIVELMONTARDADOS_MINIMOS);
                if(cliente != null) {
                    clientes.add(cliente);
                }
            }

            if(clientes.isEmpty() && !cpf.isEmpty()) {
                clientes = clienteDao.consultarPorCPF(cpf, empresa, Uteis.NIVELMONTARDADOS_MINIMOS);
            }

            if(clientes.isEmpty() && matricula > 0) {
                clientes = clienteDao.consultarPorMatricula(String.valueOf(matricula), empresa, false, Uteis.NIVELMONTARDADOS_MINIMOS);
            }

            if (clientes.isEmpty()){
                return "ERRO: Nenhum cliente encontrado";
            }

            PeriodoAcessoClienteInterfaceFacade periodoAcessoDao = DaoAuxiliar.retornarAcessoControle(chave).getPeriodoAcessoDao();
            AcessoClienteInterfaceFacade acessoClienteDao = DaoAuxiliar.retornarAcessoControle(chave).getAcessoClienteDao();

            JSONArray alunos = new JSONArray();

            for(ClienteVO cliente : clientes) {
                JSONObject aluno = new JSONObject();

                aluno.put("matricula", cliente.getMatricula());
                aluno.put("nome", cliente.getPessoa().getNome());
                aluno.put("email", cliente.getPessoa().getEmail());
                aluno.put("cpf", cliente.getPessoa().getCfp());

                JSONArray contratos = new JSONArray();
                List<PeriodoAcessoClienteVO> listaDeContratos = periodoAcessoDao.consultarPorPessoa(cliente.getPessoa().getCodigo(), true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                for(PeriodoAcessoClienteVO l : listaDeContratos) {
                    JSONObject contrato = new JSONObject();
                    contrato.put("inicio", l.getDataInicioAcesso());
                    contrato.put("fim", l.getDataFinalAcesso());
                    contrato.put("gympass", l.getTokenGymPass());
                    contrato.put("gogood", l.getTokenGoGood());
                    contratos.put(contrato);
                }
                aluno.put("contratos", contratos);

                JSONArray acessos = new JSONArray();
                List<AcessoClienteVO> listaDeAcessos = acessoClienteDao.consultarTodosAcessos(cliente, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                for(AcessoClienteVO l : listaDeAcessos) {
                    JSONObject acesso = new JSONObject();
                    acesso.put("inicio", l.getDataHoraEntrada());
                    acesso.put("fim", l.getDataHoraSaida());
                    acesso.put("descricao", l.getLocalAcesso().getDescricao());
                    acessos.put(acesso);
                }
                aluno.put("acessos", acessos);

                alunos.put(aluno);
            }

            return alunos.toString();
        } catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
            return "ERRO:" + ex.getMessage();
        }
    }

    private String consultarContratoConcomitante(String key, Integer codigoEmpresa) throws Exception {
        try {
            Connection con = DaoAuxiliar.retornarAcessoControle(key).getCon();
            Empresa empresaDao = new Empresa(con);
            EmpresaVO empresaVO = empresaDao.consultarPorChavePrimaria(codigoEmpresa, Uteis.NIVELMONTARDADOS_CONSULTA_WS);

            JSONObject jsonEmpresa = new JSONObject();
            jsonEmpresa.put("codigo", empresaVO.getCodigo());
            jsonEmpresa.put("nome", empresaVO.getNome());
            jsonEmpresa.put("permiteContratosConcomitante", empresaVO.isPermiteContratosConcomintante());

            return jsonEmpresa.toString();
        } catch (Exception e) {
            throw new Exception("Erro ao consultar empresa: " + e.getMessage());
        }
    }
}

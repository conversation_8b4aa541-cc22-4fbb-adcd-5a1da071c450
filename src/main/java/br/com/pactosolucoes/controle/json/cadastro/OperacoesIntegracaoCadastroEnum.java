package br.com.pactosolucoes.controle.json.cadastro;

public enum OperacoesIntegracaoCadastroEnum {

    obterClientePorCPF,
    gravarUtilizacaoAvaliacaoFisica,
    removerUtilizacaoAvaliacaoFisica,
    obterClienteAmigoFitPorCPF,
    consultarParcelas,
    consultarParcelasVencidas,
    consultarParcelasVencidasOuUltimaRenovacao,
    obterPessoaPorIdExternoIntegracao,
    atualizarTemplateFacial,
    sgpAvaliacaoFisica,
    sgpModalidadesComTurma,
    sgpModalidadesSemTurma,
    consultarClientePelaMatriculaSESC,
    atualizaAlunoPelaMatriculaSESC,
    atualizaAlunoPeloCPF,
    atualizaCodigoAfiliadoVitio,
    OBTER_CLIENTE_CODACESSO,
    OBTER_COLABORADOR_CODACESSO,
    atualizarFotoPerfilAlunoCPFMatricula,
    LIMPAR_SINCRONIZADO_EM_POR_CODACESSO,
    RESET_CACHE_MAP_BANNERS,
    STATUS_CACHE_MAP_BANNERS,
    RESET_CACHE_MAP_CLUBE_DE_BENEFICIOS,
    STATUS_CACHE_MAP_CLUBE_DE_BENEFICIOS,
    consultarContratosRecorrenciaTotemRenovar,
    consultarClienteGymnamic,
    ATUALIZAR_STATUS_SINC_FOTO_PESSOA_AUTORIZACAO_ACESSO,
    atualizarAcessoCatraca,
    consultarAcessosCatraca,
    consultarContratoConcomitante,
    ;


    public static OperacoesIntegracaoCadastroEnum obterOperacao(String o) {
        if (o == null) {
            return null;
        }
        for (OperacoesIntegracaoCadastroEnum op : values()) {
            if (op.name().equalsIgnoreCase(o)) {
                return op;
            }
        }
        return null;
    }
}

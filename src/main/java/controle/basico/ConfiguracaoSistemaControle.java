package controle.basico;

import acesso.webservice.DaoAuxiliar;
import br.com.pactosolucoes.atualizadb.negocio.PovoadorDadosBancoInicial;
import br.com.pactosolucoes.atualizadb.processo.*;
import br.com.pactosolucoes.atualizadb.processo.ajustebd.AdicionarInicioContrato;
import br.com.pactosolucoes.atualizadb.processo.ajustebd.AdicionarModalidadeEmPlano;
import br.com.pactosolucoes.atualizadb.processo.ajustebd.AlterarHorarioPlano;
import br.com.pactosolucoes.atualizadb.processo.ajustebd.MarcarEmailCorrespondencia;
import br.com.pactosolucoes.atualizadb.processo.ajustebd.RetirarModalidadeEmPlano;
import br.com.pactosolucoes.atualizadb.processo.geolocalizacao.GoogleApiGeocodeService;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoObjetosCobrarEnum;
import br.com.pactosolucoes.ce.comuns.to.PessoaTO;
import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.controle.json.nuvemshop.OperacoesIntegracaoNuvemshopEnum;
import br.com.pactosolucoes.enumeradores.*;
import controle.arquitetura.MenuControle;
import controle.arquitetura.RoboControle;
import controle.arquitetura.SuperControle;
import controle.arquitetura.exceptions.ServiceException;
import controle.arquitetura.security.AutorizacaoFuncionalidadeControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeListener;
import controle.arquitetura.security.LoginControle;
import controle.basico.clube.MensagemGenericaControle;
import controle.crm.AberturaMetaControle;
import controle.financeiro.EstornoMovProdutoControle;
import controle.financeiro.EstornoReciboControle;
import edu.emory.mathcs.backport.java.util.Arrays;
import importador.outros.ProcessoAlterarDataFinalContratoCancelado;
import importador.outros.ProcessoAlterarParcelasRecibo;
import importador.outros.ProcessoAtualizarDependenciaEngenharia;
import negocio.comuns.CadastroDinamicoItemVO;
import negocio.comuns.acesso.AcessoClienteVO;
import negocio.comuns.acesso.ColetorVO;
import negocio.comuns.acesso.LocalAcessoVO;
import negocio.comuns.arquitetura.LogProcessoSistemaVO;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.RoboVO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.*;
import negocio.comuns.basico.enumerador.StatusMatriculaSesiCeEnum;
import negocio.comuns.basico.enumerador.TipoServicoEnum;
import negocio.comuns.basico.enumerador.TiposMensagensEnum;
import negocio.comuns.basico.enumerador.negocio.comuns.basico.enumerador.cadastrodinamico.CadastroDinamicoColaboradorEnum;
import negocio.comuns.contrato.*;
import negocio.comuns.crm.FecharMetaDetalhadoVO;
import negocio.comuns.feed.PerfilUsuarioEnum;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.*;
import negocio.comuns.notaFiscal.NotaFiscalFamiliaVO;
import negocio.comuns.notaFiscal.NotaFiscalVO;
import negocio.comuns.notaFiscal.OperacaoNotaFiscalEnum;
import negocio.comuns.plano.HorarioVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisServletExportData;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.arquitetura.Robo;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.*;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.financeiro.*;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.oamd.RedeEmpresaVO;
import negocio.oamd.dto.BackupClienteDTO;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.util.EntityUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.controle.sad.SituacaoClienteSinteticoDWControle;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoDWVO;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;
import servicos.AdministrativoRunner;
import servicos.adm.CreditoDCCService;
import servicos.configuracoes.ThreadProcessarPendenciaAluno;
import servicos.http.MetodoHttpEnum;
import servicos.discovery.ClientDiscoveryDataDTO;
import servicos.discovery.DiscoveryMsService;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.impl.apf.RecorrenciaService;
import servicos.impl.boleto.caixa.CaixaService;
import servicos.impl.boleto.pjbank.PJBankService;
import servicos.impl.dcc.base.*;
import servicos.impl.vindi.SincronizarDadosVindiService;
import servicos.integracao.TreinoWSConsumer;
import servicos.integracao.impl.conciliadora.ConciliadoraServiceImpl;
import servicos.integracao.pjbank.api.PJBankClient;
import servicos.operacoes.EstornoContratoAutomaticoService;
import servicos.pix.*;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;
import test.simulacao.ProcessoPropagarContratoHtml;

import javax.faces.event.ActionEvent;
import javax.faces.event.ValueChangeEvent;
import javax.faces.model.SelectItem;
import java.io.File;
import java.net.URL;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.lang.Integer.parseInt;
import static java.util.Objects.isNull;
import static servicos.integracao.TreinoWSConsumer.empresaTemIntegracaoTW;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das
 * páginas configuracaoSistemaForm.jsp configuracaoSistemaCons.jsp) com as
 * funcionalidades da classe
 * <code>ConfiguracaoSistema</code>. Implemtação da camada controle (Backing
 * Bean).
 *
 * @see SuperControle
 * @see ConfiguracaoSistema
 * @see ConfiguracaoSistemaVO
 */
public class ConfiguracaoSistemaControle extends SuperControle {

    private ConfiguracaoSistemaVO configuracaoSistemaVO;
    protected List listaSelectItemQuestionarioPrimeiraVisita;
    protected List listaSelectItemQuestionarioRetorno;
    protected List listaSelectItemQuestionarioReMatricula;
    protected List listaSelectItemQuestionarioPrimeiraCompra;
    protected List listaSelectItemQuestionarioRetornoCompra;
    private List listaSelectItemLocalAcessoChamada;
    private List listaSelectItemColetorChamada;
    private VerificarRecibosPagamentosSemVinculo verificacao;
    private OperacaoColetivaVO operacaoColetivaVO;
    /**
     * Interface
     * <code>ConfiguracaoSistemaInterfaceFacade</code> responsável pela
     * interconexão da camada de controle com a camada de negócio. Criando uma
     * independência da camada de controle com relação a tenologia de
     * persistência dos dados (DesignPatter: Façade).
     */
    private Boolean menuHistoricoVinculo;
    private Boolean menuNovo;
    private Boolean menuBasico;
    private Boolean menuQuestionario;
    private Boolean menuQuestionarioSessao;
    private Boolean menuCamposObrigatorio;
    private Boolean menuCamposObrigatorioApresentarPendentes;
    private Boolean menuCamposObrigatorioApresentarColaborador;
    private Boolean menuCamposObrigatorioApresentarVisitantes;
    private Boolean menuAcesso;
    private Boolean menuRobo;
    private Boolean menuContrato;
    private String textoExplicativoRobo;
    private boolean menuOutros;
    private boolean menuManutencaoBI;
    private boolean menuManutencao;
    private boolean menuEcf;
    private boolean menuRecorrencia;
    private boolean menuMovProdutoParcelas;
    private boolean menuAjustesGerais;
    private boolean menuPactoPay = false;
    private boolean menuConciliacao = false;
    private boolean importacao;
    private boolean menuProcessos = false;
    private boolean updateClienteSituacao;
    private boolean migradorMOVProdutoModalidade;
    private boolean ajustarLinhaDoTempoDeContratos;
    private boolean ajustarDatasRenovacaoImportacao;
    private boolean menuExclusaoMensagensBVsPendentes;
    private EmpresaVO empresaVO;
    private Integer totalContratosAProcessar = 0;
    private Integer totalContratosProcessados;
    private double totalAProcessarMovProdutoParcela = 0;
    private double totalAProcessarParcelas = 0;
    private double totalProcessadosParcelas = 0;
    private double totalProcessadosMovProdutoParcela = 0;
    private double totalAProcessarHistoricoVinculo = 0;
    private double totalProcessadosHistoricoVinculo = 0;
    private double totalAProcessarVinculoProfessor = 0;
    private double totalProcessadosVinculoProfessor = 0;
    private String emailResponsavel;
    private ColaboradorVO colaboradorBVsPendentes = new ColaboradorVO();
    private Date dataInicialBVsPendentes = Calendario.hoje();
    private Date dataProcessoIntgF360 = Calendario.hoje();
    private Date dtInicialPontucaoRetroativa = Calendario.somarDias(Calendario.hoje(), -1);
    private Date dataFinalBVsPendentes = Calendario.hoje();
    private String msgExclusaoMsgBVsPendentes;
    private String onCompleteRestaurar = "";
    private boolean restaurarAtivo = true;
    private double totalAProcessarParcelasCC = 0;
    private double totalProcessadosParcelasCC = 0;
    private int contrato = 0;
    private String ddd;
    private boolean abrirModal = true;
    private List<String> alterados = new ArrayList<>();
    private int empresaBV = 0;

    private int fasecrm = 0;
    private List<SelectItem> empresasBV = new ArrayList<>();

    private List<SelectItem> fases = new ArrayList<>();
    private boolean deletarProduto = false;
    private Integer produtoSubstituir = 0;
    private Integer produtoSubstituido = 0;
    private List<CadastroDinamicoItemVO> listaCamposColaboradorDinamico;
    private boolean selecionarTodosApresentar = false;
    private boolean selecionarTodosObrigatorio = false;
    private CadastroDinamicoItemVO cadastroDinamicoItemVO;
    private ManutencaoAjusteGeralTO manutencaoAjusteGeralTO;
    private List<LogAjusteGeralVO> logAjusteGeralVOList;
    private List<ObjetoGenerico> clientesDuplicados;
    private String obsClientesDuplicados;

    private List<SelectItem> listaSelectItemModalidades;
    private ModalidadeVO modalidadeVO;
    private ModalidadeVO modalidadeVORetirar;
    private List<SelectItem> listaSelectItemPlanosAdicionarModalidadeAoPlano;
    private List<SelectItem> listaSelectItemHorariosAdicionarModalidadeAoPlano;
    private List<SelectItem> listaSelectItemPlanosRetirarModalidadeAoPlano;
    private EmpresaVO empresaAdicionarModalidadeAoPlano;
    private EmpresaVO empresaRetirarModalidadeAoPlano;
    private List<SelectItem> listaSelectItemPlanosAdicionarDiasContratoDePlano;
    private EmpresaVO empresaAdicionarDiasContratoDePlano;
    private PlanoVO planoAdicionarModalidadeAoPlano;
    private PlanoVO planoRetirarModalidadeAoPlano;
    private PlanoVO planoAdicionarDiasContratoDePlano;
    private List<ContratoVO> contratosAdicionarModalidade;
    private List<ContratoVO> contratosRetirarModalidade;
    private Date dataInicioContrato;
    private int qtdDiasAdicionarInicioContrato = 0;
    private Boolean alterarParcelasContrato = true;
    private List<ContratoVO> contratosAdicionarDiasInicio;
    private boolean terminouProcessoBDPadraoImportacao = true;
    private EmpresaVO empresaSelecionadoSenhaAcessoCpf;
    private EmpresaVO empresaSelecionadoSenhaAcessoCpfColaborador;
    List<PessoaTO> clientesNaoProcessarSenhaAcessoCPF = new ArrayList<>();
    List<PessoaTO> clientesProcessarSenhaAcessoCPF = new ArrayList<>();
    private String emailBackup;
    private List<SelectItem> backupExcel = new ArrayList<>();
    private String backupEscolha;

    private MarcarEmailCorrespondencia marcarEmailCorrespondencia;
    private Boolean processarClienteAcessoCpf = null;
    private Boolean processarClienteAcessoMatricula = null;

    private ManutencaoAjusteGeralTO manutencaoAjusteGeralTOSenhaAcessoCliente;

    private ManutencaoAjusteGeralTO manutencaoAjusteGeralTOSenhaAcessoColaborador;

    private List<AcessoClienteVO> listaAcessoCliente = new ArrayList<>();

    private EmpresaVO empresaAcesso;
    private int quantidadeEmpresas;
    private ConfiguracaoSistemaVO configuracaoSistemaVOClone;

    private String[] displayIdentificadorFront;

    List<PessoaTO> clientesNaoProcessarSenhaAcessoMatricula = new ArrayList<>();
    List<PessoaTO> clientesProcessarSenhaAcessoMatricula = new ArrayList<>();
    private String enderecoModal = "";
    private boolean pollHistoricoVinculo = false;
    private boolean pollMovProdutoParcelas = false;
    private boolean disableBtnPontuacaoRetroativa = false;
    private List<SelectItem> listaSelectItemPlanosOperacaoColetiva;
    private List<OperacaoColetivaVO> listaOperacoesColetivas;
    private EmpresaVO empresaAdicionarHorarioAoPlano;
    private EmpresaVO empresaAdicionarDataPlano;
    private PlanoVO planoAdicionarHorarioAoPlano;
    private PlanoVO planoAdicionarDataAoPlano;
    private List<SelectItem> listaSelectItemHorarios;
    private List<SelectItem> listaSelectItemHorariosAnterior;
    private List<SelectItem> listaSelectItemPlanosAdicionarHorarioAoPlano;
    private List<SelectItem> listaSelectItemPlanosAdicionarDataAoPlano;
    private HorarioVO horarioVO;
    private HorarioVO horarioVOAnterior;
    private List<ContratoVO> contratosAdicionarHorario;
    private AtomicInteger qtdNotasCorrigidas = new AtomicInteger(0);
    private AtomicInteger qtdNotasEncontradas = new AtomicInteger(0);
    private AtomicInteger qtdNotasExcluida = new AtomicInteger(0);
    private String empresasTentativaUnicaDeCobrancaAtivada;
    private Date mesMovParcelaResultadoCobranca;
    private List<SelectItem> listaSelectItemEmpresa;
    private List<SelectItem> listaSelectItemConvenioCobranca;
    private List<SelectItem> listaSelectItemConvenioCobrancaPjBank;
    private List<SelectItem> listaSelectItemConvenioCobrancaBBOnline;
    private List<SelectItem> listaSelectItemPlano;
    private List<SelectItem> listaSelectItemModalidade;
    private List<ObjetoGenerico> transacoesDuplicadas;
    private String obsTransacoesDuplicadas;

    private String obsBoletosStatusErrado;

    private List<MovContaRateioVO> listaRateioComValorDivergenteContaAPagar = new ArrayList<>();
    private List<MovContaVO> listaContaAPagarComValorDivergenteRateio = new ArrayList<>();
    private List<PixVO> listaPixProcessados;
    private boolean integranteRedeEmpresa = false;
    private String onComplete;
    private List<ConvenioCobrancaVO> listaConveniosCobranca;
    private List<EmpresaVO> listaEmpresasCadastradas;
    private boolean exibirReplicarPlanoRedeEmpresa = false;

    private boolean manutencaoAjusteCliente = false;

    List<BoletoVO> listaBoletos = new ArrayList<>();
    private boolean exibirBtnCorrigir;

    private boolean manutencaoFasecrm = false;
    private String msgRetornoReprocessarIntegracaoF360 = "";
    private Integer horarioContratoAdicionarModalidade = 0;

    private Boolean novaTelaConfiguracao;
    private boolean manutencaoExcluirSenhaAcessoCatraca = false;
    private boolean excluirSenhaAcessoCatracaColaboradores = false;
    private boolean excluirSenhaAcessoCatracaAlunos = false;
    private String infoCidadeMescladaMantida;
    private String infoCidadeMescladaExcluida;
    private boolean apresentarCorrigirHistoricoVinculo = false;

    private String idVendaNuvemShop;
    private String msgRetornoReprocessarIntegracaoNuvemShop;

    public ConfiguracaoSistemaControle() throws Exception {
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
        setConfiguracaoSistemaVO(getFacade().getConfiguracaoSistema().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_TODOS));
        identificacaoPessoalInternacional();
        povoarTabelasCadastroDinamico();
        montarListaEmpresas();
        setMensagemID("msg_entre_prmconsulta");
        this.terminouProcessoBDPadraoImportacao = true;
        this.backupExcel.add(new SelectItem("1", "ZW"));
        this.backupExcel.add(new SelectItem("2", "TW"));

        try {
            this.apresentarCorrigirHistoricoVinculo = ProcessoCorrecaoVinculosE21356.existeRegistrosComProblema(Conexao.getFromSession());
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        avaliarEmpresaIntegranteRedeEmpresa();
        avaliarExibirReplicarRecursosRedeEmpresa();
        setErro(false);
        setSucesso(false);
        setMsgAlert("");
    }

    public void abrirNovaConfiguracao() {
        try {
            limparMsg();
            setMsgAlert("");
            MenuControle menuControle = JSFUtilities.getControlador(MenuControle.class);
            menuControle.setUrlGoBackRedirect(null);
            getFacade().getUsuario().gravarRecurso(TipoInfoMigracaoEnum.CONFIGURACOES, getUsuarioLogado().getCodigo(), "true", getUsuarioLogado());
            notificarRecursoEmpresa(RecursoSistema.PADRAO_NOVA_CONFIGURACOES);
            LoginControle loginControle = (LoginControle) JSFUtilities.getFromSession("LoginControle");
            String openWindow = "window.open('"
                    + loginControle.getAbrirNovaPlataforma(Modulo.NOVO_ZW.getSiglaModulo())
                    + "&redirect=/adm/configuracao/v2', '_self')";
            setMsgAlert(openWindow);
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        }
    }

    private void avaliarExibirReplicarRecursosRedeEmpresa() {
        UsuarioVO usuarioLogado = null;

        boolean usuarioPacto = false;
        boolean usuarioAdministrador = false;

        try {
            usuarioLogado = getUsuarioLogado();
        } catch (Exception ignored) {
        }

        if (usuarioLogado != null) {
            usuarioPacto = usuarioLogado.getUsuarioPactoSolucoes();

            for (UsuarioPerfilAcessoVO userPerfAcess : usuarioLogado.getUsuarioPerfilAcessoVOs()) {
                if (userPerfAcess.getPerfilAcesso().getTipo() != null && userPerfAcess.getPerfilAcesso().getTipo().equals(PerfilUsuarioEnum.ADMINISTRADOR)) {
                    usuarioAdministrador = true;
                    break;
                }
            }
        }

        setExibirReplicarPlanoRedeEmpresa((usuarioPacto || usuarioAdministrador) && integranteRedeEmpresa);
    }

    public boolean isAbrirModal() {
        return abrirModal;
    }

    public void setAbrirModal(boolean abrirModal) {
        this.abrirModal = abrirModal;
    }

    public EmpresaVO getEmpresaVO() {
        if (empresaVO == null) {
            empresaVO = new EmpresaVO();
        }
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public EmpresaVO getEmpresaAdicionarModalidadeAoPlano() {
        if (empresaAdicionarModalidadeAoPlano == null) {
            empresaAdicionarModalidadeAoPlano = new EmpresaVO();
        }
        return empresaAdicionarModalidadeAoPlano;
    }

    public void setEmpresaAdicionarModalidadeAoPlano(EmpresaVO empresaAdicionarModalidadeAoPlano) {
        this.empresaAdicionarModalidadeAoPlano = empresaAdicionarModalidadeAoPlano;
    }

    public EmpresaVO getEmpresaRetirarModalidadeAoPlano() {
        if (empresaRetirarModalidadeAoPlano == null) {
            empresaRetirarModalidadeAoPlano = new EmpresaVO();
        }
        return empresaRetirarModalidadeAoPlano;
    }

    public void setEmpresaRetirarModalidadeAoPlano(EmpresaVO empresaRetirarModalidadeAoPlano) {
        this.empresaRetirarModalidadeAoPlano = empresaRetirarModalidadeAoPlano;
    }

    public EmpresaVO getEmpresaAdicionarDiasContratoDePlano() {
        if (empresaAdicionarDiasContratoDePlano == null) {
            empresaAdicionarDiasContratoDePlano = new EmpresaVO();
        }
        return empresaAdicionarDiasContratoDePlano;
    }

    public void setEmpresaAdicionarDiasContratoDePlano(EmpresaVO empresaAdicionarDiasContratoDePlano) {
        this.empresaAdicionarDiasContratoDePlano = empresaAdicionarDiasContratoDePlano;
    }

    private void avaliarEmpresaIntegranteRedeEmpresa() {
        this.integranteRedeEmpresa = false;
        try {
            RedeEmpresaVO rede = (RedeEmpresaVO) JSFUtilities.getFromSession(JSFUtilities.REDE_EMPRESA);
            this.integranteRedeEmpresa = rede != null && !UteisValidacao.emptyNumber(rede.getId());
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public void restaurarVinculosConsultor() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("restaurarVinculosConsultor", getEmpresaLogado());
            setMsgAlert("");
            setRestaurarAtivo(false);
            getFacade().getVinculo().restaurarUltimoVinculoConsultor();

            setMensagemID("msg_restaurarVinculos");
            montarMsgAlert(getMensagem());
            setRestaurarAtivo(true);
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setMsgAlert(e.getMessage());
            setSucesso(false);
            setErro(true);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    private void povoarTabelasCadastroDinamico() throws Exception {
        getFacade().getCadastroDinamico().incluirTabelaCadastroDinamico("colaborador", Arrays.asList(CadastroDinamicoColaboradorEnum.values()));

        listaCamposColaboradorDinamico = getFacade().getCadastroDinamicoItem().consultar("colaborador", Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        for (CadastroDinamicoItemVO dinamicoItemVO : listaCamposColaboradorDinamico) {
            if (dinamicoItemVO.getLabelCampo().equalsIgnoreCase("CPF")) {
                dinamicoItemVO.setLabelCampo(displayIdentificadorFront[0]);
            } else if (dinamicoItemVO.getLabelCampo().equalsIgnoreCase("RG")) {
                dinamicoItemVO.setLabelCampo((displayIdentificadorFront[1]));
            }
        }

        if (configuracaoSistemaVO.isUsarSistemaInternacional() && (JSFUtilities.isJSFContext() && !getUsuarioLogado().getUsuarioAdminPACTO())) {
            listaCamposColaboradorDinamico.removeIf(r -> r.getNomeCampo().equalsIgnoreCase("ORGAOEMISSOR"));
            listaCamposColaboradorDinamico.removeIf(r -> r.getNomeCampo().equalsIgnoreCase("ESTADOEMISSAO"));
            //listaCamposColaboradorDinamico.forEach(r-> System.out.println("Admin List: " + r.getLabelCampo() + " " + r.getNomeCampo() + getEmpresa().getPais().getNome()));
        }

        this.listaCamposColaboradorDinamico = listaCamposColaboradorDinamico;
    }

    public void substituirProdutos() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("substituirProdutos", getEmpresaLogado());
            SubsitituirProdutos.substituir(getProdutoSubstituir(), getProdutoSubstituido(), getDeletarProduto(), Conexao.getFromSession());
            setMensagemID("msg_sucesso_substituido");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(true);
            setErro(false);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    /**
     * Rotina responsável por disponibilizar um novo objeto da classe
     * <code>ConfiguracaoSistema</code> para edição pelo usuário da aplicação.
     */
    public void novo() throws Exception {
        setProdutoSubstituido(0);
        setProdutoSubstituir(0);
        todosDesabilitados();
        setMenuNovo(true);
        setTotalContratosAProcessar(0);
        setTotalContratosProcessados(0);
        setInformacoesHistoricoContrato("");
        setTotalAProcessarMovProdutoParcela(0);
        setTotalAProcessarParcelas(0);
        setTotalProcessadosMovProdutoParcela(0);
        setTotalProcessadosParcelas(0);
        setTotalAProcessarHistoricoVinculo(0);
        setTotalProcessadosHistoricoVinculo(0);
        setConfiguracaoSistemaVO(new ConfiguracaoSistemaVO());
        setQuantidadeEmpresas(getFacade().getEmpresa().quantidadeEmpresas(true));
        setConfiguracaoSistemaVO(getFacade().getConfiguracaoSistema().buscarPorCodigo(1,
                false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));

        if (getConfiguracaoSistemaVO().getCodigo() != null) {
            getConfiguracaoSistemaVO().setNovoObj(false);
            getConfiguracaoSistemaVO().registrarObjetoVOAntesDaAlteracao();
        }

        if (getConfiguracaoSistemaVO().getCodigo().intValue() != 0) {
            editar();
        }
        verificacao = new VerificarRecibosPagamentosSemVinculo(Conexao.getFromSession());
        inicializarListasSelectItemTodosComboBox();
        /*setTextoExplicativoRobo("Esta configuração permite que o sistema controle uma rotina para validar seus clientes cadastrados na academia."
         + "Que será executado uma vez ao dia no horário entre 00:00 e 01:00 da manhã.");*/
        setTextoExplicativoRobo("");
        setMensagemID("msg_entre_dados");
    }

    public List consultarConfiguracaoFinanceiro(Integer prm) throws Exception {
        return getFacade().getConfiguracaoSistema().consultarPorCodigo(prm, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
    }

    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe
     * <code>ConfiguracaoSistema</code> para alteração. O objeto desta classe é
     * disponibilizado na session da página (request) para que o JSP
     * correspondente possa disponibilizá-lo para edição.
     */
    public String editar() {
        try {
            configuracaoSistemaVOClone = (ConfiguracaoSistemaVO) configuracaoSistemaVO.getClone(true);
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InstantiationException e) {
            e.printStackTrace();
        }
        todosDesabilitados();
        setMenuNovo(true);
        inicializarListasSelectItemTodosComboBox();

        setTextoExplicativoRobo("Esta configuração permite que o sistema controle uma rotina para validar seus clientes cadastrados na academia."
                + "Que será executado uma vez ao dia no horário entre 00:00 e 01:00 da manhã.");
        setMensagemID("msg_dados_editar");
        return "editar";
    }

    /**
     * Método responsável inicializar objetos relacionados a classe
     * <code>ConfiguracaoSistemaVO</code>. Esta inicialização é necessária por
     * exigência da tecnologia JSF, que não trabalha com valores nulos para
     * estes atributos.
     */
    public void inicializarAtributosRelacionados(ConfiguracaoSistemaVO obj) {
        if (obj.getQuestionarioPrimeiraVisita() == null) {
            obj.setQuestionarioPrimeiraVisita(new QuestionarioVO());
        }
        if (obj.getQuestionarioRetorno() == null) {
            obj.setQuestionarioRetorno(new QuestionarioVO());
        }
        if (obj.getQuestionarioReMatricula() == null) {
            obj.setQuestionarioReMatricula(new QuestionarioVO());
        }
    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto
     * da classe
     * <code>ConfiguracaoSistema</code>. Caso o objeto seja novo (ainda não
     * gravado no BD) é acionado a operação
     * <code>incluir()</code>. Caso contrário é acionado o
     * <code>alterar()</code>. Se houver alguma inconsistência o objeto não é
     * gravado, sendo re-apresentado para o usuário juntamente com uma mensagem
     * de erro.
     */
    public boolean verificandoCamposAlterados() throws Exception {
        if (!getFacade().getConfiguracaoSistema().maisEmpresa()) {
            return false;
        }
        ConfiguracaoSistemaVO antesDaAlteracao = getFacade().getConfiguracaoSistema().buscarPorCodigo(1, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        alterados = new ArrayList<>();
        if (!antesDaAlteracao.getQuestionarioReMatricula().getCodigo().equals(configuracaoSistemaVO.getQuestionarioReMatricula().getCodigo())) {
            alterados.add("Questionário Rematrícula");
        }
        if (!antesDaAlteracao.getQuestionarioPrimeiraVisita().getCodigo().equals(configuracaoSistemaVO.getQuestionarioPrimeiraVisita().getCodigo())) {
            alterados.add("Questionário Primeira Visita");
        }
        if (!antesDaAlteracao.getQuestionarioRetorno().getCodigo().equals(configuracaoSistemaVO.getQuestionarioRetorno().getCodigo())) {
            alterados.add("Questionário Retorno");
        }
        if (!antesDaAlteracao.getMascaraMatricula().equals(configuracaoSistemaVO.getMascaraMatricula())) {
            alterados.add("Máscara Matrícula");
        }
        if (!antesDaAlteracao.getUrlRecorrencia().equals(configuracaoSistemaVO.getUrlRecorrencia())) {
            alterados.add("URL Recorrência");
        }
        if (antesDaAlteracao.getNrDiasVigenteQuestionarioVista() != (configuracaoSistemaVO.getNrDiasVigenteQuestionarioVista())) {
            alterados.add("Número de Dias Vigentes Para Questionário Primeira Visita");
        }
        if (antesDaAlteracao.getNrDiasVigenteQuestionarioRetorno() != (configuracaoSistemaVO.getNrDiasVigenteQuestionarioRetorno())) {
            alterados.add("Número de Dias Vigentes Para Questionário Retorno");
        }
        if (antesDaAlteracao.getNrDiasVigenteQuestionarioRematricula() != (configuracaoSistemaVO.getNrDiasVigenteQuestionarioRematricula())) {
            alterados.add("Número de Dias Vigentes Para Questionário Rematrícula");
        }
        if (antesDaAlteracao.getCarenciaRenovacao() != (configuracaoSistemaVO.getCarenciaRenovacao())) {
            alterados.add("Férias Renovação");
        }
        if (antesDaAlteracao.getNrDiasAvencer() != (configuracaoSistemaVO.getNrDiasAvencer())) {
            alterados.add("Número de Dias A Vencer Renovação");
        }
        if (antesDaAlteracao.getToleranciaPagamento() != (configuracaoSistemaVO.getToleranciaPagamento())) {
            alterados.add("Tolerância Pagamento");
        }
        if (antesDaAlteracao.getQtdFaltaPeso1() != (configuracaoSistemaVO.getQtdFaltaPeso1())) {
            alterados.add("Quantidade Máxima de Faltas para Cliente obter Peso 1");
        }
        if (antesDaAlteracao.getQtdFaltaInicioPeso2() != (configuracaoSistemaVO.getQtdFaltaInicioPeso2())) {
            alterados.add("Quantidade Mínima e Máxima de Faltas para Cliente obter Peso 2");
        }
        if (antesDaAlteracao.getQtdFaltaTerminoPeso2() != (configuracaoSistemaVO.getQtdFaltaTerminoPeso2())) {
            alterados.add("Quantidade Mínima e Máxima de Faltas para Cliente obter Peso 2");
        }
        if (antesDaAlteracao.getQtdFaltaPeso3() != (configuracaoSistemaVO.getQtdFaltaPeso3())) {
            alterados.add("Quantidade Mínima de Faltas para Cliente obter Peso 3");
        }
        if (antesDaAlteracao.getCarencia() != (configuracaoSistemaVO.getCarencia())) {
            alterados.add("Número Mínimo de Dias para Cada Solicitação de Férias");
        }
        if (antesDaAlteracao.getNrDiasProrata() != (configuracaoSistemaVO.getNrDiasProrata())) {
            alterados.add("Número Máx.de Dias para Gerar um Mês Pró-Rata a Mais");
        }
        if (antesDaAlteracao.getToleranciaDiasContratoVencido() != (configuracaoSistemaVO.getToleranciaDiasContratoVencido())) {
            alterados.add("Tolerância Contrato Vencido");
        }
        if (antesDaAlteracao.isBloquearAcessoSeParcelaAberta() != (configuracaoSistemaVO.isBloquearAcessoSeParcelaAberta())) {
            alterados.add("Bloquear acesso se houver alguma parcela em aberto");
        }

        return !alterados.isEmpty();
    }

    /*------------------------------EXECUTAR PROCESSOS-----------------------------------*/
    private String informacaoExecutarProcessos;
    private boolean rodandoExecutarProcessos = false;
    private boolean pararExecutarProcessos = false;
    private boolean executandoTodosProcessos = false;

    public void abrirModalExecutarProcessos() {
        try {
            informacaoExecutarProcessos = "Iniciar execução de todos os processos";
        } catch (Exception e) {
            Logger.getLogger(ConfiguracaoSistemaControle.class.getName()).log(Level.SEVERE, null, e);
        }
    }

    public void iniciarProcessoExecutarProcessos() {
        rodandoExecutarProcessos = true;
        executandoTodosProcessos = true;
        pararExecutarProcessos = false;
        setMsgAlert("processarProximoExecutarProcessos();");
    }

    public void processarExecutarProcessos() {
        try {
            setMsgAlert("");
            if (pararExecutarProcessos) {
                return;
            }

            //CHAMANDO ESSE MÉTODO ELE IRA VERIFICAR SE ESTA RODANDO O PROCESSO DE EXECUTAR PROCESSOS
            //É VERDADEIRO, ASSIM ELE VAI CHAMANDO OS DEMAIS MÉTODOS ATÉ EXECUTAR TODOS.
            processarHistoricoContrato();
        } catch (Exception e) {
            informacaoExecutarProcessos = "Ocorreu um erro: " + e.getMessage();
            rodandoExecutarProcessos = false;
        }
    }

    public void pararProcessoExecutarProcessos() {
        pararExecutarProcessos = true;
        rodandoExecutarProcessos = false;
        setMsgAlert("");
    }

    public void resetarProcessoExecutarProcessos() {
        pararExecutarProcessos = true;
        rodandoExecutarProcessos = false;
        setMsgAlert("");

        //HISTORICO DE CONTRATO
        posicaoContratoHistorico = 0;
        historicos = null;

        //AJUSTAR LINHA DO TEMPO DE CONTRATOS
        contratos = null;

        abrirModalExecutarProcessos();
    }

    public String getInformacaoExecutarProcessos() {
        return informacaoExecutarProcessos;
    }

    public void setInformacaoExecutarProcessos(String informacaoExecutarProcessos) {
        this.informacaoExecutarProcessos = informacaoExecutarProcessos;
    }

    public boolean isRodandoExecutarProcessos() {
        return rodandoExecutarProcessos;
    }

    public void setRodandoExecutarProcessos(boolean rodandoExecutarProcessos) {
        this.rodandoExecutarProcessos = rodandoExecutarProcessos;
    }

    public boolean isPararExecutarProcessos() {
        return pararExecutarProcessos;
    }

    public void setPararExecutarProcessos(boolean pararExecutarProcessos) {
        this.pararExecutarProcessos = pararExecutarProcessos;
    }

    public boolean isExecutandoTodosProcessos() {
        return executandoTodosProcessos;
    }

    public void setExecutandoTodosProcessos(boolean executandoTodosProcessos) {
        this.executandoTodosProcessos = executandoTodosProcessos;
    }

    /*-----------------------------------------------------------------------------------*/

    /*------------------ PROCESSO DE CORRIGIR HISTÓRICO CONTRATO-------------------------*/
    private String informacoesHistoricoContrato = "";
    private List<Integer> historicos;
    private Integer posicaoContratoHistorico = 0;
    private boolean rodandoCorrigirHistorico = false;
    private boolean pararCorrigirHistorico = false;
    private LogProcessoSistemaVO logCorrigirHistoricoContrato = null;

    public void abrirModalCorrigirHistoricoContrato() {
        try {
            if (historicos == null || historicos.isEmpty()) {
                historicos = getFacade().getHistoricoContrato().obterListaTodosHistoricoContrato();
                informacoesHistoricoContrato = historicos.size() + " resgistros para processar.";
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public void corrigirHistoricoContrato() {
        try {

            if (logCorrigirHistoricoContrato == null) {
                logCorrigirHistoricoContrato = criarLog("corrigirHistoricoContrato", getEmpresaLogado());
            }

            rodandoCorrigirHistorico = true;
            pararCorrigirHistorico = false;
            setMsgAlert("processarProximoContratoHistorico();");
        } catch (Exception ex) {
            Logger.getLogger(ConfiguracaoSistemaControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void processarHistoricoContrato() {
        try {
            setMsgAlert("");
            rodandoExecutarProcessos = true;
            posicaoContratoHistorico++;

            //EXECUÇÃO DE TODOS OS PROCESSOS
            if (rodandoExecutarProcessos) {
                if (historicos == null || historicos.isEmpty()) {
                    historicos = getFacade().getHistoricoContrato().obterListaTodosHistoricoContrato();
                }
                rodandoCorrigirHistorico = true;
            }

            getFacade().getContrato().gerarSituacoesTemporaisContratos(0,0);

            //EXECUÇÃO DE TODOS OS PROCESSOS
            if (executandoTodosProcessos && !pararExecutarProcessos) {
                rodandoExecutarProcessos = true;
                executarUpdateClienteSituacao();
            }
            historicos = null;
        } catch (Exception e) {
            informacoesHistoricoContrato = "Ocorreu um erro: " + e.getMessage();
            informacaoExecutarProcessos = "Ocorreu um erro: " + e.getMessage();
            historicos = null;
        } finally {
            finalizarLogProcesso(logCorrigirHistoricoContrato);
            logCorrigirHistoricoContrato = null;
        }
    }

    public void pararProcessoHistoricoContrato() {
        pararCorrigirHistorico = true;
        rodandoCorrigirHistorico = false;
        setMsgAlert("");
        finalizarLogProcesso(logCorrigirHistoricoContrato);
        logCorrigirHistoricoContrato = null;
    }

    public void resetarProcessoHistoricoContrato() {
        pararCorrigirHistorico = true;
        rodandoCorrigirHistorico = false;
        setMsgAlert("");
        posicaoContratoHistorico = 0;
        historicos = null;
        abrirModalCorrigirHistoricoContrato();
    }

    public String getInformacoesHistoricoContrato() {
        return informacoesHistoricoContrato;
    }

    public void setInformacoesHistoricoContrato(String informacoesHistoricoContrato) {
        this.informacoesHistoricoContrato = informacoesHistoricoContrato;
    }

    public List<Integer> getHistoricos() {
        return historicos;
    }

    public void setHistoricos(List<Integer> historicos) {
        this.historicos = historicos;
    }

    public boolean isRodandoCorrigirHistorico() {
        return rodandoCorrigirHistorico;
    }

    public void setRodandoCorrigirHistorico(boolean rodandoCorrigirHistorico) {
        this.rodandoCorrigirHistorico = rodandoCorrigirHistorico;
    }

    /*-----------------------------------------------------------------------------------*/

    /*---------------------- PROCESSO DE UPDADE CLIENTE SITUAÇÃO-------------------------*/
    private String informacaoUpdadeClienteSituacao;
    private LogProcessoSistemaVO logUpdateClienteSituacao = null;

    public void abrirModalUpdateClienteSituacao() {
        try {
            informacaoUpdadeClienteSituacao = "UPDATE cliente SET situacao\n " +
                    " e DELETE FROM situacaoclientesinteticodw";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public void executarUpdateClienteSituacao() {
        try {
            rodandoExecutarProcessos = true;

            if (logUpdateClienteSituacao == null) {
                logUpdateClienteSituacao = criarLog("executarUpdateClienteSituacao", getEmpresaLogado());
            }

            if (getFacade().getCliente().alterarClienteSituacao()) {
                informacaoUpdadeClienteSituacao = "UPDATE executado com Sucesso!";
                informacaoExecutarProcessos = "UPDATE executado com Sucesso!";
            } else {
                informacaoUpdadeClienteSituacao = "Falha ao executar UPDATE!";
                informacaoExecutarProcessos = "Falha ao executar UPDATE!";
            }

            //EXECUÇÃO DE TODOS OS PROCESSOS
            if (executandoTodosProcessos && !pararExecutarProcessos) {
                rodandoExecutarProcessos = true;
                processarAjustarLinhaDoTempoDeContratos();
            }
        } catch (Exception e) {
            Logger.getLogger(ConfiguracaoSistemaControle.class.getName()).log(Level.SEVERE, null, e);
        } finally {
            finalizarLogProcesso(logUpdateClienteSituacao);
            logUpdateClienteSituacao = null;
        }
    }

    public String getInformacaoUpdadeClienteSituacao() {
        return informacaoUpdadeClienteSituacao;
    }

    public void setInformacaoUpdadeClienteSituacao(String informacaoUpdadeClienteSituacao) {
        this.informacaoUpdadeClienteSituacao = informacaoUpdadeClienteSituacao;
    }

    /*-----------------------------------------------------------------------------------*/

    /*------------------ PROCESSO DE AJUSTAR LINHA DO TEMPO CONTRATOS-------------------------*/
    private String informacaoAjustarLinhaDoTempoContratos;
    private List<Integer> contratos;
    private boolean rodandoAjustarLinhaDoTempoDeContratos = false;
    private boolean pararAjustarLinhaDoTempoDeContratos = false;
    private LogProcessoSistemaVO logAjustarLinhaDoTempo = null;

    public void abrirModalAjustarLinhaDoTempoDeContratos() {
        try {
            informacaoAjustarLinhaDoTempoContratos = "Iniciar execução de ajustes";
        } catch (Exception e) {
            Logger.getLogger(ConfiguracaoSistemaControle.class.getName()).log(Level.SEVERE, null, e);
        }
    }

    public void iniciarProcessoAjustarLinhaDoTempoDeContratos() {
        rodandoAjustarLinhaDoTempoDeContratos = true;
        pararAjustarLinhaDoTempoDeContratos = false;
        setMsgAlert("processarProximoAjusteLinhaDoTempoDeContratos();");
    }

    public void processarAjustarLinhaDoTempoDeContratos() {
        try {
            setMsgAlert("");
            rodandoExecutarProcessos = true;
            if (pararAjustarLinhaDoTempoDeContratos) {
                return;
            }

            if (logAjustarLinhaDoTempo == null) {
                logAjustarLinhaDoTempo = criarLog("processarAjustarLinhaDoTempoDeContratos", getEmpresaLogado());
            }

            rodandoAjustarLinhaDoTempoDeContratos = true;
            AjustarLinhaDoTempoDeContratos.ajustarContratos(getFacade().getContrato().getCon(), 0);

            //EXECUÇÃO DE TODOS OS PROCESSOS
            if (executandoTodosProcessos && !pararExecutarProcessos) {
                rodandoExecutarProcessos = true;
                processarAjustarDatasRenovacaoImportacao();
            }
        } catch (Exception e) {
            informacaoAjustarLinhaDoTempoContratos = "Ocorreu um erro: " + e.getMessage();
            informacaoExecutarProcessos = "Ocorreu um erro: " + e.getMessage();
        } finally {
            finalizarLogProcesso(logAjustarLinhaDoTempo);
            logAjustarLinhaDoTempo = null;
        }
    }

    public void pararProcessoAjustarLinhaDoTempoDeContratos() {
        pararAjustarLinhaDoTempoDeContratos = true;
        rodandoAjustarLinhaDoTempoDeContratos = false;
        setMsgAlert("");
        finalizarLogProcesso(logAjustarLinhaDoTempo);
        logAjustarLinhaDoTempo = null;
    }

    public void resetarProcessoAjustarLinhaDoTempoContratos() {
        pararAjustarLinhaDoTempoDeContratos = true;
        rodandoAjustarLinhaDoTempoDeContratos = false;
        setMsgAlert("");
        contratos = null;
        abrirModalAjustarLinhaDoTempoDeContratos();
    }

    public String getInformacaoAjustarLinhaDoTempoContratos() {
        return informacaoAjustarLinhaDoTempoContratos;
    }

    public void setInformacaoAjustarLinhaDoTempoContratos(String informacaoAjustarLinhaDoTempoContratos) {
        this.informacaoAjustarLinhaDoTempoContratos = informacaoAjustarLinhaDoTempoContratos;
    }

    public List<Integer> getContratos() {
        return contratos;
    }

    public void setContratos(List<Integer> contratos) {
        this.contratos = contratos;
    }

    public boolean isRodandoAjustarLinhaDoTempoDeContratos() {
        return rodandoAjustarLinhaDoTempoDeContratos;
    }

    public void setRodandoAjustarLinhaDoTempoDeContratos(boolean rodandoAjustarLinhaDoTempoDeContratos) {
        this.rodandoAjustarLinhaDoTempoDeContratos = rodandoAjustarLinhaDoTempoDeContratos;
    }
    /*-----------------------------------------------------------------------------------*/

    /*------------------ PROCESSO DE AJUSTAR DATAS RENOVACAO IMPORTACAO-------------------------*/
    private String informacaoAjustarDatasRenovacaoImportacao;
    private boolean rodandoAjustarDatasRenovacaoImportacao = false;
    private boolean pararAjustarDatasRenovacaoImportacao = false;
    private LogProcessoSistemaVO logAjustarDatasRenovacao = null;

    public void abrirModalAjustarDatasRenovacaoImportacao() {
        try {
            informacaoAjustarDatasRenovacaoImportacao = "Iniciar execução de ajustes para Data Renovação";
        } catch (Exception e) {
            Logger.getLogger(ConfiguracaoSistemaControle.class.getName()).log(Level.SEVERE, null, e);
        }
    }

    public void iniciarProcessoAjustarDatasRenovacaoImportacao() {
        rodandoAjustarDatasRenovacaoImportacao = true;
        pararAjustarDatasRenovacaoImportacao = false;
        setMsgAlert("processarProximoAjusteDatasRenovacaoImportacao();");
    }

    public void processarAjustarDatasRenovacaoImportacao() {
        try {
            setMsgAlert("");
            rodandoExecutarProcessos = true;
            if (pararAjustarDatasRenovacaoImportacao) {
                return;
            }

            if (logAjustarDatasRenovacao == null) {
                logAjustarDatasRenovacao = criarLog("processarAjustarDatasRenovacaoImportacao", getEmpresaLogado());
            }

            rodandoAjustarDatasRenovacaoImportacao = true;
            Connection connection;
            connection = Conexao.getFromSession();
            AjustarDatasRenovacaoImportacao.ajustarDatasRenovacao(connection);

            //EXECUÇÃO DE TODOS OS PROCESSOS
            if (executandoTodosProcessos && !pararExecutarProcessos) {
                rodandoExecutarProcessos = true;
                processarProdutosPagosServico();
            }
        } catch (Exception e) {
            informacaoAjustarDatasRenovacaoImportacao = "Ocorreu um erro: " + e.getMessage();
            informacaoExecutarProcessos = "Ocorreu um erro: " + e.getMessage();
        } finally {
            finalizarLogProcesso(logAjustarDatasRenovacao);
            logAjustarDatasRenovacao = null;
        }
    }

    public void pararProcessoAjustarDatasRenovacaoImportacao() {
        pararAjustarDatasRenovacaoImportacao = true;
        rodandoAjustarDatasRenovacaoImportacao = false;
        setMsgAlert("");
        finalizarLogProcesso(logAjustarDatasRenovacao);
        logAjustarDatasRenovacao = null;
    }

    public void resetarProcessoAjustarDatasRenovacaoImportacao() {
        pararAjustarDatasRenovacaoImportacao = true;
        rodandoAjustarDatasRenovacaoImportacao = false;
        setMsgAlert("");
        abrirModalAjustarDatasRenovacaoImportacao();
    }

    public String getInformacaoAjustarDatasRenovacaoImportacao() {
        return informacaoAjustarDatasRenovacaoImportacao;
    }

    public void setInformacaoAjustarDatasRenovacaoImportacao(String informacaoAjustarDatasRenovacaoImportacao) {
        this.informacaoAjustarDatasRenovacaoImportacao = informacaoAjustarDatasRenovacaoImportacao;
    }

    public boolean isRodandoAjustarDatasRenovacaoImportacao() {
        return rodandoAjustarDatasRenovacaoImportacao;
    }

    public void setRodandoAjustarDatasRenovacaoImportacao(boolean rodandoAjustarDatasRenovacaoImportacao) {
        this.rodandoAjustarDatasRenovacaoImportacao = rodandoAjustarDatasRenovacaoImportacao;
    }

    public boolean isPararAjustarDatasRenovacaoImportacao() {
        return pararAjustarDatasRenovacaoImportacao;
    }

    public void setPararAjustarDatasRenovacaoImportacao(boolean pararAjustarDatasRenovacaoImportacao) {
        this.pararAjustarDatasRenovacaoImportacao = pararAjustarDatasRenovacaoImportacao;
    }

    /*-----------------------------------------------------------------------------------*/

    /*------------------ PROCESSO DE PRODUTOS PAGOS SERVICO-------------------------*/
    private String informacaoProdutosPagosServico;
    private boolean rodandoProdutosPagosServico = false;
    private boolean pararProdutosPagosServico = false;
    private LogProcessoSistemaVO logProdutosPagosServico = null;

    public void abrirModalProdutosPagosServico() {
        try {
            informacaoProdutosPagosServico = "Iniciar execução de Produtos";
        } catch (Exception e) {
            Logger.getLogger(ConfiguracaoSistemaControle.class.getName()).log(Level.SEVERE, null, e);
        }
    }

    public void iniciarProcessoProdutosPagosServico() {
        rodandoProdutosPagosServico = true;
        pararProdutosPagosServico = false;
        setMsgAlert("processarProximoProdutosPagosServico();");
    }

    public void processarProdutosPagosServico() {
        try {
            setMsgAlert("");
            rodandoExecutarProcessos = true;
            if (pararProdutosPagosServico) {
                return;
            }

            if (logProdutosPagosServico == null) {
                logProdutosPagosServico = criarLog("processarHistoricoContrato", getEmpresaLogado());
            }

            Connection connection;
            connection = Conexao.getFromSession();
            ProdutosPagosServico.atualizarTodosProdutosPagos(connection);

            //EXECUÇÃO DE TODOS OS PROCESSOS
            if (executandoTodosProcessos && !pararExecutarProcessos) {
                rodandoExecutarProcessos = true;
                processarCriarPendenciaBVClientesImportacao();
            }
        } catch (Exception e) {
            informacaoProdutosPagosServico = "Ocorreu um erro: " + e.getMessage();
            informacaoExecutarProcessos = "Ocorreu um erro: " + e.getMessage();
        } finally {
            finalizarLogProcesso(logProdutosPagosServico);
            logProdutosPagosServico = null;
        }
    }

    public void pararProcessoProdutosPagosServico() {
        pararProdutosPagosServico = true;
        rodandoProdutosPagosServico = false;
        setMsgAlert("");
        finalizarLogProcesso(logProdutosPagosServico);
        logProdutosPagosServico = null;
    }

    public void resetarProcessoProdutosPagosServico() {
        pararProdutosPagosServico = true;
        rodandoProdutosPagosServico = false;
        setMsgAlert("");
        abrirModalProdutosPagosServico();
    }

    public String getInformacaoProdutosPagosServico() {
        return informacaoProdutosPagosServico;
    }

    public void setInformacaoProdutosPagosServico(String informacaoProdutosPagosServico) {
        this.informacaoProdutosPagosServico = informacaoProdutosPagosServico;
    }

    public boolean isRodandoProdutosPagosServico() {
        return rodandoProdutosPagosServico;
    }

    public void setRodandoProdutosPagosServico(boolean rodandoProdutosPagosServico) {
        this.rodandoProdutosPagosServico = rodandoProdutosPagosServico;
    }

    public boolean isPararProdutosPagosServico() {
        return pararProdutosPagosServico;
    }

    public void setPararProdutosPagosServico(boolean pararProdutosPagosServico) {
        this.pararProdutosPagosServico = pararProdutosPagosServico;
    }
    /*-----------------------------------------------------------------------------------*/

    /*------------------ PROCESSO DE CRIAR PENDENCIA BV CLIENTES IMPORTACAO-------------------------*/
    private String informacaoCriarPendenciaBVClientesImportacao;
    private boolean rodandoCriarPendenciaBVClientesImportacao = false;
    private boolean pararCriarPendenciaBVClientesImportacao = false;
    private LogProcessoSistemaVO logCriarPendenciaBV = null;

    public void abrirModalCriarPendenciaBVClientesImportacao() {
        try {
            informacaoCriarPendenciaBVClientesImportacao = "Iniciar criação de Pendência";
        } catch (Exception e) {
            Logger.getLogger(ConfiguracaoSistemaControle.class.getName()).log(Level.SEVERE, null, e);
        }
    }

    public void iniciarProcessoCriarPendenciaBVClientesImportacao() {
        rodandoCriarPendenciaBVClientesImportacao = true;
        pararCriarPendenciaBVClientesImportacao = false;
        setMsgAlert("processarProximoCriarPendenciaBVClientesImportacao();");
    }

    public void processarCriarPendenciaBVClientesImportacao() {
        try {
            setMsgAlert("");
            rodandoExecutarProcessos = true;
            if (pararCriarPendenciaBVClientesImportacao) {
                return;
            }

            if (logCriarPendenciaBV == null) {
                logCriarPendenciaBV = criarLog("processarCriarPendenciaBVClientesImportacao", getEmpresaLogado());
            }

            rodandoCriarPendenciaBVClientesImportacao = true;
            Connection connection;
            connection = Conexao.getFromSession();
            CriarPendenciaBVClientesImportacao.receberConexao(connection);

            //EXECUÇÃO DE TODOS OS PROCESSOS
            if (executandoTodosProcessos && !pararExecutarProcessos) {
                rodandoExecutarProcessos = true;
                processarGerarMOVProdutoModalidade();
            }
        } catch (Exception e) {
            informacaoCriarPendenciaBVClientesImportacao = "Ocorreu um erro: " + e.getMessage();
            informacaoExecutarProcessos = "Ocorreu um erro: " + e.getMessage();
        } finally {
            finalizarLogProcesso(logCriarPendenciaBV);
            logCriarPendenciaBV = null;
        }
    }

    public void pararProcessoCriarPendenciaBVClientesImportacao() {
        pararCriarPendenciaBVClientesImportacao = true;
        rodandoCriarPendenciaBVClientesImportacao = false;
        setMsgAlert("");
        finalizarLogProcesso(logCriarPendenciaBV);
        logCriarPendenciaBV = null;
    }

    public void resetarProcessoCriarPendenciaBVClientesImportacao() {
        pararCriarPendenciaBVClientesImportacao = true;
        rodandoCriarPendenciaBVClientesImportacao = false;
        setMsgAlert("");
        abrirModalCriarPendenciaBVClientesImportacao();
    }

    public String getInformacaoCriarPendenciaBVClientesImportacao() {
        return informacaoCriarPendenciaBVClientesImportacao;
    }

    public void setInformacaoCriarPendenciaBVClientesImportacao(String informacaoCriarPendenciaBVClientesImportacao) {
        this.informacaoCriarPendenciaBVClientesImportacao = informacaoCriarPendenciaBVClientesImportacao;
    }

    public boolean isRodandoCriarPendenciaBVClientesImportacao() {
        return rodandoCriarPendenciaBVClientesImportacao;
    }

    public void setRodandoCriarPendenciaBVClientesImportacao(boolean rodandoCriarPendenciaBVClientesImportacao) {
        this.rodandoCriarPendenciaBVClientesImportacao = rodandoCriarPendenciaBVClientesImportacao;
    }

    public boolean isPararCriarPendenciaBVClientesImportacao() {
        return pararCriarPendenciaBVClientesImportacao;
    }

    public void setPararCriarPendenciaBVClientesImportacao(boolean pararCriarPendenciaBVClientesImportacao) {
        this.pararCriarPendenciaBVClientesImportacao = pararCriarPendenciaBVClientesImportacao;
    }
    /*----------------------------------------------------------------------------------------------*/

    /*------------------ PROCESSO DE GERAR MOV PRODUTO MODALIDADE-------------------------*/
    private String informacaoGerarMOVProdutoModalidade;
    private boolean rodandoGerarMOVProdutoModalidade = false;
    private boolean pararGerarMOVProdutoModalidade = false;
    private LogProcessoSistemaVO logGerarMovProdutoModalidade = null;

    public void abrirModalGerarMOVProdutoModalidade() {
        try {
            informacaoGerarMOVProdutoModalidade = "Iniciar geração Movimentação Produto Modalidade";
        } catch (Exception e) {
            Logger.getLogger(ConfiguracaoSistemaControle.class.getName()).log(Level.SEVERE, null, e);
        }
    }

    public void iniciarProcessoGerarMOVProdutoModalidade() {
        rodandoGerarMOVProdutoModalidade = true;
        pararGerarMOVProdutoModalidade = false;
        setMsgAlert("processarProximoGerarMOVProdutoModalidade();");
    }

    public void processarGerarMOVProdutoModalidade() {
        try {
            setMsgAlert("");
            rodandoExecutarProcessos = true;
            if (pararGerarMOVProdutoModalidade) {
                return;
            }

            if (logGerarMovProdutoModalidade == null) {
                logGerarMovProdutoModalidade = criarLog("processarGerarMOVProdutoModalidade", getEmpresaLogado());
            }

            rodandoGerarMOVProdutoModalidade = true;
            Connection connection;
            connection = Conexao.getFromSession();
            GerarMovProdutoModalidade.gerarMovProdutoModalidadeProdutoCancelado(connection);

            //EXECUÇÃO DE TODOS OS PROCESSOS
            if (executandoTodosProcessos && !pararExecutarProcessos) {
                rodandoExecutarProcessos = true;
                executarMigradorMOVProdutoModalidade();
            }
        } catch (Exception e) {
            informacaoGerarMOVProdutoModalidade = "Ocorreu um erro: " + e.getMessage();
            informacaoExecutarProcessos = "Ocorreu um erro: " + e.getMessage();
        } finally {
            finalizarLogProcesso(logGerarMovProdutoModalidade);
            logGerarMovProdutoModalidade = null;
        }
    }

    public void pararProcessoGerarMOVProdutoModalidade() {
        pararGerarMOVProdutoModalidade = true;
        rodandoGerarMOVProdutoModalidade = false;
        setMsgAlert("");
        finalizarLogProcesso(logGerarMovProdutoModalidade);
        logGerarMovProdutoModalidade = null;
    }

    public void resetarProcessoGerarMOVProdutoModalidade() {
        pararGerarMOVProdutoModalidade = true;
        rodandoGerarMOVProdutoModalidade = false;
        setMsgAlert("");
        abrirModalGerarMOVProdutoModalidade();
    }

    public String getInformacaoGerarMOVProdutoModalidade() {
        return informacaoGerarMOVProdutoModalidade;
    }

    public void setInformacaoGerarMOVProdutoModalidade(String informacaoGerarMOVProdutoModalidade) {
        this.informacaoGerarMOVProdutoModalidade = informacaoGerarMOVProdutoModalidade;
    }

    public boolean isRodandoGerarMOVProdutoModalidade() {
        return rodandoGerarMOVProdutoModalidade;
    }

    public void setRodandoGerarMOVProdutoModalidade(boolean rodandoGerarMOVProdutoModalidade) {
        this.rodandoGerarMOVProdutoModalidade = rodandoGerarMOVProdutoModalidade;
    }

    public boolean isPararGerarMOVProdutoModalidade() {
        return pararGerarMOVProdutoModalidade;
    }

    public void setPararGerarMOVProdutoModalidade(boolean pararGerarMOVProdutoModalidade) {
        this.pararGerarMOVProdutoModalidade = pararGerarMOVProdutoModalidade;
    }
    /*-----------------------------------------------------------------------------------*/

    /*------------------ PROCESSO MIGRADOR MOV PRODUTO MODALIDADE-------------------------*/
    private String informacaoMigradorMOVProdutoModalidade;
    private boolean rodandoMigradorMOVProdutoModalidade = false;
    private LogProcessoSistemaVO logMigradorMovProdutoModalidade = null;

    public void abrirModalMigradorMOVProdutoModalidade() {
        try {
            informacaoMigradorMOVProdutoModalidade = "Executar SELECT migrador_movprodutomodalidade()";
        } catch (Exception e) {
            Logger.getLogger(ConfiguracaoSistemaControle.class.getName()).log(Level.SEVERE, null, e);
        }
    }

    public void executarMigradorMOVProdutoModalidade() {
        try {
            rodandoExecutarProcessos = true;

            if (logMigradorMovProdutoModalidade == null) {
                logMigradorMovProdutoModalidade = criarLog("executarMigradorMOVProdutoModalidade", getEmpresaLogado());
            }

            if (getFacade().getMovProdutoModalidade().selectMigradorMOVProdutoModalidade()) {
                informacaoMigradorMOVProdutoModalidade = "SELECT executado com Sucesso";
                informacaoExecutarProcessos = "SELECT executado com Sucesso";
                rodandoMigradorMOVProdutoModalidade = false;
            } else {
                informacaoMigradorMOVProdutoModalidade = "Falha ao executar SELECT";
                informacaoExecutarProcessos = "Falha ao executar SELECT";
                rodandoMigradorMOVProdutoModalidade = false;
            }

            Connection connection;
            connection = Conexao.getFromSession();
            GerarMovProdutoModalidade.preencherTabelaMovProdutoModalidade(connection);

            //EXECUÇÃO DE TODOS OS PROCESSOS
            if (executandoTodosProcessos && !pararExecutarProcessos) {
                rodandoExecutarProcessos = true;
                processarGerarMOVPagamentosMovimento();
            }
        } catch (Exception e) {
            informacaoMigradorMOVProdutoModalidade = "Ocorreu um erro: " + e.getMessage();
            informacaoExecutarProcessos = "Ocorreu um erro: " + e.getMessage();
            Uteis.logarDebug("Erro no processo: GerarMovProdutoModalidade preencher tabela MovProdutoModalidade");
            e.printStackTrace();
        } finally {
            finalizarLogProcesso(logMigradorMovProdutoModalidade);
            logMigradorMovProdutoModalidade = null;
        }
    }

    public String getInformacaoMigradorMOVProdutoModalidade() {
        return informacaoMigradorMOVProdutoModalidade;
    }

    public void setInformacaoMigradorMOVProdutoModalidade(String informacaoMigradorMOVProdutoModalidade) {
        this.informacaoMigradorMOVProdutoModalidade = informacaoMigradorMOVProdutoModalidade;
    }
    /*-----------------------------------------------------------------------------------*/

    /*------------------ PROCESSO DE GERAR MOV PAGAMENTOS MOVIMENTO-------------------------*/
    private String informacaoGerarMOVPagamentosMovimento;
    private boolean rodandoGerarMOVPagamentosMovimento = false;
    private boolean pararGerarMOVPagamentosMovimento = false;
    private LogProcessoSistemaVO logGerarMovPagamentoMovimento = null;

    public void abrirModalGerarMOVPagamentosMovimento() {
        try {
            informacaoGerarMOVPagamentosMovimento = "Inicar geração Movimentação Pagamentos Movimento";
        } catch (Exception e) {
            Logger.getLogger(ConfiguracaoSistemaControle.class.getName()).log(Level.SEVERE, null, e);
        }
    }

    public void iniciarProcessoGerarMOVPagamentosMovimento() {
        rodandoGerarMOVPagamentosMovimento = true;
        pararGerarMOVPagamentosMovimento = false;
        setMsgAlert("processarProximoGerarMOVPagamentosMovimento();");
    }

    public void processarGerarMOVPagamentosMovimento() {
        try {
            setMsgAlert("");
            rodandoExecutarProcessos = true;
            if (pararGerarMOVPagamentosMovimento) {
                return;
            }

            if (logGerarMovPagamentoMovimento == null) {
                logGerarMovPagamentoMovimento = criarLog("processarGerarMOVPagamentosMovimento", getEmpresaLogado());
            }

            rodandoGerarMOVPagamentosMovimento = true;
            Connection connection;
            connection = Conexao.getFromSession();
            GerarMovPagamentosMovimento.receberConexao(connection);

            //EXECUÇÃO DE TODOS OS PROCESSOS
            if (executandoTodosProcessos && !pararExecutarProcessos) {
                rodandoExecutarProcessos = true;
                processarReciboCliente();
            }
        } catch (Exception e) {
            informacaoGerarMOVPagamentosMovimento = "Ocorreu um erro: " + e.getMessage();
            informacaoExecutarProcessos = "Ocorreu um erro: " + e.getMessage();
        } finally {
            finalizarLogProcesso(logGerarMovPagamentoMovimento);
            logGerarMovPagamentoMovimento = null;
        }
    }

    public void pararProcessoGerarMOVPagamentosMovimento() {
        pararGerarMOVPagamentosMovimento = true;
        rodandoGerarMOVPagamentosMovimento = false;
        setMsgAlert("");
        finalizarLogProcesso(logGerarMovPagamentoMovimento);
        logGerarMovPagamentoMovimento = null;
    }

    public void resetarProcessoGerarMOVPagamentosMovimento() {
        pararGerarMOVPagamentosMovimento = true;
        rodandoGerarMOVPagamentosMovimento = false;
        setMsgAlert("");
        abrirModalGerarMOVPagamentosMovimento();
    }

    public String getInformacaoGerarMOVPagamentosMovimento() {
        return informacaoGerarMOVPagamentosMovimento;
    }

    public void setInformacaoGerarMOVPagamentosMovimento(String informacaoGerarMOVPagamentosMovimento) {
        this.informacaoGerarMOVPagamentosMovimento = informacaoGerarMOVPagamentosMovimento;
    }

    public boolean isRodandoGerarMOVPagamentosMovimento() {
        return rodandoGerarMOVPagamentosMovimento;
    }

    public void setRodandoGerarMOVPagamentosMovimento(boolean rodandoGerarMOVPagamentosMovimento) {
        this.rodandoGerarMOVPagamentosMovimento = rodandoGerarMOVPagamentosMovimento;
    }

    public boolean isPararGerarMOVPagamentosMovimento() {
        return pararGerarMOVPagamentosMovimento;
    }

    public void setPararGerarMOVPagamentosMovimento(boolean pararGerarMOVPagamentosMovimento) {
        this.pararGerarMOVPagamentosMovimento = pararGerarMOVPagamentosMovimento;
    }
    /*-----------------------------------------------------------------------------------*/

    /*------------------ PROCESSO RECIBO CLINTE------------------------------------------*/
    private String informacaoReciboCliente;
    private boolean rodandoReciboCliente = false;
    private boolean pararReciboCliente = false;
    private LogProcessoSistemaVO logReciboCliente = null;

    public void abrirModalReciboCliente() {
        try {
            informacaoReciboCliente = "Execução dos SQL's DELETE FROM reciboclienteconsultor; \n" +
                    "SELECT migrador_povoarreciboclienteconsultor(); \n" +
                    "UPDATE movproduto SET valorfaturado = totalfinal";
        } catch (Exception e) {
            Logger.getLogger(ConfiguracaoSistemaControle.class.getName()).log(Level.SEVERE, null, e);
        }
    }

    public void iniciarProcessoReciboCliente() {
        rodandoReciboCliente = true;
        pararReciboCliente = false;
        setMsgAlert("processarProximoReciboCliente();");
    }

    public void processarReciboCliente() {
        try {
            setMsgAlert("");
            rodandoExecutarProcessos = true;
            if (pararReciboCliente) {
                return;
            }

            if (logReciboCliente == null) {
                logReciboCliente = criarLog("processarReciboCliente", getEmpresaLogado());
            }

            boolean reciboCliente = getFacade().getReciboClienteConsultor().excluirDados();
            boolean migrador = getFacade().getReciboClienteConsultor().selectMigradorPovoarReciboClienteConsultor();
            boolean movProduto = getFacade().getMovProduto().alterarValorFaturado();

            if (reciboCliente && migrador && movProduto) {
                informacaoReciboCliente = "SQL's executados com sucesso";
                informacaoExecutarProcessos = "SQL's executados com sucesso";
                rodandoReciboCliente = false;
            } else {
                informacaoReciboCliente = "Falha ao executar SQL's";
                informacaoExecutarProcessos = "Falha ao executar SQL's";
                rodandoReciboCliente = false;
            }

            //EXECUÇÃO DE TODOS OS PROCESSOS
            if (executandoTodosProcessos && !pararExecutarProcessos) {
                rodandoExecutarProcessos = true;
                processarSituacaoClienteSinteticoDW();
            }
        } catch (Exception e) {
            informacaoReciboCliente = "Ocorreu um erro: " + e.getMessage();
            informacaoExecutarProcessos = "Ocorreu um erro: " + e.getMessage();
        } finally {
            finalizarLogProcesso(logReciboCliente);
            logReciboCliente = null;
        }
    }

    public void pararProcessoReciboCliente() {
        pararReciboCliente = true;
        rodandoReciboCliente = false;
        setMsgAlert("");
        finalizarLogProcesso(logReciboCliente);
        logReciboCliente = null;
    }

    public void resetarProcessoReciboCliente() {
        pararReciboCliente = true;
        rodandoReciboCliente = false;
        setMsgAlert("");
        abrirModalReciboCliente();
    }

    public String getInformacaoReciboCliente() {
        return informacaoReciboCliente;
    }

    public void setInformacaoReciboCliente(String informacaoReciboCliente) {
        this.informacaoReciboCliente = informacaoReciboCliente;
    }

    public boolean isRodandoReciboCliente() {
        return rodandoReciboCliente;
    }

    public void setRodandoReciboCliente(boolean rodandoReciboCliente) {
        this.rodandoReciboCliente = rodandoReciboCliente;
    }

    public boolean isPararReciboCliente() {
        return pararReciboCliente;
    }

    public void setPararReciboCliente(boolean pararReciboCliente) {
        this.pararReciboCliente = pararReciboCliente;
    }
    /*-----------------------------------------------------------------------------------*/

    /*------------------ PROCESSO SITUACAO CLIENTE SINTEICO DW-------------------------*/
    private String informacaoSituacaoClienteSinteticoDW;
    private boolean rodandoSituacaoClienteSinteticoDW = false;
    private boolean pararSituacaoClienteSinteticoDW = false;
    private LogProcessoSistemaVO logSituacaoClienteSinteticoDW = null;

    public void abrirModalSituacaoClienteSinteticoDW() {
        try {
            informacaoSituacaoClienteSinteticoDW = "Iniciar execução da Situação Cliente Sintetico DW";
        } catch (Exception e) {
            Logger.getLogger(ConfiguracaoSistemaControle.class.getName()).log(Level.SEVERE, null, e);
        }
    }

    public void iniciarProcessoSituacaoClienteSinteticoDW() {
        rodandoSituacaoClienteSinteticoDW = true;
        pararSituacaoClienteSinteticoDW = false;
        setMsgAlert("processarProximoSituacaoClienteSinteticoDW();");
    }

    public void processarSituacaoClienteSinteticoDW() {
        try {
            setMsgAlert("");
            if (pararSituacaoClienteSinteticoDW) {
                return;
            }

            if (logSituacaoClienteSinteticoDW == null) {
                logSituacaoClienteSinteticoDW = criarLog("processarSituacaoClienteSinteticoDW", getEmpresaLogado());
            }

            rodandoSituacaoClienteSinteticoDW = true;
            SituacaoClienteSinteticoDWControle.executar();

        } catch (Exception e) {
            informacaoSituacaoClienteSinteticoDW = "Ocorreu um erro: " + e.getMessage();
            informacaoExecutarProcessos = "Ocorreu um erro: " + e.getMessage();
        } finally {
            finalizarLogProcesso(logSituacaoClienteSinteticoDW);
            logSituacaoClienteSinteticoDW = null;
        }
    }

    public void pararProcessoSituacaoClienteSinteticoDW() {
        pararSituacaoClienteSinteticoDW = true;
        rodandoSituacaoClienteSinteticoDW = false;
        setMsgAlert("");
        finalizarLogProcesso(logSituacaoClienteSinteticoDW);
        logSituacaoClienteSinteticoDW = null;
    }

    public void resetarProcessoSituacaoClienteSinteticoDW() {
        pararSituacaoClienteSinteticoDW = true;
        rodandoSituacaoClienteSinteticoDW = false;
        setMsgAlert("");
        abrirModalSituacaoClienteSinteticoDW();
    }

    public String getInformacaoSituacaoClienteSinteticoDW() {
        return informacaoSituacaoClienteSinteticoDW;
    }

    public void setInformacaoSituacaoClienteSinteticoDW(String informacaoSituacaoClienteSinteticoDW) {
        this.informacaoSituacaoClienteSinteticoDW = informacaoSituacaoClienteSinteticoDW;
    }

    public boolean isRodandoSituacaoClienteSinteticoDW() {
        return rodandoSituacaoClienteSinteticoDW;
    }

    public void setRodandoSituacaoClienteSinteticoDW(boolean rodandoSituacaoClienteSinteticoDW) {
        this.rodandoSituacaoClienteSinteticoDW = rodandoSituacaoClienteSinteticoDW;
    }

    public boolean isPararSituacaoClienteSinteticoDW() {
        return pararSituacaoClienteSinteticoDW;
    }

    public void setPararSituacaoClienteSinteticoDW(boolean pararSituacaoClienteSinteticoDW) {
        this.pararSituacaoClienteSinteticoDW = pararSituacaoClienteSinteticoDW;
    }
    /*-----------------------------------------------------------------------------------*/

    /*------------------ PROCESSO DE APLICAR MATRICULA IMPORTAÇÃO-------------------------*/
    private String informacaoAplicarMatriculaImportacao;
    private boolean rodandoAplicarMatriculaImportacao = false;
    private boolean pararAplicarMatriculaImportacao = false;
    private LogProcessoSistemaVO logAplicarMatriculaImportacao = null;

    public void abrirModalAplicarMatriculaImportacao() {
        try {
            informacaoAplicarMatriculaImportacao = "Iniciar aplicação de matrícula importação";
        } catch (Exception e) {
            Logger.getLogger(ConfiguracaoSistemaControle.class.getName()).log(Level.SEVERE, null, e);
        }
    }

    public void iniciarProcessoAplicarMatriculaImportacao() {
        rodandoAplicarMatriculaImportacao = true;
        pararAplicarMatriculaImportacao = false;
        setMsgAlert("processarProximoAplicarMatriculaImportacao();");
    }

    public void processarAplicarMatriculaImportacao() {
        try {
            setMsgAlert("");
            if (pararAplicarMatriculaImportacao) {
                return;
            }

            if (logAplicarMatriculaImportacao == null) {
                logAplicarMatriculaImportacao = criarLog("processarAplicarMatriculaImportacao", getEmpresaLogado());
            }

            rodandoAplicarMatriculaImportacao = true;
            Connection connection;
            connection = Conexao.getFromSession();
            AplicarMatriculaImportacao.executar(connection);
        } catch (Exception e) {
            informacaoAplicarMatriculaImportacao = "Ocorreu um erro: " + e.getMessage();
            informacaoExecutarProcessos = "Ocorreu um erro: " + e.getMessage();
        } finally {
            finalizarLogProcesso(logAplicarMatriculaImportacao);
            logAplicarMatriculaImportacao = null;
        }
    }

    public void pararProcessoAplicarMatriculaImportacao() {
        pararAplicarMatriculaImportacao = true;
        rodandoAplicarMatriculaImportacao = false;
        setMsgAlert("");
        finalizarLogProcesso(logAplicarMatriculaImportacao);
        logAplicarMatriculaImportacao = null;
    }

    public void resetarProcessoAplicarMatriculaImportacao() {
        pararAplicarMatriculaImportacao = true;
        rodandoAplicarMatriculaImportacao = false;
        setMsgAlert("");
        abrirModalAplicarMatriculaImportacao();
    }

    public String getInformacaoAplicarMatriculaImportacao() {
        return informacaoAplicarMatriculaImportacao;
    }

    public void setInformacaoAplicarMatriculaImportacao(String informacaoAplicarMatriculaImportacao) {
        this.informacaoAplicarMatriculaImportacao = informacaoAplicarMatriculaImportacao;
    }

    public boolean isRodandoAplicarMatriculaImportacao() {
        return rodandoAplicarMatriculaImportacao;
    }

    public void setRodandoAplicarMatriculaImportacao(boolean rodandoAplicarMatriculaImportacao) {
        this.rodandoAplicarMatriculaImportacao = rodandoAplicarMatriculaImportacao;
    }

    public boolean isPararAplicarMatriculaImportacao() {
        return pararAplicarMatriculaImportacao;
    }

    public void setPararAplicarMatriculaImportacao(boolean pararAplicarMatriculaImportacao) {
        this.pararAplicarMatriculaImportacao = pararAplicarMatriculaImportacao;
    }
    /*-----------------------------------------------------------------------------------*/

    /*------------------------------AJUSTAR MASCARA TELEFONE-----------------------------*/
    private String informacaoAjustarMascaraTelefone;
    private String dadosQueNaoSeraoAlterados;
    private boolean rodandoAjustarMascaraTelefone = false;
    private boolean pararAjustarMascaraTelefone = false;
    private LogProcessoSistemaVO logAjustarMascaraTelefone = null;
    public void abrirModalAjustarMascaraTelefone() {
        try {
            informacaoAjustarMascaraTelefone = "";
        } catch (Exception e) {
            Logger.getLogger(ConfiguracaoSistemaControle.class.getName()).log(Level.SEVERE, null, e);
        }
    }

    public void iniciarProcessoAjustarMascaraTelefone() {
        rodandoAjustarMascaraTelefone = true;
        pararAjustarMascaraTelefone = false;
        if (ddd.length() < 2) {
            informacaoAjustarMascaraTelefone = "Digite um DDD";
            rodandoAjustarMascaraTelefone = false;
            pararAjustarMascaraTelefone = true;
        } else {
            informacaoAjustarMascaraTelefone = "";
            processarAjustarMascaraTelefone();
        }
    }

    public void processarAjustarMascaraTelefone() {
        try {
            if (logAjustarMascaraTelefone == null) {
                logAjustarMascaraTelefone = criarLog("processarAjustarMascaraTelefone", getEmpresaLogado());
            }

            if (getFacade().getTelefone().updateEspacoVazio(empresaAcesso.getCodigo())) {
                getFacade().getTelefone().limparRegistrosSujos(empresaAcesso.getCodigo());
            }

            // --Remover o 0 do inicio dos números
            boolean updateRemoverZero = getFacade().getTelefone().updateRemoverZero(empresaAcesso.getCodigo());

            // --Ajustar quem está no DDD correto porém na máscara errada
            boolean upadateMascara = getFacade().getTelefone().updateMascaraErrada(ddd, empresaAcesso.getCodigo());

            // --Para todos números que estejam sem máscara porém com 10 digitos, ajustar para o DDD em questão;
            boolean dezDigitos = getFacade().getTelefone().dezDigitos(empresaAcesso.getCodigo());

            // --Para todos números sem máscara porém estão com 8 dígitos;
            boolean oitoDigitos = getFacade().getTelefone().oitoDigitos(ddd, empresaAcesso.getCodigo());

            // -- Para ajustar os celulares que tem o 9 porém não tem a máscara
            boolean noveDigitos = getFacade().getTelefone().noveDigitos(empresaAcesso.getCodigo());

            if (updateRemoverZero && upadateMascara && dezDigitos && oitoDigitos && noveDigitos) {
                informacaoAjustarMascaraTelefone = "Telefones ajustados.";
                rodandoAjustarMascaraTelefone = false;
            } else {
                informacaoAjustarMascaraTelefone = "Falha ao ajustar Telefones";
                rodandoAjustarMascaraTelefone = false;
            }

            // -- Dados que não serão alterados
            Map<Integer, String> numerosNaoAlterados = getFacade().getTelefone().naoAlterados(empresaAcesso.getCodigo());

            if (!numerosNaoAlterados.isEmpty()) {
                dadosQueNaoSeraoAlterados = numerosNaoAlterados.size() + " telefones serão removidos por não estarem no padrão esperado.";
                for (Map.Entry<Integer, String> numeroNaoAlterado : numerosNaoAlterados.entrySet()) {
                    getFacade().getTelefone().removeTelefonesNaoAtualizados(numeroNaoAlterado.getKey(), numeroNaoAlterado.getValue());
                }
                rodandoAjustarMascaraTelefone = false;
            }
        } catch (Exception e) {
            informacaoAjustarMascaraTelefone = "Ocorreu um erro: " + e.getMessage();
            rodandoAjustarMascaraTelefone = false;
        } finally {
            finalizarLogProcesso(logAjustarMascaraTelefone);
            logAjustarMascaraTelefone = null;
        }
    }

    public String getInformacaoAjustarMascaraTelefone() {
        return informacaoAjustarMascaraTelefone;
    }

    public void setInformacaoAjustarMascaraTelefone(String informacaoAjustarMascaraTelefone) {
        this.informacaoAjustarMascaraTelefone = informacaoAjustarMascaraTelefone;
    }

    public String getDadosQueNaoSeraoAlterados() {
        return dadosQueNaoSeraoAlterados;
    }

    public void setDadosQueNaoSeraoAlterados(String dadosQueNaoSeraoAlterados) {
        this.dadosQueNaoSeraoAlterados = dadosQueNaoSeraoAlterados;
    }

    public boolean isRodandoAjustarMascaraTelefone() {
        return rodandoAjustarMascaraTelefone;
    }

    public void setRodandoAjustarMascaraTelefone(boolean rodandoAjustarMascaraTelefone) {
        this.rodandoAjustarMascaraTelefone = rodandoAjustarMascaraTelefone;
    }

    public boolean isPararAjustarMascaraTelefone() {
        return pararAjustarMascaraTelefone;
    }

    public void setPararAjustarMascaraTelefone(boolean pararAjustarMascaraTelefone) {
        this.pararAjustarMascaraTelefone = pararAjustarMascaraTelefone;
    }
    /*-----------------------------------------------------------------------------------*/


    /*------------------ PROCESSO PREENCHER TABELA CARTAOCREDITO PARA PRODUTO DIARIA IMORTACAO -------------------------*/
    private String informacaoGerarCartaoCreditoProdutoDiariaImportacao;
    private LogProcessoSistemaVO logPreencherTabelaCartaoCredito = null;

    public void abrirModalGerarCartaoCreditoProdutoDiariaImportacao() {
        try {
            informacaoGerarCartaoCreditoProdutoDiariaImportacao = "INSERT CartaoCredito referente Diária Importação";
        } catch (Exception e) {
            Logger.getLogger(ConfiguracaoSistemaControle.class.getName()).log(Level.SEVERE, null, e);
        }
    }

    public void executarGerarCartaoCreditoProdutoDiariaImportacao() {
        try {
            rodandoExecutarProcessos = true;

            if (logPreencherTabelaCartaoCredito == null) {
                logPreencherTabelaCartaoCredito = criarLog("executarGerarCartaoCreditoProdutoDiariaImportacao", getEmpresaLogado());
            }

            List<String[]> listaArraysDadosAjustar = new ArrayList<>();
            listaArraysDadosAjustar = getFacade().getMovProduto().consultarParaInsertCartaoCreditoProdutosDiariaImportacao();

            Uteis.logarDebug("MovProduto de Diária Importação encontrados: " + listaArraysDadosAjustar.size());
            int numeroCartaoCreditoInseridos = 0;

            for(String[] item: listaArraysDadosAjustar){
                //Preencher ProdutosPagos de MovPagamento
                Integer codigoMovPagamento = parseInt(item[1]);
                String produtosPagos = item[4];
                getFacade().getMovPagamento().atualizarProdutosPagosMovPagamento(produtosPagos, codigoMovPagamento);

                //Fazer Insert CartaoCredito da Diaria Importação
                List<CartaoCreditoVO> listaCartoes = new ArrayList<>();
                listaCartoes = getFacade().getCartaoCredito().consultarPorMovPagamento(codigoMovPagamento, false, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if(UteisValidacao.emptyList(listaCartoes)){
                    CartaoCreditoVO obj = new CartaoCreditoVO();
                    montarObjetoCartaoCredito(item, codigoMovPagamento, produtosPagos, obj);
                    try {
                        getFacade().getCartaoCredito().incluir(obj);
                    } catch (Exception e){
                        Uteis.logarDebug("Posição do insert tabela CartaoCredito que deu erro: " + numeroCartaoCreditoInseridos);
                    }
                    numeroCartaoCreditoInseridos++;
                }
            }
            Uteis.logarDebug("Número inserts tabela CartaoCredito: " + numeroCartaoCreditoInseridos);
            informacaoGerarCartaoCreditoProdutoDiariaImportacao = "INSERT executado com Sucesso";
        } catch (Exception e) {
            informacaoGerarCartaoCreditoProdutoDiariaImportacao = "Ocorreu um erro: " + e.getMessage();
            e.printStackTrace();
        } finally {
            rodandoExecutarProcessos = false;
            finalizarLogProcesso(logPreencherTabelaCartaoCredito);
            logPreencherTabelaCartaoCredito = null;
        }
    }

    private static void montarObjetoCartaoCredito(String[] item, Integer codigoMovPagamento, String produtosPagos, CartaoCreditoVO obj) throws ParseException {
        Date dataCompensacao = new Date();
        dataCompensacao = Calendario.getDate("yyyy-MM-dd hh:mm:ss", item[0]);
        obj.setDataCompensacao(dataCompensacao);

        MovPagamentoVO movPagamentoVO = new MovPagamentoVO();
        movPagamentoVO.setCodigo(codigoMovPagamento);
        obj.setMovpagamento(movPagamentoVO);

        Double valor = Double.parseDouble(item[2]);
        obj.setValor(valor);
        obj.setValorTotal(valor);

        OperadoraCartaoVO operadoraCartaoVO = new OperadoraCartaoVO();
        operadoraCartaoVO.setCodigo(parseInt(item[3]));
        obj.setOperadora(operadoraCartaoVO);

        obj.setProdutosPagos(produtosPagos);
        obj.setNrParcela(parseInt(item[5]));
    }

    public String getInformacaoGerarCartaoCreditoProdutoDiariaImportacao() {
        return informacaoGerarCartaoCreditoProdutoDiariaImportacao;
    }

    public void setInformacaoGerarCartaoCreditoProdutoDiariaImportacao(String informacaoGerarCartaoCreditoProdutoDiariaImportacao) {
        this.informacaoGerarCartaoCreditoProdutoDiariaImportacao = informacaoGerarCartaoCreditoProdutoDiariaImportacao;
    }
    /*-----------------------------------------------------------------------------------*/

    public void ativarClientesBI() {
        try {
            Integer codigoEmpresa = getEmpresaAcesso().getCodigo();
            if (codigoEmpresa > 0) {
                getFacade().getCliente().ativarVerificacaoClientesAtivos(codigoEmpresa);

                setMsgAlert("Verificação de Clientes ATIVA");
            } else {
                setMsgAlert("Selecione uma Empresa!");
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    /*------------------ PROCESSO DE ATULIZAR MENSAGENS DO ALUNO-------------------------*/
    private String informacoesUsuario;
    private boolean parar = false;
    private boolean rodando = false;
    private List<Integer> clientes;
    private Integer posicao = 0;
    private List<ConfiguracaoSistemaCadastroClienteVO> listaCli;
    private List<ConfiguracaoSistemaCadastroClienteVO> listaVi;
    private LogProcessoSistemaVO logAtualizarMensagensAluno = null;


    public void abrirModalAtualizarMensagens() {
        try {
            if (clientes == null || clientes.isEmpty()) {
                posicao = 0;
                clientes = getFacade().getCliente().obterListaTodosClientes();
                informacoesUsuario = clientes.size() + " registros para processar.";
                listaCli = getFacade().getConfiguracaoSistemaCadastroCliente().consultar(false);
                listaVi = getFacade().getConfiguracaoSistemaCadastroCliente().consultar(true);
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public void resetar() {
        parar = true;
        rodando = false;
        setMsgAlert("");
        posicao = 0;
        clientes = null;
        abrirModalAtualizarMensagens();
    }

    public void iniciarProcesso() throws Exception {
        try {
            if (logAtualizarMensagensAluno == null) {
                logAtualizarMensagensAluno = criarLog("iniciarProcesso", getEmpresaLogado());
            }
        } catch (Exception ex) {
            Uteis.logar(null, "Falha ao criar o log de processo: iniciarProcesso ", ex);
        }
        getFacade().getConfiguracaoSistemaCadastroCliente().alterarConfiguracaoClienteSemCommit(getConfiguracaoSistemaVO());
        parar = false;
        rodando = true;
        setMsgAlert("processarProximoAluno();");
    }

    public void pararProcesso() {
        parar = true;
        rodando = false;
        setMsgAlert("");
    }

    public void processarAtual() {
        try {
            setMsgAlert("");
            if (parar) {
                return;
            }
            if (posicao >= clientes.size()) {
                informacoesUsuario = "Todos os alunos atualizados com sucesso!";
                parar = true;
                rodando = false;
                setMsgAlert("processarTerminou();");
                finalizarLogProcesso(logAtualizarMensagensAluno);
                logAtualizarMensagensAluno = null;
                return;
            }
            Integer codigoCliente = clientes.get(posicao);
            posicao++;
            getFacade().getCliente().gerarPendenciaCadastroCliente(codigoCliente, getUsuarioLogado(),
                    getConfiguracaoSistemaVO(),
                    listaCli, listaVi);
            setMsgAlert("processarProximoAluno();");
            informacoesUsuario = posicao + "/" + clientes.size() + " - Processou o aluno " + codigoCliente;
        } catch (Exception e) {
            informacoesUsuario = "Ocorreu um erro: " + e.getMessage();
        }

    }

    public String getInformacoesUsuario() {
        return informacoesUsuario;
    }

    public void setInformacoesUsuario(String informacoesUsuario) {
        this.informacoesUsuario = informacoesUsuario;
    }

    public boolean isRodando() {
        return rodando;
    }

    public void setRodando(boolean rodando) {
        this.rodando = rodando;
    }

    /*-------------------------------------------*/
    public void gravar() {
        try {

            setMsgAlert("");
            boolean exibirAlerta = verificandoCamposAlterados();
            //Se tiver configurado "Emitir nota fiscal no nome e CPF do responsável caso aluno seja menor de 18 anos" ele tem que abilitar as informações da mãe e pai para aparecer na tela.
            if (getConfiguracaoSistemaVO().isUsarNomeResponsavelNota()) {
                if (getConfiguracaoSistemaVO().getListaConfiguracaoCamposVisitante() != null) {
                    for (Object objCfgVisitasOriginal : getConfiguracaoSistemaVO().getListaConfiguracaoCamposVisitante()) {
                        if (((ConfiguracaoSistemaCadastroClienteVO) objCfgVisitasOriginal).getNome().equals("CPF Pai")) {
                            ((ConfiguracaoSistemaCadastroClienteVO) objCfgVisitasOriginal).setMostrar(true);
                        }
                        if (((ConfiguracaoSistemaCadastroClienteVO) objCfgVisitasOriginal).getNome().equals("CPF Mãe")) {
                            ((ConfiguracaoSistemaCadastroClienteVO) objCfgVisitasOriginal).setMostrar(true);
                        }
                        if (((ConfiguracaoSistemaCadastroClienteVO) objCfgVisitasOriginal).getNome().equals("Nome Mãe ou Responsável")) {
                            ((ConfiguracaoSistemaCadastroClienteVO) objCfgVisitasOriginal).setMostrar(true);
                        }
                        if (((ConfiguracaoSistemaCadastroClienteVO) objCfgVisitasOriginal).getNome().equals("Nome Pai ou Responsável")) {
                            ((ConfiguracaoSistemaCadastroClienteVO) objCfgVisitasOriginal).setMostrar(true);
                        }
                    }
                }
                for (Object objCfgVisitasOriginal : getConfiguracaoSistemaVO().getListaConfiguracaoCamposCliente()) {
                    if (((ConfiguracaoSistemaCadastroClienteVO) objCfgVisitasOriginal).getNome().equals("CPF Pai")) {
                        ((ConfiguracaoSistemaCadastroClienteVO) objCfgVisitasOriginal).setMostrar(true);
                    }
                    if (((ConfiguracaoSistemaCadastroClienteVO) objCfgVisitasOriginal).getNome().equals("CPF Mãe")) {
                        ((ConfiguracaoSistemaCadastroClienteVO) objCfgVisitasOriginal).setMostrar(true);
                    }
                    if (((ConfiguracaoSistemaCadastroClienteVO) objCfgVisitasOriginal).getNome().equals("Nome Mãe ou Responsável")) {
                        ((ConfiguracaoSistemaCadastroClienteVO) objCfgVisitasOriginal).setMostrar(true);
                    }
                    if (((ConfiguracaoSistemaCadastroClienteVO) objCfgVisitasOriginal).getNome().equals("Nome Pai ou Responsável")) {
                        ((ConfiguracaoSistemaCadastroClienteVO) objCfgVisitasOriginal).setMostrar(true);
                    }
                }
            }
            //precisa-se que a configuração já esteja definida no banco de dados com código '1'
            ConfiguracaoSistemaVO.validarDadosConfiguracaoCidadeEstadoPais(this.listaCamposColaboradorDinamico, "Campos Colaborador");
            getFacade().getConfiguracaoSistema().alterar(configuracaoSistemaVO);
            if (getConfiguracaoSistemaVO().isPermiteImpressaoContratoMutavel()){
                getFacade().getContratoTextoPadrao().anularContratosHtml();
            }
            getFacade().getCadastroDinamicoItem().alterar(this.listaCamposColaboradorDinamico);
            notificarOuvintes("updateConfiguracaoSistemaWS");
            //}
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);

            if (exibirAlerta) {
                setMsgAlert("Richfaces.showModalPanel('msgAlerta');");
            }
            boolean houveAlteracaoListaCampos = false;
            if (configuracaoSistemaVO.isNovoObj().booleanValue()) {
                try {
                    configuracaoSistemaVO.setObjetoVOAntesAlteracao(new ConfiguracaoSistemaVO());
                    configuracaoSistemaVO.setNovoObj(true);
                    registrarLogObjetoVO(configuracaoSistemaVO, configuracaoSistemaVO.getCodigo(), "CONFIGURACAOSISTEMA", 0);
                    if (getConfiguracaoSistemaVO().getListaConfiguracaoCamposCliente() != null && getConfiguracaoSistemaVO().getListaConfiguracaoCamposClienteOriginal() != null) {
                        registrarLogCamposCadastroClientesVisitantes(getConfiguracaoSistemaVO().getListaConfiguracaoCamposCliente(), getConfiguracaoSistemaVO().getListaConfiguracaoCamposClienteOriginal(), "ALTERAÇÃO - CAMPOS: CLIENTES");
                    }
                    if (getConfiguracaoSistemaVO().getListaConfiguracaoCamposVisitante() != null && getConfiguracaoSistemaVO().getListaConfiguracaoCamposVisitanteOriginal() != null) {
                        registrarLogCamposCadastroClientesVisitantes(getConfiguracaoSistemaVO().getListaConfiguracaoCamposVisitante(), getConfiguracaoSistemaVO().getListaConfiguracaoCamposVisitanteOriginal(), "ALTERAÇÃO - CAMPOS: VISITANTES");
                    }
                } catch (Exception e) {
                    registrarLogErroObjetoVO("CONFIGURACAOSISTEMA", configuracaoSistemaVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE CONFIGURAÇÃO SISTEMA", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
                    e.printStackTrace();
                }
            } else {
                try {
                    registrarLogObjetoVO(configuracaoSistemaVO, configuracaoSistemaVO.getCodigo(), "CONFIGURACAOSISTEMA", 0);
                    if (getConfiguracaoSistemaVO().getListaConfiguracaoCamposCliente() != null
                            && !getConfiguracaoSistemaVO().getListaConfiguracaoCamposCliente().isEmpty()
                            && getConfiguracaoSistemaVO().getListaConfiguracaoCamposClienteOriginal() != null) {
                        FORCFGCLI:
                        for (Object objCfgCliente : getConfiguracaoSistemaVO().getListaConfiguracaoCamposCliente()) {
                            ConfiguracaoSistemaCadastroClienteVO cfgCliente = (ConfiguracaoSistemaCadastroClienteVO) objCfgCliente;
                            for (Object objCfgClientesOriginal : getConfiguracaoSistemaVO().getListaConfiguracaoCamposClienteOriginal()) {
                                ConfiguracaoSistemaCadastroClienteVO cfgClienteOriginal = (ConfiguracaoSistemaCadastroClienteVO) objCfgClientesOriginal;
                                if (cfgCliente.getNome().equals(cfgClienteOriginal.getNome())
                                        && (cfgCliente.getPendente() != cfgClienteOriginal.getPendente()
                                        || cfgCliente.getObrigatorio() != cfgClienteOriginal.getObrigatorio()
                                        || cfgCliente.getMostrar() != cfgClienteOriginal.getMostrar())) {
                                    houveAlteracaoListaCampos = true;
                                    break FORCFGCLI;
                                }
                            }
                        }
                        registrarLogCamposCadastroClientesVisitantes(getConfiguracaoSistemaVO().getListaConfiguracaoCamposCliente(), getConfiguracaoSistemaVO().getListaConfiguracaoCamposClienteOriginal(), "ALTERAÇÃO - CAMPOS: CLIENTES");

                    }
                    if (getConfiguracaoSistemaVO().getListaConfiguracaoCamposVisitante() != null
                            && !getConfiguracaoSistemaVO().getListaConfiguracaoCamposVisitante().isEmpty()
                            && getConfiguracaoSistemaVO().getListaConfiguracaoCamposVisitanteOriginal() != null) {

                        FORCFGVISITAS:
                        for (Object objCfgVisitas : getConfiguracaoSistemaVO().getListaConfiguracaoCamposVisitante()) {
                            ConfiguracaoSistemaCadastroClienteVO cfgVisitas = (ConfiguracaoSistemaCadastroClienteVO) objCfgVisitas;
                            for (Object objCfgVisitasOriginal : getConfiguracaoSistemaVO().getListaConfiguracaoCamposVisitanteOriginal()) {
                                ConfiguracaoSistemaCadastroClienteVO cfgVisitasOriginal = (ConfiguracaoSistemaCadastroClienteVO) objCfgVisitasOriginal;
                                if (cfgVisitas.getNome().equals(cfgVisitasOriginal.getNome())
                                        && (cfgVisitas.getPendente() != cfgVisitasOriginal.getPendente()
                                        || cfgVisitas.getObrigatorio() != cfgVisitasOriginal.getObrigatorio()
                                        || cfgVisitas.getMostrar() != cfgVisitasOriginal.getMostrar())) {
                                    houveAlteracaoListaCampos = true;
                                    break FORCFGVISITAS;
                                }
                            }
                        }

                        registrarLogCamposCadastroClientesVisitantes(getConfiguracaoSistemaVO().getListaConfiguracaoCamposVisitante(), getConfiguracaoSistemaVO().getListaConfiguracaoCamposVisitanteOriginal(), "ALTERAÇÃO - CAMPOS: VISITANTES");

                    }
                    notificarRecursoConfiguracao();
                    configuracaoSistemaVOClone = (ConfiguracaoSistemaVO) configuracaoSistemaVO.getClone(true);
                } catch (Exception e) {
                    registrarLogErroObjetoVO("CONFIGURACAOSISTEMA", configuracaoSistemaVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE CONFIGURAÇÃO SISTEMA", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
                    e.printStackTrace();
                }
            }

            if (houveAlteracaoListaCampos && UteisValidacao.emptyNumber(posicao)) {
                setMensagemID("msg_dados_gravados_processo_pendencias");
                consultarConfiguracaoSistemaCadastroCliente();
                consultarConfiguracaoSistemaCadastroVisitantes();
                ThreadProcessarPendenciaAluno thread = new ThreadProcessarPendenciaAluno(new Cliente(Conexao.getInstance().obterNovaConexaoBaseadaOutra(Conexao.getFromSession())),
                        getConfiguracaoSistemaVO().getListaConfiguracaoCamposCliente(),
                        getConfiguracaoSistemaVO().getListaConfiguracaoCamposVisitante(), getConfiguracaoSistemaVO(),
                        getUsuarioLogado());
                thread.start();
            }

            getConfiguracaoSistemaVO().registrarObjetoVOAntesDaAlteracao();
            if (getConfiguracaoSistemaVO().getListaConfiguracaoCamposCliente() != null) {
                getConfiguracaoSistemaVO().setListaConfiguracaoCamposClienteOriginal(getClonarListaConfiguracaoSistemaCadastroClienteVO(getConfiguracaoSistemaVO().getListaConfiguracaoCamposCliente()));
            }
            if (getConfiguracaoSistemaVO().getListaConfiguracaoCamposVisitante() != null) {
                getConfiguracaoSistemaVO().setListaConfiguracaoCamposVisitanteOriginal(getClonarListaConfiguracaoSistemaCadastroClienteVO(getConfiguracaoSistemaVO().getListaConfiguracaoCamposVisitante()));
            }

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
//            return "editar";
        }

    }


    public String getNomeAlterados() {
        StringBuilder nomesAlterados = new StringBuilder();
        for (String nomes : alterados) {
            nomesAlterados.append("; ").append(nomes);
        }
        return nomesAlterados.toString().replaceFirst("; ", "");
    }

    public void removerEmail() {
        String email = (String) context().getExternalContext().getRequestMap().get("email");
        this.getConfiguracaoSistemaVO().getListaEmailsRecorrencia().remove(email);
    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP
     * ConfiguracaoSistemaCons.jsp. Define o tipo de consulta a ser executada,
     * por meio de ComboBox denominado campoConsulta, disponivel neste mesmo
     * JSP. Como resultado, disponibiliza um List com os objetos selecionados na
     * sessao da pagina.
     */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getConfiguracaoSistema().consultarPorCodigo(valorInt, true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("descricaoQuestionario")) {
                objs = getFacade().getConfiguracaoSistema().consultarPorDescricaoQuestionario(getControleConsulta().getValorConsulta(), Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("descricaoQuestionario")) {
                objs = getFacade().getConfiguracaoSistema().consultarPorDescricaoQuestionario(getControleConsulta().getValorConsulta(), Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("descricaoQuestionario")) {
                objs = getFacade().getConfiguracaoSistema().consultarPorDescricaoQuestionario(getControleConsulta().getValorConsulta(), Uteis.NIVELMONTARDADOS_TODOS);
            }
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            setMensagemID("msg_dados_consultados");
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "consultar";
        }
    }

    public void menuNovo() {
        todosDesabilitados();
        setMenuNovo(true);
    }

    public void basico() {
        try {
            consultarConfiguracaoSistemaCadastroVisitantes();
            setMenuCamposObrigatorioApresentarVisitantes(true);
            consultarConfiguracaoSistemaCadastroCliente();
            setMenuCamposObrigatorioApresentarPendentes(true);
            todosDesabilitados();
            setMenuBasico(true);
            configuracaoSistemaVOClone = (ConfiguracaoSistemaVO) configuracaoSistemaVO.getClone(true);
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InstantiationException e) {
            e.printStackTrace();
        }
    }

    public void questionario() {
        todosDesabilitados();
        setMenuQuestionario(true);
    }

    public void questionarioSessao() {
        todosDesabilitados();
        setMenuQuestionarioSessao(true);
    }

    public void camposObrigatorio() {
        todosDesabilitados();
        setMenuCamposObrigatorio(true);
    }

    public void camposObrigatorioApresentarPendentes() throws Exception {
        todosDesabilitados();
        consultarConfiguracaoSistemaCadastroCliente();
        setMenuCamposObrigatorioApresentarPendentes(true);
    }

    public void camposObrigatorioApresentarPendentesColaborador() throws Exception {
        todosDesabilitados();
        povoarTabelasCadastroDinamico();
        setMenuCamposObrigatorioApresentarColaborador(true);
    }

    public void camposObrigatorioApresentarVisitantes() throws Exception {
        todosDesabilitados();
        consultarConfiguracaoSistemaCadastroVisitantes();
        setMenuCamposObrigatorioApresentarVisitantes(true);
    }

    public void acesso() {
        consultarConfiguracaoSistemaCadastroVisitantes();
        setMenuCamposObrigatorioApresentarVisitantes(true);
        consultarConfiguracaoSistemaCadastroCliente();
        setMenuCamposObrigatorioApresentarPendentes(true);
        todosDesabilitados();
        setMenuAcesso(true);
    }

    public void robo() {
        todosDesabilitados();
        setMenuRobo(true);
    }

    public void manutencaoBI() {
        todosDesabilitados();
        setMenuManutencaoBI(true);

    }

    public void manutencaoTurma() {
        todosDesabilitados();
        setMenuManutencao(true);
    }

    public void manutencaoECF() {
        todosDesabilitados();
        setMenuEcf(true);
    }

    public void manutencaoMovProdutoParcela() {
        todosDesabilitados();
        setMenuMovProdutoParcelas(true);
        prepararMovProdutoParcelas();
        setTotalAProcessarParcelasCC(0);
        setTotalProcessadosParcelasCC(0);
        setVerificacao(new VerificarRecibosPagamentosSemVinculo(Conexao.getFromSession()));
    }

    public void manutencaoAjustesGerais() {
        todosDesabilitados();
        setMenuAjustesGerais(true);
        inicializarDadosOperacaoColetiva();
        this.manutencaoAjusteGeralTO = new ManutencaoAjusteGeralTO();
        this.manutencaoAjusteGeralTOSenhaAcessoCliente = new ManutencaoAjusteGeralTO();
        this.manutencaoAjusteGeralTOSenhaAcessoColaborador = new ManutencaoAjusteGeralTO();
        carregarInformacoesPanelBloquearClientes();
        carregarConvenios();
        carregarEmpresas();
        setErro(false);
        setSucesso(false);
        setMsgAlert("");
    }

    public Boolean getPermiteAcessarManutencaoUsuarioComum() throws Exception{
        try {
            return getUsuarioLogado().isPermiteExclusaoCliente() || permissao("PermitirExcluirSenhaAcessoCatraca");
        } catch (Exception ex){
            return false;
        }
    }

    public void manutencaoCliente() {
        todosDesabilitados();
        setManutencaoAjusteCliente(true);
        inicializarDadosOperacaoColetiva();
        this.manutencaoAjusteGeralTO = new ManutencaoAjusteGeralTO();
        this.manutencaoAjusteGeralTOSenhaAcessoCliente = new ManutencaoAjusteGeralTO();
        this.manutencaoAjusteGeralTOSenhaAcessoColaborador = new ManutencaoAjusteGeralTO();
        carregarInformacoesPanelBloquearClientes();
        carregarConvenios();
        carregarEmpresas();
    }

    public Boolean getPermiteExclusaoSenhaAcessoCatracaUsuarioComum() throws Exception{
        try {
            return permissao("PermitirExcluirSenhaAcessoCatraca");
        } catch (Exception ex){
            return false;
        }
    }
    public void openModalExcluirSenhaAcessoCatraca() {
        todosDesabilitados();
        setManutencaoExcluirSenhaAcessoCatraca(true);
    }

    public void manutencaoPactoPay() {
        todosDesabilitados();
        setMenuPactoPay(true);
        inicializarDadosOperacaoColetiva();
        this.manutencaoAjusteGeralTO = new ManutencaoAjusteGeralTO();
        this.manutencaoAjusteGeralTOSenhaAcessoCliente = new ManutencaoAjusteGeralTO();
        this.manutencaoAjusteGeralTOSenhaAcessoColaborador = new ManutencaoAjusteGeralTO();
        carregarConvenios();
        carregarEmpresas();
    }

    public void manutencaoConcilicao() {
        todosDesabilitados();
        setMenuConciliacao(true);
    }

    private void carregarConvenios() {
        try {
            setListaConveniosCobranca(new ArrayList<>());
            setListaConveniosCobranca(getFacade().getConvenioCobranca().consultarTodos(Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void carregarEmpresas() {
        try {
            setListaEmpresasCadastradas(new ArrayList<>());
            setListaEmpresasCadastradas(getFacade().getEmpresa().consultarTodas(null, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public void importacao() {
        todosDesabilitados();
        setImportacao(true);
    }

    public void updateClienteSituacao() {
        todosDesabilitados();
        setUpdateClienteSituacao(true);
    }

    public void migradorMOVProdutoModalidade() {
        todosDesabilitados();
        setMigradorMOVProdutoModalidade(true);
    }

    public void ajustarLinhaDoTempoDeContratos() {
        todosDesabilitados();
        setAjustarLinhaDoTempoDeContratos(true);
    }

    public void ajustarDatasRenovacaoImportacao() {
        todosDesabilitados();
        setAjustarDatasRenovacaoImportacao(true);
    }

    public void manutencaoHistoricoVinculo() {
        todosDesabilitados();
        setMenuHistoricoVinculo(true);
        setTotalAProcessarVinculoProfessor(0);
        setTotalProcessadosVinculoProfessor(0);
        setPollHistoricoVinculo(false);
    }

    public void manutencaoExclusaoBVsPendentes() {
        todosDesabilitados();
        setMenuExclusaoMensagensBVsPendentes(true);
    }

    public void manutencaofaseCrm() {
        todosDesabilitados();
        setManutencaoFasecrm(true);
    }

    public void recorrencia() {
        todosDesabilitados();
        setMenuRecorrencia(true);
        consultarInformacoesCobranca();
    }

    public void outros() {
        todosDesabilitados();
        consultarConfiguracaoSistemaCadastroVisitantes();
        setMenuCamposObrigatorioApresentarVisitantes(true);
        consultarConfiguracaoSistemaCadastroCliente();
        setMenuCamposObrigatorioApresentarPendentes(true);
        setMenuOutros(true);
    }

    public void email() {
        todosDesabilitados();
    }

    public void manutencaoContratos() {
        todosDesabilitados();
        setMenuContrato(true);
    }

    private void todosDesabilitados() {
        setMenuAcesso(false);
        setMenuNovo(false);
        setMenuBasico(false);
        setMenuQuestionario(false);
        setMenuQuestionarioSessao(false);
        setMenuCamposObrigatorio(false);
        setMenuCamposObrigatorioApresentarPendentes(false);
        setMenuCamposObrigatorioApresentarVisitantes(false);
        setMenuCamposObrigatorioApresentarColaborador(false);

        setMenuRobo(false);
        setMenuContrato(false);
        setMenuOutros(false);
        setMenuManutencaoBI(false);
        setMenuManutencao(false);
        setMenuEcf(false);
        setMenuRecorrencia(false);
        setMenuMovProdutoParcelas(false);
        setMenuHistoricoVinculo(false);
        setMenuExclusaoMensagensBVsPendentes(false);
        setMenuAjustesGerais(false);
        setMenuPactoPay(false);
        setImportacao(false);
        setUpdateClienteSituacao(false);
        setAjustarLinhaDoTempoDeContratos(false);
        setMenuProcessos(false);
        setManutencaoAjusteCliente(false);
        setManutencaoExcluirSenhaAcessoCatraca(false);
        setManutencaoFasecrm(false);
        setMenuConciliacao(false);
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe
     * <code>ConfiguracaoSistemaVO</code> Após a exclusão ela automaticamente
     * aciona a rotina para uma nova inclusão.
     */
    public String excluir() {
        try {
            getFacade().getConfiguracaoSistema().excluir(configuracaoSistemaVO);
            setConfiguracaoSistemaVO(new ConfiguracaoSistemaVO());
            setMensagemID("msg_dados_excluidos");
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "editar";
        }

    }

    public void executarFase() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("executarFase", getEmpresaLogado());
            new AberturaMetaControle().fecharMetas(Uteis.somarDias(Calendario.hoje(), -1));
            new AberturaMetaControle().abrirMetasEspecifica(Calendario.hoje(), FasesCRMEnum.getFase(getFasecrm()));
            setMsgAlert("alert('Fase ajustada com sucesso!')");
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void verificarPgtoParcelaSemRecibo() throws Exception {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("verificarPgtoParcelaSemRecibo", getEmpresaLogado());
            getVerificacao().obterPgtosParcelasSemRecibo();
            montarMensagemAlert(getVerificacao().getPgtosParcelasSemRecibo());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void verificarPgtoSemProdutosPagos() throws Exception {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("verificarPgtoSemProdutosPagos", getEmpresaLogado());
            getVerificacao().obterPgtosSemProdutosPagos();
            montarMensagemAlert(getVerificacao().getPgtosSemProdutosPagos());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void verificarCorrigirCarenciaContrato() {
        try {
            this.manutencaoAjusteGeralTO.setMsgResultado("");
            if (this.manutencaoAjusteGeralTO.getCodigoPlano() <= 0) {
                this.manutencaoAjusteGeralTO.setMsgResultado("Informe o código do Plano.");
            } else {
                this.manutencaoAjusteGeralTO.setTotalContratos(getFacade().getContrato().consultarTotalContratos(this.manutencaoAjusteGeralTO.getCodigoPlano()));
                montarSucesso("");
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void processarPix() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("processarPix", getEmpresaLogado());
            setMsgAlert("");
            setErro(false);
            PixScheduleService pixScheduleService = new PixScheduleService(getKey());
            listaPixProcessados = pixScheduleService.processarCobrancas();
            pixScheduleService = null;
        } catch (Exception e) {
            setErro(true);
            setMsgAlert("Erro ao processsar cobranças pix: " + e.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void ajustarDataCompensacaoPixPjBank() {
        PixService pixService;
        Connection connection;
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("ajustarDataCompensacaoPixPjBank", getEmpresaLogado());
            connection = Conexao.getFromSession();
            pixService = new PixService(connection);

            setMsgAlert("");
            setErro(false);

            StringBuilder sql = new StringBuilder();
            sql.append(" select p.codigo, p.txid, mp.codigo as codMovPagamento, p.conveniocobranca from pix p \n");
            sql.append(" inner join conveniocobranca c on c.codigo = p.conveniocobranca \n");
            sql.append(" inner join movpagamento mp on mp.recibopagamento = p.recibopagamento \n");
            sql.append(" where p.status  = '" + PixStatusEnum.CONCLUIDA.getDescricao() + "' \n");
            sql.append(" and c.tipoconvenio = " + TipoConvenioCobrancaEnum.PIX_PJBANK.getCodigo() + " \n");
            sql.append(" and p.\"data\" between '" + Uteis.getDataFormatoBD(manutencaoAjusteGeralTO.getProcessarDataCompensacaoPjbankAPartirDe()) + " 00:00:00.000'");
            sql.append(" and '" + Uteis.getDataFormatoBD(manutencaoAjusteGeralTO.getProcessarDataCompensacaoPjbankAte()) + " 23:59:59.000'");

            List<PixVO> listaPix = getFacade().getPix().consultarParaProcesso(sql.toString());
            int qtdSucesso = 0;
            int qtdErro = 0;

            if (UteisValidacao.emptyList(listaPix)) {
                throw new Exception("Não encontrei nenhum pix PJBANK pago apto para processar");
            }

            for (PixVO pixVO : listaPix) {
                try {
                    PixRequisicaoDto pixRequisicaoDto = pixService.consultarCobranca(pixVO);
                    if (pixRequisicaoDto != null && !UteisValidacao.emptyString(pixRequisicaoDto.getResposta())) {
                        JSONObject jsonObject = new JSONArray(pixRequisicaoDto.getResposta()).getJSONObject(0);
                        if (!UteisValidacao.emptyString(jsonObject.optString("data_credito"))) {
                            Date dataCredito = new Date(jsonObject.optString("data_credito"));
                            getFacade().getMovPagamento().alterarDataCompensacao(pixVO.getMovPagamentoVO().getCodigo(), dataCredito);
                            qtdSucesso++;
                        }

                    }
                } catch (Exception ex) {
                    qtdErro++;
                }
            }
            setErro(false);
            StringBuilder resposta = new StringBuilder();
            resposta.append("Encontrei " + listaPix.size() + " pix para processar... \n");
            resposta.append("Pix processados com Sucesso: " + qtdSucesso + "\n");
            resposta.append("Pix processados com Erro: " + qtdErro + "\n");
            setMsgAlert(resposta.toString());
        } catch (Exception e) {
            setErro(true);
            setMsgAlert("Erro ao rodar o processo: " + e.getMessage());
        } finally {
            finalizarLogProcesso(log);
            pixService = null;
            connection = null;
        }
    }

    public void verificarQtdBoletosEstornados() {
        try {
            setExibirBtnCorrigir(false);
            setListaBoletos(new ArrayList<>());

            StringBuilder sql = new StringBuilder();
            sql.append(" select b.codigo, b.idexterno, b.conveniocobranca, b.empresa , b.numerointerno  \n");
            sql.append(" from boleto b  \n");
            sql.append(" inner join conveniocobranca c on c.codigo = b.conveniocobranca  \n");
            sql.append(" where b.situacao  = " + SituacaoBoletoEnum.ESTORNADO.getCodigo() + " \n");
            sql.append(" and c.tipoconvenio = " + TipoConvenioCobrancaEnum.BOLETO_PJBANK.getCodigo());
            sql.append(" and idexterno <> '' ");

            setListaBoletos(getFacade().getBoleto().consultarParaProcesso(sql.toString()));

            if (UteisValidacao.emptyList(getListaBoletos())) {
                setErro(true);
                setExibirBtnCorrigir(false);
                setMsgAlert("Não encontrei nenhum boleto estornado no sistema.");
            } else {
                setErro(false);
                setExibirBtnCorrigir(true);
            }

        } catch (Exception e) {
            setErro(true);
            setMsgAlert("Erro ao pesquisar os boletos estornados: " + e.getMessage());
            setExibirBtnCorrigir(false);
        }
    }

    public void ajustarSituacaoBoletosEstornadosPjBank() {
        Connection con;
        PJBankClient client;
        PJBankService service;
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("processarNotas", getEmpresaLogado());
            con = Conexao.getFromSession();
            setMsgAlert("");
            setErro(false);

            UsuarioVO usuarioVO = getUsuarioRecorrencia();
            int qtdBoletosEstornadosEstavamPagosSucesso = 0;
            int qtdBoletosEstornadosEstavamPagosErro = 0;
            int qtdBoletosEstornadosForamCanceladosSucesso = 0;
            int qtdBoletosEstornadosForamCanceladosErro = 0;
            int qtdBoletosEstornadosJaEstavamCancelados = 0;
            int naoConseguiuObterRetornoDaApi = 0;

            for (BoletoVO boleto : getListaBoletos()) {
                String resposta = "";
                try {
                    client = new PJBankClient("recebimentos/" + boleto.getConvenioCobrancaVO().getCredencialPJBank() + "/transacoes/" + boleto.getIdExterno(), AmbienteEnum.PRODUCAO);
                    HttpGet httpGet = client.getHttpGetClient();
                    httpGet.addHeader("x-chave", boleto.getConvenioCobrancaVO().getChavePJBank());

                    HttpResponse response = client.doRequest(httpGet);
                    resposta = EntityUtils.toString(response.getEntity());
                } catch (Exception e) {
                    client = null;
                }

                if (resposta.equals("[]")) {
                    //aqui quer dizer que o boleto já está cancelado na pjbank, não precisa fazer nada
                    qtdBoletosEstornadosJaEstavamCancelados++;
                    continue;
                }

                JSONArray jsonArray;
                JSONObject jsonRetorno;
                try {
                    jsonArray = new JSONArray(resposta);
                    jsonRetorno = jsonArray.optJSONObject(0);
                } catch (Exception ignore) {
                    naoConseguiuObterRetornoDaApi++;
                    continue;
                }

                //está estornado no sistema mas foi pago, gerar crédito em conta corrente
                if (!UteisValidacao.emptyString(jsonRetorno.optString("data_pagamento"))) {
                    service = new PJBankService(con, boleto.getEmpresaVO().getCodigo(), boleto.getConvenioCobrancaVO().getCodigo());
                    try {
                        BoletoVO boletoCompleto = getFacade().getBoleto().consultarPorChavePrimaria(boleto.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        boletoCompleto.setSituacao(SituacaoBoletoEnum.PAGO);
                        boletoCompleto.setDataPagamento(Uteis.getDate(jsonRetorno.optString("data_pagamento"), "MM/dd/yyyy"));
                        boletoCompleto.setDataCredito(Uteis.getDate(jsonRetorno.optString("data_credito"), "MM/dd/yyyy"));
                        boletoCompleto.setValorPago(Double.valueOf(jsonRetorno.optString("valor_pago")));
                        service.gerarPagamentoBoleto(boletoCompleto, usuarioVO, false, true);
                        qtdBoletosEstornadosEstavamPagosSucesso++;
                    } catch (Exception ignore) {
                        qtdBoletosEstornadosEstavamPagosErro++;
                    }
                } else {  //se não tá cancelado, não tá pago, então cancelar
                    try {
                        service = new PJBankService(con, boleto.getEmpresaVO().getCodigo(), boleto.getConvenioCobrancaVO().getCodigo());
                        service.cancelar(boleto, usuarioVO, "Cancelamento pelo ajuste do processo geral", true);
                        qtdBoletosEstornadosForamCanceladosSucesso++;
                    } catch (Exception e) {
                        qtdBoletosEstornadosForamCanceladosErro++;
                    }
                }
            }

            setErro(false);
            StringBuilder resposta = new StringBuilder();
            resposta.append("Verifiquei um a um, os " + getListaBoletos().size() + " boletos estornados... Veja o resultado dos boletos abaixo. \n");
            resposta.append("Qtd. estornados que estavam pagos e geraram crédito (Sucesso): " + qtdBoletosEstornadosEstavamPagosSucesso + "\n");
            resposta.append("Qtd. estornados que estavam pagos e geraram crédito (Erro): " + qtdBoletosEstornadosEstavamPagosErro + "\n");
            resposta.append("Qtd. estornados que não estavam cancelados e foram cancelados (Sucesso): " + qtdBoletosEstornadosForamCanceladosSucesso + "\n");
            resposta.append("Qtd. estornados que não estavam cancelados e foram cancelados (Erro): " + qtdBoletosEstornadosForamCanceladosErro + "\n");
            resposta.append("Qtd. estornados que já estavam cancelados: " + qtdBoletosEstornadosJaEstavamCancelados + "\n");
            resposta.append("Qtd. não conseguiu obter retorno da consulta da api: " + naoConseguiuObterRetornoDaApi + "\n");
            setMsgAlert(resposta.toString());
        } catch (Exception e) {
            setErro(true);
            setMsgAlert("Erro ao rodar o processo: " + e.getMessage());
        } finally {
            finalizarLogProcesso(log);
            con = null;
            client = null;
            service = null;
        }
    }

    public void processarIdsExternosBoletosPJBank() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("processarIdsExternosBoletosPJBank", getEmpresaLogado());
            ProcessarBoletosPJBankEngenharia.buscarInformacoesBoletosPJBank(Conexao.getFromSession());
            setSucesso(true);
            setErro(false);
            setMsgAlert("Terminou a execução do processo!");
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void ajustarNomeCidadesCaracteresInvalidos() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("ajustarNomeCidadesCaracteresInvalidos", getEmpresaLogado());
            ProcessoAjustarNomeCidades.corrigirCidadesComNomeErrado(Conexao.getFromSession());
            setSucesso(true);
            setErro(false);
            setMsgAlert("Terminou a execução do processo!");
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public int getQuantidadeRegistrosAfetadosExclusaoSenhaAcessoCatraca() {
        try {
            return ProcessoExcluirSenhaAcessoCatraca.contarRegistrosAfetados(Conexao.getFromSession(), isExcluirSenhaAcessoCatracaColaboradores(), isExcluirSenhaAcessoCatracaAlunos());
        } catch (Exception ex){
            return 0;
        }
    }

    public void validarDadosExcluirSenhaAcessoCatraca() {
        try {
            limparMsg();
            if (!isExcluirSenhaAcessoCatracaAlunos() && !isExcluirSenhaAcessoCatracaColaboradores()){
                setMsgAlert("Selecione ao menos um tipo de senha para ser excluída.");
                throw new Exception("Selecione ao menos um tipo de senha para ser excluída.");
            }
            setOnComplete("Richfaces.showModalPanel('modalConfirmarExclusaoSenhaAcessoCatraca')");
        } catch (Exception ex) {
            Logger.getLogger(ConfiguracaoSistemaControle.class.getName()).log(Level.SEVERE, null, ex);
            montarErro(ex);
        }
    }

    public void excluirSenhaAcessoCatraca() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("excluirSenhaAcessoCatraca", getEmpresaLogado());

            ProcessoExcluirSenhaAcessoCatraca.excluirSenhaAcessoCatraca(Conexao.getFromSession(), isExcluirSenhaAcessoCatracaColaboradores(), isExcluirSenhaAcessoCatracaAlunos());
            String operacao = "Excluir senhas de acesso - Catraca de";
            if (isExcluirSenhaAcessoCatracaColaboradores()){
                operacao += ", Colaboradores";
            }
            if (isExcluirSenhaAcessoCatracaAlunos()){
                operacao += ", Alunos";
            }
            operacao = operacao.replaceFirst(",","");
            registrarLog(getUsuarioLogado(),
                    operacao.toUpperCase(), "CONFIGURACAOSISTEMA", "Excluir senhas de acesso - Catraca", "senhaAcesso", "", "Excluído");
            setSucesso(true);
            setErro(false);
            setMsgAlert("Terminou a execução do processo!");
        } catch (Exception ex) {
            Logger.getLogger(ConfiguracaoSistemaControle.class.getName()).log(Level.SEVERE, null, ex);
            montarErro(ex);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    private void registrarLog(UsuarioVO usuarioSessao,
                              String operacao, String entidade, String entidadeDescricao, String nomeCampo, String valorAnterior, String valorAlterado) throws Exception {
        LogVO log = new LogVO();
        log.setOperacao(operacao);
        log.setChavePrimaria(configuracaoSistemaVO.getCodigo().toString());
        if (usuarioSessao == null) {
            log.setResponsavelAlteracao("PROCESSO EXCLUIR SENHAS DE ACESSO");
        } else {
            log.setResponsavelAlteracao(usuarioSessao.getNome());
            log.setUserOAMD(usuarioSessao.getUserOamd());
        }
        log.setNomeEntidade(entidade);
        log.setNomeEntidadeDescricao(entidadeDescricao);
        log.setDataAlteracao(Calendario.hoje());
        log.setNomeCampo(nomeCampo);
        log.setValorCampoAnterior(valorAnterior);
        log.setValorCampoAlterado(valorAlterado);
        log.setPessoa(configuracaoSistemaVO.getCodigo());
        getFacade().getLog().incluirSemCommit(log);
    }

    public void reprocessarExtratos() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("reprocessarExtratos", getEmpresaLogado());
            this.manutencaoAjusteGeralTO.setMsgResultado("");
            if (this.manutencaoAjusteGeralTO.getReprocessarAPartirDe() == null) {
                this.manutencaoAjusteGeralTO.setSucesso(false);
                this.manutencaoAjusteGeralTO.setMsgResultado("Informe a data inicial antes de reprocessar.");
            } else {
                Date reprocessarFim;
                reprocessarFim = this.manutencaoAjusteGeralTO.getReprocessarAte() == null ? Calendario.hoje() : this.manutencaoAjusteGeralTO.getReprocessarAte();
                if (this.manutencaoAjusteGeralTO.getReprocessarAte() != null) {
                    if (Calendario.maior(this.manutencaoAjusteGeralTO.getReprocessarAPartirDe(), reprocessarFim)) {
                        this.manutencaoAjusteGeralTO.setSucesso(false);
                        this.manutencaoAjusteGeralTO.setMsgResultado("Data final maior que a inicial, verifique!");
                        return;
                    } else {
                        if (Calendario.diferencaEmDias(this.manutencaoAjusteGeralTO.getReprocessarAPartirDe(), reprocessarFim) > 31) {
                            this.manutencaoAjusteGeralTO.setSucesso(false);
                            this.manutencaoAjusteGeralTO.setMsgResultado("Período não deve ter mais que 31 dias!");
                            return;
                        }
                    }
                }
                Date datainicio = Calendario.hoje();
                Uteis.logarDebug("Pessoa executou Reprocessar Extrato: " + getNomeUsuarioLogado());
                Uteis.logarDebug("Hora executou Reprocessar Extrato: " + Calendario.getData(Calendario.hoje(), "dd/MM/yyyy HH:mm:ss"));
                ReprocessarExtratos reprocessarExtratos = new ReprocessarExtratos(this.manutencaoAjusteGeralTO.getReprocessarAPartirDe(), this.manutencaoAjusteGeralTO.getReprocessarAte());
                reprocessarExtratos.reprocessarExtratos(getKey(), false);
                Date datafim = Calendario.hoje();
                this.manutencaoAjusteGeralTO.setSucesso(true);
                this.manutencaoAjusteGeralTO.setMsgResultado("Extratos reprocessados com sucesso. Iniciado: " + Calendario.getData(datainicio, "dd/MM/yyyy HH:mm:ss") + ", Finalizado: " + Calendario.getData(datafim, "dd/MM/yyyy HH:mm:ss"));
                Uteis.logarDebug("Hora finalizou Reprocessar Extrato: " + Calendario.getData(Calendario.hoje(), "dd/MM/yyyy HH:mm:ss"));
            }
        } catch (Exception e) {
            this.manutencaoAjusteGeralTO.setSucesso(false);
            this.manutencaoAjusteGeralTO.setMsgResultado(e.getMessage());
            e.printStackTrace();
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void processoTransacoesInconsistentesStone() {
        Connection con;
        LogProcessoSistemaVO log = null;
        try {
            this.manutencaoAjusteGeralTO.setMsgResultado("");
            if (this.manutencaoAjusteGeralTO.getDataInicioProcessoStone() == null) {
                this.manutencaoAjusteGeralTO.setSucesso(false);
                this.manutencaoAjusteGeralTO.setMsgResultado("Informe a data inicial antes de prosseguir.");
            }
            if (this.manutencaoAjusteGeralTO.getDataFimProcessoStone() == null) {
                this.manutencaoAjusteGeralTO.setSucesso(false);
                this.manutencaoAjusteGeralTO.setMsgResultado("Informe a data final antes de prosseguir.");
            }
            if (this.manutencaoAjusteGeralTO.getDataInicioProcessoStone() != null) {
                if (Calendario.maiorOuIgual(this.manutencaoAjusteGeralTO.getDataInicioProcessoStone(), Calendario.hoje())) {
                    this.manutencaoAjusteGeralTO.setSucesso(false);
                    this.manutencaoAjusteGeralTO.setMsgResultado("Data inicial não pode ser hoje e nem maior que hoje! Só permite no máximo até ontem!");
                    return;
                }
            }
            if (this.manutencaoAjusteGeralTO.getDataFimProcessoStone() != null) {
                if (Calendario.maiorOuIgual(this.manutencaoAjusteGeralTO.getDataFimProcessoStone(), Calendario.hoje())) {
                    this.manutencaoAjusteGeralTO.setSucesso(false);
                    this.manutencaoAjusteGeralTO.setMsgResultado("Data final não pode ser hoje e nem maior que hoje! Só permite no máximo até ontem!");
                    return;
                } else if (Calendario.maior(this.manutencaoAjusteGeralTO.getDataInicioProcessoStone(), this.manutencaoAjusteGeralTO.getDataFimProcessoStone())) {
                    this.manutencaoAjusteGeralTO.setSucesso(false);
                    this.manutencaoAjusteGeralTO.setMsgResultado("Data final não pode ser menor que a inicial!");
                    return;
                } else if (Calendario.diferencaEmDias(this.manutencaoAjusteGeralTO.getDataInicioProcessoStone(), this.manutencaoAjusteGeralTO.getDataFimProcessoStone()) > 31) {
                    this.manutencaoAjusteGeralTO.setSucesso(false);
                    this.manutencaoAjusteGeralTO.setMsgResultado("Período não deve ter mais que 31 dias!");
                    return;
                }
            }

            log = criarLog("processoTransacoesInconsistentesStone", getEmpresaLogado());
            con = Conexao.getInstance().obterNovaConexaoBaseadaOutra(Conexao.getFromSession());
            String retorno = ProcessoIdentificarCobrancasStoneAprovadasInterrogacao.processarOrigemSistemaPacto(con,
                    Calendario.getDataAplicandoFormatacao(this.manutencaoAjusteGeralTO.getDataInicioProcessoStone(), "yyyy-MM-dd"),
                    Calendario.getDataAplicandoFormatacao(this.manutencaoAjusteGeralTO.getDataFimProcessoStone(), "yyyy-MM-dd"));
            this.manutencaoAjusteGeralTO.setSucesso(true);
            this.manutencaoAjusteGeralTO.setMsgResultado(retorno);
        } catch (Exception e) {
            this.manutencaoAjusteGeralTO.setSucesso(false);
            this.manutencaoAjusteGeralTO.setMsgResultado(e.getMessage());
            e.printStackTrace();
        } finally {
            finalizarLogProcesso(log);
            con = null;
        }
    }

    public void reprocessarMovimentacaoAutomaticaConciliacao() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("reprocessarMovimentacaoAutomaticaConciliacao", getEmpresaLogado());
            ConfiguracaoFinanceiro configuracaoFinanceiro = new ConfiguracaoFinanceiro(Conexao.getFromSession());
            ConfiguracaoFinanceiroVO configuracaoFinanceiroVO = configuracaoFinanceiro.consultar();

            if(!configuracaoFinanceiroVO.isMovimentacaoAutomaticaRecebiveisConciliacao()){
                throw new Exception("Empresa não possui configuração de conciliação automatica habilitada!");
            }
            this.manutencaoAjusteGeralTO.setMsgResultado("");
            if (this.manutencaoAjusteGeralTO.getReprocessarMovimentacaoAutomaticaConciliacaoAPartirDe() == null) {
                this.manutencaoAjusteGeralTO.setSucesso(false);
                this.manutencaoAjusteGeralTO.setMsgResultado("Informe a data inicial antes de reprocessar.");
            } else {
                Date reprocessarFim = this.manutencaoAjusteGeralTO.getReprocessarMovimentacaoAutomaticaConciliacaoAte() == null
                        ? Calendario.hoje() : this.manutencaoAjusteGeralTO.getReprocessarMovimentacaoAutomaticaConciliacaoAte();
                if (this.manutencaoAjusteGeralTO.getReprocessarMovimentacaoAutomaticaConciliacaoAte() != null) {
                    if (Calendario.maior(this.manutencaoAjusteGeralTO.getReprocessarMovimentacaoAutomaticaConciliacaoAPartirDe(), reprocessarFim)) {
                        this.manutencaoAjusteGeralTO.setSucesso(false);
                        this.manutencaoAjusteGeralTO.setMsgResultado("Data final maior que a inicial, verifique!");
                        return;
                    } else {
                        if (Calendario.diferencaEmDias(this.manutencaoAjusteGeralTO.getReprocessarMovimentacaoAutomaticaConciliacaoAPartirDe(), reprocessarFim) > 31) {
                            this.manutencaoAjusteGeralTO.setSucesso(false);
                            this.manutencaoAjusteGeralTO.setMsgResultado("Período não deve ter mais que 31 dias!");
                            return;
                        }
                    }
                }
                Date datainicio = Calendario.hoje();
                Uteis.logarDebug("Usuario que executou Reprocessar Movimentação Automática da Conciliação: " + getNomeUsuarioLogado());
                Uteis.logarDebug("Início executou Reprocessar Movimentação Automática da Conciliação: " + Calendario.getData(datainicio, "dd/MM/yyyy HH:mm:ss"));

                ExtratoDiarioService extratoDiarioService = new ExtratoDiarioService();

                extratoDiarioService.processarMovimentacaoAutomaticaRecebiveisConciliacao(this.manutencaoAjusteGeralTO.getReprocessarMovimentacaoAutomaticaConciliacaoAPartirDe(),
                        this.manutencaoAjusteGeralTO.getReprocessarMovimentacaoAutomaticaConciliacaoAte(), Conexao.getFromSession());

                this.manutencaoAjusteGeralTO.setSucesso(true);
                this.manutencaoAjusteGeralTO.setMsgResultado("Reprocessar Movimentação Automática da Conciliação --> SUCESSO");
            }
        } catch (Exception e) {
            this.manutencaoAjusteGeralTO.setSucesso(false);
            this.manutencaoAjusteGeralTO.setMsgResultado(e.getMessage());
            e.printStackTrace();
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void realizarExclusaoContasPagarReceber() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("realizarExclusaoContasPagarReceber", getEmpresaLogado());
            this.manutencaoAjusteGeralTO.setMsgResultado("");
            if (this.manutencaoAjusteGeralTO.getExclusaoAPartirDe() == null) {
                this.manutencaoAjusteGeralTO.setSucesso(false);
                this.manutencaoAjusteGeralTO.setMsgResultado("Informe a data inicial antes de processar.");
            } else {
                Date processarFim;
                processarFim = this.manutencaoAjusteGeralTO.getExclusaoAte() == null ? Calendario.hoje() : this.manutencaoAjusteGeralTO.getExclusaoAte();
                if (this.manutencaoAjusteGeralTO.getExclusaoAte() != null) {
                    if (Calendario.maior(this.manutencaoAjusteGeralTO.getExclusaoAPartirDe(), processarFim)) {
                        this.manutencaoAjusteGeralTO.setSucesso(false);
                        this.manutencaoAjusteGeralTO.setMsgResultado("Data final maior que a inicial, verifique!");
                        return;
                    } else {
                        if (Calendario.diferencaEmDias(this.manutencaoAjusteGeralTO.getExclusaoAPartirDe(), processarFim) > 93) {
                            this.manutencaoAjusteGeralTO.setSucesso(false);
                            this.manutencaoAjusteGeralTO.setMsgResultado("Período não deve ter mais que 93 dias!");
                            return;
                        }
                    }
                }
                Date dataIniciado = Calendario.hoje();
                Uteis.logarDebug("Usuario que executou Processar Exclusão Contas Pagar/Receber: " + getNomeUsuarioLogado());
                Uteis.logarDebug("Início executou Processar Exclusão Contas Pagar/Receber: " + Calendario.getData(dataIniciado, "dd/MM/yyyy HH:mm:ss"));

                ProcessarExclusaoContasPagarReceber processar = new ProcessarExclusaoContasPagarReceber(
                        this.manutencaoAjusteGeralTO.getExclusaoAPartirDe(), this.manutencaoAjusteGeralTO.getExclusaoAte(), this.manutencaoAjusteGeralTO.isExcluirMovContaTodosTipoOperacaoLancamento(), getFacade().getMovConta().getCon());
                processar.processarExclusaoContasPagarReceber();
                Date dataFinalizado = Calendario.hoje();
                this.manutencaoAjusteGeralTO.setSucesso(true);
                this.manutencaoAjusteGeralTO.setMsgResultado("Processo executado com sucesso. Iniciado: " + Calendario.getData(dataIniciado, "dd/MM/yyyy HH:mm:ss") + ", Finalizado: " + Calendario.getData(dataFinalizado, "dd/MM/yyyy HH:mm:ss"));

                Uteis.logarDebug("Fim executou Processar Exclusão Contas Pagar/Receber: " + Calendario.getData(dataFinalizado, "dd/MM/yyyy HH:mm:ss"));
            }
        } catch (Exception e) {
            this.manutencaoAjusteGeralTO.setSucesso(false);
            this.manutencaoAjusteGeralTO.setMsgResultado(e.getMessage());
            e.printStackTrace();
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void verificarAtualizarSinteticoCliente() {
        try {
            this.manutencaoAjusteGeralTO.setMsgResultado("");
            if (this.manutencaoAjusteGeralTO.getClienteVO().getCodigo() <= 0) {
                this.manutencaoAjusteGeralTO.setMsgResultado("Informe o código do Cliente.");
            } else {
                ClienteVO clienteVO = getFacade().getCliente().consultarPorCodigo(this.manutencaoAjusteGeralTO.getClienteVO().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (clienteVO == null) {
                    this.manutencaoAjusteGeralTO.setMsgResultado("Cliente nao encontrado.");
                } else {
                    manutencaoAjusteGeralTO.setClienteVO(clienteVO);
                    //this.manutencaoAjusteGeralTO.setMsgResultado("Cliente: " + clienteVO.getPessoa().getNome());
                }

                montarSucesso("");
            }
        } catch (Exception e) {
            this.manutencaoAjusteGeralTO.setSucesso(false);
            this.manutencaoAjusteGeralTO.setMsgResultado(e.getMessage());
            e.printStackTrace();
        }
    }

    public void verificarAtualizarImpressaoContrato() {
        try {
            this.manutencaoAjusteGeralTO.setNomePessoa("");;
            this.manutencaoAjusteGeralTO.setMsgResultado("");
            if (this.manutencaoAjusteGeralTO.getAtualizarImpressaoContratoNaoAssinado()) {
                List<ContratoVO> contratoVOS = getFacade().getContrato().consultarContratosAtivosNaoAssinados(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (UteisValidacao.emptyList(contratoVOS)) {
                    this.manutencaoAjusteGeralTO.setMsgResultado("Não existem contratos elegíveis para ação solicitada.");
                } else {
                    StringBuilder nomes = new StringBuilder();
                    for (ContratoVO contratoVO : contratoVOS) {
                        nomes.append(contratoVO.getPessoa().getNome() + " - CONTRATO: " + contratoVO.getCodigo() + ",\n\n");
                    }
                    this.manutencaoAjusteGeralTO.setNomePessoa(nomes.toString());
                }
            } else {
                if (UteisValidacao.emptyString(this.manutencaoAjusteGeralTO.getCodigoContratoAtualizarImpressao())) {
                    this.manutencaoAjusteGeralTO.setMsgResultado("Informe o código do contrato.");
                } else {
                    List<ContratoVO> contratoVOS = getFacade().getContrato().consultarPorCodigos(this.manutencaoAjusteGeralTO.getCodigoContratoAtualizarImpressao(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if (UteisValidacao.emptyList(contratoVOS)) {
                        this.manutencaoAjusteGeralTO.setMsgResultado("Contrato não encontrado.");
                    } else {
                        StringBuilder nomes = new StringBuilder();
                        for (ContratoVO contratoVO : contratoVOS) {
                            nomes.append(contratoVO.getPessoa().getNome() + " - CONTRATO: " + contratoVO.getCodigo() + ",\n\n");
                        }
                        this.manutencaoAjusteGeralTO.setNomePessoa(nomes.toString());
                    }
                }
            }
            montarSucesso("");
        } catch (Exception e) {
            this.manutencaoAjusteGeralTO.setSucesso(false);
            this.manutencaoAjusteGeralTO.setMsgResultado(e.getMessage());
            e.printStackTrace();
        }
    }

    public void atualizarImpressaoContrato() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("atualizarImpressaoContrato", getEmpresaLogado());
            if (this.manutencaoAjusteGeralTO.getAtualizarImpressaoContratoNaoAssinado()) {
                List<ContratoVO> contratoVOS = getFacade().getContrato().consultarContratosAtivosNaoAssinados(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (UteisValidacao.emptyList(contratoVOS)) {
                    this.manutencaoAjusteGeralTO.setMsgResultado("Não existem contratos elegíveis para ação solicitada.");
                } else {
                    for (ContratoVO contratoVO : contratoVOS) {
                        if (this.manutencaoAjusteGeralTO.getAtualizarModeloContrato()) {
                            getFacade().getContratoTextoPadrao().alterarTextoPadraoContrato(contratoVO.getCodigo(), contratoVO.getPlano().getPlanoTextoPadrao().getCodigo());
                        }
                        if (!getConfiguracaoSistemaVO().isPermiteImpressaoContratoMutavel()) {
                            getFacade().getContratoTextoPadrao().gravarHtmlContrato(contratoVO.getCodigo(), getUsuarioLogado());
                        }
                    }
                }
            } else {
                if (UteisValidacao.emptyString(this.manutencaoAjusteGeralTO.getCodigoContratoAtualizarImpressao())) {
                    this.manutencaoAjusteGeralTO.setMsgResultado("Informe o código do contrato.");
                } else {
                    List<ContratoVO> contratoVOS = getFacade().getContrato().consultarPorCodigos(this.manutencaoAjusteGeralTO.getCodigoContratoAtualizarImpressao(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if (UteisValidacao.emptyList(contratoVOS)) {
                        this.manutencaoAjusteGeralTO.setMsgResultado("Contrato não encontrado.");
                    } else {
                        for (ContratoVO contratoVO : contratoVOS) {
                            if (this.manutencaoAjusteGeralTO.getAtualizarModeloContrato()) {
                                getFacade().getContratoTextoPadrao().alterarTextoPadraoContrato(contratoVO.getCodigo(), contratoVO.getPlano().getPlanoTextoPadrao().getCodigo());
                            }
                            if (!getConfiguracaoSistemaVO().isPermiteImpressaoContratoMutavel()) {
                                getFacade().getContratoTextoPadrao().gravarHtmlContrato(contratoVO.getCodigo(), getUsuarioLogado());
                            }
                        }
                    }
                }
            }
            montarSucesso("");
            this.manutencaoAjusteGeralTO.setMsgResultado("Impressão do contrato atualizada com sucesso.");
        } catch (Exception e) {
            this.manutencaoAjusteGeralTO.setMsgResultado("Erro ao atualizar impressão do contrato. Erro:" + e.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    private List<ClienteVO> verificarCliente(String matricula) {
        List<ClienteVO> lista = new ArrayList<>();
        try {
            this.manutencaoAjusteGeralTO.setSucesso(false);
            this.manutencaoAjusteGeralTO.setMsgResultado("");
            this.manutencaoAjusteGeralTO.setNomePessoa(null);
            if (matricula.trim().equals("")) {
                this.manutencaoAjusteGeralTO.setMsgResultado("Informe a matrícula do cliente.");
            } else {
                lista = getFacade().getCliente().consultarPorCodigosMatricula(matricula, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (UteisValidacao.emptyList(lista)) {
                    this.manutencaoAjusteGeralTO.setMsgResultado("Matrícula do cliente não encontrada.");
                    this.manutencaoAjusteGeralTO.setMsgAviso("");
                } else {
                    StringBuilder nomes = new StringBuilder();
                    for (ClienteVO clienteVO : lista) {
                        nomes.append(clienteVO.getPessoa().getNome() + " " + clienteVO.getMatricula() + ",\n\n");
                    }
                    this.manutencaoAjusteGeralTO.setNomePessoa(nomes.toString());
                    this.manutencaoAjusteGeralTO.setMsgAviso("Por favor, observe que a exclusão que você está prestes a realizar é uma ação irreversível e permanente. Isso significa que, uma vez concluída, não será possível desfazer a ação ou recuperar qualquer conteúdo ou dados que tenham sido excluídos.");
                }
                montarSucesso("");
            }
        } catch (Exception e) {
            montarErro(e);
        }
        return lista;
    }

    public void verificarExcluirCliente() {
        if (this.manutencaoAjusteGeralTO.getMatriculaClienteExclusao().trim().isEmpty()) {
            verificarCliente(this.manutencaoAjusteGeralTO.getMatriculaClienteTokenGymPass());
        } else {
            verificarCliente(this.manutencaoAjusteGeralTO.getMatriculaClienteExclusao());
        }
    }

    public void excluirItemFaseMeta() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("excluirItemFaseMeta", getEmpresaLogado());
            this.manutencaoAjusteGeralTO.setMsgResultado("");
            if (UteisValidacao.emptyString(this.manutencaoAjusteGeralTO.getListaCodigosFaseMetaExcluir())) {
                this.manutencaoAjusteGeralTO.setMsgResultado("Informe o código ou a lista com os códigos.");
            } else {
                Integer qtdEncontrados = 0;
                for (String item : this.manutencaoAjusteGeralTO.getListaCodigosFaseMetaExcluir().split(",")) {
                    Integer codigoItem;
                    try {
                        codigoItem = parseInt(item);

                        FecharMetaDetalhadoVO fecharMetaDetalhado = getFacade().getFecharMetaDetalhado().consultarPorCodigo(codigoItem, false, Uteis.NIVELMONTARDADOS_TODOS);
                        if (fecharMetaDetalhado != null) {
                            getFacade().getFecharMetaDetalhado().excluir(fecharMetaDetalhado);
                            qtdEncontrados++;
                        }
                    } catch (Exception ignored) {
                    }
                }

                if (qtdEncontrados > 0) {
                    this.manutencaoAjusteGeralTO.setMsgResultado("Quantidade de itens excluidos com sucesso: " + qtdEncontrados + ".");
                } else {
                    this.manutencaoAjusteGeralTO.setMsgResultado("Nenhuma Item encontrado!");
                }
            }
        } catch (Exception e) {
            montarErro(e);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void verificarExcluirClientesDuplicados() {
        try {
            List<ObjetoGenerico> clientesDuplicados = new ExcluirClientesDuplicados().obterClientesDuplicados();
            setClientesDuplicados(clientesDuplicados);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void excluirClientesDuplicados() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("excluirClientesDuplicados", getEmpresaLogado());
            String observacoesProcesso = new ExcluirClientesDuplicados().processar();
            setObsClientesDuplicados(observacoesProcesso);
            verificarExcluirClientesDuplicados();
        } catch (Exception e) {
            montarErro(e);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void verificarAcessoFuturos() {
        try {
            if (getEmpresaAcesso().getCodigo() == 0) {
                throw new Exception("Selecione a empresa");
            }
            List<AcessoClienteVO> listaAcesso = getFacade().getAcessoCliente().consultarAcessoFuturo(getEmpresaAcesso().getCodigo(), Calendario.hoje(), Uteis.NIVELMONTARDADOS_TODOS);
            setListaAcessoCliente(listaAcesso);
            if (getListaAcessoCliente().isEmpty()) {
                throw new Exception("Não foram encontrados acessos futuros nessa empresa.");
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void excluirAcessoFuturos() {
        LogProcessoSistemaVO log = null;
        try {
            if (!getListaAcessoCliente().isEmpty()) {
                log = criarLog("excluirAcessoFuturos", getEmpresaLogado());
                for (AcessoClienteVO acessoClienteVO : getListaAcessoCliente()) {
                    getFacade().getAcessoCliente().excluirComLog(acessoClienteVO, "ACESSO FUTURO", getFacade().getControleCreditoTreino(), getUsuario());

                    //Atualizando situacaoclientesinteticodw.dataultimoacesso para não ficar com data futura lá
                    getFacade().getSituacaoClienteSinteticoDW().atualizarUltimoAcesso(acessoClienteVO.getCliente().getCodigo());
                }

                setListaAcessoCliente(new ArrayList<>());
                setSucesso(true);
                setErro(false);
                setMensagemDetalhada("Acessos futuros excluídos com sucesso para essa empresa");
            }
        } catch (Exception e) {
            montarErro(e);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void corrigirCarenciaContrato() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("corrigirCarenciaContrato", getEmpresaLogado());
            if (this.manutencaoAjusteGeralTO.getCodigoPlano() <= 0) {
                this.manutencaoAjusteGeralTO.setMsgResultado("Informe o código do Plano.");
            } else {
                getFacade().getContrato().alterarCarenciaTodosContratoParaCarenciaQueEstaNoPlano(this.manutencaoAjusteGeralTO.getCodigoPlano());
                this.manutencaoAjusteGeralTO.setMsgResultado("Todos os contratos foram corrigidos com sucesso.");
            }
            montarSucesso("");
        } catch (Exception e) {
            this.manutencaoAjusteGeralTO.setMsgResultado("Erro ao corrigir contratos. Erro:" + e.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void atualizarSinteticoCliente() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("atualizarSinteticoCliente", getEmpresaLogado());
            if ((this.manutencaoAjusteGeralTO.getClienteVO().getCodigo() == null) || (this.manutencaoAjusteGeralTO.getClienteVO().getCodigo() <= 0)) {
                this.manutencaoAjusteGeralTO.setMsgResultado("Informe o código do Cliente.");
            } else {
                getFacade().getZWFacade().atualizarSintetico(this.manutencaoAjusteGeralTO.getClienteVO(), Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
                this.manutencaoAjusteGeralTO.setMsgResultado("Sintético atualizado com sucesso.");
            }
            montarSucesso("");

        } catch (Exception e) {
            this.manutencaoAjusteGeralTO.setMsgResultado("Erro ao atualizar sintético do cliente. Erro:" + e.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void excluirCliente() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("excluirCliente", getEmpresaLogado());
            setMsgAlert("");
            manutencaoAjusteGeralTO.setMsgAviso("");
            manutencaoAjusteGeralTO.setNomePessoa(null);
            if (this.manutencaoAjusteGeralTO.getMatriculaClienteExclusao().trim().equals("")) {
                this.manutencaoAjusteGeralTO.setMsgResultado("Informe a matrícula do cliente.");
            } else {
                List<ClienteVO> lista = getFacade().getCliente().consultarPorCodigosMatricula(this.manutencaoAjusteGeralTO.getMatriculaClienteExclusao(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                if (!UteisValidacao.emptyList(lista)) {

                    boolean existeClienteEmRemessa = false;
                    for (ClienteVO clienteVO : lista) {
                        if (UtilReflection.objetoMaiorQueZero(clienteVO, "getCodigo()")) {
                            if (getFacade().getCliente().clientePossuiRemessa(clienteVO)) {
                                existeClienteEmRemessa = true;
                                break;
                            }
                        }
                    }

                    if (existeClienteEmRemessa) {
                        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
                        control.setMensagemDetalhada("", "");
                        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
                        control.init("Exclusão de Cliente",
                                "Existe cliente(s) com remessas de cobrança. Deseja excluir mesmo assim? Isso poderá causar problemas caso haja retorno de remessas deste(s) cliente(s).",
                                this, "acaoExcluirCliente", "", "", "", "form");

                    } else {
                        acaoExcluirCliente();
                    }

                } else {
                    this.manutencaoAjusteGeralTO.setMsgResultado("Nenhum cliente encontrado.");
                }

            }
        } catch (Exception e) {
            this.manutencaoAjusteGeralTO.setMsgResultado("Erro ao excluir cliente. Erro:" + e.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void excluirClienteRemessa(){
        try {
            acaoExcluirCliente();
        } catch (Exception ex){
            manutencaoAjusteGeralTO.setSucesso(false);
            manutencaoAjusteGeralTO.setMsgResultado(ex.getMessage());
        }
    }

    public void excluirTokenGymPassCliente() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("excluirTokenGymPassCliente", getEmpresaLogado());
            setMsgAlert("");
            if (this.manutencaoAjusteGeralTO.getMatriculaClienteTokenGymPass().trim().isEmpty()) {
                this.manutencaoAjusteGeralTO.setMsgResultado("Informe a matrícula do cliente.");
            } else {
                List<ClienteVO> lista = getFacade().getCliente().consultarPorCodigosMatricula(this.manutencaoAjusteGeralTO.getMatriculaClienteTokenGymPass(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                if (!UteisValidacao.emptyList(lista)) {
                    acaoExcluirTokenGymPassCliente(lista);
                } else {
                    this.manutencaoAjusteGeralTO.setMsgResultado("Nenhum cliente encontrado.");
                }

            }
        } catch (Exception e) {
            this.manutencaoAjusteGeralTO.setMsgResultado("Erro ao excluir token cliente. Erro:" + e.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void acaoExcluirCliente() throws Exception {

        List<ClienteVO> lista = getFacade().getCliente().consultarPorCodigosMatricula(this.manutencaoAjusteGeralTO.getMatriculaClienteExclusao(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        StringBuilder msg = new StringBuilder();
        for (ClienteVO clienteVO : lista) {
            boolean existeTreino = empresaTemIntegracaoTW(getKey());
            boolean excluiuNoTreino = false;
            try {
                if (existeTreino) {
                    excluiuNoTreino = TreinoWSConsumer.excluirCliente(getKey(), clienteVO.getCodigo()).equals("ok");

                    if (!excluiuNoTreino) {
                        msg.append(clienteVO.getMatricula()).append(" Treino está fora - O cliente não pode ser excluído, pois pode ficar com o registro órfão no treino. \n\n");
                        throw new Exception("");
                    } else {
                        getFacade().getCliente().excluirClienteETodosSeusRelacionamentos(clienteVO, getUsuario());
                        msg.append(clienteVO.getMatricula()).append(" - Cliente possui registro no treino e ambos foram excluídos com sucesso. \n\n");
                    }
                }

                if (!existeTreino) {
                    getFacade().getCliente().excluirClienteETodosSeusRelacionamentos(clienteVO, getUsuario());
                    msg.append(clienteVO.getMatricula()).append(" - Cliente excluído com sucesso.");
                }

                manutencaoAjusteGeralTO.setSucesso(true);
            } catch (Exception ex) {
                manutencaoAjusteGeralTO.setSucesso(false);
                msg.append(clienteVO.getMatricula()).append(" - Falha ao remover cliente." + ex.getMessage()).append(" \n\n");
                throw new  Exception(msg.toString());
            }
        }
        this.manutencaoAjusteGeralTO.setMsgResultado(msg.toString());

    }

    public void acaoExcluirTokenGymPassCliente(List<ClienteVO> listaCliente) throws Exception {
        StringBuilder msg = new StringBuilder();
        for (ClienteVO clienteVO : listaCliente) {
            try {
                getFacade().getCliente().excluirTokenGymPassCliente(clienteVO, this.getUsuarioLogado());
                msg.append(clienteVO.getMatricula()).append(", O Token Wellhub do cliente foi excluído com sucesso.");
                manutencaoAjusteGeralTO.setSucesso(true);
            } catch (Exception ex) {
                manutencaoAjusteGeralTO.setSucesso(false);
                msg.append(clienteVO.getMatricula()).append("Falha ao remover token do cliente." + ex.getMessage()).append(" \n\n");
            }
        }
        this.manutencaoAjusteGeralTO.setMsgResultado(msg.toString());
    }


    public void verificarExcluirNFSeEmitida() {
        try {
            this.manutencaoAjusteGeralTO.setMsgResultado("");
            this.manutencaoAjusteGeralTO.setDescricaoNotaFiscalEmitida("");
            if (UteisValidacao.emptyString(this.manutencaoAjusteGeralTO.getListaIdRPS())) {
                this.manutencaoAjusteGeralTO.setMsgResultado("Informe o ID_RPS ou a lista com os ID_RPS's.");
            } else {
                StringBuilder descricao = new StringBuilder();
                String msgInfo = "";
                Integer qtdEncontrados = 0;
                for (String rps : this.manutencaoAjusteGeralTO.getListaIdRPS().split(",")) {
                    Integer codigoRPS;
                    try {
                        codigoRPS = parseInt(rps);

                        NFSeEmitidaVO nfSeEmitidaVO = getFacade().getNFSeEmitida().consultaPorRPS(codigoRPS);
                        if (nfSeEmitidaVO != null) {
                            descricao.append("<br><br>***** CÓDIGO ID_RPS: ").append(nfSeEmitidaVO.getIdRps()).append(" *****<br>");
                            descricao.append(getFacade().getNFSeEmitida().montarDescricaoExcluirNotaFiscal(nfSeEmitidaVO));
                            qtdEncontrados++;
                        }
                    } catch (Exception ignored) {
                    }
                }

                if (qtdEncontrados > 0) {
                    msgInfo += "Quantidade de RPS solicitado: " + this.manutencaoAjusteGeralTO.getListaIdRPS().split(",").length + "<br>";
                    msgInfo += "Quantidade encontrado: " + qtdEncontrados + "<br>";
                    this.manutencaoAjusteGeralTO.setDescricaoNotaFiscalEmitida(msgInfo + descricao.toString());
                } else {
                    this.manutencaoAjusteGeralTO.setMsgResultado("Nenhuma Nota Fiscal encontrada!");
                }
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void excluirNFSeEmitida() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("excluirNFSeEmitida", getEmpresaLogado());
            this.manutencaoAjusteGeralTO.setMsgResultado("");

            int qtdExcluida = 0;
            for (String rps : this.manutencaoAjusteGeralTO.getListaIdRPS().split(",")) {
                Integer codigoRPS;
                try {
                    codigoRPS = parseInt(rps);

                    NFSeEmitidaVO nfSeEmitidaVO = getFacade().getNFSeEmitida().consultaPorRPS(codigoRPS);
                    if (nfSeEmitidaVO != null) {
                        getFacade().getNFSeEmitida().excluirComLog(nfSeEmitidaVO, getUsuarioLogado(), ProcessoAjusteGeralEnum.EXCLUIR_NOTA_FISCAL);
                        qtdExcluida++;
                        nfSeEmitidaVO = null;
                    }
                } catch (Exception ignored) {
                }
            }

            this.manutencaoAjusteGeralTO.setDescricaoNotaFiscalEmitida("");
            if (qtdExcluida > 1) {
                this.manutencaoAjusteGeralTO.setMsgResultado("As Notas Fiscais foram excluídas com sucesso. Total de " + qtdExcluida + " RPS's.");
            } else {
                this.manutencaoAjusteGeralTO.setMsgResultado("Nota Fiscal excluída com sucesso");
            }

        } catch (Exception e) {
            this.manutencaoAjusteGeralTO.setMsgResultado("Erro ao excluir Nota Fiscal. Erro:" + e.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void verificarExcluirNFCeEmitida() {
        try {
            this.manutencaoAjusteGeralTO.setMsgResultado("");
            this.manutencaoAjusteGeralTO.setDescricaoNotaFiscalEmitida("");
            if (UteisValidacao.emptyString(this.manutencaoAjusteGeralTO.getListaIdNFCe())) {
                this.manutencaoAjusteGeralTO.setMsgResultado("Informe o ID_NFCe ou a lista com os ID_NFCe's.");
            } else {
                StringBuilder descricao = new StringBuilder();
                String msgInfo = "";
                Integer qtdEncontrados = 0;
                for (String nfce : this.manutencaoAjusteGeralTO.getListaIdNFCe().split(",")) {
                    Integer codigoNFCe;
                    try {
                        codigoNFCe = parseInt(nfce);

                        NotaFiscalConsumidorEletronicaVO notaFiscalConsumidorEletronicaVO = getFacade().getNotaFiscalConsumidorEletronica().consultarPorIdNFCe(codigoNFCe, Uteis.NIVELMONTARDADOS_TODOS);
                        if (!UteisValidacao.emptyNumber(notaFiscalConsumidorEletronicaVO.getCodigo())) {
                            descricao.append("<br><br>***** CÓDIGO ID_NFCe: ").append(notaFiscalConsumidorEletronicaVO.getId_NFCe()).append(" *****<br>");
                            descricao.append(getFacade().getNotaFiscalConsumidorEletronica().montarDescricaoExcluirNotaFiscal(notaFiscalConsumidorEletronicaVO));
                            qtdEncontrados++;
                        }
                    } catch (Exception ignored) {
                    }
                }

                if (qtdEncontrados > 0) {
                    msgInfo += "Quantidade de NFCe solicitado: " + this.manutencaoAjusteGeralTO.getListaIdRPS().split(",").length + "<br>";
                    msgInfo += "Quantidade encontrado: " + qtdEncontrados + "<br>";
                    this.manutencaoAjusteGeralTO.setDescricaoNotaFiscalEmitida(msgInfo + descricao.toString());
                } else {
                    this.manutencaoAjusteGeralTO.setMsgResultado("Nenhuma Nota Fiscal do Consumidor encontrada!");
                }
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void excluirNFCeEmitida() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("excluirNFCeEmitida", getEmpresaLogado());
            this.manutencaoAjusteGeralTO.setMsgResultado("");

            Integer qtdExcluida = 0;
            for (String nfce : this.manutencaoAjusteGeralTO.getListaIdNFCe().split(",")) {
                Integer codigoNFCe;
                try {
                    codigoNFCe = parseInt(nfce);

                    NotaFiscalConsumidorEletronicaVO notaFiscalConsumidorEletronicaVO = getFacade().getNotaFiscalConsumidorEletronica().consultarPorIdNFCe(codigoNFCe, Uteis.NIVELMONTARDADOS_TODOS);
                    if (!UteisValidacao.emptyNumber(notaFiscalConsumidorEletronicaVO.getCodigo())) {
                        getFacade().getNotaFiscalConsumidorEletronica().excluirComLog(notaFiscalConsumidorEletronicaVO, getUsuarioLogado(), ProcessoAjusteGeralEnum.EXCLUIR_NFCe);
                        qtdExcluida++;
                        notaFiscalConsumidorEletronicaVO = null;
                    }
                } catch (Exception ignored) {
                }
            }

            this.manutencaoAjusteGeralTO.setDescricaoNotaFiscalEmitida("");
            if (qtdExcluida > 1) {
                this.manutencaoAjusteGeralTO.setMsgResultado("As NFCe's foram excluídas com sucesso. Total de " + qtdExcluida + " NFCe's.");
            } else {
                this.manutencaoAjusteGeralTO.setMsgResultado("NFCe excluída com sucesso");
            }

        } catch (Exception e) {
            this.manutencaoAjusteGeralTO.setMsgResultado("Erro ao excluir Nota Fiscal do Consumidor. Erro:" + e.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void verificarExcluirNotaFamiliaEmitida() {
        try {
            this.manutencaoAjusteGeralTO.setMsgResultado("");
            this.manutencaoAjusteGeralTO.setDescricaoNotaFiscalEmitida("");
            if (UteisValidacao.emptyString(this.manutencaoAjusteGeralTO.getListaIdNotaFamilia())) {
                this.manutencaoAjusteGeralTO.setMsgResultado("Informe o ID_NotaFamilia ou a lista com os ID_NotaFamilia's.");
            } else {
                StringBuilder descricao = new StringBuilder();
                String msgInfo = "";
                Integer qtdEncontrados = 0;
                for (String notaFamilia : this.manutencaoAjusteGeralTO.getListaIdNotaFamilia().split(",")) {
                    Integer codigoNotaFamilia;
                    try {
                        codigoNotaFamilia = parseInt(notaFamilia);
                        List<NotaFiscalFamiliaVO> notaFiscalFamiliaVOS = getFacade().getNotaFiscalFamilia().consultarPorNota(codigoNotaFamilia, Uteis.NIVELMONTARDADOS_TODOS);
                        if (!UteisValidacao.emptyNumber(notaFiscalFamiliaVOS.get(0).getCodigo())) {
                            descricao.append(getFacade().getNotaFiscalFamilia().montarDescricaoExcluirNotaFiscal(notaFiscalFamiliaVOS));
                            qtdEncontrados++;
                        }
                    } catch (Exception ignored) {
                    }
                }

                if (qtdEncontrados > 0) {
                    msgInfo += "Quantidade de Nota em Grupo solicitado: " + this.manutencaoAjusteGeralTO.getListaIdNotaFamilia().split(",").length + "<br>";
                    msgInfo += "Quantidade encontrado: " + qtdEncontrados + "<br>";
                    this.manutencaoAjusteGeralTO.setDescricaoNotaFiscalEmitida(msgInfo + descricao.toString());
                } else {
                    this.manutencaoAjusteGeralTO.setMsgResultado("Nenhuma Nota Fiscal encontrada!");
                }
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void excluirNotaFamilia() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("excluirNotaFamilia", getEmpresaLogado());
            this.manutencaoAjusteGeralTO.setMsgResultado("");
            Integer qtdExcluida = 0;

            for (String notaFamilia : this.manutencaoAjusteGeralTO.getListaIdNotaFamilia().split(",")) {
                Integer codigoNotaFamilia;
                try {
                    codigoNotaFamilia = parseInt(notaFamilia);
                    List<NotaFiscalFamiliaVO> notaFiscalFamiliaVOS = getFacade().getNotaFiscalFamilia().consultarPorNota(codigoNotaFamilia, Uteis.NIVELMONTARDADOS_TODOS);
                    for (int i = 0; i < notaFiscalFamiliaVOS.size(); i++) {
                        if (!UteisValidacao.emptyNumber(notaFiscalFamiliaVOS.get(i).getCodigo())) {
                            getFacade().getNotaFiscalFamilia().excluirComLogNotaFamilia(
                                    codigoNotaFamilia, getFacade().getNFSeEmitida().consultarPorChavePrimaria(notaFiscalFamiliaVOS.get(i).getNfSeEmitidaVO().getCodigo()), getUsuarioLogado());
                        }
                    }
                    qtdExcluida++;
                    notaFiscalFamiliaVOS = null;
                } catch (Exception ignored) {
                }
            }

            this.manutencaoAjusteGeralTO.setDescricaoNotaFiscalEmitida("");
            if (qtdExcluida > 1) {
                this.manutencaoAjusteGeralTO.setMsgResultado("As Notas em Grupo foram excluídas com sucesso. Total de " + qtdExcluida + " Notas em Grupo.");
            } else {
                this.manutencaoAjusteGeralTO.setMsgResultado("Nota em Grupo excluída com sucesso");
            }

        } catch (Exception e) {
            this.manutencaoAjusteGeralTO.setMsgResultado("Erro ao excluir Nota em Grupo. Erro:" + e.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void consultarLogNotaFiscalExcluida() {
        try {
            setLogAjusteGeralVOList(new ArrayList<>());
            List<LogAjusteGeralVO> novoLog = getFacade().getLogAjusteGeral().consultarTodas(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            setLogAjusteGeralVOList(novoLog);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void verificarPagamentosPixPJBankDataCompensacaoIncorreta() {
        try {
            limparMensagem();
            this.manutencaoAjusteGeralTO.setSucesso(false);
            this.manutencaoAjusteGeralTO.setSucessoAposCorrecaoAjustarPagamentoPixPjBank(false);
            this.manutencaoAjusteGeralTO.setMsgExibirAjustarPagamentoPixPjBank("");
            this.manutencaoAjusteGeralTO.setMsgAposCorrecaoExibirAjustarPagamentoPixPjBank("");
            this.manutencaoAjusteGeralTO.setPagadoresExibirAjustarPagamentoPixPjBank("");
            this.manutencaoAjusteGeralTO.setListaMovPgtoAjustarPagamentoPixPjBank(new ArrayList<>());

            if (this.manutencaoAjusteGeralTO.getDtInicioAjustarPagamentoPixPjBank() == null) {
                throw new Exception("Informe a data de início.");
            }
            if (this.manutencaoAjusteGeralTO.getDtFimAjustarPagamentoPixPjBank() == null) {
                throw new Exception("Informe a data fim.");
            }
            if (Uteis.nrDiasEntreDatas(this.manutencaoAjusteGeralTO.getDtInicioAjustarPagamentoPixPjBank(), this.manutencaoAjusteGeralTO.getDtFimAjustarPagamentoPixPjBank()) > 31) {
                throw new Exception("Para evitar um possível erro de período informado, este recurso só permite processar no máximo 31 dias por vez");
            }

            this.manutencaoAjusteGeralTO.setListaMovPgtoAjustarPagamentoPixPjBank(
                    getFacade().getMovPagamento().consultarPagamentosPixPJBankDataCompensacaoIncorreta(
                            this.manutencaoAjusteGeralTO.getDtInicioAjustarPagamentoPixPjBank(),
                            this.manutencaoAjusteGeralTO.getDtFimAjustarPagamentoPixPjBank())
            );

            if (UteisValidacao.emptyList(this.manutencaoAjusteGeralTO.getListaMovPgtoAjustarPagamentoPixPjBank())) {
                throw new Exception("Não foi encontrado registro(s) incorreto(s) para ajustar");
            }

            this.manutencaoAjusteGeralTO.setMsgExibirAjustarPagamentoPixPjBank("Foram encontrados " + this.manutencaoAjusteGeralTO.getListaMovPgtoAjustarPagamentoPixPjBank().size() +
                    " registro(s) incorreto(s) para ajustar:");

            StringBuilder pagadores = new StringBuilder();
            for (MovPagamentoVO mov : this.manutencaoAjusteGeralTO.getListaMovPgtoAjustarPagamentoPixPjBank()) {
                pagadores.append(mov.getNomePagador()).append(" , ");
            }

            this.manutencaoAjusteGeralTO.setPagadoresExibirAjustarPagamentoPixPjBank(Uteis.removerUltimosCaracteres(pagadores.toString(), 3));

            this.manutencaoAjusteGeralTO.setSucesso(true);

        } catch (Exception e) {
            this.manutencaoAjusteGeralTO.setSucesso(false);
            this.manutencaoAjusteGeralTO.setMsgExibirAjustarPagamentoPixPjBank(e.getMessage());
        }
    }

    public void corrigirPagamentosPixPJBankDataCompensacaoIncorreta() {
        LogProcessoSistemaVO log = null;
        try {
            this.manutencaoAjusteGeralTO.setSucessoAposCorrecaoAjustarPagamentoPixPjBank(false);
            this.manutencaoAjusteGeralTO.setMsgAposCorrecaoExibirAjustarPagamentoPixPjBank("");

            if (Uteis.nrDiasEntreDatas(this.manutencaoAjusteGeralTO.getDtInicioAjustarPagamentoPixPjBank(), this.manutencaoAjusteGeralTO.getDtFimAjustarPagamentoPixPjBank()) > 31) {
                throw new Exception("Para evitar um possível erro de período informado, este recurso só permite processar no máximo de 31 dias por vez");
            }

            if (UteisValidacao.emptyList(this.manutencaoAjusteGeralTO.getListaMovPgtoAjustarPagamentoPixPjBank())) {
                throw new Exception("Não foi foi possível obter a lista com os registros incorretos para ajustar");
            }

            log = criarLog("corrigirPagamentosPixPJBankDataCompensacaoIncorreta", getEmpresaLogado());
            getFacade().getMovPagamento().corrigirPagamentosPixPJBankDataCompensacaoIncorreta(this.manutencaoAjusteGeralTO.getListaMovPgtoAjustarPagamentoPixPjBank());

            this.manutencaoAjusteGeralTO.setSucessoAposCorrecaoAjustarPagamentoPixPjBank(true);
            this.manutencaoAjusteGeralTO.setMsgAposCorrecaoExibirAjustarPagamentoPixPjBank("Alterações nas datas de compensações realizadas com sucesso!");
        } catch (Exception e) {
            this.manutencaoAjusteGeralTO.setSucessoAposCorrecaoAjustarPagamentoPixPjBank(false);
            this.manutencaoAjusteGeralTO.setMsgAposCorrecaoExibirAjustarPagamentoPixPjBank(e.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public boolean getAmbienteDeTeste() {
        try {
            URL u = new URL(request().getRequestURL().toString());
            return ((u.getHost().equals("localhost") ||
                    u.getHost().equals("devadm.pactosolucoes.com.br") ||
                    u.getHost().equals("squad-pay.pactosolucoes.com.br") ||
                    u.getHost().equals("devzw.pactosolucoes.com.br") ||
                    u.getHost().equals("swarm5.pactosolucoes.com.br") ||
                    u.getHost().equals("swarm3.pactosolucoes.com.br") ||
                    u.getHost().equals("devi9.pactosolucoes.com.br") ||
                    Uteis.isAmbienteDesenvolvimentoTeste()) && getUsuarioLogado().getUsuarioPactoSolucoes());
        } catch (Exception ex) {
            return false;
        }
    }

    public boolean getUsuarioPermiteVisualizarAjustesGerais() {
        try {
            if (getAmbienteDeTeste()) {
                return true;
            } else if (getUsuarioLogado().getUserOamd().isEmpty()) {
                return false;
            } else return getUsuarioLogado().getPermiteOperacaoAjusteBD();
        } catch (Exception e) {
            return false;
        }
    }

    private void montarMensagemAlert(List<VerificarRecibosPagamentosSemVinculo.ClientesProblemasPagamento> lista) {
        setMsgAlert("");
        if (lista.isEmpty()) {
            montarMsgAlert("Nenhum registro foi encontrado com este problema.");
        }
    }

    private void montarMensagemAlertCorrecao(List<VerificarRecibosPagamentosSemVinculo.ClientesProblemasPagamento> lista) {
        setMsgAlert("");
        if (lista.isEmpty()) {
            montarMsgAlert("Todos os registros foram corrigidos.");
        } else {
            montarMsgAlert("Alguns registros não foram corrigidos. Analise cada caso.");
        }
    }

    public void corrigirPgtosParcelasSemRecibo() throws Exception {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("corrigirPgtosParcelasSemRecibo", getEmpresaLogado());
            getVerificacao().corrigirPgtosParcelasSemRecibo();
            getVerificacao().obterPgtosParcelasSemRecibo();
            montarMensagemAlertCorrecao(getVerificacao().getPgtosParcelasSemRecibo());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void corrigirPgtosSemProdutosPagos() throws Exception {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("corrigirPgtosSemProdutosPagos", getEmpresaLogado());
            getVerificacao().corrigirPagementosSemProdutosPagos();
            getVerificacao().obterPgtosSemProdutosPagos();
            montarMensagemAlertCorrecao(getVerificacao().getPgtosSemProdutosPagos());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void corrigirParcelasEAComPagamento() throws Exception {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("corrigirParcelasEAComPagamento", getEmpresaLogado());
            getVerificacao().corrigirParcelasEAComPagamento();
            getVerificacao().obterParcelasEAComPagamento();
            montarMensagemAlertCorrecao(getVerificacao().getParcelasEAComPagamento());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void obterPgtosSemVinculoParcela() throws Exception {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("obterPgtosSemVinculoParcela", getEmpresaLogado());
            getVerificacao().obterPgtosSemVinculoParcela();
            montarMensagemAlert(getVerificacao().getPgtosSemVinculosParcelas());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void obterMovparcelasSemMovprodutos() throws Exception {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("obterMovparcelasSemMovprodutos", getEmpresaLogado());
            getVerificacao().obterMovparcelasSemMovprodutos();
            montarMensagemAlert(getVerificacao().getMovparcelasSemMovprodutos());
        } catch (Exception ex) {
            montarErro(ex);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void obterMovprodutosSemMovparcelas() throws Exception {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("obterMovprodutosSemMovparcelas", getEmpresaLogado());
            getVerificacao().obterMovprodutosSemMovparcelas();
            montarMensagemAlert(getVerificacao().getMovprodutosSemMovparcelas());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void obterParcelasEAComPagamento() throws Exception {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("obterParcelasEAComPagamento", getEmpresaLogado());
            getVerificacao().obterParcelasEAComPagamento();
            montarMensagemAlert(getVerificacao().getParcelasEAComPagamento());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    public void consultarConfiguracaoSistemaCadastroVisitantes() {
        try {
            List listaVisitante = getFacade().getConfiguracaoSistemaCadastroCliente().consultar(true);

            Iterator<Object> it = listaVisitante.iterator();

            while (it.hasNext()) {
                Object item = it.next();
                if (((ConfiguracaoSistemaCadastroClienteVO) item).getNome().startsWith("CPF")) {
                    String identificador = ((ConfiguracaoSistemaCadastroClienteVO) item).getNome().replace("CPF", displayIdentificadorFront[0]);
                    ((ConfiguracaoSistemaCadastroClienteVO) item).setNome(identificador);
                }
                if (((ConfiguracaoSistemaCadastroClienteVO) item).getNome().equalsIgnoreCase("RG")) {
                    ((ConfiguracaoSistemaCadastroClienteVO) item).setNome(displayIdentificadorFront[1]);
                }
            }

            getConfiguracaoSistemaVO().setListaConfiguracaoCamposVisitante(listaVisitante);
            getConfiguracaoSistemaVO().setHiddenQuantRegistrosConfVisitante(listaVisitante.size());
            getConfiguracaoSistemaVO().setListaConfiguracaoCamposVisitanteOriginal(getClonarListaConfiguracaoSistemaCadastroClienteVO(getConfiguracaoSistemaVO().getListaConfiguracaoCamposVisitante()));
        } catch (Exception ex) {
            ex.getStackTrace();
        }
    }

    public void consultarConfiguracaoSistemaCadastroCliente() {
        try {
            List lista = getFacade().getConfiguracaoSistemaCadastroCliente().consultar(false);
            Iterator<Object> it = lista.iterator();

            while (it.hasNext()) {
                Object item = it.next();
                if (((ConfiguracaoSistemaCadastroClienteVO) item).getNome().startsWith("CPF")) {
                    String identificador = ((ConfiguracaoSistemaCadastroClienteVO) item).getNome().replace("CPF", displayIdentificadorFront[0]);
                    ((ConfiguracaoSistemaCadastroClienteVO) item).setNome(identificador);
                }
                if (((ConfiguracaoSistemaCadastroClienteVO) item).getNome().equalsIgnoreCase("RG")) {
                    ((ConfiguracaoSistemaCadastroClienteVO) item).setNome(displayIdentificadorFront[1]);
                }
            }
            getConfiguracaoSistemaVO().setListaConfiguracaoCamposCliente(lista);
            getConfiguracaoSistemaVO().setHiddenQuantRegistrosConfCliente(lista.size());
            getConfiguracaoSistemaVO().setListaConfiguracaoCamposClienteOriginal(getClonarListaConfiguracaoSistemaCadastroClienteVO(getConfiguracaoSistemaVO().getListaConfiguracaoCamposCliente()));
        } catch (Exception ex) {
            ex.getStackTrace();
        }
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo
     * <code>SelectItem</code> para preencher o comboBox relativo ao atributo
     * <code>QuestionarioReMatricula</code>.
     */
    public void montarListaSelectItemQuestionarioReMatricula(String prm) throws Exception {
        List resultadoConsulta = consultarQuestionarioPorDescricao(prm, TipoServicoEnum.PLANO.getTipo());
        Iterator i = resultadoConsulta.iterator();
        List<SelectItem> objs = new ArrayList<>();
        objs.add(new SelectItem(0, ""));
        while (i.hasNext()) {
            QuestionarioVO obj = (QuestionarioVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getTituloPesquisa()));
        }
        setListaSelectItemQuestionarioReMatricula(objs);
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo
     * <code>QuestionarioReMatricula</code>. Buscando todos os objetos
     * correspondentes a entidade
     * <code>Questionario</code>. Esta rotina não recebe parâmetros para
     * filtragem de dados, isto é importante para a inicialização dos dados da
     * tela para o acionamento por meio requisições Ajax.
     */
    public void montarListaSelectItemQuestionarioReMatricula() {
        try {
            montarListaSelectItemQuestionarioReMatricula("");
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo
     * <code>SelectItem</code> para preencher o comboBox relativo ao atributo
     * <code>QuestionarioRetorno</code>.
     */
    public void montarListaSelectItemQuestionarioRetorno(String prm) throws Exception {
        List resultadoConsulta = consultarQuestionarioPorDescricao(prm, TipoServicoEnum.PLANO.getTipo());
        Iterator i = resultadoConsulta.iterator();
        List<SelectItem> objs = new ArrayList<>();
        objs.add(new SelectItem(0, ""));
        while (i.hasNext()) {
            QuestionarioVO obj = (QuestionarioVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getTituloPesquisa()));
        }

        setListaSelectItemQuestionarioRetorno(objs);
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo
     * <code>QuestionarioRetorno</code>. Buscando todos os objetos
     * correspondentes a entidade
     * <code>Questionario</code>. Esta rotina não recebe parâmetros para
     * filtragem de dados, isto é importante para a inicialização dos dados da
     * tela para o acionamento por meio requisições Ajax.
     */
    public void montarListaSelectItemQuestionarioRetorno() {
        try {
            montarListaSelectItemQuestionarioRetorno("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo
     * <code>SelectItem</code> para preencher o comboBox relativo ao atributo
     * <code>QuestionarioPrimeiraVisita</code>.
     */
    public void montarListaSelectItemQuestionarioPrimeiraVisita(String prm) throws Exception {
        List resultadoConsulta = consultarQuestionarioPorDescricao(prm, TipoServicoEnum.PLANO.getTipo());
        Iterator i = resultadoConsulta.iterator();
        List<SelectItem> objs = new ArrayList<>();
        objs.add(new SelectItem(0, ""));
        while (i.hasNext()) {
            QuestionarioVO obj = (QuestionarioVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getTituloPesquisa()));
        }

        setListaSelectItemQuestionarioPrimeiraVisita(objs);
    }

    public void montarListaSelectItemQuestionarioPrimeiraCompra(String prm) throws Exception {
        List resultadoConsulta = consultarQuestionarioPorDescricao(prm, TipoServicoEnum.SERVICO.getTipo());
        Iterator i = resultadoConsulta.iterator();
        List<SelectItem> objs = new ArrayList<>();
        objs.add(new SelectItem(0, ""));
        while (i.hasNext()) {
            QuestionarioVO obj = (QuestionarioVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getTituloPesquisa()));
        }

        setListaSelectItemQuestionarioPrimeiraCompra(objs);
    }

    public void montarListaSelectItemQuestionarioRetornoCompra(String prm) throws Exception {
        List resultadoConsulta = consultarQuestionarioPorDescricao(prm, TipoServicoEnum.SERVICO.getTipo());
        Iterator i = resultadoConsulta.iterator();
        List<SelectItem> objs = new ArrayList<>();
        objs.add(new SelectItem(0, ""));
        while (i.hasNext()) {
            QuestionarioVO obj = (QuestionarioVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getTituloPesquisa()));
        }

        setListaSelectItemQuestionarioRetornoCompra(objs);
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo
     * <code>QuestionarioPrimeiraVisita</code>. Buscando todos os objetos
     * correspondentes a entidade
     * <code>Questionario</code>. Esta rotina não recebe parâmetros para
     * filtragem de dados, isto é importante para a inicialização dos dados da
     * tela para o acionamento por meio requisições Ajax.
     */
    public void montarListaSelectItemQuestionarioPrimeiraVisita() {
        try {
            montarListaSelectItemQuestionarioPrimeiraVisita("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void montarListaSelectItemQuestionarioPrimeiraCompra() {
        try {
            montarListaSelectItemQuestionarioPrimeiraCompra("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void montarListaSelectItemQuestionarioRetornoCompra() {
        try {
            montarListaSelectItemQuestionarioRetornoCompra("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void montarListaSelectItemLocalAcessoChamada() {
        try {
            List resultadoConsulta = getFacade().getLocalAcesso().consultarPorNome("", false, Uteis.NIVELMONTARDADOS_TODOS);
            Iterator i = resultadoConsulta.iterator();
            setListaSelectItemLocalAcessoChamada(new ArrayList());
            getListaSelectItemLocalAcessoChamada().add(new SelectItem(0, ""));
            while (i.hasNext()) {
                LocalAcessoVO obj = (LocalAcessoVO) i.next();
                getListaSelectItemLocalAcessoChamada().add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void montarListaSelectItemColetorChamada() {
        try {
            List resultadoConsulta = getFacade().getColetor().consultarPorDescricao("", Uteis.NIVELMONTARDADOS_TODOS);
            Iterator i = resultadoConsulta.iterator();
            setListaSelectItemColetorChamada(new ArrayList());
            getListaSelectItemColetorChamada().add(new SelectItem(0, ""));
            while (i.hasNext()) {
                ColetorVO obj = (ColetorVO) i.next();
                getListaSelectItemColetorChamada().add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Método responsável por consultar dados da entidade
     * <code><code> e montar o atributo
     * <code>descricao</code> Este atributo é uma lista (
     * <code>List</code>) utilizada para definir os valores a serem apresentados
     * no ComboBox correspondente
     */
    public List consultarQuestionarioPorDescricao(String descricaoPrm, String tipoServico) throws Exception {
        return getFacade().getQuestionario().consultarPorDescricao(descricaoPrm, tipoServico, false, Uteis.NIVELMONTARDADOS_TODOS);
    }

    /**
     * Método responsável por inicializar a lista de valores (
     * <code>SelectItem</code>) para todos os ComboBox's.
     */
    public void inicializarListasSelectItemTodosComboBox() {
        montarListaSelectItemQuestionarioPrimeiraVisita();
        montarListaSelectItemQuestionarioRetorno();
        montarListaSelectItemQuestionarioReMatricula();
        montarListaSelectItemQuestionarioPrimeiraCompra();
        montarListaSelectItemQuestionarioRetornoCompra();
        montarListaSelectItemLocalAcessoChamada();
        montarListaSelectItemColetorChamada();
    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List<SelectItem> getTipoConsultaCombo() {
        List<SelectItem> itens = new ArrayList<>();
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("descricaoQuestionario", "Questionário de Primeira Visita"));
        itens.add(new SelectItem("descricaoQuestionario", "Questionário de Retorno"));
        itens.add(new SelectItem("descricaoQuestionario", "Questionário de Rematrícula"));
        return itens;
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes
     * de uma consulta.
     */
    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("msg_entre_prmconsulta");
        try {
            configuracaoSistemaVOClone = (ConfiguracaoSistemaVO) configuracaoSistemaVO.getClone(true);
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InstantiationException e) {
            e.printStackTrace();
        }
        return "consultar";
    }

    /**
     * Método usado para atualizar mensagens de cadastro incompleto que não
     * continham os campos que faltavam no cadastro de visitantes
     *
     * @throws Exception
     */
    public void atualizarMensagensCadastroVisitantes() throws Exception {
        ConfiguracaoSistemaVO cfgSistema = getFacade().getConfiguracaoSistema().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_TODOS);
        cfgSistema.inicializarConfiguracaoSistemaVisitante();
        atualizarMensagens(getFacade().getClienteMensagem().consultarVisitantesMensagemCadastroIncompleto(getEmpresaLogado().getCodigo(), false),
                cfgSistema);
    }

    private void atualizarMensagens(ResultSet resultadoConsulta, ConfiguracaoSistemaVO validacao) {
        try {
            while (resultadoConsulta.next()) {
                ClienteMensagemVO mensagemCliente = new ClienteMensagemVO();
                mensagemCliente.setCodigo(resultadoConsulta.getInt("codigo"));
                mensagemCliente.getCliente().setCodigo(resultadoConsulta.getInt("cliente"));
                ClienteVO clienteVO = getFacade().getCliente().consultarPorChavePrimaria(mensagemCliente.getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                mensagemCliente.setMensagem(TiposMensagensEnum.DADOS_INCOMPLETOS.getMensagem().replace("campos",
                        mensagemCliente.getCliente().getValidarDadosPessoaisStringCliente(validacao, clienteVO)));
                getFacade().getClienteMensagem().alterarMensagemCadastroCliente(mensagemCliente);
            }
            setMsgAlert("");
            setMensagemID("msg_dados_mensagens_atualizadas");
            montarMsgAlert(getMensagem());
            setMensagemDetalhada("");
        } catch (ConsistirException e) {
            setMensagemID("msg_erro");
            setMensagemDetalhada("Nenhum Registro Encontrado !");
        } catch (Exception e) {
            setMensagemID("msg_erro_mensagens");
            setMensagemDetalhada("msg_erro_mensagens ", e.getMessage());
        }
    }

    /**
     * Método usado para atualizar mensagens de cadastro incompleto que não
     * continham os campos que faltavam no cadastro de clientes
     *
     * @throws Exception
     */
    public void atualizarMensagensCadastroClientes() throws Exception {
        ConfiguracaoSistemaVO cfgSistema = getFacade().getConfiguracaoSistema().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_TODOS);
        cfgSistema.inicializarConfiguracaoSistemaCliente();
        atualizarMensagens(getFacade().getClienteMensagem().consultarClientesMensagemCadastroIncompleto(getEmpresaLogado().getCodigo(), false),
                cfgSistema);
    }

    public Object realizarConsultaLog() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = configuracaoSistemaVO.getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));

        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(),
                configuracaoSistemaVO.getCodigo(), null);
        return true;
    }


    /**
     * Responsável por adicionar o Email do Responsavel pela Recorrencia na
     * listagem
     *
     * <AUTHOR> 01/07/2011
     */
    public void adicionarEmailResponsavelRecorrencia() {
        if (this.getEmailResponsavel().trim().equals("")) {
            setMensagemID("msg_erro_emailResponsavelRecorrenciaVazio");
            return;
        }
        if (this.getConfiguracaoSistemaVO().getListaEmailsRecorrencia().contains(this.getEmailResponsavel())) {
            setMensagemID("msg_erro_emailResponsavelRecorrenciaExistente");
            return;
        }
        if (!UteisEmail.getValidEmail(this.getEmailResponsavel())) {
            setMensagemID("msg_erro_emailInvalido");
            return;
        }
        this.getConfiguracaoSistemaVO().getListaEmailsRecorrencia().add(this.getEmailResponsavel());
        this.setEmailResponsavel("");
        this.setMensagemID("msg_entre_dados");
    }

    public void prepararHistoricoContrato() {
        setTotalContratosAProcessar(1);
        setTotalContratosProcessados(0);
        setInformacoesHistoricoContrato("");
    }

    public void prepararMovProdutoParcelas() {
        setTotalAProcessarMovProdutoParcela(0);
        setTotalProcessadosMovProdutoParcela(0);
    }

    public void corrigirMovProdutoParcelas() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("corrigirMovProdutoParcelas", getEmpresaLogado());
            prepararMovProdutoParcelas();
            new CorrigirMovProdutos().executarProcesso();
            getVerificacao().obterMovprodutosSemMovparcelas();
            montarMensagemAlertCorrecao(getVerificacao().getMovprodutosSemMovparcelas());
        } catch (Exception ex) {
            Logger.getLogger(ConfiguracaoSistemaControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void prepararParcelas() {
        setTotalAProcessarParcelas(0);
        setTotalProcessadosParcelas(0);
    }

    public void prepararParcelasCC() {
        setTotalAProcessarParcelasCC(0);
        setTotalProcessadosParcelasCC(0);
        setPollMovProdutoParcelas(true);
    }

    public void corrigirParcelas() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("corrigirParcelas", getEmpresaLogado());
            prepararParcelas();
            String erros = new CorrigirMovParcela().executarProcesso(true);
            getVerificacao().obterMovparcelasSemMovprodutos();
            montarMensagemAlertCorrecao(getVerificacao().getMovparcelasSemMovprodutos());
        } catch (Exception ex) {
            montarErro(ex);
            Logger.getLogger(ConfiguracaoSistemaControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void corrigirProdutosPagosRecibosValoresZoados() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("corrigirProdutosPagosRecibosValoresZoados", getEmpresaLogado());
            ProdutosPagosServico.contratosViculosZoados(Conexao.getFromSession());
            montarMsgAlert("Fantástico!!! Todos os registros foram corrigidos.");
        } catch (Exception ex) {
            if (ex.getMessage().contains("registros para serem")) {
                montarMsgAlert(ex.getMessage());
            } else {
                montarMsgAlert(" =( Algo aconteceu de errado.");
            }
            Logger.getLogger(ConfiguracaoSistemaControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void prepararHistoricoVinculo() {
        setTotalAProcessarHistoricoVinculo(1);
        setTotalProcessadosHistoricoVinculo(0);
    }

    public void corrigirHistoricoVinculo() {
        try {
            new CorrigirHistoricoVinculos().executarProcesso(false);
        } catch (Exception ex) {
            Logger.getLogger(ConfiguracaoSistemaControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void prepararVinculoProfessor() {
        setTotalAProcessarVinculoProfessor(0);
        setTotalProcessadosVinculoProfessor(0);
        setPollHistoricoVinculo(true);
    }

    public void corrigirVinculoProfessor() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("corrigirVinculoProfessor", getEmpresaLogado());
            new CorrigirVinculoProfessor().executarProcesso();
            setPollHistoricoVinculo(false);
            setMensagemID("msg_restaurarVinculos_professores");
            setSucesso(true);
            setErro(false);
        } catch (Exception ex) {
            setPollHistoricoVinculo(false);
            setTotalAProcessarVinculoProfessor(0);
            setTotalProcessadosVinculoProfessor(0);
            Logger.getLogger(ConfiguracaoSistemaControle.class.getName()).log(Level.SEVERE, null, ex);
            setMensagemDetalhada("msg_erro", ex.getMessage());
            setSucesso(true);
            setErro(false);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void corrigirProdutosContaCorrente() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("corrigirProdutosContaCorrente", getEmpresaLogado());
            new CriarProdutosContaCorrente().executarProcessoRegerarReciboClienteConsultor();
            verificacao = new VerificarRecibosPagamentosSemVinculo(Conexao.getFromSession());

            setPollMovProdutoParcelas(false);
            setMensagemID("msg_sucesso_recibo_sem_prod_cc");
            setSucesso(true);
            setErro(false);
        } catch (Exception ex) {
            setPollMovProdutoParcelas(false);
            setTotalAProcessarParcelasCC(0);
            setTotalProcessadosParcelasCC(0);
            Logger.getLogger(ConfiguracaoSistemaControle.class.getName()).log(Level.SEVERE, null, ex);
            setMensagemDetalhada("msg_erro", ex.getMessage());
            setSucesso(true);
            setErro(false);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void corrigirMatriculas() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("corrigirMatriculas", getEmpresaLogado());
            getFacade().getMatriculaAlunoHorarioTurma().matricularAlunos();
        } catch (Exception ex) {
            Logger.getLogger(ConfiguracaoSistemaControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    /**
     * Método que consulta os consultores cadastrados
     *
     * @param suggest
     * @return
     */
    public List<ColaboradorVO> executarAutocompleteColaborador(Object suggest) {
        String pref = (String) suggest;
        List<ColaboradorVO> result;
        try {
            if (pref.equals("%")) {
                result = getFacade().getColaborador().
                        consultarTodosColaboradoresPorTipoComLimite(TipoColaboradorEnum.CONSULTOR, false, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            } else {
                result = getFacade().getColaborador().
                        consultarTodosColaboradoresPorTipoPorNomeComLimite(pref, TipoColaboradorEnum.CONSULTOR, true, false, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            }

        } catch (Exception ex) {
            result = (new ArrayList<ColaboradorVO>());
        }
        return result;
    }

    public void limparPeriodo() {
        setDataInicialBVsPendentes(null);
        setDataFinalBVsPendentes(null);
    }

    /**
     * Método que seleciona o colaborador escolhido no suggestionBox
     *
     * @throws Exception
     */
    public void selecionarColaborador() throws Exception {
        ColaboradorVO obj = (ColaboradorVO) context().getExternalContext().getRequestMap().get("result");
        this.setColaboradorBVsPendentes(obj);
    }

    /**
     * Método que exclui as mensagens de clientes pendentes de acordo com
     * colaborador e datas de cadastro de bvs que forem informados.
     */
    public void excluirMsgsBVsPendentes() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("excluirMsgsBVsPendentes", getEmpresaLogado());
            List<ColaboradorVO> listaConsultor = getFacade().getColaborador().
                    consultarTodosColaboradoresPorTipoPorNomeComLimite(
                            getColaboradorBVsPendentes().getPessoa().getNome(), TipoColaboradorEnum.CONSULTOR, false, false, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            if (getColaboradorBVsPendentes().getPessoa().getNome().isEmpty()) {
                setColaboradorBVsPendentes(new ColaboradorVO());
            }
            if (listaConsultor.isEmpty() && !getColaboradorBVsPendentes().getPessoa().getNome().isEmpty()) {
                throw new Exception("Informe um colaborador válido");
            }
            getFacade().getClienteMensagem().excluirClienteMensagemBVsPendentes(getColaboradorBVsPendentes().getCodigo(), dataInicialBVsPendentes, dataFinalBVsPendentes, getEmpresaBV());
            setMsgExclusaoMsgBVsPendentes("alert('Mensagens excluídas com sucesso!')");
        } catch (Exception ex) {
            String msg = "alert('" + ex.getMessage() + "')";
            setMsgExclusaoMsgBVsPendentes(msg);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void renovacaoParaRematricula() {
        LogProcessoSistemaVO log = null;
        try {
            if (contrato > 0) {
                log = criarLog("renovacaoParaRematricula", getEmpresaLogado());
                Contrato.converterRenovacaoEmRematricula(contrato, "DE", getFacade().getRisco().getCon());
                gerarLogAlteracaoSituacaoContrato("RN", "RE");
            }
            setMsgExclusaoMsgBVsPendentes("alert('Contrato alterado com sucesso!')");
        } catch (Exception ex) {
            String msg = "alert('" + ex.getMessage() + "')";
            setMsgExclusaoMsgBVsPendentes(msg);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void rematriculaParaRenovacao() {
        LogProcessoSistemaVO log = null;
        try {
            if (contrato > 0) {
                log = criarLog("rematriculaParaRenovacao", getEmpresaLogado());
                Contrato.converterRematriculaEmRenovacao(contrato, "DE", getFacade().getRisco().getCon());
                gerarLogAlteracaoSituacaoContrato("RE", "RN");
            }
            setMsgExclusaoMsgBVsPendentes("alert('Contrato alterado com sucesso!')");
        } catch (Exception ex) {
            String msg = "alert('" + ex.getMessage() + "')";
            setMsgExclusaoMsgBVsPendentes(msg);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void gerarLogAlteracaoSituacaoContrato(final String valorAnterior, final String valorNovo) throws Exception {
        ContratoVO contratoVO = getFacade().getContrato().consultarPorChavePrimaria(contrato, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        LogVO log = new LogVO();
        log.setNomeCampo("situacaoContrato");
        log.setChavePrimaria(contratoVO.getCodigo().toString());
        log.setNomeEntidade("CONTRATO");
        log.setPessoa(contratoVO.getPessoa().getCodigo());
        log.setValorCampoAnterior(valorAnterior);
        log.setValorCampoAlterado(valorNovo);
        log.setOperacao("ALTERAR SITUAÇÃO DE CONTRATO");
        log.setResponsavelAlteracao(getUsuarioLogado().getNome());
        log.setUserOAMD(getUsuarioLogado().getUserOamd());
        getFacade().getLog().incluirSemCommit(log);
    }

    public void executarBackupCliente() {
        LogProcessoSistemaVO log = null;
        try {
            if (!UteisValidacao.validaEmail(emailBackup)) {
                throw new Exception("Email Invalido");
            }
            log = criarLog("executarBackupCliente", getEmpresaLogado());
            String url = ExecuteRequestHttpService.gerarURLConexaoOAMD(getKey());
            BackupClienteDTO backup = ExecuteRequestHttpService.consumirEndPointDadosParaTestOAMD(url);

            String path = getPathBackup();

            if (backupEscolha.equals("2")) {
                backup.setNomeBD(backup.getNomeBD().replace("bdzillyon", "bdmusc"));
            }
            UteisServletExportData uteisServletExportData = new UteisServletExportData(backup.getHostBD(), backup.getPorta(), "postgres",
                    backup.getPasswordBD(), backup.getNomeBD(), "*", "excel", true,
                    emailBackup, getServletContext().getRealPath("relatorio"), path);
            uteisServletExportData.run();
            montarSucesso("Excel gerado e enviado com sucesso!");
        } catch (Exception e) {
            Uteis.logar(e, ConfiguracaoSistema.class);
            montarErro("Não foi possivel realizar o backup: " + e.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void executarPontuacaoRetroativa() {
        LogProcessoSistemaVO log = null;
        try {
            if (dtInicialPontucaoRetroativa.after(Calendario.hoje()))
                throw new Exception("Data deve ser inferior a data de Hoje.");

            log = criarLog("executarPontuacaoRetroativa", getEmpresaLogado());

            RoboControle roboControle = getControlador(RoboControle.class) != null ? getControlador(RoboControle.class) : new RoboControle();
            Robo roboFacade = getFacade().getRobo();
            Date dataUltimoProcessamento = roboFacade.consultaParaObterUltimoDiaprocessado();
            Date diaProcessar = getDtInicialPontucaoRetroativa();
            if (dataUltimoProcessamento.before(Calendario.hoje()))
                dataUltimoProcessamento = Calendario.hoje();
            while (dataUltimoProcessamento.after(diaProcessar)) {
                roboControle.setDataSimulada(diaProcessar);
                roboControle.startPontuacao(getKey());
                diaProcessar = Calendario.somarDias(diaProcessar, 1);
            }
            montarSucesso("");
            setMensagem("Reprocessamento de pontos realizado com sucesso!");
        } catch (Exception e) {
            Uteis.logar(e, ConfiguracaoSistema.class);
            montarErro("Não foi possivel realizar reprocessamento de pontos: " + e.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void executarRemoverPontuacaoAlunoInativo() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("executarRemoverPontuacaoAlunoInativo", getEmpresaLogado());
            manutencaoAjusteGeralTO.setMsgResultado("");
            List<ClienteVO> clientes = getFacade().getCliente().consultarPontuacaoSituacao(0, Uteis.NIVELMONTARDADOS_DADOSBASICOS, true);
            for (ClienteVO cliente : clientes) {
                getFacade().getHistoricoPontos().zerarPontuacaoRobo(cliente.getPessoa());
            }
            manutencaoAjusteGeralTO.setMsgResultado("O Processo foi executado com sucesso! A pontuação de " + clientes.size() + " alunos inativos foram zeradas com sucesso!");
        } catch (Exception e) {
            Uteis.logar(e, ConfiguracaoSistema.class);
            manutencaoAjusteGeralTO.setMsgResultado("Não foi possivel realizar remoção dos pontos: " + e.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void executarRemoverPontuacaoTodosAlunos() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("executarRemoverPontuacaoTodosAlunos", getEmpresaLogado());
            manutencaoAjusteGeralTO.setMsgResultado("");
            List<ClienteVO> clientes = getFacade().getCliente().consultarPontuacaoSituacao(0, Uteis.NIVELMONTARDADOS_DADOSBASICOS, false);
            for (ClienteVO cliente : clientes) {
                getFacade().getHistoricoPontos().zerarPontuacaoRobo(cliente.getPessoa());
            }
            manutencaoAjusteGeralTO.setMsgResultado("O Processo foi executado com sucesso! A pontuação de " + clientes.size() + " alunos foram zeradas com sucesso!");
        } catch (Exception e) {
            Uteis.logar(e, ConfiguracaoSistema.class);
            manutencaoAjusteGeralTO.setMsgResultado("Não foi possivel realizar remoção dos pontos: " + e.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void incluirReciboDaTransacao() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("incluirReciboDaTransacao", getEmpresaLogado());
            manutencaoAjusteGeralTO.setMsgResultado("");

            JSONObject json = getFacade().getTransacao().gerarReciboTransacoesSemRecibo();

            if (!json.getBoolean("sucesso")) {
                throw new Exception(json.getString("msg"));
            }

            StringBuilder msg = new StringBuilder();
            msg.append("Total: ").append(json.getInt("total")).append(" ");
            msg.append("Sucesso: ").append(json.getInt("ajustado")).append(" ");
            msg.append("Falha: ").append(json.getInt("erro")).append(" ");

            manutencaoAjusteGeralTO.setMsgResultado(msg.toString());
        } catch (Exception e) {
            Uteis.logar(e, ConfiguracaoSistema.class);
            manutencaoAjusteGeralTO.setMsgResultado("Não foi possivel realizar inclusão dos recibos: " + e.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void ajustarAberturaMetaDuplicada() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("ajustarAberturaMetaDuplicada", getEmpresaLogado());
            manutencaoAjusteGeralTO.setMsgResultado("");
            AjustarAberturaMetaDuplicada.ajustarDuplicacoes(getFacade().getZWFacade().getCon(), this.manutencaoAjusteGeralTO.getProcessarAPartirDe());
            manutencaoAjusteGeralTO.setMsgResultado("O Processo foi executado com sucesso.");
        } catch (Exception e) {
            Uteis.logar(e, ConfiguracaoSistema.class);
            manutencaoAjusteGeralTO.setMsgResultado("Não foi possivel realizar ajuste dos itens da meta duplicados: " + e.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void preencherOperadoraCartao() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("preencherOperadoraCartao", getEmpresaLogado());
            manutencaoAjusteGeralTO.setMsgResultado("");
            int qtdAjustada = GerarReciboTransacao.povoarPagamentoSemOperadora(getFacade().getConvenioCobranca().getCon(), 0);
            manutencaoAjusteGeralTO.setMsgResultado("O Processo ajustou: " + qtdAjustada + " pagamentos.");
        } catch (Exception e) {
            Uteis.logar(e, ConfiguracaoSistema.class);
            manutencaoAjusteGeralTO.setMsgResultado("Não foi possivel ajustar os pagamentos: " + e.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void processarConciliadora() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("processarConciliadora", getEmpresaLogado());
            manutencaoAjusteGeralTO.setMsgResultado("");

            ConciliadoraServiceImpl conciliadoraService = new ConciliadoraServiceImpl(getFacade().getConvenioCobranca().getCon());
            conciliadoraService.processarAutomatico(Calendario.hoje());
            conciliadoraService = null;

            manutencaoAjusteGeralTO.setMsgResultado("O Processo executado");
        } catch (Exception e) {
            Uteis.logar(e, ConfiguracaoSistema.class);
            manutencaoAjusteGeralTO.setMsgResultado("Não foi possivel ajustar os pagamentos: " + e.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void cancelarBoletosCaixaDeAcordoComParcelas() {
        limparMsg();
        manutencaoAjusteGeralTO.setSucesso(false);
        manutencaoAjusteGeralTO.setMsgResultado("");
        Connection con;
        CaixaService service;
        Boleto boletoDAO;
        LogProcessoSistemaVO log = null;
        try {
            if (UteisValidacao.emptyString(getManutencaoAjusteGeralTO().getParcelasBoleto())) {
                throw new Exception("Não foi possivel cancelar os boletos, nenhuma parcela informada! ");
            }
            log = criarLog("processarCancelarBoletosCaixaDeAcordoComParcelas", getEmpresaLogado());
            con = Conexao.getFromSession();

            UsuarioVO usuarioVO = getUsuarioRecorrencia();
            int qtdBoletosEstavamDiferenteAguardandoPagamento = 0;
            int qtdBoletosForamCanceladosSucesso = 0;
            int qtdParcelasErro = 0;

            List<BoletoVO> boletosCancelar = new ArrayList<>();
            List<String> listaParcelas = Arrays.asList(getManutencaoAjusteGeralTO().getParcelasBoleto().split(";"));
            boletoDAO = new Boleto(con);
            for (String parcela : listaParcelas) {
                boletosCancelar.addAll(boletoDAO.consultarPorMovParcela(Integer.parseInt(parcela), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            }
            if (boletosCancelar.isEmpty()) {
                throw new Exception("Nenhum boleto encontrado para as parcelas mencionadas.");
            }
            for (BoletoVO boleto : boletosCancelar) {
                if (boleto.getSituacao().equals(SituacaoBoletoEnum.AGUARDANDO_PAGAMENTO)) {
                    try {
                        service = new CaixaService(con, boleto.getEmpresaVO().getCodigo(), boleto.getConvenioCobrancaVO().getCodigo());
                        service.cancelarBoletoViaProcesso(boleto, usuarioVO, "Cancelamento pelo ajuste do processo geral", true,
                                getManutencaoAjusteGeralTO().isTentarCancelarBoletosCaixaSemRegistro());
                        qtdBoletosForamCanceladosSucesso++;
                    } catch (Exception e) {
                        qtdParcelasErro++;
                    }
                } else {
                    qtdBoletosEstavamDiferenteAguardandoPagamento++;
                }
            }

            StringBuilder resposta = new StringBuilder();
            resposta.append("Verifiquei um a um, os " + boletosCancelar.size() + " boletos encontrados para cancelar... Veja o resultado dos boletos, \n");
            resposta.append(" Qtd. que conseguimos cancelar com sucesso: " + qtdBoletosForamCanceladosSucesso + "\n");
            resposta.append(" Qtd. que tentamos cancelar porem deu erro: " + qtdParcelasErro + "\n");
            resposta.append(" Qtd. que estava diferente da situação aguardando pagamento: " + qtdBoletosEstavamDiferenteAguardandoPagamento + "\n");
            manutencaoAjusteGeralTO.setSucesso(true);
            manutencaoAjusteGeralTO.setMsgResultado(resposta.toString());
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
            manutencaoAjusteGeralTO.setSucesso(false);
            manutencaoAjusteGeralTO.setMsgResultado(ex.getMessage());
        } finally {
            finalizarLogProcesso(log);
            con = null;
            boletoDAO = null;
            service = null;
        }
    }

    public void geolocalizarAlunos() {
        manutencaoAjusteGeralTO.setSucesso(false);
        manutencaoAjusteGeralTO.setMsgResultado("");
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("geolocalizarAlunos", getEmpresaLogado());
            GoogleApiGeocodeService servico = new GoogleApiGeocodeService(Conexao.getFromSession());
            servico.processarTodos();
            manutencaoAjusteGeralTO.setSucesso(true);
            manutencaoAjusteGeralTO.setMsgResultado("Alunos geolocalizados com sucesso.");
        } catch (Exception e) {
            Uteis.logar(e, ConfiguracaoSistema.class);
            manutencaoAjusteGeralTO.setMsgResultado("Não foi possível geolocalizar alunos, " + e.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public boolean isDisableBtnPontuacaoRetroativa() {
        disableBtnPontuacaoRetroativa = CorrigirPontuacaoRetroativa.getThreadRobo() != null && !CorrigirPontuacaoRetroativa.getThreadRobo().isFinalizado();
        return disableBtnPontuacaoRetroativa;
    }

    public void setDisableBtnPontuacaoRetroativa(boolean disableBtnPontuacaoRetroativa) {
        this.disableBtnPontuacaoRetroativa = disableBtnPontuacaoRetroativa;
    }

    private String getPathBackup() throws Exception {
        final String protocol = (String) JSFUtilities.getFromSession(JSFUtilities.PROTOCOL_LOGGED);
        StringBuffer url2 = request().getRequestURL();
        String path = Uteis.getPathPortApp(protocol, url2.toString(), request().getContextPath());
        path += "/faces/";
        if (!UteisValidacao.emptyString(protocol) && !UteisValidacao.emptyString(path)) {
            return path;
        } else {
            throw new Exception("Não foi possivel montar o path");
        }
    }

    public void persitirChavesSesc() {
        try {
            getFacade().getConfiguracaoSistema().persistirChavePublicaPrivadaSesc(getConfiguracaoSistemaVO());
            montarSucesso("msg_dados_gravados");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public List getListaSelectItemQuestionarioReMatricula() {
        return (listaSelectItemQuestionarioReMatricula);
    }

    public void setListaSelectItemQuestionarioReMatricula(List listaSelectItemQuestionarioReMatricula) {
        this.listaSelectItemQuestionarioReMatricula = listaSelectItemQuestionarioReMatricula;
    }

    public List getListaSelectItemQuestionarioRetorno() {
        return listaSelectItemQuestionarioRetorno;
    }

    public void setListaSelectItemQuestionarioRetorno(List listaSelectItemQuestionarioRetorno) {
        this.listaSelectItemQuestionarioRetorno = listaSelectItemQuestionarioRetorno;
    }

    public List getListaSelectItemQuestionarioPrimeiraVisita() {
        return listaSelectItemQuestionarioPrimeiraVisita;
    }

    public void setListaSelectItemQuestionarioPrimeiraVisita(List listaSelectItemQuestionarioPrimeiraVisita) {
        this.listaSelectItemQuestionarioPrimeiraVisita = listaSelectItemQuestionarioPrimeiraVisita;
    }

    public ConfiguracaoSistemaVO getConfiguracaoSistemaVO() {
        return configuracaoSistemaVO;
    }

    public void setConfiguracaoSistemaVO(ConfiguracaoSistemaVO configuracaoSistemaVO) {
        this.configuracaoSistemaVO = configuracaoSistemaVO;
    }

    public Boolean getMenuBasico() {
        return menuBasico;
    }

    public void setMenuBasico(Boolean menuBasico) {
        this.menuBasico = menuBasico;
    }

    public Boolean getMenuQuestionario() {
        return menuQuestionario;
    }

    public void setMenuQuestionario(Boolean menuQuestionario) {
        this.menuQuestionario = menuQuestionario;
    }

    public Boolean getMenuNovo() {
        return menuNovo;
    }

    public void setMenuNovo(Boolean menuNovo) {
        this.menuNovo = menuNovo;
    }

    public Boolean getMenuCamposObrigatorio() {
        return menuCamposObrigatorio;
    }

    public void setMenuCamposObrigatorio(Boolean menuCamposObrigatorio) {
        this.menuCamposObrigatorio = menuCamposObrigatorio;
    }

    public Boolean getMenuAcesso() {
        return menuAcesso;
    }

    public void setMenuAcesso(Boolean menuAcesso) {
        this.menuAcesso = menuAcesso;
    }

    public Boolean getMenuRobo() {
        return menuRobo;
    }

    public void setMenuRobo(Boolean menuRobo) {
        this.menuRobo = menuRobo;
    }

    public String getTextoExplicativoRobo() {
        return textoExplicativoRobo;
    }

    public void setTextoExplicativoRobo(String textoExplicativoRobo) {
        this.textoExplicativoRobo = textoExplicativoRobo;
    }

    public boolean isMenuOutros() {
        return menuOutros;
    }

    public void setMenuOutros(boolean menuOutros) {
        this.menuOutros = menuOutros;
    }

    public Boolean getMenuCamposObrigatorioApresentarPendentes() {
        return menuCamposObrigatorioApresentarPendentes;
    }

    public void setMenuCamposObrigatorioApresentarPendentes(Boolean menuCamposObrigatorioApresentarPendentes) {
        this.menuCamposObrigatorioApresentarPendentes = menuCamposObrigatorioApresentarPendentes;
    }

    public Boolean getMenuCamposObrigatorioApresentarVisitantes() {
        return menuCamposObrigatorioApresentarVisitantes;
    }

    public void setMenuCamposObrigatorioApresentarVisitantes(Boolean menuCamposObrigatorioApresentarVisitantes) {
        this.menuCamposObrigatorioApresentarVisitantes = menuCamposObrigatorioApresentarVisitantes;
    }

    public void setMenuManutencaoBI(boolean menuManutencaoBI) {
        this.menuManutencaoBI = menuManutencaoBI;
    }

    public boolean isMenuManutencaoBI() {
        return menuManutencaoBI;
    }

    public void setTotalContratosAProcessar(Integer totalContratosAProcessar) {
        this.totalContratosAProcessar = totalContratosAProcessar;
    }

    public void setTotalContratosProcessados(Integer totalContratosProcessados) {
        this.totalContratosProcessados = totalContratosProcessados;
    }

    public Integer getTotalContratosAProcessar() {
        return totalContratosAProcessar;
    }

    public Integer getTotalContratosProcessados() {
        return totalContratosProcessados;
    }

    public String getPorcProcessConcluido() {
        if (totalContratosAProcessar != 0 && totalContratosProcessados != 0) {
            return Math.rint(totalContratosProcessados / totalContratosAProcessar * 100) + "%";
        } else {
            return "0%";
        }
    }

    public String getPorcProcessMPPConcluido() {
        if (totalAProcessarMovProdutoParcela != 0 && totalProcessadosMovProdutoParcela != 0) {
            return Math.rint(totalProcessadosMovProdutoParcela / totalAProcessarMovProdutoParcela * 100) + "%";
        } else {
            return "0%";
        }
    }

    public String getPorcProcessParcelaConcluido() {
        if (totalAProcessarParcelas != 0 && totalProcessadosParcelas != 0) {
            return Math.rint(totalProcessadosParcelas / totalAProcessarParcelas * 100) + "%";
        } else {
            return "0%";
        }
    }

    public String getPorcProcessParcelaCCConcluido() {
        if (totalAProcessarParcelasCC != 0 && totalProcessadosParcelasCC != 0) {
            return Math.rint(totalProcessadosParcelasCC / totalAProcessarParcelasCC * 100) + "%";
        } else {
            return "0%";
        }
    }

    public String getPorcProcessCHVConcluido() {
        if (totalAProcessarHistoricoVinculo != 0 && totalProcessadosHistoricoVinculo != 0) {
            return Math.rint(totalProcessadosHistoricoVinculo / totalAProcessarHistoricoVinculo * 100) + "%";
        } else {
            return "0%";
        }
    }

    public String getPorcProcessCVProfessorConcluido() {
        if (totalAProcessarVinculoProfessor != 0 && totalProcessadosVinculoProfessor != 0) {
            return Math.rint(totalProcessadosVinculoProfessor / totalAProcessarVinculoProfessor * 100) + "%";
        } else {
            return "0%";
        }
    }

    public boolean isMenuManutencao() {
        return menuManutencao;
    }

    public boolean isMenuEcf() {
        return menuEcf;
    }

    public void setMenuManutencao(boolean menuManutencao) {
        this.menuManutencao = menuManutencao;
    }

    public void setMenuEcf(boolean menuEcf) {
        this.menuEcf = menuEcf;
    }

    public List<SelectItem> getListaAliquotas() {
        List<SelectItem> listaAliquotas = new ArrayList<>();
        AliquotasFiscaisEnum[] aliquotas = AliquotasFiscaisEnum.values();
        for (AliquotasFiscaisEnum aliquota : aliquotas) {
            SelectItem item = new SelectItem(aliquota.getCodigo(), aliquota.getDescricao());
            listaAliquotas.add(item);
        }
        return listaAliquotas;
    }

    public void setMenuRecorrencia(boolean menuRecorrencia) {
        this.menuRecorrencia = menuRecorrencia;
    }

    public boolean isMenuRecorrencia() {
        return menuRecorrencia;
    }

    public void setEmailResponsavel(String emailResponsavel) {
        this.emailResponsavel = emailResponsavel;
    }

    public String getEmailResponsavel() {
        if (emailResponsavel == null) {
            emailResponsavel = "";
        }
        return emailResponsavel;
    }

    public void setMenuMovProdutoParcelas(boolean menuMovProdutoParcelas) {
        this.menuMovProdutoParcelas = menuMovProdutoParcelas;
    }

    public boolean isMenuMovProdutoParcelas() {
        return menuMovProdutoParcelas;
    }

    public void setTotalProcessadosMovProdutoParcela(double totalProcessadosMovProdutoParcela) {
        this.totalProcessadosMovProdutoParcela = totalProcessadosMovProdutoParcela;
    }

    public double getTotalProcessadosMovProdutoParcela() {
        return totalProcessadosMovProdutoParcela;
    }

    public void setTotalAProcessarMovProdutoParcela(double totalAProcessarMovProdutoParcela) {
        this.totalAProcessarMovProdutoParcela = totalAProcessarMovProdutoParcela;
    }

    public double getTotalAProcessarMovProdutoParcela() {
        return totalAProcessarMovProdutoParcela;
    }

    public void setMenuHistoricoVinculo(Boolean menuHistoricoVinculo) {
        this.menuHistoricoVinculo = menuHistoricoVinculo;
    }

    public Boolean getMenuHistoricoVinculo() {
        return menuHistoricoVinculo;
    }

    public void setTotalAProcessarHistoricoVinculo(double totalAProcessarHistoricoVinculo) {
        this.totalAProcessarHistoricoVinculo = totalAProcessarHistoricoVinculo;
    }

    public double getTotalAProcessarHistoricoVinculo() {
        return totalAProcessarHistoricoVinculo;
    }

    public void setTotalProcessadosHistoricoVinculo(double totalProcessadosHistoricoVinculo) {
        this.totalProcessadosHistoricoVinculo = totalProcessadosHistoricoVinculo;
    }

    public double getTotalProcessadosHistoricoVinculo() {
        return totalProcessadosHistoricoVinculo;
    }

    public List getListaSelectItemLocalAcessoChamada() {
        return listaSelectItemLocalAcessoChamada;
    }

    public void setListaSelectItemLocalAcessoChamada(List listaSelectItemLocalAcessoChamada) {
        this.listaSelectItemLocalAcessoChamada = listaSelectItemLocalAcessoChamada;
    }

    public List getListaSelectItemColetorChamada() {
        return listaSelectItemColetorChamada;
    }

    public void setListaSelectItemColetorChamada(List listaSelectItemColetorChamada) {
        this.listaSelectItemColetorChamada = listaSelectItemColetorChamada;
    }

    public boolean isMenuExclusaoMensagensBVsPendentes() {
        return menuExclusaoMensagensBVsPendentes;
    }

    public void setMenuExclusaoMensagensBVsPendentes(boolean menuExclusaoMensagensBVsPendentes) {
        this.menuExclusaoMensagensBVsPendentes = menuExclusaoMensagensBVsPendentes;
    }

    public ColaboradorVO getColaboradorBVsPendentes() {
        return colaboradorBVsPendentes;
    }

    public void setColaboradorBVsPendentes(ColaboradorVO colaboradorBVsPendentes) {
        this.colaboradorBVsPendentes = colaboradorBVsPendentes;
    }

    public Date getDataInicialBVsPendentes() {
        return dataInicialBVsPendentes;
    }

    public void setDataInicialBVsPendentes(Date dataInicialBVsPendentes) {
        this.dataInicialBVsPendentes = dataInicialBVsPendentes;
    }

    public Date getDtInicialPontucaoRetroativa() {
        return dtInicialPontucaoRetroativa;
    }

    public void setDtInicialPontucaoRetroativa(Date dtInicialPontucaoRetroativa) {
        this.dtInicialPontucaoRetroativa = dtInicialPontucaoRetroativa;
    }

    public Date getDataFinalBVsPendentes() {
        return dataFinalBVsPendentes;
    }

    public void setDataFinalBVsPendentes(Date dataFinalBVsPendentes) {
        this.dataFinalBVsPendentes = dataFinalBVsPendentes;
    }

    public String getMsgExclusaoMsgBVsPendentes() {
        return msgExclusaoMsgBVsPendentes;
    }

    public void setMsgExclusaoMsgBVsPendentes(String msgExclusaoMsgBVsPendentes) {
        this.msgExclusaoMsgBVsPendentes = msgExclusaoMsgBVsPendentes;
    }

    public void setTotalAProcessarParcelas(double totalAProcessarParcelas) {
        this.totalAProcessarParcelas = totalAProcessarParcelas;
    }

    public double getTotalAProcessarParcelas() {
        return totalAProcessarParcelas;
    }

    public void setTotalProcessadosParcelas(double totalAProcessadosParcelas) {
        this.totalProcessadosParcelas = totalAProcessadosParcelas;
    }

    public double getTotalProcessadosParcelas() {
        return totalProcessadosParcelas;
    }

    public void setOnCompleteRestaurar(String onCompleteRestaurar) {
        this.onCompleteRestaurar = onCompleteRestaurar;
    }

    public String getOnCompleteRestaurar() {
        return onCompleteRestaurar;
    }

    public void setRestaurarAtivo(boolean restaurarAtivo) {
        this.restaurarAtivo = restaurarAtivo;
    }

    public boolean getRestaurarAtivo() {
        return restaurarAtivo;
    }

    public void setDeletarProduto(boolean deletarProduto) {
        this.deletarProduto = deletarProduto;
    }

    public boolean getDeletarProduto() {
        return deletarProduto;
    }

    public void setProdutoSubstituir(Integer produtoSubstituir) {
        this.produtoSubstituir = produtoSubstituir;
    }

    public Integer getProdutoSubstituir() {
        return produtoSubstituir;
    }

    public void setProdutoSubstituido(Integer produtoSubstituido) {
        this.produtoSubstituido = produtoSubstituido;
    }

    public Integer getProdutoSubstituido() {
        return produtoSubstituido;
    }

    public double getTotalAProcessarParcelasCC() {
        return totalAProcessarParcelasCC;
    }

    public void setTotalAProcessarParcelasCC(double totalAProcessarParcelasCC) {
        this.totalAProcessarParcelasCC = totalAProcessarParcelasCC;
    }

    public double getTotalProcessadosParcelasCC() {
        return totalProcessadosParcelasCC;
    }

    public void setTotalProcessadosParcelasCC(double totalProcessadosParcelasCC) {
        this.totalProcessadosParcelasCC = totalProcessadosParcelasCC;
    }

    public Boolean getMenuContrato() {
        return menuContrato;
    }

    public void setMenuContrato(Boolean menuContrato) {
        this.menuContrato = menuContrato;
    }

    public int getContrato() {
        return contrato;
    }

    public void setContrato(int contrato) {
        this.contrato = contrato;
    }

    public String getDdd() {
        return ddd;
    }

    public void setDdd(String ddd) {
        this.ddd = ddd;
    }

    public void setEmpresaBV(int empresaBV) {
        this.empresaBV = empresaBV;
    }

    public int getEmpresaBV() {
        return empresaBV;
    }

    public void setEmpresasBV(List<SelectItem> empresasBV) {
        this.empresasBV = empresasBV;
    }

    public List<SelectItem> getEmpresasBV() throws Exception {
        if (UteisValidacao.emptyList(empresasBV)) {
            empresasBV.add(new SelectItem(0, ""));
            List<EmpresaVO> emps = getFacade().getEmpresa().consultarPorNome("", true, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            for (EmpresaVO empresa : emps) {
                empresasBV.add(new SelectItem(empresa.getCodigo(), empresa.getNome()));
            }
        }
        return empresasBV;
    }

    public double getTotalAProcessarVinculoProfessor() {
        return totalAProcessarVinculoProfessor;
    }

    public void setTotalAProcessarVinculoProfessor(double totalAProcessarVinculoProfessor) {
        this.totalAProcessarVinculoProfessor = totalAProcessarVinculoProfessor;
    }

    public double getTotalProcessadosVinculoProfessor() {
        return totalProcessadosVinculoProfessor;
    }

    public void setTotalProcessadosVinculoProfessor(double totalProcessadosVinculoProfessor) {
        this.totalProcessadosVinculoProfessor = totalProcessadosVinculoProfessor;
    }

    public void setVerificacao(VerificarRecibosPagamentosSemVinculo verificacao) {

        this.verificacao = verificacao;
    }

    public VerificarRecibosPagamentosSemVinculo getVerificacao() {
        if (verificacao == null){
            verificacao = new VerificarRecibosPagamentosSemVinculo(Conexao.getFromSession());
        }
        return verificacao;
    }

    public List<String> getAlterados() {
        return alterados;
    }

    public void setAlterados(List<String> alterados) {
        this.alterados = alterados;
    }

    public Boolean getMenuQuestionarioSessao() {
        return menuQuestionarioSessao;
    }

    public void setMenuQuestionarioSessao(Boolean menuQuestionarioSessao) {
        this.menuQuestionarioSessao = menuQuestionarioSessao;
    }

    public List getListaSelectItemQuestionarioPrimeiraCompra() {
        return listaSelectItemQuestionarioPrimeiraCompra;
    }

    public void setListaSelectItemQuestionarioPrimeiraCompra(List listaSelectItemQuestionarioPrimeiraCompra) {
        this.listaSelectItemQuestionarioPrimeiraCompra = listaSelectItemQuestionarioPrimeiraCompra;
    }

    public List getListaSelectItemQuestionarioRetornoCompra() {
        return listaSelectItemQuestionarioRetornoCompra;
    }

    public void setListaSelectItemQuestionarioRetornoCompra(List listaSelectItemQuestionarioRetornoCompra) {
        this.listaSelectItemQuestionarioRetornoCompra = listaSelectItemQuestionarioRetornoCompra;
    }

    public String getIdVendaNuvemShop() {
        return idVendaNuvemShop;
    }

    public void setIdVendaNuvemShop(String idVendaNuvemShop) {
        this.idVendaNuvemShop = idVendaNuvemShop;
    }

    public String getMsgRetornoReprocessarIntegracaoNuvemShop() {
        if (msgRetornoReprocessarIntegracaoNuvemShop == null) {
            return "";
        }
        return msgRetornoReprocessarIntegracaoNuvemShop;
    }

    public void setMsgRetornoReprocessarIntegracaoNuvemShop(String msgRetornoReprocessarIntegracaoNuvemShop) {
        this.msgRetornoReprocessarIntegracaoNuvemShop = msgRetornoReprocessarIntegracaoNuvemShop;
    }

    public List getClonarListaConfiguracaoSistemaCadastroClienteVO(List lista) throws Exception {
        List listaNova = new ArrayList<>(0);
        for (int i = 0; i < lista.size(); i++) {
            listaNova.add(((ConfiguracaoSistemaCadastroClienteVO) lista.get(i)).getClone(false));
        }
        return listaNova;
    }


    public void registrarLogCamposCadastroClientesVisitantes(List listaAlterada, List listaOriginal, String operacao) throws Exception {
        for (int i = 0; i < listaAlterada.size(); i++) {
            ConfiguracaoSistemaCadastroClienteVO alterado = (ConfiguracaoSistemaCadastroClienteVO) listaAlterada.get(i);
            ConfiguracaoSistemaCadastroClienteVO original = (ConfiguracaoSistemaCadastroClienteVO) listaOriginal.get(i);
            if (alterado.getNome().equals(original.getNome())) {
                if (alterado.getMostrar() != original.getMostrar()) {
                    registrarLogObjetoVO(montarRegistroLogCampoCadastroClientesVisitantes(alterado.getNome() + " (Apresentar) ", alterado.getMostrar(), original.getMostrar(), operacao), getConfiguracaoSistemaVO().getCodigo());
                }
                if (alterado.getPendente() != original.getPendente()) {
                    registrarLogObjetoVO(montarRegistroLogCampoCadastroClientesVisitantes(alterado.getNome() + " (Necessário) ", alterado.getPendente(), original.getPendente(), operacao), getConfiguracaoSistemaVO().getCodigo());
                }
                if (alterado.getObrigatorio() != original.getObrigatorio()) {
                    registrarLogObjetoVO(montarRegistroLogCampoCadastroClientesVisitantes(alterado.getNome() + " (Obrigatório) ", alterado.getObrigatorio(), original.getObrigatorio(), operacao), getConfiguracaoSistemaVO().getCodigo());
                }
            }
        }
    }

    public Boolean getMenuCamposObrigatorioApresentarColaborador() {
        return menuCamposObrigatorioApresentarColaborador;
    }

    public void setMenuCamposObrigatorioApresentarColaborador(Boolean menuCamposObrigatorioApresentarColaborador) {
        this.menuCamposObrigatorioApresentarColaborador = menuCamposObrigatorioApresentarColaborador;
    }

    public LogVO montarRegistroLogCampoCadastroClientesVisitantes(String campo, boolean alterado, boolean original, String operacao) throws Exception {
        LogVO obj = new LogVO();
        obj.setChavePrimaria(getConfiguracaoSistemaVO().getCodigo().toString());
        obj.setNomeEntidade("CONFIGURACAOSISTEMA");
        obj.setNomeEntidadeDescricao("Configurações");
        obj.setOperacao(operacao);
        obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
        obj.setUserOAMD(getUsuarioLogado().getUserOamd());
        obj.setNomeCampo(campo);
        obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
        obj.setValorCampoAnterior(getMarcouDesmacou(original));
        obj.setValorCampoAlterado(getMarcouDesmacou(alterado));
        return obj;
    }

    public String getMarcouDesmacou(Boolean valor) {
        if (!valor) {
            return " DESMARCADO ";
        } else {
            return " MARCADO ";
        }
    }

    public void marcarDesmarcarTodosCamposColaboradorObrigatorio() {
        //boolean marcarObrigatorio = this.listaCamposColaboradorDinamico.get(0).isCampoObrigatorio();
        for (CadastroDinamicoItemVO cadastroDinamicoItemVO : this.listaCamposColaboradorDinamico) {
            cadastroDinamicoItemVO.setCampoObrigatorio(this.selecionarTodosObrigatorio);
            if (this.selecionarTodosObrigatorio) {
                this.selecionarTodosApresentar = true;
                cadastroDinamicoItemVO.setMostrarCampo(true);
            }
        }
    }

    public void marcarCampoColaboradorObrigatorio() {
        if (cadastroDinamicoItemVO.isCampoObrigatorio()) {
            cadastroDinamicoItemVO.setMostrarCampo(true);
        }
    }

    public void marcarCampoColaboradorApresentar() {
        if (!(cadastroDinamicoItemVO.isMostrarCampo())) {
            cadastroDinamicoItemVO.setCampoObrigatorio(false);
        }
    }

    public void resetCamposMascaraTelefoneDtNasc() {
        if (!configuracaoSistemaVO.isUsarSistemaInternacional()) {
            configuracaoSistemaVO.setMascaraTelefone("");
            configuracaoSistemaVO.setUtilizarFormatoMMDDYYYDtNascimento(false);
        }
    }

    public void marcarDesmarcarTodosCamposColaboradorApresentar() {
        //boolean marcarApresentar = this.listaCamposColaboradorDinamico.get(0).isMostrarCampo();
        for (CadastroDinamicoItemVO cadastroDinamicoItemVO : this.listaCamposColaboradorDinamico) {
            cadastroDinamicoItemVO.setMostrarCampo(this.selecionarTodosApresentar);
        }
    }

    public List<CadastroDinamicoItemVO> getListaCamposColaboradorDinamico() {
        return listaCamposColaboradorDinamico;
    }

    public void setListaCamposColaboradorDinamico(List<CadastroDinamicoItemVO> listaCamposColaboradorDinamico) {
        this.listaCamposColaboradorDinamico = listaCamposColaboradorDinamico;
    }

    public boolean isSelecionarTodosApresentar() {
        return selecionarTodosApresentar;
    }

    public void setSelecionarTodosApresentar(boolean selecionarTodosApresentar) {
        this.selecionarTodosApresentar = selecionarTodosApresentar;
    }

    public boolean isSelecionarTodosObrigatorio() {
        return selecionarTodosObrigatorio;
    }

    public void setSelecionarTodosObrigatorio(boolean selecionarTodosObrigatorio) {
        this.selecionarTodosObrigatorio = selecionarTodosObrigatorio;
    }

    public CadastroDinamicoItemVO getCadastroDinamicoItemVO() {
        return cadastroDinamicoItemVO;
    }

    public void setCadastroDinamicoItemVO(CadastroDinamicoItemVO cadastroDinamicoItemVO) {
        this.cadastroDinamicoItemVO = cadastroDinamicoItemVO;
    }

    public boolean isMenuAjustesGerais() {
        return menuAjustesGerais;
    }

    public void setMenuAjustesGerais(boolean menuAjustesGerais) {
        this.menuAjustesGerais = menuAjustesGerais;
    }

    public boolean isImportacao() {
        return importacao;
    }

    public void setImportacao(boolean importacao) {
        this.importacao = importacao;
    }

    public boolean isUpdateClienteSituacao() {
        return updateClienteSituacao;
    }

    public void setUpdateClienteSituacao(boolean updateClienteSituacao) {
        this.updateClienteSituacao = updateClienteSituacao;
    }

    public boolean isMigradorMOVProdutoModalidade() {
        return migradorMOVProdutoModalidade;
    }

    public void setMigradorMOVProdutoModalidade(boolean migradorMOVProdutoModalidade) {
        this.migradorMOVProdutoModalidade = migradorMOVProdutoModalidade;
    }

    public boolean isAjustarLinhaDoTempoDeContratos() {
        return ajustarLinhaDoTempoDeContratos;
    }

    public void setAjustarLinhaDoTempoDeContratos(boolean ajustarLinhaDoTempoDeContratos) {
        this.ajustarLinhaDoTempoDeContratos = ajustarLinhaDoTempoDeContratos;
    }

    public boolean isAjustarDatasRenovacaoImportacao() {
        return ajustarDatasRenovacaoImportacao;
    }

    public void setAjustarDatasRenovacaoImportacao(boolean ajustarDatasRenovacaoImportacao) {
        this.ajustarDatasRenovacaoImportacao = ajustarDatasRenovacaoImportacao;
    }

    public ManutencaoAjusteGeralTO getManutencaoAjusteGeralTO() {
        return manutencaoAjusteGeralTO;
    }

    public void setManutencaoAjusteGeralTO(ManutencaoAjusteGeralTO manutencaoAjusteGeralTO) {
        this.manutencaoAjusteGeralTO = manutencaoAjusteGeralTO;
    }

    public List<LogAjusteGeralVO> getLogAjusteGeralVOList() {
        if (logAjusteGeralVOList == null) {
            logAjusteGeralVOList = new ArrayList<>();
        }
        return logAjusteGeralVOList;
    }

    public void setLogAjusteGeralVOList(List<LogAjusteGeralVO> logAjusteGeralVOList) {
        this.logAjusteGeralVOList = logAjusteGeralVOList;
    }

    public List<ObjetoGenerico> getClientesDuplicados() {
        if (clientesDuplicados == null) {
            clientesDuplicados = new ArrayList<>();
        }
        return clientesDuplicados;
    }

    public void setClientesDuplicados(List<ObjetoGenerico> clientesDuplicados) {
        this.clientesDuplicados = clientesDuplicados;
    }

    public String getObsClientesDuplicados() {
        if (obsClientesDuplicados == null) {
            obsClientesDuplicados = "";
        }
        return obsClientesDuplicados;
    }

    public void setObsClientesDuplicados(String obsClientesDuplicados) {
        this.obsClientesDuplicados = obsClientesDuplicados;
    }

    public List<SelectItem> getListaSelectItemModalidades() {
        if (listaSelectItemModalidades == null) {
            listaSelectItemModalidades = new ArrayList<>();
        }
        return listaSelectItemModalidades;
    }

    public void setListaSelectItemModalidades(List<SelectItem> listaSelectItemModalidades) {
        this.listaSelectItemModalidades = listaSelectItemModalidades;
    }

    public ModalidadeVO getModalidadeVO() {
        if (modalidadeVO == null) {
            modalidadeVO = new ModalidadeVO();
        }
        return modalidadeVO;
    }

    public void setModalidadeVO(ModalidadeVO modalidadeVO) {
        this.modalidadeVO = modalidadeVO;
    }

    public ModalidadeVO getModalidadeVORetirar() {
        if (modalidadeVORetirar == null) {
            modalidadeVORetirar = new ModalidadeVO();
        }
        return modalidadeVORetirar;
    }

    public void setModalidadeVORetirar(ModalidadeVO modalidadeVORetirar) {
        this.modalidadeVORetirar = modalidadeVORetirar;
    }

    public void montarListasAdicionarModalidadeAoPlano() {
        montarListaSelectItemModalidades();
        setPlanoAdicionarModalidadeAoPlano(new PlanoVO());
        setListaSelectItemPlanosAdicionarModalidadeAoPlano(montarListaSelectItemPlanos(getEmpresaAdicionarModalidadeAoPlano(), false, null));
        setHorarioContratoAdicionarModalidade(0);
        setListaSelectItemHorariosAdicionarModalidadeAoPlano(montarListaSelectItemHorariosContrato(getEmpresaAdicionarModalidadeAoPlano()));
    }

    public void montarListasRetirarModalidadeAoPlano() {
        montarListaSelectItemModalidades();
        setPlanoRetirarModalidadeAoPlano(new PlanoVO());
        setListaSelectItemPlanosRetirarModalidadeAoPlano(montarListaSelectItemPlanos(getEmpresaRetirarModalidadeAoPlano(), false, null));
    }

    public void montarListasAdicionarDiasContratoDePlano() {
        setPlanoAdicionarDiasContratoDePlano(new PlanoVO());
        setListaSelectItemPlanosAdicionarDiasContratoDePlano(montarListaSelectItemPlanos(getEmpresaAdicionarDiasContratoDePlano(), false, null));
    }

    public void montarListaSelectItemModalidades() {
        try {
            setListaSelectItemModalidades(new ArrayList<>());
            if (getEmpresaAdicionarModalidadeAoPlano() != null && getEmpresaAdicionarModalidadeAoPlano().getCodigo() > 0) {
                List<ModalidadeVO> resultadoConsulta = getFacade().getModalidade().consultarTodasModalidades(getEmpresaAdicionarModalidadeAoPlano().getCodigo(), true, null);
                for (ModalidadeVO obj : resultadoConsulta) {
                    getListaSelectItemModalidades().add(new SelectItem(obj.getCodigo(), obj.getNome()));
                }
            } else if (getEmpresaRetirarModalidadeAoPlano() != null && getEmpresaRetirarModalidadeAoPlano().getCodigo() > 0) {
                List<ModalidadeVO> resultadoConsulta = getFacade().getModalidade().consultarTodasModalidades(getEmpresaRetirarModalidadeAoPlano().getCodigo(), true, null);
                for (ModalidadeVO modalidadeVO : resultadoConsulta) {
                    getListaSelectItemModalidades().add(new SelectItem(modalidadeVO.getCodigo(), modalidadeVO.getNome()));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public List<SelectItem> getListaSelectItemPlanosAdicionarModalidadeAoPlano() {
        if (listaSelectItemPlanosAdicionarModalidadeAoPlano == null) {
            listaSelectItemPlanosAdicionarModalidadeAoPlano = new ArrayList<>();
        }
        return listaSelectItemPlanosAdicionarModalidadeAoPlano;
    }

    public void setListaSelectItemPlanosAdicionarModalidadeAoPlano(List<SelectItem> listaSelectItemPlanosAdicionarModalidadeAoPlano) {
        this.listaSelectItemPlanosAdicionarModalidadeAoPlano = listaSelectItemPlanosAdicionarModalidadeAoPlano;
    }

    public List<SelectItem> getListaSelectItemHorariosAdicionarModalidadeAoPlano() {
        if (listaSelectItemHorariosAdicionarModalidadeAoPlano == null) {
            listaSelectItemHorariosAdicionarModalidadeAoPlano = new ArrayList<>();
        }
        return listaSelectItemHorariosAdicionarModalidadeAoPlano;
    }

    public void setListaSelectItemHorariosAdicionarModalidadeAoPlano(List<SelectItem> listaSelectItemHorariosAdicionarModalidadeAoPlano) {
        this.listaSelectItemHorariosAdicionarModalidadeAoPlano = listaSelectItemHorariosAdicionarModalidadeAoPlano;
    }

    public List<SelectItem> getListaSelectItemHorariosAnterior() {
        if (listaSelectItemHorariosAnterior == null) {
            listaSelectItemHorariosAnterior = new ArrayList<>();
        }
        return listaSelectItemHorariosAnterior;
    }

    public void setListaSelectItemHorariosAnterior(List<SelectItem> listaSelectItemHorariosAnterior) {
        this.listaSelectItemHorariosAnterior = listaSelectItemHorariosAnterior;
    }

    public List<SelectItem> getListaSelectItemPlanosRetirarModalidadeAoPlano() {
        if (listaSelectItemPlanosRetirarModalidadeAoPlano == null) {
            listaSelectItemPlanosRetirarModalidadeAoPlano = new ArrayList<>();
        }
        return listaSelectItemPlanosRetirarModalidadeAoPlano;
    }

    public void setListaSelectItemPlanosRetirarModalidadeAoPlano(List<SelectItem> listaSelectItemPlanosRetirarModalidadeAoPlano) {
        this.listaSelectItemPlanosRetirarModalidadeAoPlano = listaSelectItemPlanosRetirarModalidadeAoPlano;
    }

    public List<SelectItem> getListaSelectItemPlanosAdicionarDiasContratoDePlano() {
        if (listaSelectItemPlanosAdicionarDiasContratoDePlano == null) {
            listaSelectItemPlanosAdicionarDiasContratoDePlano = new ArrayList<>();
        }
        return listaSelectItemPlanosAdicionarDiasContratoDePlano;
    }

    public void setListaSelectItemPlanosAdicionarDiasContratoDePlano(List<SelectItem> listaSelectItemPlanosAdicionarDiasContratoDePlano) {
        this.listaSelectItemPlanosAdicionarDiasContratoDePlano = listaSelectItemPlanosAdicionarDiasContratoDePlano;
    }

    public PlanoVO getPlanoAdicionarModalidadeAoPlano() {
        if (planoAdicionarModalidadeAoPlano == null) {
            planoAdicionarModalidadeAoPlano = new PlanoVO();
        }
        return planoAdicionarModalidadeAoPlano;
    }

    public void setPlanoAdicionarModalidadeAoPlano(PlanoVO planoAdicionarModalidadeAoPlano) {
        this.planoAdicionarModalidadeAoPlano = planoAdicionarModalidadeAoPlano;
    }

    public PlanoVO getPlanoRetirarModalidadeAoPlano() {
        if (planoRetirarModalidadeAoPlano == null) {
            planoRetirarModalidadeAoPlano = new PlanoVO();
        }
        return planoRetirarModalidadeAoPlano;
    }

    public void setPlanoRetirarModalidadeAoPlano(PlanoVO planoRetirarModalidadeAoPlano) {
        this.planoRetirarModalidadeAoPlano = planoRetirarModalidadeAoPlano;
    }

    public PlanoVO getPlanoAdicionarDiasContratoDePlano() {
        if (planoAdicionarDiasContratoDePlano == null) {
            planoAdicionarDiasContratoDePlano = new PlanoVO();
        }
        return planoAdicionarDiasContratoDePlano;
    }

    public void setPlanoAdicionarDiasContratoDePlano(PlanoVO planoAdicionarDiasContratoDePlano) {
        this.planoAdicionarDiasContratoDePlano = planoAdicionarDiasContratoDePlano;
    }

    public List<SelectItem> montarListaSelectItemPlanos(EmpresaVO empresa, boolean addSemValor, final String nomeSemValor) {
        List<SelectItem> listaSelectItemPlanos = new ArrayList<>();
        try {
            if (addSemValor) {
                listaSelectItemPlanos.add(new SelectItem(0, nomeSemValor));
            }
            if (empresa != null && empresa.getCodigo() > 0) {
                List<PlanoVO> resultadoConsulta = getFacade().getPlano().consultarTodosOsPlanosVendas(empresa.getCodigo());
                for (PlanoVO obj : resultadoConsulta) {
                    listaSelectItemPlanos.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return listaSelectItemPlanos;
    }

    public List<SelectItem> montarListaSelectItemHorariosContrato(EmpresaVO empresa) {
        List<SelectItem> listaSelectItemHorarios = new ArrayList<>();
        try {
            if (empresa != null && empresa.getCodigo() > 0) {
                listaSelectItemHorarios.add(new SelectItem(0, ""));

                List<String> resultadoConsulta = getFacade().getHorario().consultarTodosHorariosPorContrato();
                for (String obj : resultadoConsulta) {
                    listaSelectItemHorarios.add(new SelectItem(resultadoConsulta.indexOf(obj)+1, obj));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return listaSelectItemHorarios;
    }

    public List<ContratoVO> getContratosAdicionarModalidade() {
        if (contratosAdicionarModalidade == null) {
            contratosAdicionarModalidade = new ArrayList<>();
        }
        return contratosAdicionarModalidade;
    }

    public void setContratosAdicionarModalidade(List<ContratoVO> contratosAdicionarModalidade) {
        this.contratosAdicionarModalidade = contratosAdicionarModalidade;
    }

    public List<ContratoVO> getContratosRetirarModalidade() {
        if (contratosRetirarModalidade == null) {
            contratosRetirarModalidade = new ArrayList<>();
        }
        return contratosRetirarModalidade;
    }

    public void setContratosRetirarModalidade(List<ContratoVO> contratosRetirarModalidade) {
        this.contratosRetirarModalidade = contratosRetirarModalidade;
    }

    public void consultarContratosAdicionarModalidade() throws Exception {
        AdicionarModalidadeEmPlano adicionarModalidadeEmPlano = new AdicionarModalidadeEmPlano();

        setContratosAdicionarModalidade(adicionarModalidadeEmPlano.consultarContratosAdicionarModalidade(getEmpresaAdicionarModalidadeAoPlano().getCodigo(), getPlanoAdicionarModalidadeAoPlano(), getModalidadeVO(), getHorarioContratoAdicionarModalidadeString()));
        manutencaoAjusteGeralTO.setMsgResultado(UteisValidacao.emptyList(getContratosAdicionarModalidade()) ? "Todos contratos ativos desse plano já tem a modalidade" : "");
    }

    public void processarContratosAdicionarModalidade() throws Exception {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("processarContratosAdicionarModalidade", getEmpresaLogado());
            AdicionarModalidadeEmPlano adicionarModalidadeEmPlano = new AdicionarModalidadeEmPlano();
            setModalidadeVO(getFacade().getModalidade().consultarPorChavePrimaria(getModalidadeVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            setPlanoAdicionarModalidadeAoPlano(getFacade().getPlano().consultarPorChavePrimaria(getPlanoAdicionarModalidadeAoPlano().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            int qtdContratosAfetados = 0;
            StringBuilder contratosNaoAdicionados = new StringBuilder("");
            for (ContratoVO contratoVO : getContratosAdicionarModalidade()) {
                try {
                    adicionarModalidadeEmPlano.adicionarModalidadeContrato(contratoVO, getModalidadeVO(), getUsuarioLogado());
                    getFacade().getZWFacade().atualizarSintetico(contratoVO.getCliente(), Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_CONTRATO, true);
                    qtdContratosAfetados++;
                } catch (Exception ex) {
                    contratosNaoAdicionados.append(contratoVO.getCodigo()).append(",");
                }
            }

            LogVO obj = new LogVO();
            obj.setChavePrimaria("0");
            obj.setPessoa(0);
            obj.setNomeEntidade("CONFIGURACAOSISTEMA-ADICIONAR_MODALIDADE_CONTRATO");
            obj.setNomeEntidadeDescricao("Configurações");
            obj.setOperacao("Adicionar Modalidade (Processo)");
            obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
            obj.setUserOAMD(getUsuarioLogado().getUserOamd());
            obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            obj.setNomeCampo("");
            obj.setValorCampoAnterior("");
            String msg = "Adicionada a modalidade " + getModalidadeVO().getNome() + " para os contratos do plano " + getPlanoAdicionarModalidadeAoPlano().getDescricao() +
                    (!UteisValidacao.emptyString(getHorarioContratoAdicionarModalidadeString()) ? " e horário " + getHorarioContratoAdicionarModalidadeString() : "") +
                    " (Total: " + qtdContratosAfetados + ").";
            if (contratosNaoAdicionados.length() > 0) {
                msg += " Contratos não afetados: " + contratosNaoAdicionados.substring(0, contratosNaoAdicionados.length() - 2);
            }
            obj.setValorCampoAlterado(msg);
            getFacade().getLog().incluir(obj);

            setContratosAdicionarModalidade(adicionarModalidadeEmPlano.consultarContratosAdicionarModalidade(getEmpresaAdicionarModalidadeAoPlano().getCodigo(), getPlanoAdicionarModalidadeAoPlano(), getModalidadeVO(), getHorarioContratoAdicionarModalidadeString()));
            manutencaoAjusteGeralTO.setMsgResultado("Modalidade adicionada");
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void consultarLogAdicionarModalidadeEmPlano(ActionEvent event) throws Exception {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        loginControle.setNomeClasse("Adicionar modalidade em contratos de determinado plano");
        List<LogVO> logs = getFacade().getLog().consultarPorNomeEntidade("CONFIGURACAOSISTEMA-ADICIONAR_MODALIDADE_CONTRATO", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for (LogVO logVO : logs) {
            logVO.setDescricao(logVO.getValorCampoAlterado());
        }
        loginControle.setListaConsultaLog(logs);
    }

    public void consultarLogAlterarDiasAcessoSemana(ActionEvent event) throws Exception {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        loginControle.setNomeClasse("Alterar dias acesso semana");
        List<LogVO> logs = getFacade().getLog().consultarPorNomeEntidade("CONFIGURACAOSISTEMA-ALTERAR_DIAS_ACESSO_SEMANA", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for (LogVO logVO : logs) {
            logVO.setDescricao(logVO.getValorCampoAlterado());
        }
        loginControle.setListaConsultaLog(logs);
    }


    public void consultarLogAlterarHorarioEmPlano(ActionEvent event) throws Exception {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        loginControle.setNomeClasse("Alterar horario de planos em massa");
        List<LogVO> logs = getFacade().getLog().consultarPorNomeEntidade("CONFIGURACAOSISTEMA-ALTERAR_HORARIO_CONTRATO", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for (LogVO logVO : logs) {
            logVO.setDescricao(logVO.getValorCampoAlterado());
        }
        loginControle.setListaConsultaLog(logs);
    }

    public void consultarContratosRetirarModalidade() throws Exception {
        RetirarModalidadeEmPlano retirarModalidadeEmPlano = new RetirarModalidadeEmPlano();

        setContratosRetirarModalidade(retirarModalidadeEmPlano.consultarContratosRetirarModalidade(getEmpresaRetirarModalidadeAoPlano().getCodigo(), getPlanoRetirarModalidadeAoPlano(), getModalidadeVORetirar()));
        manutencaoAjusteGeralTO.setMsgResultado("");
    }

    public void processarContratosRetirarModalidade() throws Exception {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("processarContratosRetirarModalidade", getEmpresaLogado());
            RetirarModalidadeEmPlano retirarModalidadeEmPlano = new RetirarModalidadeEmPlano();
            setModalidadeVORetirar(getFacade().getModalidade().consultarPorChavePrimaria(getModalidadeVORetirar().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            int qtdContratosAfetados = 0;
            StringBuilder contratosNaoRetirados = new StringBuilder("");
            for (ContratoVO contratoVO : getContratosRetirarModalidade()) {
                try {
                    retirarModalidadeEmPlano.retirarModalidadeContrato(contratoVO, getModalidadeVORetirar(), getUsuarioLogado());
                    getFacade().getZWFacade().atualizarDadosSintetico(contratoVO.getCliente(), getModalidadeVORetirar());
                    qtdContratosAfetados++;
                } catch (Exception ex) {
                    contratosNaoRetirados.append(contratoVO.getCodigo()).append(",");
                }
            }

            LogVO obj = new LogVO();
            obj.setChavePrimaria("0");
            obj.setPessoa(0);
            obj.setNomeEntidade("CONFIGURACAOSISTEMA-RETIRAR_MODALIDADE_CONTRATO");
            obj.setNomeEntidadeDescricao("Configurações");
            obj.setOperacao("Retirar Modalidade (Processo)");
            obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
            obj.setUserOAMD(getUsuarioLogado().getUserOamd());
            obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            obj.setNomeCampo("");
            obj.setValorCampoAnterior("");
            String msg = "Retirada a modalidade " + getModalidadeVORetirar().getNome() + " para os contratos do plano " + getPlanoRetirarModalidadeAoPlano().getDescricao() + " (Total: " + qtdContratosAfetados + ").";
            if (contratosNaoRetirados.length() > 0) {
                msg += " Contratos não afetados: " + contratosNaoRetirados.substring(0, contratosNaoRetirados.length() - 2);
            }
            obj.setValorCampoAlterado(msg);
            getFacade().getLog().incluir(obj);

            setContratosRetirarModalidade(retirarModalidadeEmPlano.consultarContratosRetirarModalidade(getEmpresaRetirarModalidadeAoPlano().getCodigo(), getPlanoRetirarModalidadeAoPlano(), getModalidadeVORetirar()));
            manutencaoAjusteGeralTO.setMsgResultado("Modalidade Retirada");
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void consultarLogRetirarModalidadeEmPlano(ActionEvent event) throws Exception {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        loginControle.setNomeClasse("Retirar modalidade em contratos de determinado plano");
        List<LogVO> logs = getFacade().getLog().consultarPorNomeEntidade("CONFIGURACAOSISTEMA-RETIRAR_MODALIDADE_CONTRATO", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for (LogVO logVO : logs) {
            logVO.setDescricao(logVO.getValorCampoAlterado());
        }
        loginControle.setListaConsultaLog(logs);
    }

    public Date getDataInicioContrato() {
        if (dataInicioContrato == null) {
            dataInicioContrato = Calendario.hoje();
        }
        return dataInicioContrato;
    }

    public void setDataInicioContrato(Date dataInicioContrato) {
        this.dataInicioContrato = dataInicioContrato;
    }

    public int getQtdDiasAdicionarInicioContrato() {
        return qtdDiasAdicionarInicioContrato;
    }

    public void setQtdDiasAdicionarInicioContrato(int qtdDiasAdicionarInicioContrato) {
        this.qtdDiasAdicionarInicioContrato = qtdDiasAdicionarInicioContrato;
    }

    public String getDataInicioContratoAtualizada() {
        return Uteis.getData(Uteis.somarDias(getDataInicioContrato(), getQtdDiasAdicionarInicioContrato()));
    }

    public Boolean getAlterarParcelasContrato() {
        if (alterarParcelasContrato == null) {
            alterarParcelasContrato = true;
        }
        return alterarParcelasContrato;
    }

    public void setAlterarParcelasContrato(Boolean alterarParcelasContrato) {
        this.alterarParcelasContrato = alterarParcelasContrato;
    }

    public void consultarContratosAdicionarDiasInicioContrato() throws Exception {
        AdicionarInicioContrato adicionarInicioContrato = new AdicionarInicioContrato();

        setContratosAdicionarDiasInicio(adicionarInicioContrato.consultarContratosAfetados(getEmpresaAdicionarDiasContratoDePlano().getCodigo(), getDataInicioContrato(), getPlanoAdicionarDiasContratoDePlano()));
        manutencaoAjusteGeralTO.setMsgResultado("");
    }

    public List<ContratoVO> getContratosAdicionarDiasInicio() {
        if (contratosAdicionarDiasInicio == null) {
            contratosAdicionarDiasInicio = new ArrayList<>();
        }
        return contratosAdicionarDiasInicio;
    }

    public void setContratosAdicionarDiasInicio(List<ContratoVO> contratosAdicionarDiasInicio) {
        this.contratosAdicionarDiasInicio = contratosAdicionarDiasInicio;
    }

    public void processarContratosAdicionarDiasInicio() throws Exception {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("processarContratosAdicionarDiasInicio", getEmpresaLogado());
            AdicionarInicioContrato adicionarInicioContrato = new AdicionarInicioContrato();

            int qtdContratosAfetados = 0;
            StringBuilder contratosNaoProcessados = new StringBuilder("");
            for (ContratoVO contratoVO : getContratosAdicionarDiasInicio()) {
                try {
                    adicionarInicioContrato.adicionarDias(contratoVO, getQtdDiasAdicionarInicioContrato(), getUsuarioLogado(), getAlterarParcelasContrato());
                    qtdContratosAfetados++;
                } catch (Exception ex) {
                    contratosNaoProcessados.append(contratoVO.getCodigo()).append(",");
                }
            }

            LogVO obj = new LogVO();
            obj.setChavePrimaria("0");
            obj.setPessoa(0);
            obj.setNomeEntidade("CONFIGURACAOSISTEMA-ADICIONAR_DIAS_INICIO_CONTRATO");
            obj.setNomeEntidadeDescricao("Configurações");
            obj.setOperacao("Adicionar Dias ao Início Contrato (Processo)");
            obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
            obj.setUserOAMD(getUsuarioLogado().getUserOamd());
            obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            obj.setNomeCampo("");
            obj.setValorCampoAnterior("");
            String msg = "Adicionado " + getQtdDiasAdicionarInicioContrato() + " dia(s) no início dos contratos do plano " + getPlanoAdicionarDiasContratoDePlano().getDescricao() + " (Total: " + qtdContratosAfetados + ") que começavam em: " + Uteis.getData(getDataInicioContrato()) + " passando para " + getDataInicioContratoAtualizada() + ".";
            if (contratosNaoProcessados.length() > 0) {
                msg += " Contratos não afetados: " + contratosNaoProcessados.substring(0, contratosNaoProcessados.length() - 2) + ".";
            }
            msg += "\nAdicionar dias nas parcelas do contrato: " + (getAlterarParcelasContrato() ? "SIM" : "NÃO") + ".";
            obj.setDescricao(msg);
            obj.setValorCampoAlterado(msg);
            getFacade().getLog().incluir(obj);

            setContratosAdicionarDiasInicio(adicionarInicioContrato.consultarContratosAfetados(getEmpresaVO().getCodigo(), getDataInicioContrato(), getPlanoAdicionarDiasContratoDePlano()));
            manutencaoAjusteGeralTO.setMsgResultado("Processo executado com sucesso. Verifique o log.");
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void consultarLogAdicionarDiasInicioContrato(ActionEvent event) throws Exception {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        loginControle.setNomeClasse("Adicionar Dias ao Início Contrato");
        List<LogVO> logs = getFacade().getLog().consultarPorNomeEntidade("CONFIGURACAOSISTEMA-ADICIONAR_DIAS_INICIO_CONTRATO", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for (LogVO logVO : logs) {
            logVO.setDescricao(logVO.getValorCampoAlterado());
        }
        loginControle.setListaConsultaLog(logs);
    }

    public MarcarEmailCorrespondencia getMarcarEmailCorrespondencia() throws Exception {
        if (marcarEmailCorrespondencia == null) {
            marcarEmailCorrespondencia = new MarcarEmailCorrespondencia();
            marcarEmailCorrespondencia.setUsuarioVO(getUsuarioLogado());
        }
        return marcarEmailCorrespondencia;
    }

    public void setMarcarEmailCorrespondencia(MarcarEmailCorrespondencia marcarEmailCorrespondencia) {
        this.marcarEmailCorrespondencia = marcarEmailCorrespondencia;
    }

    public void verificarCorrigirCupomFiscal() {
        try {
            this.manutencaoAjusteGeralTO.setMsgResultado("");
            this.manutencaoAjusteGeralTO.setDescricaoCupomFiscal("");
            this.manutencaoAjusteGeralTO.setCupomFiscal(null);
            if (this.manutencaoAjusteGeralTO.getCodigoCupom() <= 0) {
                this.manutencaoAjusteGeralTO.setMsgResultado("Informe o código do cupom.");
            } else {
                this.manutencaoAjusteGeralTO.setCupomFiscal(getFacade().getCupomFiscal().obterCupom(this.manutencaoAjusteGeralTO.getCodigoCupom()));
                if (this.manutencaoAjusteGeralTO.getCupomFiscal() != null) {
                    if (this.manutencaoAjusteGeralTO.getCupomFiscal().getDataHoraEmissao() == null) {
                        if (this.manutencaoAjusteGeralTO.getCupomFiscal().getStatusImpressao().equals(StatusImpressaoEnum.NAO_DEFINIDO)) {
                            this.manutencaoAjusteGeralTO.setDescricaoCupomFiscal("Cupom " + this.manutencaoAjusteGeralTO.getCupomFiscal().getCodigo() + " já está pronto para impressão");
                            this.manutencaoAjusteGeralTO.setCupomFiscal(null);
                        }
                        if (this.manutencaoAjusteGeralTO.getCupomFiscal().getStatusImpressao().equals(StatusImpressaoEnum.CANCELADO)) {
                            this.manutencaoAjusteGeralTO.setDescricaoCupomFiscal("Cupom " + this.manutencaoAjusteGeralTO.getCupomFiscal().getCodigo() + " está como cancelado. Ao solicitar o ajuste ele poderá ser impresso");
                        }

                    } else {
                        this.manutencaoAjusteGeralTO.setDescricaoCupomFiscal("Valor Cupom: " + this.manutencaoAjusteGeralTO.getCupomFiscal().getValorTotalFormatado()
                                + "\t    Recibo: " + this.manutencaoAjusteGeralTO.getCupomFiscal().getRecibo().getCodigo()
                                + (UteisValidacao.emptyNumber(this.manutencaoAjusteGeralTO.getCupomFiscal().getPagamento().getCodigo()) ? "" : "\t    Pagamento: " + this.manutencaoAjusteGeralTO.getCupomFiscal().getCheque().getCodigo())
                                + (UteisValidacao.emptyNumber(this.manutencaoAjusteGeralTO.getCupomFiscal().getCheque().getCodigo()) ? "" : "\t    Cheque: " + this.manutencaoAjusteGeralTO.getCupomFiscal().getCheque().getCodigo())
                                + (UteisValidacao.emptyNumber(this.manutencaoAjusteGeralTO.getCupomFiscal().getCartao().getCodigo()) ? "" : "\t    Cartao: " + this.manutencaoAjusteGeralTO.getCupomFiscal().getCartao().getCodigo())
                                + (this.manutencaoAjusteGeralTO.getCupomFiscal().getDataHoraEmissao() == null ? "" : "\t    Date emissão: " + this.manutencaoAjusteGeralTO.getCupomFiscal().getDataHoraEmissaoFormatada())
                                + (UteisValidacao.emptyNumber(this.manutencaoAjusteGeralTO.getCupomFiscal().getCo_cupom()) ? "" : "\t    Nr. cupom: " + this.manutencaoAjusteGeralTO.getCupomFiscal().getCo_cupom())
                        );
                    }
                } else {
                    this.manutencaoAjusteGeralTO.setDescricaoCupomFiscal("Não existe cupom com o codigo informado");
                }
                montarSucesso("");
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void corrigirCupomReimpressao() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("corrigirCupomReimpressao", getEmpresaLogado());
            if (this.manutencaoAjusteGeralTO.getCupomFiscal() == null) {
                this.manutencaoAjusteGeralTO.setMsgResultado("Informe o código do cupom.");
                this.manutencaoAjusteGeralTO.setCodigoCupom(0);
                this.manutencaoAjusteGeralTO.setDescricaoCupomFiscal("");
            } else {
                getFacade().getCupomFiscal().alterarCupomParaReimpressao(this.manutencaoAjusteGeralTO.getCupomFiscal().getCodigo());
                getFacade().getLogAjusteGeral().incluir(Calendario.hoje(), getUsuarioLogado().getNome(), getUsuarioLogado().getUserOamd(), ProcessoAjusteGeralEnum.REIMPRESSAO_CUPOM_FISCAL, "Codigo: " + this.manutencaoAjusteGeralTO.getCupomFiscal().getCodigo() + "\t  " + this.manutencaoAjusteGeralTO.getDescricaoCupomFiscal());
                this.manutencaoAjusteGeralTO.setMsgResultado("Cupom " + this.manutencaoAjusteGeralTO.getCupomFiscal().getCodigo() + " foi ajustado e pode ser reimpresso");
                this.manutencaoAjusteGeralTO.setCupomFiscal(null);
                this.manutencaoAjusteGeralTO.setDescricaoCupomFiscal("");
                this.manutencaoAjusteGeralTO.setCodigoCupom(0);

            }
            montarSucesso("");
        } catch (Exception e) {
            this.manutencaoAjusteGeralTO.setMsgResultado("Erro ao corrigir contratos. Erro:" + e.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public boolean isTerminouProcessoBDPadraoImportacao() {
        return terminouProcessoBDPadraoImportacao;
    }

    public void setTerminouProcessoBDPadraoImportacao(boolean terminouProcessoBDPadraoImportacao) {
        this.terminouProcessoBDPadraoImportacao = terminouProcessoBDPadraoImportacao;
    }


    public void consultarClienteMatriculaSenhaAcessoClientes() throws Exception {
        try {
            if (empresaSelecionadoSenhaAcessoCpf.getCodigo() == 0 || empresaSelecionadoSenhaAcessoCpf.getCodigo() == null) {
                throw new Exception("Selecione uma empresa!");
            } else {
                enderecoModal = "modalProcessarAcessoClienteMatricula";
                List<SituacaoClienteSinteticoDWVO> pessoas = getFacade().getSituacaoClienteSinteticoDW().consultarClienteComMatricula(empresaSelecionadoSenhaAcessoCpf.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                HashMap<Integer, PessoaTO> mapaClientes = new HashMap<>();
                clientesNaoProcessarSenhaAcessoMatricula = new ArrayList<>();
                for (int i = 0; i < pessoas.size(); i++) {
                    Integer matricula = pessoas.get(i).getMatricula();
                    if (UteisValidacao.emptyString(String.valueOf(matricula))) {
                        continue;
                    } else if (String.valueOf(matricula).length() > 5) {
                        clientesNaoProcessarSenhaAcessoMatricula.add(pessoas.get(i).toPessoaTO());
                        continue;
                    }
                    if (mapaClientes.get(matricula) != null) {
                        clientesNaoProcessarSenhaAcessoMatricula.add(mapaClientes.get(matricula));
                        clientesNaoProcessarSenhaAcessoMatricula.add(pessoas.get(i).toPessoaTO());
                        mapaClientes.remove(matricula);
                    } else {
                        mapaClientes.put(matricula, pessoas.get(i).toPessoaTO());
                    }
                }

                clientesProcessarSenhaAcessoMatricula = new ArrayList<>(mapaClientes.values());
                Ordenacao.ordenarLista(clientesNaoProcessarSenhaAcessoMatricula, "matricula");
                Ordenacao.ordenarLista(clientesProcessarSenhaAcessoMatricula, "nomeCompleto");
                processarClienteAcessoMatricula = true;
            }
        } catch (Exception e) {
            enderecoModal = "";
            this.manutencaoAjusteGeralTO.setMsgResultado("Erro ao corrigir contratos. Erro:" + e.getMessage());
        }
    }


    public void consultarClienteCpfSenhaAcessoClientes() throws Exception {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("consultarClienteCpfSenhaAcessoClientes", getEmpresaLogado());
            List<SituacaoClienteSinteticoDWVO> pessoas = getFacade().getSituacaoClienteSinteticoDW().consultarClienteComCPF(empresaSelecionadoSenhaAcessoCpf.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            HashMap<String, PessoaTO> mapaClientes = new HashMap<>();
            clientesNaoProcessarSenhaAcessoCPF = new ArrayList<>();
            for (int i = 0; i < pessoas.size(); i++) {
                String cpf = Uteis.removerMascara(pessoas.get(i).getCpf());
                if (UteisValidacao.emptyString(cpf)) {
                    continue;
                } else if (!SuperVO.verificaCPF(cpf)) {
                    clientesNaoProcessarSenhaAcessoCPF.add(pessoas.get(i).toPessoaTO());
                    continue;
                }
                if (mapaClientes.get(cpf) != null) {
                    clientesNaoProcessarSenhaAcessoCPF.add(mapaClientes.get(cpf));
                    clientesNaoProcessarSenhaAcessoCPF.add(pessoas.get(i).toPessoaTO());
                    mapaClientes.remove(cpf);
                } else {
                    mapaClientes.put(cpf, pessoas.get(i).toPessoaTO());
                }
            }
            clientesProcessarSenhaAcessoCPF = new ArrayList<>(mapaClientes.values());
            Ordenacao.ordenarLista(clientesNaoProcessarSenhaAcessoCPF, "cpf");
            Ordenacao.ordenarLista(clientesProcessarSenhaAcessoCPF, "nomeCompleto");
            processarClienteAcessoCpf = true;
        } catch (Exception e) {
            this.manutencaoAjusteGeralTO.setMsgResultado("Erro ao corrigir contratos. Erro:" + e.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void consultarClienteCpfSenhaAcessoColaborador() throws Exception {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("consultarClienteCpfSenhaAcessoColaborador", getEmpresaLogado());
            List<PessoaTO> pessoas = getFacade().getPessoa().consultarPessoaColaboradorAtivo(empresaSelecionadoSenhaAcessoCpfColaborador.getCodigo());
            HashMap<String, PessoaTO> mapaClientes = new HashMap<>();
            clientesNaoProcessarSenhaAcessoCPF = new ArrayList<>();
            for (int i = 0; i < pessoas.size(); i++) {
                String cpf = Uteis.removerMascara(pessoas.get(i).getCpf());
                if (UteisValidacao.emptyString(cpf)) {
                    continue;
                } else if (!SuperVO.verificaCPF(cpf)) {
                    clientesNaoProcessarSenhaAcessoCPF.add(pessoas.get(i));
                    continue;
                }
                if (mapaClientes.get(cpf) != null) {
                    clientesNaoProcessarSenhaAcessoCPF.add(mapaClientes.get(cpf));
                    clientesNaoProcessarSenhaAcessoCPF.add(pessoas.get(i));
                    mapaClientes.remove(cpf);
                } else {
                    mapaClientes.put(cpf, pessoas.get(i));
                }
            }
            clientesProcessarSenhaAcessoCPF = new ArrayList<>(mapaClientes.values());
            Ordenacao.ordenarLista(clientesNaoProcessarSenhaAcessoCPF, "cpf");
            Ordenacao.ordenarLista(clientesProcessarSenhaAcessoCPF, "nomeCompleto");
            processarClienteAcessoCpf = false;
        } catch (Exception e) {
            this.manutencaoAjusteGeralTO.setMsgResultado("Erro ao corrigir contratos. Erro:" + e.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void gerarCpfComoSenhaAcessoCliente() {
        StringBuilder clientesNaoProcessados = new StringBuilder();
        try {
            for (PessoaTO cli : getClientesProcessarSenhaAcessoCPF()) {
                try {
                    getFacade().getPessoa().alterarSenhaAcesso(cli.getCodigo(), Uteis.removerMascara(cli.getCpf()), false);
                    gerarLogAlterarSenhaAcessoCliente(cli);
                } catch (Exception ex) {
                    clientesNaoProcessados.append((processarClienteAcessoCpf ? cli.getMatricula() : cli.getCodigoColaborador()) + "-" + cli.getNomeCompleto() + "_Mensagem :" + ex.getMessage()).append("</br>");
                }
            }
            gerarLogAlterarSenhaAcessoProcesso("CPF");
            montarSucessoGrowl("Processo executado com sucesso!");
            if (!UteisValidacao.emptyString(clientesNaoProcessados.toString())) {
                clientesNaoProcessados = new StringBuilder("</br>" + (processarClienteAcessoCpf ? "Clientes" : "Colaboradores") + " que não foram processados:</br>").append(clientesNaoProcessados);
            }
            if (processarClienteAcessoCpf) {
                this.manutencaoAjusteGeralTOSenhaAcessoCliente.setMsgResultado("Processo executado com sucesso!\n" + clientesNaoProcessados.toString());
            } else {
                this.manutencaoAjusteGeralTOSenhaAcessoColaborador.setMsgResultado("Processo executado com sucesso!\n" + clientesNaoProcessados.toString());
            }
        } catch (Exception ex) {
            montarErro(ex);
        }
    }


    public void gerarMatriculaComoSenhaAcessoCliente() {
        StringBuilder clientesNaoProcessados = new StringBuilder();
        try {
            for (PessoaTO cli : getClientesProcessarSenhaAcessoMatricula()) {
                cli.setMatricula(StringUtils.leftPad(cli.getMatricula(), 5, "0"));
                try {
                    getFacade().getPessoa().alterarSenhaAcesso(cli.getCodigo(), cli.getMatricula(), false);
                    gerarLogAlterarSenhaAcessoCliente(cli);
                } catch (Exception ex) {
                    clientesNaoProcessados.append((processarClienteAcessoMatricula ? cli.getMatricula() : cli.getCodigoColaborador()) + "-" + cli.getNomeCompleto() + "_Mensagem :" + ex.getMessage()).append("</br>");
                }
            }
            gerarLogAlterarSenhaAcessoProcesso("Matrícula");
            montarSucessoGrowl("Processo executado com sucesso!");
            if (!UteisValidacao.emptyString(clientesNaoProcessados.toString())) {
                clientesNaoProcessados = new StringBuilder("</br>" + (processarClienteAcessoMatricula ? "Clientes" : "") + " que não foram processados:</br>").append(clientesNaoProcessados);
            }
            if (processarClienteAcessoMatricula) {
                this.manutencaoAjusteGeralTOSenhaAcessoCliente.setMsgResultado("Processo executado com sucesso!\n" + clientesNaoProcessados.toString());
            } else {
                this.manutencaoAjusteGeralTOSenhaAcessoColaborador.setMsgResultado("Processo executado com sucesso!\n" + clientesNaoProcessados.toString());
            }
        } catch (Exception ex) {
            montarErro(ex);
        }
    }


    public void gerarLogAlterarSenhaAcessoCliente(PessoaTO cli) throws Exception {
        LogVO log = new LogVO();
        log.setNomeEntidade("PESSOA");
        log.setChavePrimaria(cli.getCodigo().toString());
        log.setDataAlteracao(Calendario.hoje());
        log.setResponsavelAlteracao(getUsuarioLogado().getNome());
        log.setUserOAMD(getUsuarioLogado().getUserOamd());
        log.setOperacao("ALTERAÇÃO");
        log.setNomeCampo(" SENHA ACESSO CATRACA (PROCESSO AJUSTES GERAIS) -----");
        log.setValorCampoAlterado("");
        log.setValorCampoAlterado("***************");
        getFacade().getLog().incluir(log);
    }

    public void gerarLogAlterarSenhaAcessoProcesso(String processo) throws Exception {
        LogVO log = new LogVO();
        log.setNomeEntidade("CONFIGURACAOSISTEMA(PROCESSO_CLIENTE_ACESSO_CATRACA_" + processo);
        log.setChavePrimaria("0");
        log.setDataAlteracao(Calendario.hoje());
        log.setResponsavelAlteracao(getUsuarioLogado().getNome());
        log.setUserOAMD(getUsuarioLogado().getUserOamd());
        log.setOperacao("ALTERAÇÃO");
        getFacade().getLog().incluir(log);
    }

    public EmpresaVO getEmpresaSelecionadoSenhaAcessoCpf() {
        if (empresaSelecionadoSenhaAcessoCpf == null) {
            empresaSelecionadoSenhaAcessoCpf = new EmpresaVO();
        }
        return empresaSelecionadoSenhaAcessoCpf;
    }

    public void setEmpresaSelecionadoSenhaAcessoCpf(EmpresaVO empresaSelecionadoSenhaAcessoCpf) {
        this.empresaSelecionadoSenhaAcessoCpf = empresaSelecionadoSenhaAcessoCpf;
    }

    public EmpresaVO getEmpresaSelecionadoSenhaAcessoCpfColaborador() {
        if (empresaSelecionadoSenhaAcessoCpfColaborador == null) {
            empresaSelecionadoSenhaAcessoCpfColaborador = new EmpresaVO();
        }
        return empresaSelecionadoSenhaAcessoCpfColaborador;
    }

    public void setEmpresaSelecionadoSenhaAcessoCpfColaborador(EmpresaVO empresaSelecionadoSenhaAcessoCpfColaborador) {
        this.empresaSelecionadoSenhaAcessoCpfColaborador = empresaSelecionadoSenhaAcessoCpfColaborador;
    }

    public List<PessoaTO> getClientesNaoProcessarSenhaAcessoCPF() {
        return clientesNaoProcessarSenhaAcessoCPF;
    }

    public void setClientesNaoProcessarSenhaAcessoCPF(List<PessoaTO> clientesNaoProcessarSenhaAcessoCPF) {
        this.clientesNaoProcessarSenhaAcessoCPF = clientesNaoProcessarSenhaAcessoCPF;
    }

    public List<PessoaTO> getClientesProcessarSenhaAcessoCPF() {
        return clientesProcessarSenhaAcessoCPF;
    }

    public void setClientesProcessarSenhaAcessoCPF(List<PessoaTO> clientesProcessarSenhaAcessoCPF) {
        this.clientesProcessarSenhaAcessoCPF = clientesProcessarSenhaAcessoCPF;
    }

    public Boolean getProcessarClienteAcessoCpf() {
        return processarClienteAcessoCpf;
    }

    public void setProcessarClienteAcessoCpf(Boolean processarClienteAcessoCpf) {
        this.processarClienteAcessoCpf = processarClienteAcessoCpf;
    }

    public String[] getDisplayIdentificadorFront() {
        return displayIdentificadorFront;
    }

    public String[] identificacaoPessoalInternacional() {
        try {
            displayIdentificadorFront = identificadorPessoaInternacional(getEmpresaLogado().getPais().getNome());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return displayIdentificadorFront;
    }

    public void setDisplayIdentificadorFront(String[] displayIdentificadorFront) {
        this.displayIdentificadorFront = displayIdentificadorFront;
    }


    public void consultarLogSenhaAcessoCPF() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        loginControle.setNomeClasse("Configuração Sistema - Ajuste Geral (Processo cliente acesso catraca cpf)");

        loginControle.consultarLogObjetoSelecionado("CONFIGURACAOSISTEMA(PROCESSO_CLIENTE_ACESSO_CATRACA_CPF)", 0, 0);
    }


    public void consultarLogSenhaAcessoMatricula() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        loginControle.setNomeClasse("Configuração Sistema - Ajuste Geral (Processo cliente acesso catraca matricula)");

        loginControle.consultarLogObjetoSelecionado("CONFIGURACAOSISTEMA(PROCESSO_CLIENTE_ACESSO_CATRACA_Matricula)", 0, 0);
    }

    public void definirBancoAtualComoPadraoParaImportacao() {
        try {
            this.terminouProcessoBDPadraoImportacao = false;
            StringBuilder nomeUsuario = new StringBuilder();
            nomeUsuario.append(getUsuarioLogado().getNome());
            if ((getUsuarioLogado().getUserOamd() != null) && (!getUsuarioLogado().getUserOamd().trim().equals(""))) {
                nomeUsuario.append(" - ").append(getUsuarioLogado().getUserOamd());
            }
            PovoadorDadosBancoInicial povoadorDadosBancoInicial = new PovoadorDadosBancoInicial();
            String retorno = povoadorDadosBancoInicial.definirBancoAtualComoBancoPadraoParaImportacao(nomeUsuario.toString());
            setSucesso(true);
            setErro(false);
            setMensagemDetalhada(retorno);
        } catch (Exception e) {
            montarErro(e);
        } finally {
            this.terminouProcessoBDPadraoImportacao = true;
        }
    }

    public boolean isMostrarBotaoDefinirBDAtualComoPadraoParaImportacao() {
        return (terminouProcessoBDPadraoImportacao) && ((String) JSFUtilities.getFromSession("key")).toUpperCase().equals("CHAVEPACTO");
    }

    public ManutencaoAjusteGeralTO getManutencaoAjusteGeralTOSenhaAcessoCliente() {
        return manutencaoAjusteGeralTOSenhaAcessoCliente;
    }

    public void setManutencaoAjusteGeralTOSenhaAcessoCliente(ManutencaoAjusteGeralTO manutencaoAjusteGeralTOSenhaAcessoCliente) {
        this.manutencaoAjusteGeralTOSenhaAcessoCliente = manutencaoAjusteGeralTOSenhaAcessoCliente;
    }

    public ManutencaoAjusteGeralTO getManutencaoAjusteGeralTOSenhaAcessoColaborador() {
        return manutencaoAjusteGeralTOSenhaAcessoColaborador;
    }

    public void setManutencaoAjusteGeralTOSenhaAcessoColaborador(ManutencaoAjusteGeralTO manutencaoAjusteGeralTOSenhaAcessoColaborador) {
        this.manutencaoAjusteGeralTOSenhaAcessoColaborador = manutencaoAjusteGeralTOSenhaAcessoColaborador;
    }

    public void consultarLogAjusteGeral(ActionEvent event) {
        try {
            Integer codigoProcessoAjusteGeral = Integer.valueOf(event.getComponent().getAttributes().get("codigo").toString());
            if (codigoProcessoAjusteGeral != null) {
                setLogAjusteGeralVOList(new ArrayList<>());
                List<LogAjusteGeralVO> novoLog = getFacade().getLogAjusteGeral().consultarPorAjusteGeral(codigoProcessoAjusteGeral, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                setLogAjusteGeralVOList(novoLog);
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public List<AcessoClienteVO> getListaAcessoCliente() {
        return listaAcessoCliente;
    }

    public void setListaAcessoCliente(List<AcessoClienteVO> listaAcessoCliente) {
        this.listaAcessoCliente = listaAcessoCliente;
    }

    public EmpresaVO getEmpresaAcesso() {
        if (empresaAcesso == null) {
            empresaAcesso = new EmpresaVO();
        }
        return empresaAcesso;
    }

    public void setEmpresaAcesso(EmpresaVO empresaAcesso) {
        this.empresaAcesso = empresaAcesso;
    }

    public int getQuantidadeEmpresas() {
        return quantidadeEmpresas;
    }

    public void setQuantidadeEmpresas(int quantidadeEmpresas) {
        this.quantidadeEmpresas = quantidadeEmpresas;
    }

    public boolean isMenuProcessos() {
        return menuProcessos;
    }

    public void setMenuProcessos(boolean menuProcessos) {
        this.menuProcessos = menuProcessos;
    }

    public void manutencaoProcessos() {
        todosDesabilitados();
        setMenuProcessos(true);
    }

    public void processarRobo() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("processarRobo", getEmpresaLogado());
            RoboControle roboControle = new RoboControle();
            roboControle.start(getKey());
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void processarMensagensClientesComParcelaVencida() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("processarMensagensClientesComParcelaVencida", getEmpresaLogado());
            List<EmpresaVO> empresas = new Empresa(getFacade().getZWFacade().getCon()).consultarEmpresas();
            RoboVO robo = new RoboVO();
            robo.setDia(Calendario.hoje());
            robo.setListaEmpresa(empresas);
            robo.processarClientesComParcelaVencida();
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void processarClientesRestricoes() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("processarClientesRestricoes", getEmpresaLogado());
            RoboVO roboVO = new RoboVO();
            roboVO.setListaEmpresa(getFacade().getEmpresa().consultarPorCodigo(0, null, false, Uteis.NIVELMONTARDADOS_ROBO));
            AdministrativoRunner.processarClientesRestricoes(
                    roboVO,
                    (String) JSFUtilities.getFromSession(JSFUtilities.KEY),
                    getFacade().getZWFacade().getCon());
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void processarContratosClienteIntegracaoFoguete() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("processarContratosClientesIntegracaoFoguete", getEmpresaLogado());
            RoboVO roboVO = new RoboVO();
            roboVO.setListaEmpresa(getFacade().getEmpresa().consultarPorCodigo(0, null, false, Uteis.NIVELMONTARDADOS_ROBO));
            AdministrativoRunner.processarContratosClienteIntegracaoFoguete(
                    roboVO,
                    getFacade().getZWFacade().getCon(),
                    getUsuarioLogado());
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void processarClientesRedeEmpresa() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("processarClientesRedeEmpresa", getEmpresaLogado());
            RoboVO roboVO = new RoboVO();
            roboVO.setListaEmpresa(getFacade().getEmpresa().consultarPorCodigo(0, null, false, Uteis.NIVELMONTARDADOS_ROBO));
            AdministrativoRunner.processarClientesRedeEmpresa(
                    roboVO,
                    (String) JSFUtilities.getFromSession(JSFUtilities.KEY),
                    getFacade().getZWFacade().getCon());
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void processarOperacoesColetivas() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("processarOperacoesColetivas", getEmpresaLogado());
            RoboVO roboVO = new RoboVO();
            roboVO.setListaEmpresa(getFacade().getEmpresa().consultarPorCodigo(0, null, false, Uteis.NIVELMONTARDADOS_ROBO));
            AdministrativoRunner.processarOperacoesColetivas(
                    roboVO,
                    null,
                    (String) JSFUtilities.getFromSession(JSFUtilities.KEY),
                    getFacade().getZWFacade().getCon());
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void processarIntegracaoEVOAdministrativoRunner() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("processarIntegracaoEVOAdministrativoRunner", getEmpresaLogado());
            RoboVO roboVO = new RoboVO();
            roboVO.setListaEmpresa(getFacade().getEmpresa().consultarPorCodigo(0, null, false, Uteis.NIVELMONTARDADOS_ROBO));
            AdministrativoRunner.processarIntegracaoEVO(
                    roboVO,
                    (String) JSFUtilities.getFromSession(JSFUtilities.KEY),
                    getFacade().getZWFacade().getCon());
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void processarAlunosPlanosVIP() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("processarAlunosPlanosVIP", getEmpresaLogado());
            RoboVO roboVO = new RoboVO();
            roboVO.setListaEmpresa(getFacade().getEmpresa().consultarPorCodigo(0, null, false, Uteis.NIVELMONTARDADOS_ROBO));
            AdministrativoRunner.processarAlunosPlanosVIP(
                    roboVO,
                    (String) JSFUtilities.getFromSession(JSFUtilities.KEY),
                    getFacade().getZWFacade().getCon());
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void processarCancelamentoAutomatico() {
        try {
            final RecorrenciaService recorrenciaService = new RecorrenciaService(getFacade().getZWFacade().getCon());
            recorrenciaService.processarDia(Calendario.hoje());
            recorrenciaService.processarCancelamentoAutomaticoContratosForaRegimeRecorrencia(Calendario.hoje());
            recorrenciaService.processarCancelamentoAutomaticoContratosSemAssinatura(Calendario.hoje());
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
        }
    }

    public void processarRemessaService() {
        RemessaService service = null;
        try {
            service = new RemessaService();
            service.setKey(getKey());
            service.processarCobrancas(Calendario.hoje(), getKey());
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        } finally {
            service = null;
        }
    }

    public void processarTransacoesOnline() {
        RemessaService service = null;
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("processarTransacoesOnline", getEmpresaLogado());
            service = new RemessaService();
            service.setKey(getKey());
            service.processarTransacoesOnlineManual(Calendario.hoje());
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        } finally {
            finalizarLogProcesso(log);
            service = null;
        }
    }

    public void processarCancelarTransacoesVerificacao() {
        RemessaService service = null;
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("processarCancelarTransacoesVerificacao", getEmpresaLogado());
            service = new RemessaService();
            service.setKey(getKey());
            service.verificarPendenciasTransacaoOnline(getEmpresaLogado(), true);
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        } finally {
            finalizarLogProcesso(log);
            service = null;
        }
    }

    public boolean isAmbienteDesenvolvimento() {
        //ambiente de desenvolvimento (localhost ou swarm)
        return !isHttps();
    }
    public void processarCreditoDCC() {
        CreditoDCCService service = null;
        try {
            service = new CreditoDCCService();
            service.setKey(getKey());
            service.processarCobrancaPacto(Calendario.hoje());
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        } finally {
            service = null;
        }
    }

    public void processarEnviarRemessasPendentes() {
        try {
            limparMsg();
            processarAgrupadorRemessasGetnet();

            List<ConvenioCobrancaVO> listaConve = getFacade().getConvenioCobranca().consultarTodos(Uteis.NIVELMONTARDADOS_TODOS);
            RemessaService service = new RemessaService();
            service.setKey(getKey());
            for (ConvenioCobrancaVO conv : listaConve) {
                if (!conv.getSituacao().equals(SituacaoConvenioCobranca.ATIVO)) {
                    continue;
                }
                if (conv.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.EDI_DCC) ||
                        conv.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.BOLETO) ||
                        conv.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.EDI_DCO)) {
                    service.processoUnicoEnviarRemessas(conv.getCodigo(), conv.getEmpresa().getCodigo(), getUsuarioLogado(), false);
                }
            }
            service = null;
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
            montarErro(ex);
        }
    }

    public void processarVerificadorDeRemessasRejeitadas() {
        try {
            limparMsg();

            VerificadorRemessaRejeitadaEDIService service = new VerificadorRemessaRejeitadaEDIService();
            service.setKey(getKey());
            service.processar();
            service = null;

        } catch (Exception ex) {
            System.out.println(ex.getMessage());
            montarErro(ex);
        }
    }

    public void processarAgrupadorRemessasGetnet() {
        try {
            limparMsg();
            AgruparRemessasGetnet service = new AgruparRemessasGetnet();
            service.setKey(getKey());
            service.processar();
            service = null;
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
            montarErro(ex);
        }
    }

    public void processarProcessoPovoarCidadeEmpresaSemUsarEmpresa() {
        processarProcessoPovoarCidade(false);
    }

    public void processarProcessoPovoarCidadeEmpresaComUsarEmpresa() {
        processarProcessoPovoarCidade(true);
    }

    private void processarProcessoPovoarCidade(boolean seNaoEncontrarUsarCidadeEmpresa) {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("processarProcessoPovoarCidade", getEmpresaLogado());
            limparMsg();

            Integer empresaFiltrar = 0;
            if (getEmpresaLogado() != null && !UteisValidacao.emptyNumber(getEmpresaLogado().getCodigo())) {
                empresaFiltrar = getEmpresaLogado().getCodigo();
            }

            ProcessoPovoarCidade service = new ProcessoPovoarCidade(getFacade().getZWFacade().getCon());
            service.processar(empresaFiltrar, seNaoEncontrarUsarCidadeEmpresa, getUsuarioLogado());
            service = null;

        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void processarEnviarNotasAguardando() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("processarEnviarNotasAguardando", getEmpresaLogado());
            limparMsg();
            getFacade().getNotaFiscal().enviarNotasAguardando();
            getFacade().getNotaFiscal().retentativaEnvioEnotasNFSe();
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void processarNotas() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("processarNotas", getEmpresaLogado());
            RoboControle roboControle = new RoboControle();
            roboControle.inicializarRobo();
            RoboVO robo = roboControle.getRobo();

            AdministrativoRunner.processarNotas(robo, getKey(), getFacade().getZWFacade().getCon());
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void processarEstacionamento() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("processarEstacionamento", getEmpresaLogado());
            RoboControle roboControle = new RoboControle();
            roboControle.inicializarRobo();
            RoboVO robo = roboControle.getRobo();

            AdministrativoRunner.processarEstacionamento(robo, getKey(), getFacade().getZWFacade().getCon());
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void processarIntegracaoF360() {
        setMsgRetornoReprocessarIntegracaoF360("");
        limparMsg();
        LogProcessoSistemaVO log = null;
        try {
            if (isNull(getDataProcessoIntgF360())) {
                throw new ServiceException("Necessario informar data processo.");
            }
            log = criarLog("processarIntegracaoF360", getEmpresaLogado());
            RoboControle roboControle = new RoboControle();
            roboControle.inicializarRobo();
            RoboVO robo = roboControle.getRobo();

            setMsgRetornoReprocessarIntegracaoF360(AdministrativoRunner.processarIntegracaoF360(robo, getKey(), getFacade().getZWFacade().getCon(), getDataProcessoIntgF360()));
            montarSucessoGrowl("Processo executado com sucesso.");
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void processarIntegracaoNuvemShop() {
        setMsgRetornoReprocessarIntegracaoNuvemShop("");
        limparMsg();
        LogProcessoSistemaVO log = null;
        try {
            Integer id = null;
            try {
                id = Integer.parseInt(getIdVendaNuvemShop());
            } catch (Exception e) {}

            if (UteisValidacao.emptyString(getIdVendaNuvemShop())) {
                throw new ServiceException("Necessário informar o ID da venda.");
            }

            if (UteisValidacao.emptyNumber(id)) {
                throw new ServiceException("Necessário informar o ID da venda válido.");
            }
            log = criarLog("processarIntegracaoNuvemShop", getEmpresaLogado());

            setMsgRetornoReprocessarIntegracaoNuvemShop(processarRequestNuvemShop(getIdVendaNuvemShop()));
            montarSucessoGrowl("Processo executado com sucesso.");
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    private String processarRequestNuvemShop(String idVenda) throws Exception {
        String chave = getKey();

        if (UteisValidacao.emptyNumber(getEmpresaLogado().getCodigo())) {
            throw new Exception("Necessário estar logado em uma empresa!");
        }

        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");

        JSONObject body = new JSONObject();
        body.put("id", idVenda);

        Map<String, String> params = new HashMap<>();
        params.put("chave", chave);
        params.put("codigoEmpresa", String.valueOf(getEmpresaLogado().getCodigo()));
        params.put("operacao", OperacoesIntegracaoNuvemshopEnum.BUSCAR_PEDIDOS.toString());

        ClientDiscoveryDataDTO dataDTO = DiscoveryMsService.urlsChave(chave);

        String url = dataDTO.getServiceUrls().getZwUrl() + "/prest/integracaonuvemshop";

        RequestHttpService httpService = new RequestHttpService();
        RespostaHttpDTO respostaHttpDTO = httpService.executeRequest(url, headers, params, body.toString(), MetodoHttpEnum.POST);
        JSONObject jsonResult = new JSONObject(respostaHttpDTO.getResponse());

        if (jsonResult.has("return")) {
            return jsonResult.optString("return");
        }else if(jsonResult.has("erro")) {
            throw new Exception(jsonResult.optString("erro"));
        } else {
            return "";
        }
    }

    public void atualizarDadosCadastraisAlunosVindi() {
        limparMsg();
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("atualizarDadosCadastraisAlunosVindi", getEmpresaLogado());
            SincronizarDadosVindiService sincronizarDadosVindiService = new SincronizarDadosVindiService(Conexao.getFromSession());
            sincronizarDadosVindiService.processarDados();
            montarSucessoGrowl("Processo executado com sucesso.");
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void corrigirVinculoFamiliarTitularDependente() {
        limparMsg();
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("corrigirVinculoFamiliarTitularDependente", getEmpresaLogado());
            ProcessoAtualizarDependenciaEngenharia processo = new ProcessoAtualizarDependenciaEngenharia(Conexao.getFromSession());
            processo.corrigirVinculoFamiliarTitularDependente();
            processo.corrigirDependenteSemTitularVinculado();
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void propagarContratoHtml() {
        limparMsg();
        if (!configuracaoSistemaVO.isManterContratoAssinadoNaRenovacaoContrato()) {
            montarErro("A configuração: \"Manter contrato assinado na renovação automática\" não está habilitada!");
            return;
        }
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("propagarContratoHtml", getEmpresaLogado());
            ProcessoPropagarContratoHtml processoPropagarContratoHtml = new ProcessoPropagarContratoHtml();
            processoPropagarContratoHtml.processarContratos(0, "", Conexao.getFromSession());
            montarSucessoGrowl("Contratos ajustados");
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void corrigirDependentesInativosComCompartilhamentoAtivo() {
        limparMsg();
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("corrigirDependentesInativosComCompartilhamentoAtivo", getEmpresaLogado());
            ProcessoAtualizarDependenciaEngenharia processo = new ProcessoAtualizarDependenciaEngenharia(Conexao.getFromSession());
            processo.corrigirDependentesInativosComCompartilhamentoAtivo();
        } catch (Exception e) {
            e.printStackTrace();
            montarErro(e);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void ajustarVigenciaContratoDependente() {
        limparMsg();
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("ajustarVigenciaContratoDependente", getEmpresaLogado());
            ProcessoAtualizarDependenciaEngenharia processo = new ProcessoAtualizarDependenciaEngenharia(Conexao.getFromSession());
            processo.ajustarVigenciaContratoDependente();
            processo.corrigirVinculoFamiliarTitularDependente();
        } catch (Exception e) {
            e.printStackTrace();
            montarErro(e);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void ajustarVigenciaFinalContratoDependente() {
        limparMsg();
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("ajustarVigenciaFinalContratoDependente", getEmpresaLogado());
            ProcessoAtualizarDependenciaEngenharia processo = new ProcessoAtualizarDependenciaEngenharia(Conexao.getFromSession());
            processo.ajustarVigenciaFinalContratoDependente();
            processo.corrigirVinculoFamiliarTitularDependente();
        } catch (Exception e) {
            e.printStackTrace();
            montarErro(e);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void corrigirMetasNaoAtingidas() {
        limparMsg();
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("corrigirMetasNaoAtingidas", getEmpresaLogado());
            manutencaoAjusteGeralTO.setMsgResultado("");
            String resultado = ProcessoCorrigirMetasCRMNaoAtingidas.corrigirMetasCRMNaoAtingidas(Conexao.getFromSession(), this.manutencaoAjusteGeralTO.getEmpresaVO().getCodigo(), this.manutencaoAjusteGeralTO.getProcessarAPartirDe());
            manutencaoAjusteGeralTO.setMsgResultado("O Processo foi executado com sucesso. " + resultado);
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void processarEstorno() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("processarEstorno", getEmpresaLogado());

            EstornoContratoAutomaticoService estornoService = new EstornoContratoAutomaticoService(getFacade().getZWFacade().getCon());
            estornoService.estornarContratosAutomaticamente(Calendario.hoje());
            estornoService.estornarContratosAutomaticamenteQuandoPrimeiraParcelaNaoPaga(Calendario.hoje(), false);
            estornoService.estornarContratosAutomaticamenteQuandoPrimeiraParcelaNaoPaga(Calendario.hoje(), true);

        } catch (Exception ex) {
            System.out.println(ex.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void processarRenovacaoAutomatica() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("processarRenovacaoAutomatica", getEmpresaLogado());

            RecorrenciaService recorrenciaService = new RecorrenciaService(getFacade().getZWFacade().getCon());
            recorrenciaService.processarRenovacoesAutomaticas(Calendario.hoje());
            processarRenovacaoAutomaticaProdutos();

        } catch (Exception ex) {
            System.out.println(ex.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }
    public void processarRenovacaoAutomaticaProdutos() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("processarRenovacaoAutomaticaProduto", getEmpresaLogado());

            RecorrenciaService recorrenciaService = new RecorrenciaService(getFacade().getZWFacade().getCon());
            recorrenciaService.processarRenovacoesAutomaticasProduto(Calendario.hoje());

        } catch (Exception ex) {
            System.out.println(ex.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }


    public void processarAnuidade() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("processarAnuidade", getEmpresaLogado());

            RecorrenciaService recorrenciaService = new RecorrenciaService(getFacade().getZWFacade().getCon());
            recorrenciaService.processarAnuidadeContratoRecorrencia(Calendario.hoje());

        } catch (Exception ex) {
            System.out.println(ex.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void processarExtratos() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("processarExtratos", getEmpresaLogado());
            ExtratoDiarioService extratoService = new ExtratoDiarioService();
            extratoService.processarExtratos(getKey(), null, null, false);
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void processarMetas() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("processarMetas", getEmpresaLogado());
            new AberturaMetaControle().fecharMetas(Uteis.somarDias(Calendario.hoje(), -1));
            new AberturaMetaControle().abrirMetas(Calendario.hoje());
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void notificarRecursoConfiguracao() {
        if (configuracaoSistemaVO.isCancelarContratoNaUnidadeOrigemAoTransferirAluno()) {
            if (!configuracaoSistemaVO.equals(configuracaoSistemaVOClone)) {
                notificarRecursoEmpresa(RecursoSistema.MIGRAR_PARCELA_E_PLANO_TRANSFERENCIA_MARCOU);
            }
        }
    }

    public void notificarRecursoInativarModalAtualizarUsuario(ValueChangeEvent evt) {
        if (evt.getNewValue().equals(true)) {
            notificarRecursoEmpresa(RecursoSistema.EXIBIR_MODAL_INATIVAR_USUARIO);
        }
    }

    public void notificarRecursoInativarModalAtualizarPlano(ValueChangeEvent evt) {
        if (evt.getNewValue().equals(true)) {
            notificarRecursoEmpresa(RecursoSistema.EXIBIR_MODAL_PLANOS_INATIVOS_MES_SEGUINTE);
        }
    }

    public void alteraValor() {
        Object object = JSFUtilities.getRequestAttribute("servico");
    }

    public Boolean getProcessarClienteAcessoMatricula() {
        return processarClienteAcessoMatricula;
    }

    public void setProcessarClienteAcessoMatricula(Boolean processarClienteAcessoMatricula) {
        this.processarClienteAcessoMatricula = processarClienteAcessoMatricula;
    }

    public List<PessoaTO> getClientesNaoProcessarSenhaAcessoMatricula() {
        return clientesNaoProcessarSenhaAcessoMatricula;
    }

    public void setClientesNaoProcessarSenhaAcessoMatricula(List<PessoaTO> clientesNaoProcessarSenhaAcessoMatricula) {
        this.clientesNaoProcessarSenhaAcessoMatricula = clientesNaoProcessarSenhaAcessoMatricula;
    }

    public List<PessoaTO> getClientesProcessarSenhaAcessoMatricula() {
        return clientesProcessarSenhaAcessoMatricula;
    }

    public void setClientesProcessarSenhaAcessoMatricula(List<PessoaTO> clientesProcessarSenhaAcessoMatricula) {
        this.clientesProcessarSenhaAcessoMatricula = clientesProcessarSenhaAcessoMatricula;
    }

    public String getEnderecoModal() {
        return enderecoModal;
    }

    public void setEnderecoModal(String enderecoModal) {
        this.enderecoModal = enderecoModal;
    }

    public String getEmailBackup() {
        return emailBackup;
    }

    public void setEmailBackup(String emailBackup) {
        this.emailBackup = emailBackup;
    }

    public List<SelectItem> getBackupExcel() {
        return backupExcel;
    }

    public void setBackupExcel(List<SelectItem> backupExcel) {
        this.backupExcel = backupExcel;
    }

    public String getBackupEscolha() {
        return backupEscolha;
    }

    public void setBackupEscolha(String backupEscolha) {
        this.backupEscolha = backupEscolha;
    }

    public boolean isPollHistoricoVinculo() {
        return pollHistoricoVinculo;
    }

    public void setPollHistoricoVinculo(boolean pollHistoricoVinculo) {
        this.pollHistoricoVinculo = pollHistoricoVinculo;
    }

    public boolean isPollMovProdutoParcelas() {
        return pollMovProdutoParcelas;
    }

    public void setPollMovProdutoParcelas(boolean pollMovProdutoParcelas) {
        this.pollMovProdutoParcelas = pollMovProdutoParcelas;
    }

    public OperacaoColetivaVO getOperacaoColetivaVO() {
        if (operacaoColetivaVO == null) {
            operacaoColetivaVO = new OperacaoColetivaVO();
        }
        return operacaoColetivaVO;
    }

    public void setOperacaoColetivaVO(OperacaoColetivaVO operacaoColetivaVO) {
        this.operacaoColetivaVO = operacaoColetivaVO;
    }

    public void montarListasPlanosOperacaoColetiva() {
        operacaoColetivaVO.setPlanoVO(new PlanoVO());
        try {
            listaOperacoesColetivas = getFacade().getOperacaoColetiva().consultar(operacaoColetivaVO.getEmpresa().getCodigo(), null, null, Uteis.NIVELMONTARDADOS_TODOS);
        } catch (Exception e) {
            montarErro(e);
        }
        setListaSelectItemPlanosOperacaoColetiva(montarListaSelectItemPlanos(operacaoColetivaVO.getEmpresa(), true, "TODOS"));
    }

    public List<SelectItem> getListaSelectItemPlanosOperacaoColetiva() {
        if (listaSelectItemPlanosOperacaoColetiva == null) {
            listaSelectItemPlanosOperacaoColetiva = new ArrayList<>();
        }
        return listaSelectItemPlanosOperacaoColetiva;
    }

    public void setListaSelectItemPlanosOperacaoColetiva(List<SelectItem> listaSelectItemPlanosOperacaoColetiva) {
        this.listaSelectItemPlanosOperacaoColetiva = listaSelectItemPlanosOperacaoColetiva;
    }

    public List<OperacaoColetivaVO> getListaOperacoesColetivas() {
        if (listaOperacoesColetivas == null) {
            listaOperacoesColetivas = new ArrayList<>();
        }
        return listaOperacoesColetivas;
    }

    public void setListaOperacoesColetivas(List<OperacaoColetivaVO> listaOperacoesColetivas) {
        this.listaOperacoesColetivas = listaOperacoesColetivas;
    }

    public void inicializarDadosOperacaoColetiva() {
        try {
            listaOperacoesColetivas = getFacade().getOperacaoColetiva().consultar(null, null, null, Uteis.NIVELMONTARDADOS_TODOS);
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void gravarOperacaoColetiva() {
        limparMsg();
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("gravarOperacaoColetiva", getEmpresaLogado());
            operacaoColetivaVO.setStatus(StatusOperacaoColetivaEnum.AGUARDANDO);
            operacaoColetivaVO.setTipo(TipoOperacaoColetivaEnum.AFASTAMENTO_COLETIVO);
            operacaoColetivaVO.setUsuario(getUsuarioLogado().getNome() + " ^ " + getUsuarioLogado().getUserOamd());
            operacaoColetivaVO.validarDados();
            String validarConflito = getFacade().getOperacaoColetiva().existeOperacaoConflitante(operacaoColetivaVO);
            if (!UteisValidacao.emptyString(validarConflito)) {
                throw new Exception(validarConflito);
            }
            getFacade().getOperacaoColetiva().incluir(operacaoColetivaVO);
            if (Calendario.menorOuIgual(operacaoColetivaVO.getDataInicio(), Calendario.hoje())) {
                montarSucesso("Solicitação foi gravada e será processada automaticamente no dia " + Uteis.getData(Uteis.somarDias(Calendario.hoje(), 1)));
            } else {
                montarSucesso("Solicitação foi gravada e será processada automaticamente no dia " + Uteis.getData(operacaoColetivaVO.getDataInicio()));
            }
            listaOperacoesColetivas = getFacade().getOperacaoColetiva().consultar(null, null, null, Uteis.NIVELMONTARDADOS_TODOS);
            operacaoColetivaVO = new OperacaoColetivaVO();
        } catch (Exception ex) {
            montarErro(ex);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void excluirOperacaoColetiva() {
        limparMsg();
        OperacaoColetivaVO obj = (OperacaoColetivaVO) context().getExternalContext().getRequestMap().get("operacao");
        if (obj != null) {
            operacaoColetivaVO = obj;
        }
        try {
            if (operacaoColetivaVO.getStatus().equals(StatusOperacaoColetivaEnum.PROCESSADA)) {
                if (Calendario.menor(operacaoColetivaVO.getDataFinal(), Calendario.hoje())) {
                    throw new Exception("Operação só pode ser excluída até a data final do recesso.");
                }
                operacaoColetivaVO.setStatus(StatusOperacaoColetivaEnum.AGUARDANDO_EXCLUSAO);
                operacaoColetivaVO.setUsuarioExclusao(getUsuarioLogado().getNome() + " ^ " + getUsuarioLogado().getUserOamd());
                getFacade().getOperacaoColetiva().alterar(operacaoColetivaVO);
                montarSucesso("Solicitação já tinha sido processada. Todos alunos terão a operação estornada durante a madrugada. ");
            } else {
                getFacade().getOperacaoColetiva().excluir(operacaoColetivaVO);
                montarSucesso("Solicitação ainda não havia sido processada e foi excluída");
            }
            listaOperacoesColetivas = getFacade().getOperacaoColetiva().consultar(null, null, null, Uteis.NIVELMONTARDADOS_TODOS);
            operacaoColetivaVO = new OperacaoColetivaVO();
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public EmpresaVO getEmpresaAdicionarHorarioAoPlano() {
        if (empresaAdicionarHorarioAoPlano == null) {
            empresaAdicionarHorarioAoPlano = new EmpresaVO();
        }
        return empresaAdicionarHorarioAoPlano;
    }

    public void setEmpresaAdicionarHorarioAoPlano(EmpresaVO empresaAdicionarHorarioAoPlano) {
        this.empresaAdicionarHorarioAoPlano = empresaAdicionarHorarioAoPlano;
    }

    public PlanoVO getPlanoAdicionarHorarioAoPlano() {
        if (planoAdicionarHorarioAoPlano == null) {
            planoAdicionarHorarioAoPlano = new PlanoVO();
        }
        return planoAdicionarHorarioAoPlano;
    }

    public void setPlanoAdicionarHorarioAoPlano(PlanoVO planoAdicionarHorarioAoPlano) {
        this.planoAdicionarHorarioAoPlano = planoAdicionarHorarioAoPlano;
    }

    public void atualizarDataPlanos() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("atualizarDataPlanos", getEmpresaLogado());
            Integer diasPlano;
            int qtd = 0;
            diasPlano = getFacade().getPlano().consultarPlanoDataMaiorQueZero(getPlanoAdicionarDataAoPlano().getCodigo());
            if (diasPlano == 0) {
                montarMsgAlert("O plano selecionado não possui data informada!");
            } else {
                qtd = getFacade().getContrato().atualizarDataContratos(getPlanoAdicionarDataAoPlano().getCodigo(), diasPlano);

                LogVO obj = new LogVO();
                obj.setChavePrimaria("0");
                obj.setPessoa(0);
                obj.setNomeEntidade("CONFIGURACAOSISTEMA-ALTERAR_DIAS_ACESSO_SEMANA");
                obj.setNomeEntidadeDescricao("Configurações");
                obj.setOperacao("Atualiza Quantidade Dias de Acesso Semana");
                obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
                obj.setUserOAMD(getUsuarioLogado().getUserOamd());
                obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
                obj.setNomeCampo("");
                obj.setValorCampoAnterior("");
                setPlanoAdicionarDataAoPlano(getFacade().getPlano().consultarPorChavePrimaria(getPlanoAdicionarDataAoPlano().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                String msg = "Atualizado a quantidade de dias de acesso por semana para os contratos do plano '" + getPlanoAdicionarDataAoPlano().getCodigo().toString().concat(" - ").concat(getPlanoAdicionarDataAoPlano().getDescricao()) + "' (Total: " + qtd + ").";
                obj.setValorCampoAlterado(msg);
                getFacade().getLog().incluir(obj);

                montarMsgAlert("Atualizados: " + qtd + " contratos");
            }
        } catch (Exception e) {
            Uteis.logar(e, ConfiguracaoSistemaControle.class);
            montarMsgAlert(e.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void montarListaAdicionarDataPlano() {
        try {
            setPlanoAdicionarDataAoPlano(new PlanoVO());
            setListaSelectItemPlanosAdicionarDataAoPlano(montarListaSelectItemPlanos());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public List<SelectItem> montarListaSelectItemPlanos() throws Exception {
        List<SelectItem> listaSelectItemPlanos = new ArrayList<>();
        if (empresaAdicionarDataPlano != null && empresaAdicionarDataPlano.getCodigo() > 0) {
            List<PlanoVO> resultadoConsulta = getFacade().getPlano().consultarPorCodigoEmpresa(empresaAdicionarDataPlano.getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
            for (PlanoVO obj : resultadoConsulta) {
                listaSelectItemPlanos.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
            }
        }
        return listaSelectItemPlanos;
    }

    public EmpresaVO getEmpresaAdicionarDataAoPlano() {
        if (empresaAdicionarDataPlano == null) {
            empresaAdicionarDataPlano = new EmpresaVO();
        }
        return empresaAdicionarDataPlano;
    }

    public List<SelectItem> getListaSelectItemPlanosAdicionarDataAoPlano() {
        if (listaSelectItemPlanosAdicionarDataAoPlano == null) {
            listaSelectItemPlanosAdicionarDataAoPlano = new ArrayList<>();
        }
        return listaSelectItemPlanosAdicionarDataAoPlano;
    }

    public void setListaSelectItemPlanosAdicionarDataAoPlano(List<SelectItem> listaSelectItemPlanosAdicionarDataAoPlano) {
        this.listaSelectItemPlanosAdicionarDataAoPlano = listaSelectItemPlanosAdicionarDataAoPlano;
    }

    public PlanoVO getPlanoAdicionarDataAoPlano() {
        if (planoAdicionarDataAoPlano == null) {
            planoAdicionarDataAoPlano = new PlanoVO();
        }
        return planoAdicionarDataAoPlano;
    }

    public void setPlanoAdicionarDataAoPlano(PlanoVO planoAdicionarDataAoPlano) {
        this.planoAdicionarDataAoPlano = planoAdicionarDataAoPlano;
    }

    public void montarListasAdicionarHorarioPlano() {
        montarListaSelectItemHorarios();
        montarListaSelectItemHorariosAnterior();
        setPlanoAdicionarHorarioAoPlano(new PlanoVO());
        setListaSelectItemPlanosAdicionarHorarioAoPlano(montarListaSelectItemPlanos(getEmpresaAdicionarHorarioAoPlano(), false, ""));
    }

    public void montarListaSelectItemHorarios() {
        try {
            setListaSelectItemHorarios(new ArrayList<>());
            if (getEmpresaAdicionarHorarioAoPlano() != null && getEmpresaAdicionarHorarioAoPlano().getCodigo() > 0) {
                List<HorarioVO> resultadoConsulta = getFacade().getHorario().consultarTodosHorarios(false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                for (HorarioVO obj : resultadoConsulta) {
                    getListaSelectItemHorarios().add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void montarListaSelectItemHorariosAnterior() {
        List<SelectItem> lista = new ArrayList<>();
        lista.add(new SelectItem(0, " "));
        lista.addAll(getListaSelectItemHorarios());
        setListaSelectItemHorariosAnterior(lista);
    }

    public List<SelectItem> getListaSelectItemHorarios() {
        if (listaSelectItemHorarios == null) {
            listaSelectItemHorarios = new ArrayList<>();
        }
        return listaSelectItemHorarios;
    }

    public void setListaSelectItemHorarios(List<SelectItem> listaSelectItemHorarios) {
        this.listaSelectItemHorarios = listaSelectItemHorarios;
    }

    public List<SelectItem> getListaSelectItemPlanosAdicionarHorarioAoPlano() {
        if (listaSelectItemPlanosAdicionarHorarioAoPlano == null) {
            listaSelectItemPlanosAdicionarHorarioAoPlano = new ArrayList<>();
        }
        return listaSelectItemPlanosAdicionarHorarioAoPlano;
    }

    public void setListaSelectItemPlanosAdicionarHorarioAoPlano(List<SelectItem> listaSelectItemPlanosAdicionarHorarioAoPlano) {
        this.listaSelectItemPlanosAdicionarHorarioAoPlano = listaSelectItemPlanosAdicionarHorarioAoPlano;
    }

    public HorarioVO getHorarioVO() {
        if (horarioVO == null) {
            horarioVO = new HorarioVO();
        }
        return horarioVO;
    }

    public void setHorarioVO(HorarioVO horarioVO) {
        this.horarioVO = horarioVO;
    }

    public HorarioVO getHorarioVOAnterior() {
        if (horarioVOAnterior == null) {
            horarioVOAnterior = new HorarioVO();
        }
        return horarioVOAnterior;
    }

    public void setHorarioVOAnterior(HorarioVO horarioVOAnterior) {
        this.horarioVOAnterior = horarioVOAnterior;
    }

    public void consultarContratosAdicionarHorario() throws Exception {
        AlterarHorarioPlano alterarHorarioPlano = new AlterarHorarioPlano();

        setContratosAdicionarHorario(alterarHorarioPlano.consultarContratosAlterarHorarios(getEmpresaAdicionarHorarioAoPlano().getCodigo(), getPlanoAdicionarHorarioAoPlano(), getHorarioVO(), getHorarioVOAnterior()));
        manutencaoAjusteGeralTO.setMsgResultado("");
    }

    public List<ContratoVO> getContratosAdicionarHorario() {
        if (contratosAdicionarHorario == null) {
            contratosAdicionarHorario = new ArrayList<>();
        }
        return contratosAdicionarHorario;
    }

    public void setContratosAdicionarHorario(List<ContratoVO> contratosAdicionarHorario) {
        this.contratosAdicionarHorario = contratosAdicionarHorario;
    }

    public void processarContratosAlterarHorario() throws Exception {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("processarContratosAlterarHorario", getEmpresaLogado());

            AlterarHorarioPlano alterarHorarioPlano = new AlterarHorarioPlano();
            setHorarioVO(getFacade().getHorario().consultarPorChavePrimaria(getHorarioVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            String plano = "";
            int qtdContratosAfetados = 0;
            StringBuilder contratosNaoAdicionados = new StringBuilder();
            for (ContratoVO contratoVO : getContratosAdicionarHorario()) {
                try {
                    alterarHorarioPlano.alterarHorarioContrato(contratoVO, getHorarioVO(), getUsuarioLogado());
                    getFacade().getZWFacade().atualizarSintetico(contratoVO.getCliente(), Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_CONTRATO, true);
                    plano = contratoVO.getPlano().getDescricao();
                    qtdContratosAfetados++;
                } catch (Exception ex) {
                    contratosNaoAdicionados.append(contratoVO.getCodigo()).append(",");
                }
            }

            LogVO obj = new LogVO();
            obj.setChavePrimaria("0");
            obj.setPessoa(0);
            obj.setNomeEntidade("CONFIGURACAOSISTEMA-ALTERAR_HORARIO_CONTRATO");
            obj.setNomeEntidadeDescricao("Configurações");
            obj.setOperacao("Alterar Horario (Processo)");
            obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
            obj.setUserOAMD(getUsuarioLogado().getUserOamd());
            obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            obj.setNomeCampo("");
            obj.setValorCampoAnterior("");
            String msg = "Alterado o horário  " + getHorarioVO().getDescricao() + " para os contratos do plano " + plano;
            if (!UteisValidacao.emptyNumber(getHorarioVOAnterior().getCodigo())) {
                setHorarioVOAnterior(getFacade().getHorario().consultarPorChavePrimaria(getHorarioVOAnterior().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                msg += " com horário anterior " + getHorarioVOAnterior().getDescricao();
            }
            msg += " (Total: " + qtdContratosAfetados + ").";
            if (contratosNaoAdicionados.length() > 0) {
                msg += " Contratos não afetados: " + contratosNaoAdicionados.substring(0, contratosNaoAdicionados.length() - 2);
            }
            obj.setValorCampoAlterado(msg);
            getFacade().getLog().incluir(obj);

            setContratosAdicionarHorario(alterarHorarioPlano.consultarContratosAlterarHorarios(getEmpresaAdicionarHorarioAoPlano().getCodigo(), getPlanoAdicionarHorarioAoPlano(), getHorarioVO(), getHorarioVOAnterior()));
            manutencaoAjusteGeralTO.setMsgResultado("Horário Alterado");
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void corrigirAlunoTreinoZW() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("corrigirAlunoTreinoZW", getEmpresaLogado());
            JSONObject todosCodigoPessoa = DaoAuxiliar.retornarAcessoControle(getKey()).getClienteDao().consultarTodosCodigoPessoa();
            String retorno = TreinoWSConsumer.excluirAlunosOrfaos(getKey(), todosCodigoPessoa.toString());
            if (retorno.contains("ERRO")) {
                throw new Exception(retorno);
            } else {
                this.manutencaoAjusteGeralTO.setSucesso(true);
                montarSucesso("Alunos existentes apenas no treino excluidos com sucesso.");
            }
        } catch (Exception e) {
            this.manutencaoAjusteGeralTO.setSucesso(false);
            montarErro(e.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void corrigirNFLote0() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("corrigirNFLote0", getEmpresaLogado());
            qtdNotasEncontradas = new AtomicInteger(0);
            qtdNotasCorrigidas = new AtomicInteger(0);
            qtdNotasExcluida = new AtomicInteger(0);
            ProcessoCorrigirLoteNFe.corrigirLoteNFe(null, false, qtdNotasEncontradas, qtdNotasCorrigidas, qtdNotasExcluida);
        } catch (Exception e) {
            montarErro(e.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void ExcluirNFNaoCorrigidas() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("ExcluirNFNaoCorrigidas", getEmpresaLogado());
            qtdNotasCorrigidas = new AtomicInteger(0);
            qtdNotasExcluida = new AtomicInteger(0);
            ProcessoCorrigirLoteNFe.corrigirLoteNFe(null, true, new AtomicInteger(0), qtdNotasCorrigidas, qtdNotasExcluida);
            qtdNotasEncontradas = new AtomicInteger(0);
        } catch (Exception e) {
            montarErro(e.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public AtomicInteger getQtdNotasCorrigidas() {
        return qtdNotasCorrigidas;
    }

    public void setQtdNotasCorrigidas(AtomicInteger qtdNotasCorrigidas) {
        this.qtdNotasCorrigidas = qtdNotasCorrigidas;
    }

    public AtomicInteger getQtdNotasEncontradas() {
        return qtdNotasEncontradas;
    }

    public void setQtdNotasEncontradas(AtomicInteger qtdNotasEncontradas) {
        this.qtdNotasEncontradas = qtdNotasEncontradas;
    }

    public AtomicInteger getQtdNotasExcluida() {
        return qtdNotasExcluida;
    }

    public void setQtdNotasExcluida(AtomicInteger qtdNotasExcluida) {
        this.qtdNotasExcluida = qtdNotasExcluida;
    }

    public void consultarTransacoesCancelar() {
        manutencaoAjusteGeralTO.setSucesso(false);
        manutencaoAjusteGeralTO.setAviso(false);
        manutencaoAjusteGeralTO.setErro(false);
        manutencaoAjusteGeralTO.setMsgResultado("");
        manutencaoAjusteGeralTO.setConsultou(false);
        try {
            limparMsg();

            if (getUsuarioLogado().getAdministrador()) {
                throw new Exception("Essa operação não pode ser realizada com usuário administrador.");
            }

            if (UteisValidacao.emptyString(getManutencaoAjusteGeralTO().getListaTransacaoEstornar())) {
                throw new Exception("Informe pelo menos um código de transação.");
            }

            List<TransacaoVO> listaTransacoes = getFacade().getTransacao().consultar("select * from transacao where codigo in (" + getManutencaoAjusteGeralTO().getListaTransacaoEstornar() + ") order by codigo ", Uteis.NIVELMONTARDADOS_GESTAOTRANSACAO);

            getManutencaoAjusteGeralTO().setListaEstornoTransacao(new ArrayList<>());
            Integer podeCancelar = 0;
            for (TransacaoVO transacaoVO : listaTransacoes) {
                EstornoTransacaoTO novo = new EstornoTransacaoTO();
                novo.setSucesso(false);
                novo.setTransacaoVO(transacaoVO);
                if (transacaoVO.isPermiteCancelar()) {
                    ++podeCancelar;
                }
                getManutencaoAjusteGeralTO().getListaEstornoTransacao().add(novo);
            }

            if (UteisValidacao.emptyList(getManutencaoAjusteGeralTO().getListaEstornoTransacao())) {
                throw new Exception("Nenhuma transação encontrada.");
            }

            StringBuilder msg = new StringBuilder();
            msg.append("Total ").append(getManutencaoAjusteGeralTO().getListaEstornoTransacao().size()).append(" transações.<br/>");
            msg.append(podeCancelar).append(" transações podem ser canceladas.<br/>");

            manutencaoAjusteGeralTO.setConsultou(true);
            manutencaoAjusteGeralTO.setAviso(true);
            manutencaoAjusteGeralTO.setMsgResultado(msg.toString());
        } catch (Exception ex) {
            montarErro(ex);
            Uteis.logar(ex, ConfiguracaoSistema.class);
            manutencaoAjusteGeralTO.setErro(true);
            manutencaoAjusteGeralTO.setMsgResultado(ex.getMessage());
        }
    }

    public void excluirMovContasRetiradaLoteExcluir() {
        manutencaoAjusteGeralTO.setSucesso(false);
        manutencaoAjusteGeralTO.setErro(false);
        manutencaoAjusteGeralTO.setMsgResultado("");
        try {
            limparMsg();

            if (getUsuarioLogado().getAdministrador()) {
                throw new Exception("Essa operação não pode ser realizada com usuário administrador.");
            }

            if (UteisValidacao.emptyString(getManutencaoAjusteGeralTO().getListaMovContasRetiradaLoteExcluirString())) {
                throw new Exception("Informe pelo menos um código de uma conta.");
            }

            List<MovContaVO> listaMovContasValidasParaExclusao = getFacade().getMovConta().consultar(
                    "select * from movconta where codigo in (" + getManutencaoAjusteGeralTO().getListaMovContasRetiradaLoteExcluirString()
                            + ") and tipooperacao = " + TipoOperacaoLancamento.RETIRADA_RECEBIVEL_LOTE.getCodigo() + " order by codigo ",
                    Uteis.NIVELMONTARDADOS_PAGAMENTOS_CONJUNTO_RESUMIDOS);

            if (UteisValidacao.emptyList(listaMovContasValidasParaExclusao)) {
                throw new Exception("Das contas que você informou, não foi encontrada nenhuma conta de retirada de recebível elegível para exclusão." +
                        " Verifique se as contas informadas estão corretas e se são de retirada de lote.");
            }

            Integer excluídasSucesso = 0;
            Integer erro = 0;
            for (MovContaVO movContaVO : listaMovContasValidasParaExclusao) {
                if (movContaVO.getTipoOperacaoLancamento().equals(TipoOperacaoLancamento.RETIRADA_RECEBIVEL_LOTE)) {
                    try {
                        getFacade().getMovConta().excluir(movContaVO);
                        ++excluídasSucesso;
                    } catch (Exception ex) {

                        String msgErro = "";
                        msgErro += UteisValidacao.emptyNumber(movContaVO.getCodigo()) ? "" : movContaVO.getCodigo() + " - ";
                        msgErro += UteisValidacao.emptyString(movContaVO.getDescricao()) ? "" : movContaVO.getDescricao() + ": ";
                        msgErro += ex.getMessage();

                        montarErro(msgErro);
                        ex.printStackTrace();
                        ++erro;
                    }
                }
            }

            StringBuilder msg = new StringBuilder();
            msg.append("Total de contas elegíveis para exclusão: ").append(listaMovContasValidasParaExclusao.size()).append(".<br/>");
            msg.append("Sucesso: ").append(excluídasSucesso).append(" contas excluídas.<br/>");
            msg.append("Erro: ").append(erro).append(" contas não excluídas.<br/>");

            manutencaoAjusteGeralTO.setSucesso(true);
            manutencaoAjusteGeralTO.setMsgResultado(msg.toString());
        } catch (Exception ex) {
            montarErro(ex);
            Uteis.logar(ex, ConfiguracaoSistema.class);
            manutencaoAjusteGeralTO.setErro(true);
            manutencaoAjusteGeralTO.setMsgResultado(ex.getMessage());
        }
    }

    public void limparTransacoesCancelar() {
        limparMsg();
        manutencaoAjusteGeralTO.setConsultou(false);
        manutencaoAjusteGeralTO.setMsgResultado("");
        manutencaoAjusteGeralTO.setListaEstornoTransacao(new ArrayList<>());
    }

    public void acaoTransacoesCancelar() {
        manutencaoAjusteGeralTO.setSucesso(false);
        manutencaoAjusteGeralTO.setAviso(false);
        manutencaoAjusteGeralTO.setErro(false);
        manutencaoAjusteGeralTO.setMsgResultado("");
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("acaoTransacoesCancelar", getEmpresaLogado());
            limparMsg();

            if (UteisValidacao.emptyList(getManutencaoAjusteGeralTO().getListaEstornoTransacao())) {
                throw new Exception("Informe pelo menos um código de transação.");
            }

            Integer sucesso = 0;
            Integer erro = 0;
            for (EstornoTransacaoTO estornoTransacaoTO : getManutencaoAjusteGeralTO().getListaEstornoTransacao()) {
                try {
                    String retorno = getFacade().getTransacao().cancelarTransacao(estornoTransacaoTO.getTransacaoVO(), getManutencaoAjusteGeralTO().isEstornarRecibo(), getUsuarioLogado(), getKey());
                    estornoTransacaoTO.setMsgResultado(retorno);
                    estornoTransacaoTO.setSucesso(true);
                    ++sucesso;
                } catch (Exception ex) {
                    estornoTransacaoTO.setMsgResultado(ex.getMessage());
                    estornoTransacaoTO.setSucesso(false);
                    ++erro;
                } finally {
                    try {
                        JSONObject json = new JSONObject();
                        json.put("codigo", estornoTransacaoTO.getTransacaoVO().getCodigo());
                        json.put("sucesso", estornoTransacaoTO.isSucesso());
                        json.put("msg", estornoTransacaoTO.getMsgResultado());
                        json.put("estornarRecibo", getManutencaoAjusteGeralTO().isEstornarRecibo());
                        getFacade().getLogAjusteGeral().incluir(Calendario.hoje(), getUsuarioLogado().getNome(), getUsuarioLogado().getUserOamd(),
                                ProcessoAjusteGeralEnum.CANCELAR_TRANSACAO, json.toString());
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }
            }

            StringBuilder msg = new StringBuilder();
            msg.append("Total ").append(getManutencaoAjusteGeralTO().getListaEstornoTransacao().size()).append(" transações.<br/>");
            msg.append("Falha ").append(erro).append(" transações.<br/>");
            msg.append("Sucesso ").append(sucesso).append(" transações.<br/>");
            msg.append("<br/>Consulte o log para verificar mais detalhes.");

            manutencaoAjusteGeralTO.setConsultou(false);
            manutencaoAjusteGeralTO.setSucesso(true);
            manutencaoAjusteGeralTO.setMsgResultado(msg.toString());
        } catch (Exception ex) {
            montarErro(ex);
            Uteis.logar(ex, ConfiguracaoSistema.class);
            manutencaoAjusteGeralTO.setErro(true);
            manutencaoAjusteGeralTO.setMsgResultado(ex.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void consultarLogTransacoesCanceladas() {
        try {
            setLogAjusteGeralVOList(new ArrayList<>());
            List<LogAjusteGeralVO> novoLog = getFacade().getLogAjusteGeral().consultarPorAjusteGeral(ProcessoAjusteGeralEnum.CANCELAR_TRANSACAO, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            setLogAjusteGeralVOList(novoLog);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void consultarRecibosEstornar() {
        manutencaoAjusteGeralTO.setSucesso(false);
        manutencaoAjusteGeralTO.setAviso(false);
        manutencaoAjusteGeralTO.setErro(false);
        manutencaoAjusteGeralTO.setMsgResultado("");
        manutencaoAjusteGeralTO.setConsultou(false);
        try {
            limparMsg();

            if (getUsuarioLogado().getAdministrador()) {
                throw new Exception("Essa operação não pode ser realizada com usuário administrador.");
            }

            if (UteisValidacao.emptyString(getManutencaoAjusteGeralTO().getListaRecibosEstornarString())) {
                throw new Exception("Informe pelo menos um código de recibo.");
            }

            int qtdRecibosInformados = 0;
            String[] lstRecibos = getManutencaoAjusteGeralTO().getListaRecibosEstornarString().split(",");
            for (String recibo : lstRecibos) {
                qtdRecibosInformados++;
            }

            if (qtdRecibosInformados > 200) {
                throw new Exception("Por razões de performance, o sistema permite a consulta e estorno de no máximo 200 recibos por vez. Divida a consulta em lotes menores.");
            }


            List<ReciboPagamentoVO> listaRecibos = getFacade().getReciboPagamento().consultarPorListaDeCodigos(
                    getManutencaoAjusteGeralTO().getListaRecibosEstornarString(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            getManutencaoAjusteGeralTO().setListaRecibosEstornar(listaRecibos);

            if (UteisValidacao.emptyList(getManutencaoAjusteGeralTO().getListaRecibosEstornar())) {
                throw new Exception("Nenhum recibo encontrado.");
            }

            StringBuilder msg = new StringBuilder();
            msg.append("Total ").append(getManutencaoAjusteGeralTO().getListaRecibosEstornar().size()).append(" recibos a serem estornados.<br/>");

            manutencaoAjusteGeralTO.setConsultouRecibos(true);
            manutencaoAjusteGeralTO.setAviso(true);
            manutencaoAjusteGeralTO.setMsgResultado(msg.toString());
        } catch (Exception ex) {
            montarErro(ex);
            Uteis.logar(ex, ConfiguracaoSistema.class);
            manutencaoAjusteGeralTO.setErro(true);
            manutencaoAjusteGeralTO.setMsgResultado(ex.getMessage());
        }
    }

    public void limparRecibosEstornar() {
        limparMsg();
        manutencaoAjusteGeralTO.setConsultouRecibos(false);
        manutencaoAjusteGeralTO.setMsgResultado("");
        manutencaoAjusteGeralTO.setListaRecibosEstornarString("");
        manutencaoAjusteGeralTO.setListaRecibosEstornar(new ArrayList<>());
    }

    public void acaoRecibosEstornar() {
        manutencaoAjusteGeralTO.setSucesso(false);
        manutencaoAjusteGeralTO.setAviso(false);
        manutencaoAjusteGeralTO.setErro(false);
        manutencaoAjusteGeralTO.setMsgResultado("");
        LogProcessoSistemaVO log = null;
        try {
            limparMsg();

            if (UteisValidacao.emptyList(getManutencaoAjusteGeralTO().getListaRecibosEstornar())) {
                throw new Exception("Informe pelo menos um código de recibo.");
            }

            Integer sucesso = 0;
            Integer erro = 0;

            UsuarioVO usuarioResp = getUsuarioRecorrencia();
            log = criarLog("acaoRecibosEstornar", getEmpresaLogado());
            for (ReciboPagamentoVO reciboVO : getManutencaoAjusteGeralTO().getListaRecibosEstornar()) {
                try {
                    List<MovParcelaVO> movParcelaVOS = getFacade().getMovParcela().consultarPorCodigoRecibo(reciboVO.getCodigo(), false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    estornarRecibo(reciboVO, usuarioResp, movParcelaVOS);

                    if (getManutencaoAjusteGeralTO().isEstornarMovProduto()) {
                        List<MovProdutoVO> movProdutosEstornar = new ArrayList<>();
                        for (MovParcelaVO movParcelaVO : movParcelaVOS) {
                            movProdutosEstornar.addAll(getFacade().getMovProduto().consultarPorCodigoParcela(movParcelaVO.getCodigo(), Uteis.NIVELMONTARDADOS_VENDA));
                        }

                        if (!UteisValidacao.emptyList(movProdutosEstornar)) {
                            for (MovProdutoVO movProdutoVO : movProdutosEstornar) {
                                if (podeEstornarProduto(movProdutoVO)) {
                                    EstornoMovProdutoControle control = getControlador(EstornoMovProdutoControle.class);
                                    control.novo(movProdutoVO.getCodigo());
                                    control.gravar();
                                }
                            }
                        }
                    }

                    ++sucesso;
                    try {
                        registrarLogItemEstornoRecibo(reciboVO.getCodigo(), true);
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                } catch (Exception ex) {
                    ++erro;
                    try {
                        registrarLogItemEstornoRecibo(reciboVO.getCodigo(), false);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }


            }

            StringBuilder msg = new StringBuilder();
            msg.append("Total ").append(getManutencaoAjusteGeralTO().getListaRecibosEstornar().size()).append(" recibos.<br/>");
            msg.append("Falha ").append(erro).append(" recibos.<br/>");
            msg.append("Sucesso ").append(sucesso).append(" recibos.<br/>");
            msg.append("<br/>Consulte o log para verificar mais detalhes.");

            manutencaoAjusteGeralTO.setConsultouRecibos(false);
            manutencaoAjusteGeralTO.setSucesso(true);
            manutencaoAjusteGeralTO.setMsgResultado(msg.toString());

        } catch (Exception ex) {
            montarErro(ex);
            Uteis.logar(ex, ConfiguracaoSistema.class);
            manutencaoAjusteGeralTO.setErro(true);
            manutencaoAjusteGeralTO.setMsgResultado(ex.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }


    private boolean podeEstornarProduto(MovProdutoVO mov) {
        return mov.getProduto().getTipoProduto().equals("PE")
                || mov.getProduto().getTipoProduto().equals("SE")
                || mov.getProduto().getTipoProduto().equals("DI")
                || mov.getProduto().getTipoProduto().equals("AA")
                || mov.getProduto().getTipoProduto().equals("SS")
                || mov.getProduto().getTipoProduto().equals(TipoProduto.DESAFIO.getCodigo())
                || (mov.getProduto().getTipoProduto().equals("AC") && mov.getSituacao().equals("EA"))
                || (mov.getProduto().getTipoProduto().equals("CC") && mov.getSituacao().equals("EA"))
                || (mov.getProduto().getTipoProduto().equals(TipoProduto.ARMARIO.getCodigo()) && mov.getSituacao().equals("EA"))
                || mov.getProduto().getTipoProduto().equals(TipoProduto.HOMEFIT.getCodigo())
                || mov.getProduto().getTipoProduto().equals(TipoProduto.BIO_TOTEM.getCodigo())
                || mov.getProduto().getTipoProduto().equals(TipoProduto.CONSULTA_NUTRICIONAL.getCodigo());
    }

    private void registrarLogItemEstornoRecibo(int cod, boolean sucesso) throws Exception {
        JSONObject json = new JSONObject();
        json.put("RECIBO", cod);
        json.put("sucesso", sucesso);
        getFacade().getLogAjusteGeral().incluir(Calendario.hoje(), getUsuarioLogado().getNome(), getUsuarioLogado().getUserOamd(),
                ProcessoAjusteGeralEnum.ESTORNAR_RECIBO, json.toString());
    }

    public void consultarLogRecibosEstonados() {
        try {
            setLogAjusteGeralVOList(new ArrayList<>());
            List<LogAjusteGeralVO> novoLog = getFacade().getLogAjusteGeral().consultarPorAjusteGeral(ProcessoAjusteGeralEnum.ESTORNAR_RECIBO, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            setLogAjusteGeralVOList(novoLog);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void processoPreencherCodigoRetornoTransacao() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("processoPreencherCodigoRetornoTransacao", getEmpresaLogado());
            limparMsg();

            ProcessoPreencherCodigoRetornoTransacao processo = new ProcessoPreencherCodigoRetornoTransacao(getFacade().getTransacao().getCon());
            processo.processar();
            processo = null;

            montarSucessoGrowl("Código de retorno das transações foram processadas.");
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
            montarErro(ex);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void processoPreencherCodigoRetornoTransacaoCielo() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("processoPreencherCodigoRetornoTransacaoCielo", getEmpresaLogado());
            limparMsg();

            ProcessoPreencherCodigoRetornoTransacaoCielo processo = new ProcessoPreencherCodigoRetornoTransacaoCielo(getFacade().getTransacao().getCon());
            processo.processar();
            processo = null;

            montarSucessoGrowl("Código de retorno das transações foram processadas.");
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
            montarErro(ex);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void consultarRemessasSemSituacao() {
        try {
            limparMsg();

            manutencaoAjusteGeralTO.setRemessasSemSituacao(getFacade().getRemessa().consultarRemessasSemSitucao(null));
            if (UteisValidacao.emptyList(manutencaoAjusteGeralTO.getRemessasSemSituacao())) {
                montarSucessoGrowl("Não existem remessas sem situação.");
                return;
            }

        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void excluirTodasRemessaSemSituacao() {
        excluirRemessaSemSituacao(true);
    }

    public void excluirRemessaSemSituacaoUnica() {
        excluirRemessaSemSituacao(false);
    }

    private void excluirRemessaSemSituacao(boolean todas) {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("excluirRemessaSemSituacao_" + todas, getEmpresaLogado());
            limparMsg();

            Set<Integer> remessasExcluir = new HashSet<>();
            if (todas) {
                for (RemessaVO remessaVO : manutencaoAjusteGeralTO.getRemessasSemSituacao()) {
                    remessasExcluir.add(remessaVO.getCodigo());
                }
            } else {
                if (UteisValidacao.emptyNumber(manutencaoAjusteGeralTO.getRemessaExcluir())) {
                    throw new Exception("Informe o código da remessa");
                }
                remessasExcluir.add(manutencaoAjusteGeralTO.getRemessaExcluir());
            }

            if (remessasExcluir.isEmpty()) {
                throw new Exception("Nenhuma remessa para excluir");
            }

            for (Integer remessa : remessasExcluir) {
                RemessaVO remessaVO = getFacade().getRemessa().consultarPorChavePrimaria(remessa);
                if (!remessaVO.getSituacaoRemessa().equals(SituacaoRemessaEnum.NENHUMA)) {
                    throw new Exception("Remessa " + remessa + " - Somente remessas sem situação podem ser excluídas.");
                }
                getFacade().getRemessa().excluirComLog(remessaVO, getUsuarioLogado());
            }

            if (todas) {
                montarSucessoGrowl("Todas as remessas foram excluídas com sucesso.");
            } else {
                montarSucessoGrowl("Remessa foi excluida com sucesso.");
            }
        } catch (Exception ex) {
            montarErro(ex);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public String getEmpresasTentativaUnicaDeCobrancaAtivada() {
        if (empresasTentativaUnicaDeCobrancaAtivada == null) {
            empresasTentativaUnicaDeCobrancaAtivada = "";
        }
        return empresasTentativaUnicaDeCobrancaAtivada;
    }

    public void setEmpresasTentativaUnicaDeCobrancaAtivada(String empresasTentativaUnicaDeCobrancaAtivada) {
        this.empresasTentativaUnicaDeCobrancaAtivada = empresasTentativaUnicaDeCobrancaAtivada;
    }

    public void consultarInformacoesCobranca() {
        try {
            setEmpresasTentativaUnicaDeCobrancaAtivada("");

            StringBuilder empresas = new StringBuilder();
            List<EmpresaVO> listaEmpresas = getFacade().getEmpresa().consultarTodas(true, Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
            for (EmpresaVO empresaVO : listaEmpresas) {
                if (!empresaVO.isHabilitarReenvioAutomaticoRemessa() && empresaVO.isTentativaUnicaDeCobranca()) {
                    empresas.append("<b>").append(empresaVO.getNome()).append("</b><br/>");
                }
            }

            if (empresas.length() > 0) {
                StringBuilder title = new StringBuilder();
                title.append("As empresas abaixo estão com a opção <b>\"Tentar cobrar somente 1 vez automaticamente\"</b> habilitada.<br/><br/>");
                title.append(empresas.toString());
                title.append("<br/>Essa configuração de dias não terá efeito para essas empresas.");
                setEmpresasTentativaUnicaDeCobrancaAtivada(title.toString());
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void processoMovParcelaResultadoCobranca() {
        LogProcessoSistemaVO log = null;
        try {
            limparMsg();

            if (getMesMovParcelaResultadoCobranca() == null) {
                throw new Exception("Informe o mês para processar");
            }

            log = criarLog("processoMovParcelaResultadoCobranca", getEmpresaLogado());

            MovParcelaResultadoCobranca processo = new MovParcelaResultadoCobranca(getFacade().getTransacao().getCon());
            processo.processarParcelas(null, getMesMovParcelaResultadoCobranca());
            processo = null;

            montarSucessoGrowl("Parcelas processadas com sucesso.");
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
            montarErro(ex);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public Date getMesMovParcelaResultadoCobranca() {
        return mesMovParcelaResultadoCobranca;
    }

    public void setMesMovParcelaResultadoCobranca(Date mesMovParcelaResultadoCobranca) {
        this.mesMovParcelaResultadoCobranca = mesMovParcelaResultadoCobranca;
    }

    public List<SelectItem> getListSelectItemTipoBloqueioCobrancaEnum() {
        return TipoBloqueioCobrancaEnum.getSelectListTipoBloqueioCobrancaEnum();
    }

    public void consultarBloquearClientes() {
        manutencaoAjusteGeralTO.setSucesso(false);
        manutencaoAjusteGeralTO.setAviso(false);
        manutencaoAjusteGeralTO.setErro(false);
        manutencaoAjusteGeralTO.setMsgResultado("");
        manutencaoAjusteGeralTO.setConsultou(false);
        manutencaoAjusteGeralTO.setListaPessoasBloqueioCobrancaAutomatica(new ArrayList<>());
        try {
            limparMsg();

            if (manutencaoAjusteGeralTO.getTipoBloqueioCobrancaAutomatica() == null) {
                throw new Exception("Selecione o tipo de bloqueio");
            }

            if (manutencaoAjusteGeralTO.getTipoBloqueioCobrancaAutomatica().equals(TipoBloqueioCobrancaEnum.PARCELAS_FUTURAS) &&
                    manutencaoAjusteGeralTO.getDataBloqueioCobrancaAutomatica() == null) {
                throw new Exception("Selecione uma data de bloqueio");
            }

            if (manutencaoAjusteGeralTO.getTipoBloqueioCobrancaAutomatica().equals(TipoBloqueioCobrancaEnum.TODAS_PARCELAS)) {
                manutencaoAjusteGeralTO.setDataBloqueioCobrancaAutomatica(Calendario.hoje());
            }

            List<PessoaBloqueioCobrancaTO> listaPessoas = getFacade().getPessoa().consultarPessoasParaBloqueio(false, manutencaoAjusteGeralTO);
            getManutencaoAjusteGeralTO().setListaPessoasBloqueioCobrancaAutomatica(listaPessoas);

            if (UteisValidacao.emptyList(getManutencaoAjusteGeralTO().getListaPessoasBloqueioCobrancaAutomatica())) {
                throw new Exception("Nenhuma pessoa encontrada.");
            }

            StringBuilder msg = new StringBuilder();
            msg.append("Total ").append(getManutencaoAjusteGeralTO().getListaPessoasBloqueioCobrancaAutomatica().size()).append(" pessoas.");

            manutencaoAjusteGeralTO.setConsultou(true);
            manutencaoAjusteGeralTO.setAviso(true);
            manutencaoAjusteGeralTO.setMsgResultado(msg.toString());
        } catch (Exception ex) {
            montarErro(ex);
            Uteis.logar(ex, ConfiguracaoSistemaControle.class);
            manutencaoAjusteGeralTO.setErro(true);
            manutencaoAjusteGeralTO.setMsgResultado(ex.getMessage());
        }
    }

    public void limparBloquearClientes() {
        limparMsg();
        manutencaoAjusteGeralTO.setConsultou(false);
        manutencaoAjusteGeralTO.setMsgResultado("");
        manutencaoAjusteGeralTO.setListaPessoasBloqueioCobrancaAutomatica(new ArrayList<>());
    }

    public void acaoBloquearClientes() {
        manutencaoAjusteGeralTO.setSucesso(false);
        manutencaoAjusteGeralTO.setAviso(false);
        manutencaoAjusteGeralTO.setErro(false);
        manutencaoAjusteGeralTO.setMsgResultado("");
        LogProcessoSistemaVO log = null;
        try {
            limparMsg();

            if (manutencaoAjusteGeralTO.getTipoBloqueioCobrancaAutomatica() == null) {
                throw new Exception("Selecione o tipo de bloqueio");
            }

            if (manutencaoAjusteGeralTO.getTipoBloqueioCobrancaAutomatica().equals(TipoBloqueioCobrancaEnum.PARCELAS_FUTURAS) &&
                    manutencaoAjusteGeralTO.getDataBloqueioCobrancaAutomatica() == null) {
                throw new Exception("Selecione uma data de bloqueio");
            }

            log = criarLog("acaoBloquearClientes", getEmpresaLogado());
            if (manutencaoAjusteGeralTO.getTipoBloqueioCobrancaAutomatica().equals(TipoBloqueioCobrancaEnum.TODAS_PARCELAS)) {
                manutencaoAjusteGeralTO.setDataBloqueioCobrancaAutomatica(Calendario.hoje());
            }

            Integer sucesso = 0;
            Integer erro = 0;
            for (PessoaBloqueioCobrancaTO pessoaBloqueioCobrancaTO : getManutencaoAjusteGeralTO().getListaPessoasBloqueioCobrancaAutomatica()) {
                try {
                    getFacade().getPessoa().alterarDataBloqueioCobrancaAutomatica(getManutencaoAjusteGeralTO().getDataBloqueioCobrancaAutomatica(), getManutencaoAjusteGeralTO().getTipoBloqueioCobrancaAutomatica(),
                            pessoaBloqueioCobrancaTO.getPessoa(), getUsuarioLogado(), true, "Processo Bloqueio de Cobrança Automática");
                    pessoaBloqueioCobrancaTO.setMsgResultado("Sucesso");
                    pessoaBloqueioCobrancaTO.setSucesso(true);
                    ++sucesso;
                } catch (Exception ex) {
                    pessoaBloqueioCobrancaTO.setMsgResultado(ex.getMessage());
                    pessoaBloqueioCobrancaTO.setSucesso(false);
                    ++erro;
                } finally {
                    try {
                        JSONObject json = new JSONObject();
                        json.put("pessoa", pessoaBloqueioCobrancaTO.getPessoa());
                        json.put("sucesso", pessoaBloqueioCobrancaTO.isSucesso());
                        json.put("msg", pessoaBloqueioCobrancaTO.getMsgResultado());
                        getFacade().getLogAjusteGeral().incluir(Calendario.hoje(), getUsuarioLogado().getNome(), getUsuarioLogado().getUserOamd(),
                                ProcessoAjusteGeralEnum.COBRANCA_AUTOMATICA_BLOQUEAR, json.toString());
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }
            }

            StringBuilder msg = new StringBuilder();
            msg.append("Total ").append(getManutencaoAjusteGeralTO().getListaPessoasBloqueioCobrancaAutomatica().size()).append(" pessoas.<br/>");
            msg.append("Falha ").append(erro).append(" pessoas.<br/>");
            msg.append("Sucesso ").append(sucesso).append(" pessoas.<br/>");
            msg.append("<br/>Consulte o log para verificar mais detalhes.");

            manutencaoAjusteGeralTO.setConsultou(false);
            manutencaoAjusteGeralTO.setSucesso(true);
            manutencaoAjusteGeralTO.setMsgResultado(msg.toString());
        } catch (Exception ex) {
            montarErro(ex);
            Uteis.logar(ex, ConfiguracaoSistemaControle.class);
            manutencaoAjusteGeralTO.setErro(true);
            manutencaoAjusteGeralTO.setMsgResultado(ex.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void consultarLogBloquearClientes() {
        try {
            setLogAjusteGeralVOList(new ArrayList<>());
            List<LogAjusteGeralVO> novoLog = getFacade().getLogAjusteGeral().consultarPorAjusteGeral(ProcessoAjusteGeralEnum.COBRANCA_AUTOMATICA_BLOQUEAR, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            setLogAjusteGeralVOList(novoLog);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void consultarLogRemessaExcluida() {
        try {
            setLogAjusteGeralVOList(new ArrayList<>());
            List<LogAjusteGeralVO> novoLog = getFacade().getLogAjusteGeral().consultarPorAjusteGeral(ProcessoAjusteGeralEnum.EXCLUIR_REMESSA, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            setLogAjusteGeralVOList(novoLog);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void consultarDesbloquearClientes() {
        manutencaoAjusteGeralTO.setSucesso(false);
        manutencaoAjusteGeralTO.setAviso(false);
        manutencaoAjusteGeralTO.setErro(false);
        manutencaoAjusteGeralTO.setMsgResultado("");
        manutencaoAjusteGeralTO.setConsultou(false);
        manutencaoAjusteGeralTO.setListaPessoasBloqueioCobrancaAutomatica(new ArrayList<>());
        try {
            limparMsg();

            List<PessoaBloqueioCobrancaTO> listaPessoas = getFacade().getPessoa().consultarPessoasParaBloqueio(true, manutencaoAjusteGeralTO);
            getManutencaoAjusteGeralTO().setListaPessoasBloqueioCobrancaAutomatica(listaPessoas);

            if (UteisValidacao.emptyList(getManutencaoAjusteGeralTO().getListaPessoasBloqueioCobrancaAutomatica())) {
                throw new Exception("Nenhuma pessoa encontrada.");
            }

            StringBuilder msg = new StringBuilder();
            msg.append("Total ").append(getManutencaoAjusteGeralTO().getListaPessoasBloqueioCobrancaAutomatica().size()).append(" pessoas.");

            manutencaoAjusteGeralTO.setConsultou(true);
            manutencaoAjusteGeralTO.setAviso(true);
            manutencaoAjusteGeralTO.setMsgResultado(msg.toString());
        } catch (Exception ex) {
            montarErro(ex);
            Uteis.logar(ex, ConfiguracaoSistemaControle.class);
            manutencaoAjusteGeralTO.setErro(true);
            manutencaoAjusteGeralTO.setMsgResultado(ex.getMessage());
        }
    }

    public void acaoDesbloquearClientes() {
        manutencaoAjusteGeralTO.setSucesso(false);
        manutencaoAjusteGeralTO.setAviso(false);
        manutencaoAjusteGeralTO.setErro(false);
        manutencaoAjusteGeralTO.setMsgResultado("");
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("acaoDesbloquearClientes", getEmpresaLogado());
            limparMsg();

            Integer sucesso = 0;
            Integer erro = 0;
            for (PessoaBloqueioCobrancaTO pessoaBloqueioCobrancaTO : getManutencaoAjusteGeralTO().getListaPessoasBloqueioCobrancaAutomatica()) {
                try {
                    getFacade().getPessoa().alterarDataBloqueioCobrancaAutomatica(null, null, pessoaBloqueioCobrancaTO.getPessoa(),
                            getUsuarioLogado(), true, "Processo Desbloqueio de Cobrança Automática");
                    pessoaBloqueioCobrancaTO.setMsgResultado("Sucesso");
                    pessoaBloqueioCobrancaTO.setSucesso(true);
                    ++sucesso;
                } catch (Exception ex) {
                    pessoaBloqueioCobrancaTO.setMsgResultado(ex.getMessage());
                    pessoaBloqueioCobrancaTO.setSucesso(false);
                    ++erro;
                } finally {
                    try {
                        JSONObject json = new JSONObject();
                        json.put("pessoa", pessoaBloqueioCobrancaTO.getPessoa());
                        json.put("sucesso", pessoaBloqueioCobrancaTO.isSucesso());
                        json.put("msg", pessoaBloqueioCobrancaTO.getMsgResultado());
                        getFacade().getLogAjusteGeral().incluir(Calendario.hoje(), getUsuarioLogado().getNome(), getUsuarioLogado().getUserOamd(),
                                ProcessoAjusteGeralEnum.COBRANCA_AUTOMATICA_DESBLOQUEAR, json.toString());
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }
            }

            StringBuilder msg = new StringBuilder();
            msg.append("Total ").append(getManutencaoAjusteGeralTO().getListaPessoasBloqueioCobrancaAutomatica().size()).append(" pessoas.<br/>");
            msg.append("Falha ").append(erro).append(" pessoas.<br/>");
            msg.append("Sucesso ").append(sucesso).append(" pessoas.<br/>");
            msg.append("<br/>Consulte o log para verificar mais detalhes.");

            manutencaoAjusteGeralTO.setConsultou(false);
            manutencaoAjusteGeralTO.setSucesso(true);
            manutencaoAjusteGeralTO.setMsgResultado(msg.toString());
        } catch (Exception ex) {
            montarErro(ex);
            Uteis.logar(ex, ConfiguracaoSistemaControle.class);
            manutencaoAjusteGeralTO.setErro(true);
            manutencaoAjusteGeralTO.setMsgResultado(ex.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void consultarLogDesbloquearClientes() {
        try {
            setLogAjusteGeralVOList(new ArrayList<>());
            List<LogAjusteGeralVO> novoLog = getFacade().getLogAjusteGeral().consultarPorAjusteGeral(ProcessoAjusteGeralEnum.COBRANCA_AUTOMATICA_DESBLOQUEAR, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            setLogAjusteGeralVOList(novoLog);
        } catch (Exception e) {
            montarErro(e);
        }
    }


    public boolean isSelecionouParcelasFuturas() {
        return this.getManutencaoAjusteGeralTO() != null &&
                this.getManutencaoAjusteGeralTO().getTipoBloqueioCobrancaAutomatica() != null &&
                this.getManutencaoAjusteGeralTO().getTipoBloqueioCobrancaAutomatica().equals(TipoBloqueioCobrancaEnum.PARCELAS_FUTURAS);
    }

    public boolean isSelecionouTodasParcelas() {
        return this.getManutencaoAjusteGeralTO() != null &&
                this.getManutencaoAjusteGeralTO().getTipoBloqueioCobrancaAutomatica() != null &&
                this.getManutencaoAjusteGeralTO().getTipoBloqueioCobrancaAutomatica().equals(TipoBloqueioCobrancaEnum.TODAS_PARCELAS);
    }

    public void alterouAbaBloquearClientes(ActionEvent evt) {
        try {
            limparMsg();
            this.setManutencaoAjusteGeralTO(new ManutencaoAjusteGeralTO());
            carregarInformacoesPanelBloquearClientes();
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    private void carregarInformacoesPanelBloquearClientes() {
        try {
            limparMsg();
            this.setListaSelectItemEmpresa(new ArrayList<>());

            List<EmpresaVO> empresaVOList = getFacade().getEmpresa().consultarTodas(null, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            for (EmpresaVO obj : empresaVOList) {
                getListaSelectItemEmpresa().add(new SelectItem(obj.getCodigo(), obj.getNome()));
            }
            Ordenacao.ordenarLista(getListaSelectItemEmpresa(), "label");
            getListaSelectItemEmpresa().add(0, new SelectItem(0, "TODAS"));

            selecionouEmpresaBloquearClientes();
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        }
    }

    public void selecionouEmpresaBloquearClientes() {
        try {
            limparMsg();
            carregarListaSelectItemConvenioCobranca(this.getManutencaoAjusteGeralTO().getEmpresaVO().getCodigo());
            carregarListaSelectItemPlano(this.getManutencaoAjusteGeralTO().getEmpresaVO().getCodigo());
            carregarListaSelectItemModalidade(this.getManutencaoAjusteGeralTO().getEmpresaVO().getCodigo());
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        }
    }

    private void carregarListaSelectItemConvenioCobranca(Integer empresa) {
        try {
            setListaSelectItemConvenioCobranca(new ArrayList<>());
            List<ConvenioCobrancaVO> convenios = getFacade().getConvenioCobranca().consultarPorEmpresaESituacao(empresa,
                    false, Uteis.NIVELMONTARDADOS_DADOSBASICOS, null, null);

            Set<Integer> map = new HashSet<>();
            for (ConvenioCobrancaVO obj : convenios) {
                if (map.contains(obj.getCodigo())) {
                    continue;
                }
                getListaSelectItemConvenioCobranca().add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
                map.add(obj.getCodigo());
            }
            Ordenacao.ordenarLista(getListaSelectItemConvenioCobranca(), "label");
            getListaSelectItemConvenioCobranca().add(0, new SelectItem(0, "TODOS"));
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        }
    }

    private void carregarListaSelectItemPlano(Integer empresa) {
        try {
            setListaSelectItemPlano(new ArrayList<>());
            List<PlanoVO> planoVOS = getFacade().getPlano().consultarPorCodigoEmpresa(empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (PlanoVO obj : planoVOS) {
                getListaSelectItemPlano().add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
            }
            Ordenacao.ordenarLista(getListaSelectItemPlano(), "label");
            getListaSelectItemPlano().add(0, new SelectItem(0, "TODOS"));
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        }
    }

    private void carregarListaSelectItemModalidade(Integer empresa) {
        try {
            setListaSelectItemModalidade(new ArrayList<>());
            List<ModalidadeVO> modalidadeVOS = getFacade().getModalidade().consultarTodasModalidades(empresa, null, null);
            for (ModalidadeVO obj : modalidadeVOS) {
                getListaSelectItemModalidade().add(new SelectItem(obj.getCodigo(), obj.getNome()));
            }
            Ordenacao.ordenarLista(getListaSelectItemModalidade(), "label");
            getListaSelectItemModalidade().add(0, new SelectItem(0, "TODAS"));
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        }
    }

    public List<SelectItem> getListaSelectItemConvenioCobranca() {
        if (listaSelectItemConvenioCobranca == null) {
            listaSelectItemConvenioCobranca = new ArrayList<>();
        }
        return listaSelectItemConvenioCobranca;
    }

    public void setListaSelectItemConvenioCobranca(List<SelectItem> listaSelectItemConvenioCobranca) {
        this.listaSelectItemConvenioCobranca = listaSelectItemConvenioCobranca;
    }

    public List<SelectItem> getListaSelectItemEmpresa() {
        if (listaSelectItemEmpresa == null) {
            listaSelectItemEmpresa = new ArrayList<>();
        }
        return listaSelectItemEmpresa;
    }

    public void setListaSelectItemEmpresa(List<SelectItem> listaSelectItemEmpresa) {
        this.listaSelectItemEmpresa = listaSelectItemEmpresa;
    }

    public List<SelectItem> getListaSelectItemPlano() {
        if (listaSelectItemPlano == null) {
            listaSelectItemPlano = new ArrayList<>();
        }
        return listaSelectItemPlano;
    }

    public void setListaSelectItemPlano(List<SelectItem> listaSelectItemPlano) {
        this.listaSelectItemPlano = listaSelectItemPlano;
    }

    public List<SelectItem> getListaSelectItemModalidade() {
        if (listaSelectItemModalidade == null) {
            listaSelectItemModalidade = new ArrayList<>();
        }
        return listaSelectItemModalidade;
    }

    public void setListaSelectItemModalidade(List<SelectItem> listaSelectItemModalidade) {
        this.listaSelectItemModalidade = listaSelectItemModalidade;
    }

    public List<SelectItem> getListaSelectSituacaoAluno() {
        List<SelectItem> lista = new ArrayList<>();
        lista.add(new SelectItem(SituacaoClienteEnum.A_VENCER.getCodigo(), SituacaoClienteEnum.A_VENCER.getDescricao()));
        lista.add(new SelectItem(SituacaoClienteEnum.ATIVO.getCodigo(), SituacaoClienteEnum.ATIVO.getDescricao()));
        lista.add(new SelectItem(SituacaoClienteEnum.CANCELADO.getCodigo(), SituacaoClienteEnum.CANCELADO.getDescricao()));
        lista.add(new SelectItem(SituacaoClienteEnum.TRANCADO.getCodigo(), SituacaoClienteEnum.TRANCADO.getDescricao()));
        lista.add(new SelectItem(SituacaoClienteEnum.INATIVO.getCodigo(), SituacaoClienteEnum.INATIVO.getDescricao()));
        lista.add(new SelectItem(SituacaoClienteEnum.VISITANTE.getCodigo(), SituacaoClienteEnum.VISITANTE.getDescricao()));
        lista.add(new SelectItem(SituacaoClienteEnum.DESISTENTE.getCodigo(), SituacaoClienteEnum.DESISTENTE.getDescricao()));
        lista.add(new SelectItem(SituacaoClienteEnum.VENCIDO.getCodigo(), SituacaoClienteEnum.VENCIDO.getDescricao()));
        Ordenacao.ordenarLista(lista, "label");
        lista.add(0, new SelectItem("", "TODOS"));
        return lista;
    }

    public void mudarStatusRemessaGerada() {
        LogProcessoSistemaVO log = null;
        try {
            limparMsg();

            if (UteisValidacao.emptyNumber(getManutencaoAjusteGeralTO().getRemessaStatusGerada())) {
                throw new Exception("Informe o código da remessa");
            }
            log = criarLog("mudarStatusRemessaGerada", getEmpresaLogado());
            RemessaVO remessaVO = getFacade().getRemessa().consultarPorChavePrimaria(getManutencaoAjusteGeralTO().getRemessaStatusGerada());

            if (remessaVO.getSituacaoRemessa().equals(SituacaoRemessaEnum.REMESSA_ENVIADA)) {
                remessaVO.setSituacaoRemessa(SituacaoRemessaEnum.GERADA);
                getFacade().getRemessa().alterarSituacao(remessaVO);
            } else {
                throw new Exception("Remessa " + getManutencaoAjusteGeralTO().getRemessaStatusGerada() + " não está com Status Enviada");
            }

            montarSucessoGrowl("Remessa foi alterada com sucesso.");
        } catch (Exception ex) {
            montarErro(ex);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void mudarStatusParcela() throws Exception {

        limparMsg();

        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("mudarStatusParcela", getEmpresaLogado());
            ManutencaoAjusteGeralTO mag = new ManutencaoAjusteGeralTO();
            MovProdutoParcela mv = new MovProdutoParcela(getFacade().getTransacao().getCon());
            MovParcela mvp = new MovParcela(getFacade().getTransacao().getCon());
            List<MovProdutoParcelaVO> listaMovProdutoParcelas = mv.consultarMovProdutoParcelasPorParcelas(mag.getInformadoParcelaMudarStatus(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            ReciboPagamento recibo = new ReciboPagamento(getFacade().getTransacao().getCon());
            MovParcelaVO movparcela = mvp.consultarPorCodigo(mag.getInformadoParcelaMudarStatus(), Uteis.NIVELMONTARDADOS_TODOS);

            if (movparcela.getValorParcela() == 0.0) {
                montarErro("A parcela deve ser maior que 0 e a situação Paga.");
            }

            for (MovProdutoParcelaVO obj : listaMovProdutoParcelas) {
                ReciboPagamentoVO reciboVo = recibo.consultarPorCodigo(obj.getReciboPagamento().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                if (reciboVo == null || reciboVo.getCodigo() == 0) {
                    MovParcelaVO movo = mvp.consultarPorCodigo(mag.getInformadoParcelaMudarStatus(), Uteis.NIVELMONTARDADOS_TODOS);
                    if (movo.getValorParcela() > 0 && movo.getSituacao().equals("PG")) {
                        movo.setSituacao("EA");
                        mvp.alterar(movo);
                        mv.excluir(obj);
                        montarSucessoGrowl("Parcela alterada com sucesso.");
                    } else {
                        montarErro("A parcela deve ser maior que 0 e a situação Paga.");
                    }

                } else {
                    montarErro("A parcela não pode ser aberta, existe pagamento efetuado");
                }
            }


        } catch (Exception ex) {
            montarErro(ex);
        } finally {
            finalizarLogProcesso(log);
        }
    }


    public List<ObjetoGenerico> getTransacoesDuplicadas() {
        if (transacoesDuplicadas == null) {
            transacoesDuplicadas = new ArrayList<>();
        }
        return transacoesDuplicadas;
    }

    public void setTransacoesDuplicadas(List<ObjetoGenerico> transacoesDuplicadas) {
        this.transacoesDuplicadas = transacoesDuplicadas;
    }

    public String getObsTransacoesDuplicadas() {
        if (obsTransacoesDuplicadas == null) {
            obsTransacoesDuplicadas = "";
        }
        return obsTransacoesDuplicadas;
    }

    public void setObsTransacoesDuplicadas(String obsTransacoesDuplicadas) {
        this.obsTransacoesDuplicadas = obsTransacoesDuplicadas;
    }

    public void verificarTransacoesDuplicadas() {
        try {
            setObsTransacoesDuplicadas("");
            List<ObjetoGenerico> transacoesDuplicadas = getFacade().getTransacao().obterTransacoesDuplicadas();
            setTransacoesDuplicadas(transacoesDuplicadas);
            if (transacoesDuplicadas.isEmpty()) {
                setObsTransacoesDuplicadas("Não existem transações duplicadas");
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void estornarTransacoesDuplicadas() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("estornarTransacoesDuplicadas", getEmpresaLogado());
            Connection connection = Conexao.getFromSession();
            String erros = TratarConbrancasporConvenioDuplicadas.corrigirTransacoesDuplicadas(connection, getKey());
            verificarTransacoesDuplicadas();
            if (UteisValidacao.emptyString(erros)) {
                setObsTransacoesDuplicadas("processamento realizado com sucesso!!");
            } else {
                setObsTransacoesDuplicadas("Erros: " + erros);
            }
        } catch (Exception e) {
            montarErro(e);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void estornarRecibo(ReciboPagamentoVO reciboPagamentoVO, UsuarioVO responsavel, List<MovParcelaVO> listaParcelasRecibo) throws Exception {

        EstornoReciboControle estorno = new EstornoReciboControle();
        EstornoReciboVO estornoVO = new EstornoReciboVO();
        estornoVO.setExcluirNFSe(true);

        estornoVO.setResponsavelEstornoRecivo(responsavel);

        List<MovPagamentoVO> listaMovPagamento = getFacade().getMovPagamento().consultarPorCodigoRecibo(reciboPagamentoVO.getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);
        estornoVO.setListaMovPagamento(listaMovPagamento);

        //precisa da lista de parcelas para ele reabrir a parcela após o estorno
        estornoVO.setListaMovParcela(listaParcelasRecibo);

        estornoVO.setEstornarOperadora(false);
        estornoVO.setReciboPagamentoVO(getFacade().getReciboPagamento().consultarPorChavePrimaria(reciboPagamentoVO.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));

        estorno.setEstornoReciboVO(estornoVO);

        getFacade().getReciboPagamento().estornarReciboPagamento(estornoVO, getFacade().getMovPagamento(), getFacade().getMovProdutoParcela(), null, null, null, false);
    }

    public String getObsBoletosStatusErrado() {
        if (obsBoletosStatusErrado == null) {
            obsBoletosStatusErrado = "";
        }
        return obsBoletosStatusErrado;
    }

    public void setObsBoletosStatusErrado(String obsBoletosStatusErrado) {
        this.obsBoletosStatusErrado = obsBoletosStatusErrado;
    }

    public void alterarStatusRemessa() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("alterarStatusRemessa", getEmpresaLogado());
            limparMsg();

            RemessaVO remessaVO = getFacade().getRemessa().consultarPorChavePrimaria(this.getManutencaoAjusteGeralTO().getInformadoRemessaMudarStatus());

            if (remessaVO.getSituacaoRemessa().equals(SituacaoRemessaEnum.REMESSA_ENVIADA) ||
                    remessaVO.getSituacaoRemessa().equals(SituacaoRemessaEnum.GERADA)) {
                remessaVO.setSituacaoRemessa(SituacaoRemessaEnum.ERRO_RETORNO);
                getFacade().getRemessa().alterarSituacao(remessaVO);
            } else {
                throw new Exception("Remessa " + this.getManutencaoAjusteGeralTO().getInformadoRemessaMudarStatus() + " não está como Enviada ou Gerada.");
            }

            montarSucessoGrowl("Remessa " + this.getManutencaoAjusteGeralTO().getInformadoRemessaMudarStatus() + " foi alterada com sucesso!");

        } catch (Exception ex) {
            montarErro(ex);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void excluirRemessa() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("excluirRemessa", getEmpresaLogado());
            limparMsg();

            RemessaVO remessaVO = getFacade().getRemessa().consultarPorChavePrimaria(this.getManutencaoAjusteGeralTO().getInformadoRemessaExcluir());

            if (remessaVO.getSituacaoRemessa().equals(SituacaoRemessaEnum.GERADA) ||
                    remessaVO.getSituacaoRemessa().equals(SituacaoRemessaEnum.NENHUMA) || remessaVO.getSituacaoRemessa().equals(SituacaoRemessaEnum.ERRO_RETORNO)) {
                getFacade().getRemessa().excluirComLog(remessaVO, getUsuarioLogado());
            } else {
                throw new Exception("Remessa " + this.getManutencaoAjusteGeralTO().getInformadoRemessaExcluir() + " não está como \"GERADA\" ou \"NENHUMA\" ou \"ERRO_RETORNO\".");
            }

            montarSucessoGrowl("Remessa " + this.getManutencaoAjusteGeralTO().getInformadoRemessaExcluir() + " foi excluída com sucesso!");

        } catch (Exception ex) {
            montarErro(ex);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void alterarTipoACobrarAutorizacao(ActionEvent evt) {
        try {
            limparMsg();
            this.setManutencaoAjusteGeralTO(new ManutencaoAjusteGeralTO());
            carregarListaSelectItemEmpresa(null);
            carregarListaSelectItemConvenioCobranca(null);
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        }
    }

    private void carregarListaSelectItemEmpresa(Boolean situacao) throws Exception {
        this.setListaSelectItemEmpresa(new ArrayList<>());

        List<EmpresaVO> empresaVOList = getFacade().getEmpresa().consultarTodas(situacao, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        for (EmpresaVO obj : empresaVOList) {
            getListaSelectItemEmpresa().add(new SelectItem(obj.getCodigo(), obj.getNome()));
        }
        Ordenacao.ordenarLista(getListaSelectItemEmpresa(), "label");
        getListaSelectItemEmpresa().add(0, new SelectItem(0, "TODAS"));
    }

    public void limparTipoACobrarAutorizacao() {
        limparMsg();
        manutencaoAjusteGeralTO.setConsultou(false);
        manutencaoAjusteGeralTO.setMsgResultado("");
        manutencaoAjusteGeralTO.setListaAutorizacaoCobrancaCliente(new ArrayList<>());
    }

    public List<SelectItem> getListSelectItemTiposACobrar() {
        List<SelectItem> lista = new ArrayList<>();
        for (TipoObjetosCobrarEnum tipo : TipoObjetosCobrarEnum.values()) {
            if (tipo.equals(TipoObjetosCobrarEnum.TIPOS_PRODUTOS)) {
                continue;
            }
            lista.add(new SelectItem(tipo.getId(), tipo.getDescricao()));
        }
        return lista;
    }

    public void consultarTipoACobrarAutorizacao() {
        manutencaoAjusteGeralTO.setSucesso(false);
        manutencaoAjusteGeralTO.setAviso(false);
        manutencaoAjusteGeralTO.setErro(false);
        manutencaoAjusteGeralTO.setMsgResultado("");
        manutencaoAjusteGeralTO.setConsultou(false);
        manutencaoAjusteGeralTO.setListaAutorizacaoCobrancaCliente(new ArrayList<>());
        try {
            limparMsg();

            if (UteisValidacao.emptyNumber(manutencaoAjusteGeralTO.getTipoObjetosCobrar())) {
                throw new Exception("Selecione o tipo de parcela a cobrar");
            }

            this.getManutencaoAjusteGeralTO().setListaAutorizacaoCobrancaCliente(getFacade().getAutorizacaoCobrancaCliente().consultarProcessoAlterarTipoACobrar(
                    this.getManutencaoAjusteGeralTO().getEmpresaVO().getCodigo(),
                    this.getManutencaoAjusteGeralTO().getConvenioCobrancaVO().getCodigo(),
                    TipoObjetosCobrarEnum.valueOf(manutencaoAjusteGeralTO.getTipoObjetosCobrar()), Uteis.NIVELMONTARDADOS_MINIMOS));

            if (UteisValidacao.emptyList(getManutencaoAjusteGeralTO().getListaAutorizacaoCobrancaCliente())) {
                throw new Exception("Nenhuma autorização encontrada.");
            }

            manutencaoAjusteGeralTO.setConsultou(true);
            manutencaoAjusteGeralTO.setAviso(true);
            manutencaoAjusteGeralTO.setMsgResultado("Total " + getManutencaoAjusteGeralTO().getListaAutorizacaoCobrancaCliente().size() + " autorizações.");
        } catch (Exception ex) {
            montarErro(ex);
            Uteis.logar(ex, ConfiguracaoSistemaControle.class);
            manutencaoAjusteGeralTO.setErro(true);
            manutencaoAjusteGeralTO.setMsgResultado(ex.getMessage());
        }
    }

    public void acaoTipoACobrarAutorizacao() {
        manutencaoAjusteGeralTO.setSucesso(false);
        manutencaoAjusteGeralTO.setAviso(false);
        manutencaoAjusteGeralTO.setErro(false);
        manutencaoAjusteGeralTO.setMsgResultado("");
        LogProcessoSistemaVO log = null;
        try {
            limparMsg();

            if (UteisValidacao.emptyNumber(manutencaoAjusteGeralTO.getTipoObjetosCobrar())) {
                throw new Exception("Selecione o tipo de parcela a cobrar");
            }

            log = criarLog("acaoTipoACobrarAutorizacao", getEmpresaLogado());
            StringBuilder msg = getFacade().getAutorizacaoCobrancaCliente().processoAlterarTipoACobrar(this.getManutencaoAjusteGeralTO().getListaAutorizacaoCobrancaCliente(),
                    TipoObjetosCobrarEnum.valueOf(manutencaoAjusteGeralTO.getTipoObjetosCobrar()), getUsuarioLogado());
            msg.append("<br/>Consulte o log para verificar mais detalhes.");

            manutencaoAjusteGeralTO.setConsultou(false);
            manutencaoAjusteGeralTO.setSucesso(true);
            manutencaoAjusteGeralTO.setMsgResultado(msg.toString());
        } catch (Exception ex) {
            montarErro(ex);
            Uteis.logar(ex, ConfiguracaoSistemaControle.class);
            manutencaoAjusteGeralTO.setErro(true);
            manutencaoAjusteGeralTO.setMsgResultado(ex.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void consultarLogTipoACobrarAutorizacao() {
        try {
            setLogAjusteGeralVOList(new ArrayList<>());
            List<LogAjusteGeralVO> novoLog = getFacade().getLogAjusteGeral().consultarPorAjusteGeral(ProcessoAjusteGeralEnum.ALTERAR_TIPO_A_COBRAR, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            setLogAjusteGeralVOList(novoLog);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void consultarRateioDivergenteContaAPagar() {
        try {
            limparMsg();

            listaRateioComValorDivergenteContaAPagar = new ArrayList<>();
            listaContaAPagarComValorDivergenteRateio = getFacade().getMovConta().consultarMovContaComValoresDivergentesDoMovContaRateio();

            for (MovContaVO movContaVO : listaContaAPagarComValorDivergenteRateio) {

                List<MovContaRateioVO> listaMovContaRateio = getFacade().getMovContaRateio().consultarPorMovConta(movContaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (listaMovContaRateio.size() == 1) {
                    listaRateioComValorDivergenteContaAPagar.addAll(listaMovContaRateio);
                } else {
                    double valorTotalRateio = 0.0;

                    for (MovContaRateioVO movContaRateio : listaMovContaRateio) {
                        valorTotalRateio += movContaRateio.getValor();
                    }
                    listaMovContaRateio.get(0).setValor(valorTotalRateio);
                    listaRateioComValorDivergenteContaAPagar.add(listaMovContaRateio.get(0));
                }
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void ajustarRateioComValoresDivergentesContasAPagar() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("ajustarRateioComValoresDivergentesContasAPagar", getEmpresaLogado());
            limparMsg();

            for (MovContaVO movConta : listaContaAPagarComValorDivergenteRateio) {
                List<MovContaRateioVO> listaMovContaRateio = getFacade().getMovContaRateio().consultarPorMovConta(movConta.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                MovContaRateioVO movContaRateio = listaMovContaRateio.get(0);
                DecimalFormat decimalFormat = new DecimalFormat("0.00");

                if (listaMovContaRateio.size() > 1) {
                    double valorTotalRateio = 0.0;

                    for (MovContaRateioVO movContaRateioVO : listaMovContaRateio) {
                        valorTotalRateio += movContaRateioVO.getValor();
                    }

                    Double novoValorRateio = movConta.getValor() / listaMovContaRateio.size();
                    novoValorRateio = Double.parseDouble(decimalFormat.format(novoValorRateio).replace(",", "."));
                    valorTotalRateio = novoValorRateio * listaMovContaRateio.size();

                    for (MovContaRateioVO movContaRateioVO : listaMovContaRateio) {
                        movContaRateioVO.setValor(novoValorRateio);
                    }

                    if (valorTotalRateio != movConta.getValor()) {
                        double diferencaEntreValores = movConta.getValor() - valorTotalRateio;
                        movContaRateio = listaMovContaRateio.get(0);
                        movContaRateio.setValor(movContaRateio.getValor() + diferencaEntreValores);
                    }

                    for (MovContaRateioVO movContaRateioVO : listaMovContaRateio) {
                        getFacade().getMovContaRateio().alterarValorMovContaRateioPeloCodigo(movContaRateioVO.getCodigo(), movContaRateioVO.getValor());
                    }

                } else {
                    movContaRateio.setValor(movContaRateio.getMovConta().getValor());
                    getFacade().getMovContaRateio().alterarValorMovContaRateioPeloCodigo(movContaRateio.getCodigo(), movContaRateio.getValor());
                }
            }
            listaRateioComValorDivergenteContaAPagar = new ArrayList<>();

            montarSucesso("msg_corrigirValoresRateio");
        } catch (Exception e) {
            montarErro(e);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public List<MovContaRateioVO> getListaRateioComValorDivergenteContaAPagar() {
        return this.listaRateioComValorDivergenteContaAPagar;
    }

    public List<PixVO> getListaPixProcessados() {
        return listaPixProcessados;
    }

    public void setListaPixProcessados(List<PixVO> listaPixProcessados) {
        this.listaPixProcessados = listaPixProcessados;
    }

    public boolean isExibirListaPixProcessados() {
        return (listaPixProcessados != null && listaPixProcessados.size() > 0);
    }

    public boolean isExibirMensagemSemPixAtivos() {
        return (listaPixProcessados != null && listaPixProcessados.size() == 0);
    }

    public void clonarAutorizacaoCobrancaParaDependentes() throws Exception {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("clonarAutorizacaoCobrancaParaDependentes", getEmpresaLogado());
            StringBuilder msgRetorno = new StringBuilder();
            msgRetorno.append("Operação Realizada na(s) matricula(s): ");
            if (this.manutencaoAjusteGeralTO.getMatriculaCliente().isEmpty()) {
                throw new Exception("Nenhum aluno informado!");
            }
            List<ClienteVO> lista = getFacade().getCliente().consultarPorCodigosMatricula(this.manutencaoAjusteGeralTO.getMatriculaCliente(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            ClienteVO clienteAut = getFacade().getCliente().consultarPorMatricula(this.manutencaoAjusteGeralTO.getClienteVO().getMatricula(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            List<AutorizacaoCobrancaClienteVO> autsCobranca = getFacade().getAutorizacaoCobrancaCliente().consultarPorClienteAutoCobAtivaAndTipoAuto(
                    clienteAut.getCodigo(), true, TipoAutorizacaoCobrancaEnum.CARTAOCREDITO, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (autsCobranca.size() == 0) {
                throw new Exception("Não foi encontrada autorização para o aluno: " + clienteAut.getMatricula());
            }
            for (ClienteVO clienteVO : lista) {
                try {
                    getFacade().getAutorizacaoCobrancaCliente().clonarAutorizacaoCobrancaParaOutroCliente(autsCobranca.get(0).getCodigo(), clienteVO.getCodigo());
                    msgRetorno.append(clienteVO.getMatricula() + ". \n");
                } catch (Exception e) {
                    this.manutencaoAjusteGeralTO.setSucesso(false);
                    msgRetorno.append(e.getMessage() + " para o aluno: " + clienteVO.getMatricula() + ". \n");
                    e.printStackTrace();
                }
            }
            this.manutencaoAjusteGeralTO.setMsgResultado(msgRetorno.toString());
        } catch (Exception e) {
            this.manutencaoAjusteGeralTO.setSucesso(false);
            this.manutencaoAjusteGeralTO.setMsgResultado(e.getMessage());
            e.printStackTrace();
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void cancelarNotas() {
        LogProcessoSistemaVO log = null;
        try {
            setMsgAlert("");
            if (this.manutencaoAjusteGeralTO.getCodigosNotas().trim().isEmpty()) {
                throw new ConsistirException("Informe os códigos das notas.");
            } else {
                if (this.manutencaoAjusteGeralTO.getJustificativaCancelamentoNotas().trim().isEmpty()) {
                    throw new ConsistirException("Informe a justificativa para o cancelamento das notas");
                }

                log = criarLog("cancelarNotas", getEmpresaLogado());

                List<NotaFiscalVO> listaNotasCancelar = getFacade().getNotaFiscal().consultarNotasPorCodigos(this.manutencaoAjusteGeralTO.getCodigosNotas(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                if (!UteisValidacao.emptyList(listaNotasCancelar)) {

                    for (NotaFiscalVO obj : listaNotasCancelar) {
                        getFacade().getNotaFiscalOperacao().gerarSolicitacaoOperacao(getKey(), this.manutencaoAjusteGeralTO.getJustificativaCancelamentoNotas(), obj, getUsuarioLogado(), OperacaoNotaFiscalEnum.CANCELAR);
                    }
                    montarSucesso("Solicitações de cancelamento das notas foram enviadas");
                } else {
                    throw new ConsistirException("Nenhuma nota encontrada com os código informados.");
                }

            }
        } catch (Exception e) {
            montarErro(e.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void processarPagamentoPix() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("processarPagamentoPix", getEmpresaLogado());
            this.manutencaoAjusteGeralTO.setMsgResultado("");
            if (UteisValidacao.emptyString(this.manutencaoAjusteGeralTO.getListaCodigosPix())) {
                this.manutencaoAjusteGeralTO.setMsgResultado("Informe o código do pix ou vários códigos separados por vírgula.");
            } else if (this.manutencaoAjusteGeralTO.getDataPagamentoPix() == null) {
                this.manutencaoAjusteGeralTO.setMsgResultado("Informe a data de pagamento que será utilizada para dar baixa no pix");
            } else {
                Integer qtdProcessados = 0;
                Integer qtdErros = 0;
                List<String> listaSucesso = new ArrayList<>();
                List<String> listaErro = new ArrayList<>();

                for (String item : this.manutencaoAjusteGeralTO.getListaCodigosPix().split(",")) {
                    Integer codigoItem;
                    PixVO pixVOClone = new PixVO();
                    try {
                        Connection con;
                        con = Conexao.getFromSession();
                        codigoItem = parseInt(item);
                        PixVO pixVO = getFacade().getPix().consultarPorCodigo(codigoItem, true);

                        //usado para adicionar na lista de erros caso entre no catch
                        pixVOClone = pixVO;

                        if (pixVO != null && pixVO.getStatus().equals("ATIVA")) {
                            ConvenioCobrancaVO convenioCobrancaVO = getFacade().getConvenioCobranca().consultarDadosPix(pixVO.getConveniocobranca().getCodigo());
                            EmpresaVO empresaVO = getFacade().getEmpresa().consultarPorCodigo(pixVO.getEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                            FormaPagamentoVO formaPagamentoVO = getFacade().getPix().obterFormaPagamentoPix(pixVO, convenioCobrancaVO);
                            if (formaPagamentoVO == null || UteisValidacao.emptyNumber(formaPagamentoVO.getCodigo())) {
                                throw new Exception("Não foi encontrado uma forma de pagamento PIX!");
                            }
                            convenioCobrancaVO.setEmpresa(empresaVO);

                            PixPagamentoService pixPagamentoService = new PixPagamentoService(con);
                            pixVO.setDataPagamento(this.manutencaoAjusteGeralTO.getDataPagamentoPix());
                            pixPagamentoService.processarPagamento(pixVO, convenioCobrancaVO, formaPagamentoVO);
                            Pix pix = new Pix(con);
                            qtdProcessados++;
                            listaSucesso.add(pixVO.getCodigo().toString());
                            pix.cancelar(pixVO);
                            pix.alterarStatusAjusteManual(pixVO, PixStatusEnum.CONCLUIDA);
                            try {
                                if (!UteisValidacao.emptyNumber(pixVO.getReciboPagamento()) && getFacade().getZWFacade().getConfiguracaoSistema().realizarEnvioSesiSC()) {
                                    List<Integer> listaRecibo = new ArrayList<>();
                                    listaRecibo.add(pixVO.getReciboPagamento());
                                    getFacade().getZWFacade().startThreadIntegracaoFiesc(pixVO.getEmpresa(), listaRecibo, "incluirPix");
                                }
                            } catch (Exception e){
                            }
                        } else {
                            qtdErros++;
                            listaErro.add(pixVO.getCodigo().toString() + " - Cobrança nula ou não está com status 'AGUARDANDO PAGAMENTO'");
                        }
                    } catch (Exception e) {
                        qtdErros++;
                        listaErro.add(pixVOClone.getCodigo().toString());
                    }
                }
                this.manutencaoAjusteGeralTO.setMsgResultado("Total de pix processado(s) com sucesso: " + qtdProcessados + "<br/> " + "Lista de códigos que deram sucesso: " +
                        (!listaSucesso.isEmpty() ? listaSucesso.toString().replace("[", "(").replace("]", ")") : "0") + "<br/>"
                        + "  Total de pix que deu erro ao processar: " + qtdErros + "<br/> " + "Lista de códigos que deram erro: " +
                        (!listaErro.isEmpty() ? listaErro.toString().replace("[", "(").replace("]", ")") : "0") + "<br/>");
            }
        } catch (Exception e) {
            montarErro(e);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void processarVerificarPendenciasTransacaoOnline() {
        RemessaService remessaService = null;
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("processarVerificarPendenciasTransacaoOnline", getEmpresaLogado());
            limparMsg();
            remessaService = new RemessaService(getFacade().getContrato().getCon());
            remessaService.verificarPendenciasTransacaoOnlineGeral();
            montarSucesso("Processado");
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        } finally {
            finalizarLogProcesso(log);
            remessaService = null;
        }
    }

    public void processarProcessoCorrecaoTransacaoDuplicada() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("processarProcessoCorrecaoTransacaoDuplicada", getEmpresaLogado());
            limparMsg();
            ProcessoCorrecaoTransacaoDuplicada.corrigirTransacoes(getFacade().getZWFacade().getCon());
            montarSucesso("Processado");
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
            montarErro(ex);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void ajustarContratoSemConsultor() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("ajustarContratoSemConsultor", getEmpresaLogado());
            limparMsg();
            ProcessoAjustarContratosSemConsultor.corrigirContratos(getFacade().getZWFacade().getCon());
            montarSucessoGrowl("Contratos ajustados");
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
            montarErro(ex);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void ajustarContratosAssinaturaDigitalDuplicada(){
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("ajustarContratosAssinaturaDigitalDuplicada", getEmpresaLogado());
            limparMsg();
            ProcessoAjustarContratosAssinaturaDigitalDuplicados.corrigirContratosAssinaturaDigitalDuplicados(getFacade().getZWFacade().getCon());
            montarSucessoGrowl("Contratos ajustados");
        } catch (Exception ex){
            System.out.println(ex.getMessage());
            montarErro(ex);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public boolean isIntegranteRedeEmpresa() {
        return integranteRedeEmpresa;
    }

    public void setIntegranteRedeEmpresa(boolean integranteRedeEmpresa) {
        this.integranteRedeEmpresa = integranteRedeEmpresa;
    }

    public void processarProcessoCorrecaoCartaoVerificado() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("processarProcessoCorrecaoCartaoVerificado", getEmpresaLogado());
            limparMsg();

            Integer processado = 0;
            if (getManutencaoAjusteGeralTO().getListaCodigosGenericos().equalsIgnoreCase("TODOS")) {
                processado = ProcessoCorrecaoCartaoVerificado.corrigir(null, getFacade().getZWFacade().getCon());
            } else {
                for (String matricula : getManutencaoAjusteGeralTO().getListaCodigosGenericos().split(",")) {
                    try {
                        if (!UteisValidacao.emptyNumber(parseInt(matricula))) {
                            processado += ProcessoCorrecaoCartaoVerificado.corrigir(parseInt(matricula), getFacade().getZWFacade().getCon());
                        }
                    } catch (Exception ignored) {
                    }
                }
            }

            if (UteisValidacao.emptyNumber(processado)) {
                montarSucessoGrowl("Nenhum item foi ajustado");
            } else {
                montarSucessoGrowl("Processado " + processado + " itens");
            }
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
            montarErro(ex);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void processarCancelamentoBoletosPjBank() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("processarCancelamentoBoletosPjBank", getEmpresaLogado());
            limparMsg();
            setOnComplete("");

            if (UteisValidacao.emptyNumber(getManutencaoAjusteGeralTO().getEmpresaVOBoletoCancelar().getCodigo())) {
                throw new Exception("Nenhuma empresa foi informada");
            }

            if (UteisValidacao.emptyNumber(getManutencaoAjusteGeralTO().getConvenioCobrancaVOBoletoCancelar().getCodigo())) {
                throw new Exception("Nenhum convênio de cobrança foi informado");
            }

            if (UteisValidacao.emptyString(getManutencaoAjusteGeralTO().getListaCodigosGenericosBoletoCancelar())) {
                throw new Exception("Nenhum id externo foi informado");
            }

            File file;
            if (getManutencaoAjusteGeralTO().getListaCodigosGenericosBoletoCancelar().equalsIgnoreCase("TODOS")) {
                file = ProcessosPJBank.cancelarBoletos(getManutencaoAjusteGeralTO().getEmpresaVOBoletoCancelar().getCodigo(),
                        getManutencaoAjusteGeralTO().getConvenioCobrancaVOBoletoCancelar().getCodigo(),
                        null, getUsuarioLogado(), getFacade().getZWFacade().getCon());
            } else {
                String codigosBoleto = "";
                for (String codBoleto : getManutencaoAjusteGeralTO().getListaCodigosGenericosBoletoCancelar().split(",")) {
                    try {
                        if (!UteisValidacao.emptyNumber(parseInt(codBoleto.trim()))) {
                            codigosBoleto += ("," + "'" + codBoleto + "'");
                        }
                    } catch (Exception ignored) {
                    }
                }
                if (UteisValidacao.emptyString(codigosBoleto)) {
                    throw new Exception("Nenhum código informado");
                }
                file = ProcessosPJBank.cancelarBoletos(getManutencaoAjusteGeralTO().getEmpresaVOBoletoCancelar().getCodigo(),
                        getManutencaoAjusteGeralTO().getConvenioCobrancaVOBoletoCancelar().getCodigo(),
                        codigosBoleto.replaceFirst(",", ""), getUsuarioLogado(), getFacade().getZWFacade().getCon());
            }
            montarSucessoGrowl("Verifique o arquivo excel com o resultado do cancelamento");
            setOnComplete(getMensagemNotificar() + ";location.href='../DownloadSV?mimeType=application/vnd.ms-excel&relatorio=" + file.getName() + "'");
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
            setOnComplete(getMensagemNotificar());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void processarCancelamentoBoletosBBOnline() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("processarCancelamentoBoletosBBOnline", getEmpresaLogado());
            limparMsg();
            setOnComplete("");

            if (UteisValidacao.emptyNumber(getManutencaoAjusteGeralTO().getEmpresaVOBoletoCancelar().getCodigo())) {
                throw new Exception("Nenhuma empresa foi informada");
            }

            if (UteisValidacao.emptyNumber(getManutencaoAjusteGeralTO().getConvenioCobrancaVOBoletoCancelar().getCodigo())) {
                throw new Exception("Nenhum convênio de cobrança foi informado");
            }

            if (UteisValidacao.emptyString(getManutencaoAjusteGeralTO().getListaCodigosGenericosBoletoCancelar())) {
                throw new Exception("Nenhum id externo foi informado");
            }

            File file;
            if (getManutencaoAjusteGeralTO().getListaCodigosGenericosBoletoCancelar().equalsIgnoreCase("TODOS")) {
                file = ProcessosBoletoBBOnline.cancelarBoletos(getManutencaoAjusteGeralTO().getEmpresaVOBoletoCancelar().getCodigo(),
                        getManutencaoAjusteGeralTO().getConvenioCobrancaVOBoletoCancelar().getCodigo(),
                        null, getUsuarioLogado(), getFacade().getZWFacade().getCon());
            } else {
                String codigosBoleto = "";
                for (String codBoleto : getManutencaoAjusteGeralTO().getListaCodigosGenericosBoletoCancelar().split(",")) {
                    try {
                        if (!UteisValidacao.emptyNumber(parseInt(codBoleto.trim()))) {
                            codigosBoleto += ("," + "'" + codBoleto + "'");
                        }
                    } catch (Exception ignored) {
                    }
                }
                if (UteisValidacao.emptyString(codigosBoleto)) {
                    throw new Exception("Nenhum código informado");
                }
                file = ProcessosBoletoBBOnline.cancelarBoletos(getManutencaoAjusteGeralTO().getEmpresaVOBoletoCancelar().getCodigo(),
                        getManutencaoAjusteGeralTO().getConvenioCobrancaVOBoletoCancelar().getCodigo(),
                        codigosBoleto.replaceFirst(",", ""), getUsuarioLogado(), getFacade().getZWFacade().getCon());
            }
            montarSucessoGrowl("Verifique o arquivo excel com o resultado do cancelamento");
            setOnComplete(getMensagemNotificar() + ";location.href='../DownloadSV?mimeType=application/vnd.ms-excel&relatorio=" + file.getName() + "'");
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
            setOnComplete(getMensagemNotificar());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public String getOnComplete() {
        if (onComplete == null) {
            onComplete = "";
        }
        return onComplete;
    }

    public void setOnComplete(String onComplete) {
        this.onComplete = onComplete;
    }

    public List<ConvenioCobrancaVO> getListaConveniosCobranca() {
        if (listaConveniosCobranca == null) {
            listaConveniosCobranca = new ArrayList<>();
        }
        return listaConveniosCobranca;
    }

    public void setListaConveniosCobranca(List<ConvenioCobrancaVO> listaConveniosCobranca) {
        this.listaConveniosCobranca = listaConveniosCobranca;
    }

    public void montarListaConvenioCobrancaPJBank() {
        try {
            List<SelectItem> lista = new ArrayList<>();
            setListaSelectItemConvenioCobrancaPjBank(lista); //inicializar a lista de itens toda vez que chamar o método
            List<ConvenioCobrancaVO> convenios = getFacade().getConvenioCobranca().consultarPjBankPorEmpresa(getManutencaoAjusteGeralTO().getEmpresaVOBoletoCancelar().getCodigo(),
                    SituacaoConvenioCobranca.ATIVO, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (ConvenioCobrancaVO conv : convenios) {
                if (conv.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_PJBANK)) {
                    lista.add(new SelectItem(conv.getCodigo(), "Cód: " + conv.getCodigo() + " - " + conv.getDescricao()));
                }
            }
            Ordenacao.ordenarLista(lista, "label");
            setListaSelectItemConvenioCobrancaPjBank(lista);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void montarListaConvenioCobrancaBBOnline() {
        try {
            List<SelectItem> lista = new ArrayList<>();
            setListaSelectItemConvenioCobrancaBBOnline(lista); //inicializar a lista de itens toda vez que chamar o método
            List<ConvenioCobrancaVO> convenios = getFacade().getConvenioCobranca().consultarBBPorEmpresa(getManutencaoAjusteGeralTO().getEmpresaVOBoletoCancelar().getCodigo(),
                    SituacaoConvenioCobranca.ATIVO, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (ConvenioCobrancaVO conv : convenios) {
                if (conv.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_BANCO_BRASIL_ONLINE)) {
                    lista.add(new SelectItem(conv.getCodigo(), "Cód: " + conv.getCodigo() + " - " + conv.getDescricao()));
                }
            }
            Ordenacao.ordenarLista(lista, "label");
            setListaSelectItemConvenioCobrancaBBOnline(lista);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public List<EmpresaVO> getListaEmpresasCadastradas() {
        if (listaEmpresasCadastradas == null) {
            carregarEmpresas();
            listaEmpresasCadastradas = new ArrayList<>();
        }
        return listaEmpresasCadastradas;
    }

    public void setListaEmpresasCadastradas(List<EmpresaVO> listaEmpresasCadastradas) {
        this.listaEmpresasCadastradas = listaEmpresasCadastradas;
    }

    public List<SelectItem> getListaSelectItemEmpresasCadastradas() {
        List<SelectItem> lista = new ArrayList<>();
        for (EmpresaVO obj : getListaEmpresasCadastradas()) {
            lista.add(new SelectItem(obj.getCodigo(), "Cód: " + obj.getCodigo() + " - " + obj.getNome()));
        }
        Ordenacao.ordenarLista(lista, "label");
        lista.add(0, new SelectItem(0, ""));
        return lista;
    }

    public List<SelectItem> getListaSelectItemConvenioCobrancaPjBank() {
        if (!UteisValidacao.emptyList(listaSelectItemConvenioCobrancaPjBank)) {
            return listaSelectItemConvenioCobrancaPjBank;
        } else {
            return new ArrayList<>();
        }
    }

    public void setListaSelectItemConvenioCobrancaPjBank(List<SelectItem> listaSelectItemConvenioCobrancaPjBank) {
        this.listaSelectItemConvenioCobrancaPjBank = listaSelectItemConvenioCobrancaPjBank;
    }

    public List<SelectItem> getListaSelectItemConvenioCobrancaBBOnline() {
        if (!UteisValidacao.emptyList(listaSelectItemConvenioCobrancaBBOnline)) {
            return listaSelectItemConvenioCobrancaBBOnline;
        } else {
            return new ArrayList<>();
        }
    }

    public void setListaSelectItemConvenioCobrancaBBOnline(List<SelectItem> listaSelectItemConvenioCobrancaBBOnline) {
        this.listaSelectItemConvenioCobrancaBBOnline = listaSelectItemConvenioCobrancaBBOnline;
    }

    public boolean isAplicacaoLocal() {
        try {
            String url = request().getRequestURL().toString();
            if (url.contains("localhost") || url.contains("swarm")) {
                return true;
            }

            return false;

        } catch (Exception e) {
            return false;
        }
    }

    public void processarMensagemCartaoVencido() {
        LogProcessoSistemaVO log = null;
        try {
            limparMsg();
            ClienteMensagem clienteMensagemDAO = null;
            Usuario usuarioDAO = null;

            try (Connection con = Conexao.getInstance().obterNovaConexaoBaseadaOutra(Conexao.getFromSession())) {
                log = criarLog("processarMensagemCartaoVencido", getEmpresaLogado());
                try {
                    con.setAutoCommit(false);
                    clienteMensagemDAO = new ClienteMensagem(con);
                    usuarioDAO = new Usuario(con);
                    clienteMensagemDAO.excluirClienteMensagemClienteTipoMensagem(null, TiposMensagensEnum.CARTAO_VENCIDO);
                    clienteMensagemDAO.processarTodosClientesComCartaoCreditoVencido(usuarioDAO.getUsuarioRecorrencia(), null);
                    con.commit();
                } catch (Exception ex) {
                    ex.printStackTrace();
                    con.rollback();
                    throw ex;
                } finally {
                    con.setAutoCommit(true);
                    clienteMensagemDAO = null;
                    usuarioDAO = null;
                }
            }
            getManutencaoAjusteGeralTO().setSucesso(true);
            getManutencaoAjusteGeralTO().setMsgResultado("Clientes processados");
            montarSucessoGrowl("Clientes processados");
        } catch (Exception ex) {
            getManutencaoAjusteGeralTO().setSucesso(false);
            getManutencaoAjusteGeralTO().setMsgResultado(ex.getMessage());
            ex.printStackTrace();
            montarErro(ex);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void processarDatasBoleto() {
        LogProcessoSistemaVO log = null;
        try {
            limparMsg();

            if (UteisValidacao.emptyString(getManutencaoAjusteGeralTO().getListaCodigosGenericos())) {
                throw new Exception("Nenhum recibo informado");
            }

            log = criarLog("processarDatasBoleto", getEmpresaLogado());
            String recibos = "";
            if (!getManutencaoAjusteGeralTO().getListaCodigosGenericos().equalsIgnoreCase("TODOS")) {
                recibos = getManutencaoAjusteGeralTO().getListaCodigosGenericos();
            }

            JSONObject json;
            try (Connection con = Conexao.getInstance().obterNovaConexaoBaseadaOutra(Conexao.getFromSession())) {
                json = ProcessoCorrecaoDatasBoleto.corrigir(con, recibos);
            }

            if (!json.getBoolean("sucesso")) {
                throw new Exception(json.getString("msg"));
            }

            StringBuilder msg = new StringBuilder();
            msg.append("Total: ").append(json.getInt("total")).append(" ");
            msg.append("Sucesso: ").append(json.getInt("ajustado")).append(" ");
            msg.append("Falha: ").append(json.getInt("erro")).append(" ");

            getManutencaoAjusteGeralTO().setSucesso(true);
            getManutencaoAjusteGeralTO().setMsgResultado(msg.toString());
            montarSucessoGrowl(msg.toString());
        } catch (Exception ex) {
            getManutencaoAjusteGeralTO().setSucesso(false);
            getManutencaoAjusteGeralTO().setMsgResultado(ex.getMessage());
            ex.printStackTrace();
            montarErro(ex);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public boolean isApresentarProcessosPactoPay() {
        try {
            return getAmbienteDeTeste() ||
                    (getUsuarioLogado().getUsuarioPactoSolucoes() &&
                            (getUsuarioLogado().getUserOamd().equalsIgnoreCase("Luiz Felipe") ||
                                    getUsuarioLogado().getUserOamd().equalsIgnoreCase("marielle ferraz") ||
                                    getUsuarioLogado().getUserOamd().equalsIgnoreCase("Hebert Francato") ||
                                    getUsuarioLogado().getUserOamd().equalsIgnoreCase("eduardo") ||
                                    getUsuarioLogado().getUserOamd().equalsIgnoreCase("estulano") ||
                                    getUsuarioLogado().getUserOamd().equalsIgnoreCase("Maurin Noleto")));
        } catch (Exception ex) {
            ex.printStackTrace();
            return false;
        }
    }

    public boolean getApresentarProcessosConciliacao() {
        try {
            return getAmbienteDeTeste() || getUsuarioLogado().getUsuarioPactoSolucoes();
        } catch (Exception ex) {
            ex.printStackTrace();
            return false;
        }
    }

    public void processarCobrancaPendente() {
        PactoPayConfig pactoPayConfigDAO;
        try {
            if (getEmpresaLogado() == null || UteisValidacao.emptyNumber(getEmpresaLogado().getCodigo())) {
                throw new Exception("Nenhuma empresa logada");
            }
            pactoPayConfigDAO = new PactoPayConfig(Conexao.getFromSession());

            Integer codigoPessoa = obterPessoaReguaCobranca();
            pactoPayConfigDAO.processarCobrancaPendente(getEmpresaLogado().getCodigo(), true, codigoPessoa, !this.getManutencaoAjusteGeralTO().isEnviarUsandoJenkinsRegua());
            montarSucessoGrowl("Processo finalizado");
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        } finally {
            pactoPayConfigDAO = null;
        }
    }

    public void processarEnvioCobrancaAntecipada() {
        PactoPayConfig pactoPayConfigDAO;
        try {
            if (getEmpresaLogado() == null || UteisValidacao.emptyNumber(getEmpresaLogado().getCodigo())) {
                throw new Exception("Nenhuma empresa logada");
            }
            pactoPayConfigDAO = new PactoPayConfig(Conexao.getFromSession());

            Integer codigoPessoa = obterPessoaReguaCobranca();
            pactoPayConfigDAO.processarCobrancaAntecipada(getEmpresaLogado().getCodigo(), true, codigoPessoa, !this.getManutencaoAjusteGeralTO().isEnviarUsandoJenkinsRegua());
            montarSucessoGrowl("Processo finalizado");
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        } finally {
            pactoPayConfigDAO = null;
        }
    }

    private Integer obterPessoaReguaCobranca() throws Exception {
        if (UteisValidacao.emptyString(getManutencaoAjusteGeralTO().getMatriculaRegua())) {
            throw new Exception("Nenhuma matricula informada");
        }
        if (getManutencaoAjusteGeralTO().getMatriculaRegua().equalsIgnoreCase("geral")) {
            //caso informado "geral" então processar tudo
            return null;
        }
        if (!UteisValidacao.emptyString(getManutencaoAjusteGeralTO().getMatriculaRegua())) {
            ClienteVO clienteVO = getFacade().getCliente().consultarPorMatricula(getManutencaoAjusteGeralTO().getMatriculaRegua(), false, Uteis.NIVELMONTARDADOS_AUTORIZACAO_COBRANCA);
            if (clienteVO == null || UteisValidacao.emptyNumber(clienteVO.getPessoa().getCodigo())) {
                throw new Exception("Cliente não encontrado com a matricula: " + getManutencaoAjusteGeralTO().getMatriculaRegua());
            }
            return clienteVO.getPessoa().getCodigo();
        }
        return null;
    }

    public void processarComunicacaoCartao() {
        PactoPayConfig pactoPayConfigDAO;
        try {
            if (getEmpresaLogado() == null || UteisValidacao.emptyNumber(getEmpresaLogado().getCodigo())) {
                throw new Exception("Nenhuma empresa logada");
            }
            pactoPayConfigDAO = new PactoPayConfig(Conexao.getFromSession());

            Integer codigoPessoa = obterPessoaReguaCobranca();
            pactoPayConfigDAO.processarCartao(getEmpresaLogado().getCodigo(), true, codigoPessoa, !this.getManutencaoAjusteGeralTO().isEnviarUsandoJenkinsRegua());
            montarSucessoGrowl("Processo finalizado");
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        } finally {
            pactoPayConfigDAO = null;
        }
    }

    public void processarResultadoCobranca() {
        PactoPayConfig pactoPayConfigDAO;
        try {
            if (getEmpresaLogado() == null || UteisValidacao.emptyNumber(getEmpresaLogado().getCodigo())) {
                throw new Exception("Nenhuma empresa logada");
            }
            if (UteisValidacao.emptyString(getManutencaoAjusteGeralTO().getTransacoesRegua())) {
                throw new Exception("Nenhuma transação informada");
            }
            pactoPayConfigDAO = new PactoPayConfig(Conexao.getFromSession());

            List<TransacaoVO> transacoesLista = new ArrayList<>();
            for (String transacao : getManutencaoAjusteGeralTO().getTransacoesRegua().split(",")) {
                TransacaoVO transacaoVO = getFacade().getTransacao().consultarPorChavePrimaria(parseInt(transacao), Uteis.NIVELMONTARDADOS_BASICO_TRANSACAO);
                transacoesLista.add(transacaoVO);
            }

            for (TransacaoVO transacaoVO : transacoesLista) {
                pactoPayConfigDAO.processarResultadoCobranca(transacaoVO, true, transacaoVO.getPessoaPagador().getCodigo(), !this.getManutencaoAjusteGeralTO().isEnviarUsandoJenkinsRegua());
            }
            montarSucessoGrowl("Processo finalizado");
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        } finally {
            pactoPayConfigDAO = null;
        }
    }

    public List<SelectItem> getListSelectItemModeloEnvio() {
        List<SelectItem> lista = new ArrayList<>();
        lista.add(new SelectItem("todas", "TODAS"));
        lista.add(new SelectItem("aprovada", "APROVADA"));
        lista.add(new SelectItem("negada", "NEGADA"));
        lista.add(new SelectItem("cancelada", "CANCELADA"));
        return lista;
    }

    public void processarEnvioExemploResultadoCobranca() {
        PactoPayConfig pactoPayConfigDAO;
        try {
            if (getEmpresaLogado() == null || UteisValidacao.emptyNumber(getEmpresaLogado().getCodigo())) {
                throw new Exception("Nenhuma empresa logada");
            }
            if (UteisValidacao.emptyString(getManutencaoAjusteGeralTO().getCelularRegua()) &&
                    UteisValidacao.emptyString(getManutencaoAjusteGeralTO().getEmailRegua())) {
                throw new Exception("Informe um celular ou email");
            }
            pactoPayConfigDAO = new PactoPayConfig(Conexao.getFromSession());
            pactoPayConfigDAO.realizarEnvioExemploResultadoCobranca(getEmpresaLogado().getCodigo(),
                    getManutencaoAjusteGeralTO().getCelularRegua(),
                    getManutencaoAjusteGeralTO().getEmailRegua(),
                    getManutencaoAjusteGeralTO().getModeloExemploRegua(), "TODOS");
            montarSucessoGrowl("Processo finalizado");
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        } finally {
            pactoPayConfigDAO = null;
        }
    }

    public boolean isMenuPactoPay() {
        return menuPactoPay;
    }

    public void setMenuPactoPay(boolean menuPactoPay) {
        this.menuPactoPay = menuPactoPay;
    }

    public void processarPagamentosPJBank() {
        RemessaService remessaService = null;
        LogProcessoSistemaVO log = null;
        try {
            limparMsg();
            setOnComplete("");

            if (UteisValidacao.emptyNumber(this.getManutencaoAjusteGeralTO().getEmpresaVOPagamentoPjBank().getCodigo())) {
                throw new Exception("Selecione a empresa");
            }

            if (UteisValidacao.emptyNumber(this.getManutencaoAjusteGeralTO().getDiasProcessarPagamentoPJBank())) {
                throw new Exception("Informe o número de dias");
            }

            log = criarLog("processarPagamentosPJBank", getEmpresaLogado());
            EmpresaVO empresaVO = getFacade().getEmpresa().consultarPorChavePrimaria(this.getManutencaoAjusteGeralTO().getEmpresaVOPagamentoPjBank().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

            remessaService = new RemessaService(Conexao.getFromSession());
            remessaService.processarPagamentosPJBank(Calendario.hoje(), empresaVO, this.getManutencaoAjusteGeralTO().getDiasProcessarPagamentoPJBank());
            montarSucessoGrowl("Processado");
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        } finally {
            remessaService = null;
            setOnComplete(getMensagemNotificar());
            finalizarLogProcesso(log);
        }
    }

    public void processarPagamentosBoletoItau() {
        RemessaService remessaService = null;
        LogProcessoSistemaVO log = null;
        try {
            limparMsg();
            setOnComplete("");

            if (UteisValidacao.emptyNumber(this.getManutencaoAjusteGeralTO().getEmpresaVOPagamentoBancoItau().getCodigo())) {
                throw new Exception("Selecione a empresa");
            }

            if (UteisValidacao.emptyNumber(this.getManutencaoAjusteGeralTO().getDiasProcessarPagamentoBoletoItau())) {
                throw new Exception("Informe o número de dias");
            }

            log = criarLog("processarPagamentosBoletoItau", getEmpresaLogado());
            EmpresaVO empresaVO = getFacade().getEmpresa().consultarPorChavePrimaria(this.getManutencaoAjusteGeralTO().getEmpresaVOPagamentoBancoItau().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

            remessaService = new RemessaService(Conexao.getFromSession());
            remessaService.processarPagamentosBoletoItau(empresaVO, this.getManutencaoAjusteGeralTO().getDiasProcessarPagamentoBoletoItau());
            montarSucessoGrowl("Processado");
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        } finally {
            remessaService = null;
            setOnComplete(getMensagemNotificar());
            finalizarLogProcesso(log);
        }
    }

    public void initExcluir() {

        final AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                setProcessandoOperacao(true);
                excluirCliente();
                setProcessandoOperacao(false);
                if (getMsgAlert().contains("mdlMensagemGenerica")){
                    setExecutarAoCompletar("Richfaces.hideModalPanel('panelAutorizacaoFuncionalidade');Richfaces.showModalPanel('modalConfirmarExclusaoClienteComRemessa');");
                } else {
                    setExecutarAoCompletar("Richfaces.hideModalPanel('panelAutorizacaoFuncionalidade');" + getMensagemNotificar(true));
                }
                auto.setRenderComponents("pgExcluirClientes");
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                montarErro(e);
            }

            @Override
            public void onFecharModalAutorizacao() {
                auto.setRenderComponents("pgExcluirClientes");
            }
        };
        limparMsg();
        setMsgAlert("");
        auto.autorizar("Excluir cliente da base de dados", "ExcluirCliente",
                "9.69 - Permitir excluir cliente da base de dados.",
                "mensagembuilder", false, listener);
    }

    public void limparMensagem() {
        setMsgAlert("");
    }

    public boolean isExibirReplicarPlanoRedeEmpresa() {
        return exibirReplicarPlanoRedeEmpresa;
    }

    public void setExibirReplicarPlanoRedeEmpresa(boolean exibirReplicarPlanoRedeEmpresa) {
        this.exibirReplicarPlanoRedeEmpresa = exibirReplicarPlanoRedeEmpresa;
    }

    public List<BoletoVO> getListaBoletos() {
        return listaBoletos;
    }

    public String getListaBoletosSizeApresentar() {
        return "Encontrei " + getListaBoletos().size() + " boletos estornados no sistema. Clique no botão 'Corrigir Boletos' para prosseguir. " +
                "O processo consulta todos os boletos que estão ESTORNADOS no sistema, e consulta 1 a um na API da pjbank para saber a situação dele. Portanto este processo pode demorar. NÃO FECHE ESTA JANELA.";
    }

    public void setListaBoletos(List<BoletoVO> listaBoletos) {
        this.listaBoletos = listaBoletos;
    }

    public boolean isExibirBtnCorrigir() {
        return exibirBtnCorrigir;
    }

    public void setExibirBtnCorrigir(boolean exibirBtnCorrigir) {
        this.exibirBtnCorrigir = exibirBtnCorrigir;
    }

    public boolean isManutencaoAjusteCliente() {
        return manutencaoAjusteCliente;
    }

    public void setManutencaoAjusteCliente(boolean manutencaoAjusteCliente) {
        this.manutencaoAjusteCliente = manutencaoAjusteCliente;
    }

    public boolean isManutencaoExcluirSenhaAcessoCatraca() {
        return manutencaoExcluirSenhaAcessoCatraca;
    }

    public void setManutencaoExcluirSenhaAcessoCatraca(boolean manutencaoExcluirSenhaAcessoCatraca) {
        this.manutencaoExcluirSenhaAcessoCatraca = manutencaoExcluirSenhaAcessoCatraca;
    }

    public boolean isExcluirSenhaAcessoCatracaColaboradores() {
        return excluirSenhaAcessoCatracaColaboradores;
    }

    public void setExcluirSenhaAcessoCatracaColaboradores(boolean excluirSenhaAcessoCatracaColaboradores) {
        this.excluirSenhaAcessoCatracaColaboradores = excluirSenhaAcessoCatracaColaboradores;
    }

    public boolean isExcluirSenhaAcessoCatracaAlunos() {
        return excluirSenhaAcessoCatracaAlunos;
    }

    public void setExcluirSenhaAcessoCatracaAlunos(boolean excluirSenhaAcessoCatracaAlunos) {
        this.excluirSenhaAcessoCatracaAlunos = excluirSenhaAcessoCatracaAlunos;
    }

    public String getInfoCidadeMescladaMantida() {
        if (infoCidadeMescladaMantida == null) {
            return "-";
        }
        return infoCidadeMescladaMantida;
    }

    public void setInfoCidadeMescladaMantida(String infoCidadeMescladaMantida) {
        this.infoCidadeMescladaMantida = infoCidadeMescladaMantida;
    }

    public String getInfoCidadeMescladaExcluida() {
        if (infoCidadeMescladaExcluida == null) {
            return "-";
        }
        return infoCidadeMescladaExcluida;
    }

    public void setInfoCidadeMescladaExcluida(String infoCidadeMescladaExcluida) {
        this.infoCidadeMescladaExcluida = infoCidadeMescladaExcluida;
    }

    public Date getDataProcessoIntgF360() {
        return dataProcessoIntgF360;
    }

    public void setDataProcessoIntgF360(Date dataProcessoIntgF360) {
        this.dataProcessoIntgF360 = dataProcessoIntgF360;
    }

    public boolean isManutencaoFasecrm() {
        return manutencaoFasecrm;
    }

    public void setManutencaoFasecrm(boolean manutencaoFasecrm) {
        this.manutencaoFasecrm = manutencaoFasecrm;
    }

    public List<SelectItem> getFases() {
        List<FasesCRMEnum> list = new ArrayList<>(EnumSet.allOf(FasesCRMEnum.class));
        fases.add(new SelectItem(0, "Selecione"));
        for (FasesCRMEnum fase : list) {
            if( !UteisValidacao.emptyString( fase.getDescricao()))  fases.add(new SelectItem(fase.getCodigo(), fase.getDescricao()));
        }
        return fases;
    }

    public void setFases(List<SelectItem> fases) {
        this.fases = fases;
    }

    public int getFasecrm() {
        return fasecrm;
    }

    public void setFasecrm(int fasecrm) {
        this.fasecrm = fasecrm;
    }

    public boolean getMenuConciliacao() {
        return menuConciliacao;
    }

    public void setMenuConciliacao(boolean menuConciliacao) {
        this.menuConciliacao = menuConciliacao;
    }

    public String getMsgRetornoReprocessarIntegracaoF360() {
        return msgRetornoReprocessarIntegracaoF360;
    }

    public void setMsgRetornoReprocessarIntegracaoF360(String msgRetornoReprocessarIntegracaoF360) {
        this.msgRetornoReprocessarIntegracaoF360 = msgRetornoReprocessarIntegracaoF360;
    }

    public Integer getHorarioContratoAdicionarModalidade() {
        if (horarioContratoAdicionarModalidade == null) {
            horarioContratoAdicionarModalidade = 0;
        }
        return horarioContratoAdicionarModalidade;
    }

    public void setHorarioContratoAdicionarModalidade(Integer horarioContratoAdicionarModalidade) {
        this.horarioContratoAdicionarModalidade = horarioContratoAdicionarModalidade;
    }

    public String getHorarioContratoAdicionarModalidadeString() {
        if (UteisValidacao.emptyNumber(horarioContratoAdicionarModalidade)) {
            return "";
        }
        return getListaSelectItemHorariosAdicionarModalidadeAoPlano().get(horarioContratoAdicionarModalidade).getLabel();
    }


    public void alterarParcelasRecibo() {
        LogProcessoSistemaVO log = null;
        try {
            limparMsg();

            if (UteisValidacao.emptyNumber(this.getManutencaoAjusteGeralTO().getAlterarParcelasRecibo())) {
                throw new Exception("Recibo não informado");
            }

            if (UteisValidacao.emptyString(this.getManutencaoAjusteGeralTO().getAlterarParcelasParcelas())) {
                throw new Exception("Parcelas não informado");
            }
            List<Integer> listaParcelas = new ArrayList<>();
            for (String par : this.getManutencaoAjusteGeralTO().getAlterarParcelasParcelas().split(",")) {
                if (!UteisValidacao.emptyString(par)) {
                    listaParcelas.add(parseInt(par));
                }
            }
            if (UteisValidacao.emptyList(listaParcelas)) {
                throw new Exception("Parcelas não informado");
            }
            log = criarLog("alterarParcelasRecibo", getEmpresaLogado());
            ProcessoAlterarParcelasRecibo.processar(this.getManutencaoAjusteGeralTO().getAlterarParcelasRecibo(),
                    listaParcelas, getUsuarioLogado(), false, Conexao.getFromSession());
            montarSucessoGrowl("Recibo e parcela(s) ajustada(s)!");
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    // CONFIG - ALTERAR DATA FINAL DE UM CONTRATO CANCELADO
    public void alterarDataFinalContratoCancelado() {

        String codigoContrato = this.getManutencaoAjusteGeralTO().getCodigoContratoCancelado();

        Date dataFinalContratoCancelado = this.getManutencaoAjusteGeralTO().getNovaDataFinalContratoCancelado();

        LogProcessoSistemaVO log = null;
        try {
            if (UteisValidacao.emptyString(codigoContrato)) {
                throw new Exception("Por favor, informe o código do contrato.");
            }
            if (dataFinalContratoCancelado == null) {
                throw new Exception("Por favor, informe a data para qual deseja ajustar o fim do contrato.");
            }
            // CONFIRMAR ESSA VALIDAÇAO COM LUCAS
//            if(UteisValidacao.dataMenorDataAtualSemHora(dataFinalContratoCancelado)) {
//                throw new Exception("A data não pode ser anterior ao dia de hoje");
//            }
            log = criarLog("processarNotas", getEmpresaLogado());

            String resultado = ProcessoAlterarDataFinalContratoCancelado.processar(Conexao.getFromSession(), codigoContrato, dataFinalContratoCancelado);

            montarSucessoGrowl(resultado);

            ProcessoAlterarDataFinalContratoCancelado.atualizarSintetico(Conexao.getFromSession(), codigoContrato);

        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
            Uteis.logar(String.valueOf(ex));
        } finally {
            finalizarLogProcesso(log);
        }

    }

    public boolean isNovaTelaConfiguracao() {
        try {
            if (getUsuarioLogado().getUsuarioAdminPACTO()) {
                novaTelaConfiguracao = false;
            }
            if (novaTelaConfiguracao == null) {
                novaTelaConfiguracao = getFacade().getUsuario().recursoHabilitado(TipoInfoMigracaoEnum.CONFIGURACOES,
                        getUsuarioLogado().getCodigo(), getEmpresaLogado().getCodigo());
            }
            return novaTelaConfiguracao;
        } catch (Exception e) {
            Uteis.logar(e, TelaClienteControle.class);
            return false;
        }
    }

    // CONFIG - AJUSTE NSU CEOPAG
    public void ajusteNSUCeopag() {
        Connection connection;
        Statement stm;
        ResultSet rs;
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("ajusteNSUCeopag", getEmpresaLogado());
            connection = Conexao.getFromSession();

            setMsgAlert("");
            setErro(false);

            StringBuilder sql = new StringBuilder();
            sql.append(" select paramsresposta, movpagamento from transacao \n");
            sql.append(" where conveniocobranca in (select codigo from conveniocobranca where tipoconvenio = 44) \n");
            sql.append(" and movpagamento is not null and situacao = 4 \n");
            sql.append(" and dataprocessamento::date between '2024-03-01' and current_date;");

//            List<TransacaoVO> listaTransacoes = getFacade().getTransacao().consultar(sql.toString(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            int qtdSucesso = 0;
            int qtdErro = 0;

            stm = connection.createStatement();
            try {
                rs = stm.executeQuery(sql.toString());
                while (rs.next()) {
                    String parametrosResposta = rs.getString("paramsresposta");
                    Integer codigoMovPagamento = rs.getInt("movpagamento");

                    try {
                        JSONObject json = new JSONObject(parametrosResposta);
                        JSONObject charge = json.getJSONObject("charge");
                        JSONArray transactions = charge != null ? charge.optJSONArray("transactions") : null;
                        JSONObject transaction = transactions != null ? transactions.optJSONObject(0) : null;
                        String nsu = transaction.optString("merchantTransactionId");

                        if (!UteisValidacao.emptyString(nsu)) {
                            getFacade().getMovPagamento().alterarNSU("NSU", codigoMovPagamento);
                            qtdSucesso++;
                        } else {
                            qtdErro = qtdErro++;
                        }
                    } catch (Exception ex) {
                        qtdErro++;
                    }
                }
            } catch (Exception ex) {
                qtdErro++;
            }

//            if (UteisValidacao.emptyList(listaTransacoes)) {
//                throw new Exception("Não encontrei nenhuma transacao apto para processar");
//            }

//            for (TransacaoVO transacaoVO : listaTransacoes) {
//                try {
//                    JSONObject json = new JSONObject(transacaoVO.getParamsResposta());
//                    JSONObject charge = json.getJSONObject("charge");
//                    JSONArray transactions = charge != null ? charge.optJSONArray("transactions") : null;
//                    JSONObject transaction = transactions != null ? transactions.optJSONObject(0) : null;
//                    String nsu = transaction.optString("merchantTransactionId");
//
//                    if (!UteisValidacao.emptyString(nsu)) {
//                        getFacade().getMovPagamento().alterarNSU("NSU", transacaoVO.getMovPagamento());
//                        qtdSucesso++;
//                    } else {
//                        qtdErro = qtdErro++;
//                    }
//                } catch (Exception ex) {
//                    qtdErro++;
//                }
//            }
            setErro(false);
            StringBuilder resposta = new StringBuilder();
            resposta.append("Transações processados com Sucesso: " + qtdSucesso + "\n");
            resposta.append("Transações processados com Erro: " + qtdErro + "\n");
            setSucesso(true);
            setMensagem(resposta.toString());
        } catch (Exception e) {
            setErro(true);
            setMsgAlert("Erro ao rodar o processo: " + e.getMessage());
        } finally {
            finalizarLogProcesso(log);
            connection = null;
            stm = null;
            rs = null;
        }
    }

    public void verificarMesclarCidades()  {
        try {
            limparMensagem();
            setInfoCidadeMescladaMantida(null);
            setInfoCidadeMescladaExcluida(null);
            CidadeVO cidadeVO = null;

            try {
                int codigoCidade = Integer.parseInt(getManutencaoAjusteGeralTO().getCodigoCidadeMescladaMantido());
                cidadeVO = ProcessoAjustarNomeCidades.buscarCidadeMantida(Conexao.getFromSession(), codigoCidade);
            } catch (Exception e) {}

            if (cidadeVO != null) {
                setInfoCidadeMescladaMantida(cidadeVO.getNome());
            }

            try {
                setInfoCidadeMescladaExcluida(ProcessoAjustarNomeCidades.buscarCidadesExcluidas(Conexao.getFromSession(), cidadeVO, getManutencaoAjusteGeralTO().getCodigoCidadeMescladaRemovido()));
            } catch (Exception e) {}
        } catch (Exception ex) {}
    }

    public void mesclarCidades() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("mesclarCidades", getEmpresaLogado());
            CidadeVO cidadeVO = null;

            try {
                int codigoCidade = Integer.parseInt(getManutencaoAjusteGeralTO().getCodigoCidadeMescladaMantido());
                cidadeVO = ProcessoAjustarNomeCidades.buscarCidadeMantida(Conexao.getFromSession(), codigoCidade);
            } catch (Exception e) {
                throw new ConsistirException("Informe o 'Código Cidade Mantida' válido!");
            }

            if (cidadeVO == null) {
                throw new ConsistirException("Nenhuma cidade encontrada com o 'Código Cidade Mantida'!");
            }

            String nomeCidadesRemovidas = null;

            try {
                nomeCidadesRemovidas = ProcessoAjustarNomeCidades.buscarCidadesExcluidas(Conexao.getFromSession(), cidadeVO, getManutencaoAjusteGeralTO().getCodigoCidadeMescladaRemovido());
            } catch (Exception e) {
                throw new ConsistirException("Informe o 'Código Cidade Removida' válido!");
            }

            if (UteisValidacao.emptyString(nomeCidadesRemovidas)) {
                throw new ConsistirException("Nenhuma cidade encontrada com o 'Código Cidade Removida'!");
            }

            ProcessoAjustarNomeCidades.mesclarCidades(Conexao.getFromSession(), cidadeVO, getManutencaoAjusteGeralTO().getCodigoCidadeMescladaRemovido());
            setSucesso(true);
            setErro(false);
            getManutencaoAjusteGeralTO().setCodigoCidadeMescladaRemovido("");
            setMsgAlert("Terminou a execução do processo!");
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        } finally {
            finalizarLogProcesso(log);
        }
    }

    public void desvincularMovParcelaDeBoleto() {
        manutencaoAjusteGeralTO.setSucesso(false);
        manutencaoAjusteGeralTO.setAviso(false);
        manutencaoAjusteGeralTO.setErro(false);
        manutencaoAjusteGeralTO.setMsgResultado("");

        Connection connection;
        Statement stm;
        Integer numeroLinhasAfetadas;
        LogProcessoSistemaVO log = null;
        try {
            connection = Conexao.getFromSession();
            limparMsg();

            if (getUsuarioLogado().getAdministrador()) {
                throw new Exception("Essa operação não pode ser realizada com usuário administrador.");
            }

            if (UteisValidacao.emptyString(getManutencaoAjusteGeralTO().getCodigoMovParcelaDesvincularBoleto())) {
                throw new Exception("Informe pelo menos um código de Parcela.");
            }

            log = criarLog("desvincularMovParcelaDeBoleto", getEmpresaLogado());

            StringBuilder sql = new StringBuilder();
            sql.append("UPDATE boletomovparcela SET movparcela = NULL WHERE codigo = ( \n");
            sql.append("   SELECT codigo FROM boletomovparcela WHERE movparcela = " + getManutencaoAjusteGeralTO().getCodigoMovParcelaDesvincularBoleto() + " ORDER BY codigo DESC LIMIT 1 \n");
            sql.append(");");

            stm = connection.createStatement();
            try {
                numeroLinhasAfetadas = stm.executeUpdate(sql.toString());
            } catch (Exception ex) {
                throw new Exception("Erro executar sql Desvincular Parcela: " + ex.getMessage());
            }

            if (UteisValidacao.emptyNumber(numeroLinhasAfetadas)) {
                throw new Exception("Nenhum Boleto foi encontrado para essa Parcela.");
            }

            LogVO obj = new LogVO();
            obj.setChavePrimaria("0");
            obj.setPessoa(0);
            obj.setNomeEntidade("CONFIGURACAOSISTEMA-DESVINCULAR-MOVPARCEA-BOLETO");
            obj.setNomeEntidadeDescricao("Configurações");
            obj.setOperacao("Desvincular MovParcela de Boleto (Processo)");
            obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
            obj.setUserOAMD(getUsuarioLogado().getUserOamd());
            obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            obj.setNomeCampo("");
            obj.setValorCampoAnterior("");
            String msg = "Desvinculada a MovParcela " + getManutencaoAjusteGeralTO().getCodigoMovParcelaDesvincularBoleto() + " do Boleto.";
            obj.setValorCampoAlterado(msg);
            getFacade().getLog().incluir(obj);

            manutencaoAjusteGeralTO.setSucesso(true);
            manutencaoAjusteGeralTO.setMsgResultado(msg);
        } catch (Exception ex) {
            montarErro(ex);
            Uteis.logar(ex, ConfiguracaoSistema.class);
            manutencaoAjusteGeralTO.setErro(true);
            manutencaoAjusteGeralTO.setMsgResultado(ex.getMessage());
        } finally {
            finalizarLogProcesso(log);
            connection = null;
            stm = null;
        }
    }

    public void processoConsultarEEstornarContratosVendasOnlineComPixExpirado() {
        Connection connection;
        ProcessoEstornoPixVendasOnline processo;
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("processoConsultarEEstornarContratosVendasOnlineComPixExpirado", getEmpresaLogado());
            connection = Conexao.getFromSession();
            limparMsg();

            processo = new ProcessoEstornoPixVendasOnline();
            processo.processar(connection);

            montarSucessoGrowl("Contratos foram verificados e Estornados.");
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
            montarErro(ex);
        } finally {
            finalizarLogProcesso(log);
            connection = null;
            processo = null;
        }
    }

    public void desvincularMovParcelaDeTransacao() {
        manutencaoAjusteGeralTO.setSucesso(false);
        manutencaoAjusteGeralTO.setAviso(false);
        manutencaoAjusteGeralTO.setErro(false);
        manutencaoAjusteGeralTO.setMsgResultado("");

        Connection connection;
        Integer numeroLinhasAfetadas;
        Statement stm2;
        LogProcessoSistemaVO log = null;
        try {
            connection = Conexao.getFromSession();
            limparMsg();

            if (getUsuarioLogado().getAdministrador()) {
                throw new Exception("Essa operação não pode ser realizada com usuário administrador.");
            }

            if (UteisValidacao.emptyString(getManutencaoAjusteGeralTO().getCodigoMovParcelaDesvincularTransacao())) {
                throw new Exception("Informe pelo menos um código de Parcela.");
            }

            log = criarLog("desvincularMovParcelaDeTransacao", getEmpresaLogado());

            //Primeiro descobrir quantas parcelas tem na transação da parcela digitada pelo cliente
            StringBuilder sql = new StringBuilder();
            sql.append("select count(*) as totalParcelasNaMesmaTransacao, transacao as codTransacao from transacaomovparcela where transacao = \n");
            sql.append("(select t.codigo as codTransacao from transacao t  \n");
            sql.append("inner join transacaomovparcela tmp on tmp.transacao = t.codigo  \n");
            sql.append("inner join movparcela m on tmp.movparcela = m.codigo \n");
            sql.append("where tmp.movparcela = ").append(getManutencaoAjusteGeralTO().getCodigoMovParcelaDesvincularTransacao()).append(" \n");
            sql.append("and m.situacao = 'EA' \n");
            sql.append("order by codTransacao desc \n");
            sql.append("limit 1) \n");
            sql.append("group by transacao \n");

            Integer codTransacaoPaiDaParcela = null;
            try (Statement stm = connection.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sql.toString())) {
                    if (rs.next()) {
                        int totalParcelasNaMesmaTransacao = rs.getInt("totalParcelasNaMesmaTransacao");
                        codTransacaoPaiDaParcela = rs.getInt("codTransacao");
                        if (totalParcelasNaMesmaTransacao <= 1) {
                            throw new Exception("A transação pai dessa parcela (Transacão Cód: " + codTransacaoPaiDaParcela + ") tem apenas uma parcela vinculada," +
                                    " então não é possível desvincular ela pois dessa forma ficaria uma transação sem nenhuma parcela lançada no sistema.");
                        }
                    }
                }
            }

            if (UteisValidacao.emptyNumber(codTransacaoPaiDaParcela)) {
                throw new Exception("Nenhuma Transação foi encontrado para essa Parcela. A parcela informada pode estar incorreta, ou então a parcela não está em aberto.");
            }

            //Agora sim, apagar o registro do transacaomovparcela (desvincular)
            StringBuilder sql2 = new StringBuilder();
            sql2.append("delete from transacaomovparcela tmp WHERE transacao = ").append(codTransacaoPaiDaParcela).append(" and movparcela = ").append(getManutencaoAjusteGeralTO().getCodigoMovParcelaDesvincularTransacao()).append(";");

            stm2 = connection.createStatement();
            try {
                numeroLinhasAfetadas = stm2.executeUpdate(sql2.toString());
            } catch (Exception ex) {
                throw new Exception("Erro executar sql Desvincular Parcela: " + ex.getMessage());
            }

            if (UteisValidacao.emptyNumber(numeroLinhasAfetadas)) {
                throw new Exception("Nenhuma Transação foi encontrada para essa Parcela.");
            }

            LogVO obj = new LogVO();
            obj.setChavePrimaria("0");
            obj.setPessoa(0);
            obj.setNomeEntidade("CONFIGURACAOSISTEMA-DESVINCULAR-MOVPARCELA-TRANSACAO");
            obj.setNomeEntidadeDescricao("Configurações");
            obj.setOperacao("Desvincular MovParcela de Transação (Processo)");
            obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
            obj.setUserOAMD(getUsuarioLogado().getUserOamd());
            obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            obj.setNomeCampo("");
            obj.setValorCampoAnterior("");
            String msg = "Desvinculada a MovParcela " + getManutencaoAjusteGeralTO().getCodigoMovParcelaDesvincularTransacao() + " da Transação.";
            obj.setValorCampoAlterado(msg);
            getFacade().getLog().incluir(obj);

            manutencaoAjusteGeralTO.setSucesso(true);
            manutencaoAjusteGeralTO.setMsgResultado(msg);
        } catch (Exception ex) {
            montarErro(ex);
            Uteis.logar(ex, ConfiguracaoSistema.class);
            manutencaoAjusteGeralTO.setErro(true);
            manutencaoAjusteGeralTO.setMsgResultado(ex.getMessage());
        } finally {
            finalizarLogProcesso(log);
            connection = null;
            stm2 = null;
        }
    }

    public void alterarSituacaoDaTransacao() {
        manutencaoAjusteGeralTO.setSucesso(false);
        manutencaoAjusteGeralTO.setAviso(false);
        manutencaoAjusteGeralTO.setErro(false);
        manutencaoAjusteGeralTO.setMsgResultado("");

        Connection connection;
        Integer numeroLinhasAfetadas;
        Statement stm2;
        LogProcessoSistemaVO log = null;
        try {
            connection = Conexao.getFromSession();
            limparMsg();

            if (getUsuarioLogado().getAdministrador()) {
                throw new Exception("Essa operação não pode ser realizada com usuário administrador.");
            }

            if (UteisValidacao.emptyString(getManutencaoAjusteGeralTO().getCodigoTransacaoAlterarSituacao())) {
                throw new Exception("Informe pelo menos um código de Transação.");
            }

            log = criarLog("Alterar Situação de Transacao Online", getEmpresaLogado());

            StringBuilder sql = new StringBuilder();
            sql.append("select c.tipoConvenio, t.situacao from transacao t \n");
            sql.append("inner join conveniocobranca c on c.codigo = t.conveniocobranca \n");
            sql.append("where t.codigo = ").append(getManutencaoAjusteGeralTO().getCodigoTransacaoAlterarSituacao());

            int tipoConvenio = 0;
            int situacao = 0;
            try (Statement stm = connection.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sql.toString())) {
                    if (rs.next()) {
                        tipoConvenio = rs.getInt("tipoConvenio");
                        situacao = rs.getInt("situacao");
                        if (UteisValidacao.emptyNumber(tipoConvenio)) {
                            throw new Exception("Tipo do convênio não encontrado, não será possível prosseguir!");
                        }
                        if (UteisValidacao.emptyNumber(situacao)) {
                            throw new Exception("Situação da transação não encontrada, não será possível prosseguir!");
                        }
                    } else {
                        throw new Exception("Nenhuma Transação foi encontrada com o código informado!");
                    }
                }
            }

            SituacaoTransacaoEnum situacaoTransacaoEnum = SituacaoTransacaoEnum.getSituacaoTransacaoEnum(situacao);
            if (situacaoTransacaoEnum != SituacaoTransacaoEnum.APROVADA) {
                throw new Exception("A transação informada não está Azul (Aprovada - Aguardando Confirmação), não é possível alterar a situação dela!");
            }

            TipoConvenioCobrancaEnum tipoConvenioCobrancaEnum = TipoConvenioCobrancaEnum.obterPorCodigo(tipoConvenio);
            if (!tipoConvenioCobrancaEnum.isTransacaoOnline()) {
                throw new Exception("A transação informada não é de um convênio de cobrança online!");
            }

            //Agora sim, alterar a situação da transação
            StringBuilder sql2 = new StringBuilder();
            sql2.append("UPDATE transacao set situacao = "+ SituacaoTransacaoEnum.NAO_APROVADA.getId() + " WHERE codigo = ").append(getManutencaoAjusteGeralTO().getCodigoTransacaoAlterarSituacao());

            stm2 = connection.createStatement();
            try {
                numeroLinhasAfetadas = stm2.executeUpdate(sql2.toString());
            } catch (Exception ex) {
                throw new Exception("Erro executar sql Alterar Transação: " + ex.getMessage());
            }

            if (UteisValidacao.emptyNumber(numeroLinhasAfetadas)) {
                throw new Exception("Nenhuma Transação foi encontrada para alterar.");
            }

            LogVO obj = new LogVO();
            obj.setChavePrimaria("0");
            obj.setPessoa(0);
            obj.setNomeEntidade("CONFIGURACAOSISTEMA-ALTERAR-SITUAÇÃO-TRANSACAO");
            obj.setNomeEntidadeDescricao("Configurações");
            obj.setOperacao("Alterar Situação de Transação (Processo)");
            obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
            obj.setUserOAMD(getUsuarioLogado().getUserOamd());
            obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            obj.setNomeCampo("");
            obj.setValorCampoAnterior("");
            String msg = "A situação da transação " + getManutencaoAjusteGeralTO().getCodigoTransacaoAlterarSituacao() + " foi alterada com sucesso!";
            obj.setValorCampoAlterado(msg);
            getFacade().getLog().incluir(obj);

            manutencaoAjusteGeralTO.setSucesso(true);
            manutencaoAjusteGeralTO.setMsgResultado(msg);
        } catch (Exception ex) {
            montarErro(ex);
            Uteis.logar(ex, ConfiguracaoSistema.class);
            manutencaoAjusteGeralTO.setErro(true);
            manutencaoAjusteGeralTO.setMsgResultado(ex.getMessage());
        } finally {
            finalizarLogProcesso(log);
            connection = null;
            stm2 = null;
        }
    }

    private LogProcessoSistemaVO criarLog(String processo, EmpresaVO empresaVO) throws Exception {
        LogProcessoSistemaVO log = new LogProcessoSistemaVO(processo, getUsuarioLogado(), empresaVO);
        return getFacade().getLogProcessoSistema().incluir(log);
    }

    private void finalizarLogProcesso(LogProcessoSistemaVO log) {
        if (log == null) {
            return;
        }

        try {
            getFacade().getLogProcessoSistema().finalizar(log);
        } catch (Exception ex) {
            Uteis.logar(ex, ConfiguracaoSistema.class);
        }

    }

    public void alterarSitucaoBoletoOnlineCanceladoParaAguardandoPagamento() {
        manutencaoAjusteGeralTO.setSucesso(false);
        manutencaoAjusteGeralTO.setAviso(false);
        manutencaoAjusteGeralTO.setErro(false);
        manutencaoAjusteGeralTO.setMsgResultado("");

        Connection connection;
        Statement stm;
        Integer numeroLinhasAfetadas;
        LogProcessoSistemaVO log = null;
        try {
            connection = Conexao.getFromSession();
            limparMsg();

            Integer codigoBoletoAlterarInformado = null;
            try {
                codigoBoletoAlterarInformado = Integer.parseInt(getManutencaoAjusteGeralTO().getCodigoBoletoOnlineMudarSituacaoCanceladoParaAguardandoPagamento());
            } catch (Exception e) {
                throw new Exception("Informe um código de Boleto válido.");
            }

            if (getUsuarioLogado().getAdministrador()) {
                throw new Exception("Essa operação não pode ser realizada com usuário administrador.");
            }

            if (UteisValidacao.emptyNumber(codigoBoletoAlterarInformado)) {
                throw new Exception("Informe pelo menos um código de Boleto Online.");
            }

            log = criarLog("alterarSitucaoBoletoOnlineCanceladoParaAguardandoPagamento", getEmpresaLogado());

            BoletoVO boletoAlterar = getFacade().getBoleto().consultarPorChavePrimaria(codigoBoletoAlterarInformado, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            if (boletoAlterar == null) {
                throw new Exception("Não foi encontrado um Boleto Online com o código informado.");
            }

            if (boletoAlterar.getSituacao().equals(SituacaoBoletoEnum.CANCELADO) || boletoAlterar.getSituacao().equals(SituacaoBoletoEnum.CANCELAMENTO_PENDENTE)) {
                StringBuilder sql = new StringBuilder();
                sql.append("UPDATE boleto SET situacao = 4 WHERE codigo = ").append(boletoAlterar.getCodigo()).append(";");
                stm = connection.createStatement();
                try {
                    numeroLinhasAfetadas = stm.executeUpdate(sql.toString());
                } catch (Exception ex) {
                    throw new Exception("Erro executar sql Alterar Situação Boleto Online Cancelado: " + ex.getMessage());
                }

                if (UteisValidacao.emptyNumber(numeroLinhasAfetadas)) {
                    throw new Exception("Nenhum Boleto foi encontrado para essa Parcela.");
                }

                LogVO obj = new LogVO();
                obj.setChavePrimaria("0");
                obj.setPessoa(0);
                obj.setNomeEntidade("CONFIGURACAOSISTEMA-ALTERAR-SITUACAO-BOLETO-CANCELADO");
                obj.setNomeEntidadeDescricao("Configurações");
                obj.setOperacao("Alterar Situação de Boleto Cancelado");
                obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
                obj.setUserOAMD(getUsuarioLogado().getUserOamd());
                obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
                obj.setNomeCampo("");
                obj.setValorCampoAnterior("");
                String msg = "Alterado situação do Boleto: " + boletoAlterar.getCodigo();
                obj.setValorCampoAlterado(msg);
                getFacade().getLog().incluir(obj);

                String operacaoHistoricoBoleto = "Configurações Gerais - Processo Alterar Situação Boleto Cancelado";
                String dadosHistoricoBoleto = "Boleto teve situação alterada de Cancelado para Aguardando Pagamento pelo usuário: " + getUsuarioLogado().getNome() + " - " + getUsuarioLogado().getUserOamd();
                getFacade().getBoleto().incluirBoletoHistorico(boletoAlterar, operacaoHistoricoBoleto, dadosHistoricoBoleto);

                manutencaoAjusteGeralTO.setSucesso(true);
                manutencaoAjusteGeralTO.setMsgResultado(msg);
            } else {
                throw new Exception("Boleto não está com situação de Cancelado, para que possa ter a situação alterada.");
            }
        } catch (Exception ex) {
            montarErro(ex);
            Uteis.logar(ex, ConfiguracaoSistema.class);
            manutencaoAjusteGeralTO.setErro(true);
            manutencaoAjusteGeralTO.setMsgResultado(ex.getMessage());
        } finally {
            finalizarLogProcesso(log);
            connection = null;
            stm = null;
        }
    }

    public boolean isApresentarCorrigirHistoricoVinculo() {
        return apresentarCorrigirHistoricoVinculo;
    }

    public void setApresentarCorrigirHistoricoVinculo(boolean apresentarCorrigirHistoricoVinculo) {
        this.apresentarCorrigirHistoricoVinculo = apresentarCorrigirHistoricoVinculo;
    }

    public void corrigirHistoricoVinculoProcs() {
        try {
            CorrigirHistoricoVinculos corrigirHistoricoVinculos = new CorrigirHistoricoVinculos(Conexao.getFromSession());
            corrigirHistoricoVinculos.processarCorrecao(false, true);
            montarSucessoGrowl("Processo finalizado");
        } catch (Exception ex) {
            Logger.getLogger(ConfiguracaoSistemaControle.class.getName()).log(Level.SEVERE, null, ex);
            montarErro(ex);
        }
    }

    public void alterarSituacaoPixExpiradoParaAguardandoPagamento() {
        manutencaoAjusteGeralTO.setSucesso(false);
        manutencaoAjusteGeralTO.setAviso(false);
        manutencaoAjusteGeralTO.setErro(false);
        manutencaoAjusteGeralTO.setMsgResultado("");

        Connection connection;
        Integer numeroLinhasAfetadas;
        Statement stm2;
        LogProcessoSistemaVO log = null;
        try {
            connection = Conexao.getFromSession();
            limparMsg();

            if (getUsuarioLogado().getAdministrador()) {
                throw new Exception("Essa operação não pode ser realizada com usuário administrador.");
            }

            if (UteisValidacao.emptyString(getManutencaoAjusteGeralTO().getCodigoPixAlterarSituacaoAguardandoPagametno())) {
                throw new Exception("Informe pelo menos um código de Pix.");
            }

            int codigoPix;
            try {
                // Remover espaços em branco e tentar converter para inteiro
                codigoPix = Integer.parseInt(getManutencaoAjusteGeralTO().getCodigoPixAlterarSituacaoAguardandoPagametno().trim());
                if (UteisValidacao.emptyNumber(codigoPix)) {
                    throw new Exception("O código informado deve ser um número inteiro positivo.");
                }
            } catch (NumberFormatException e) {
                throw new Exception("O código informado não é válido. Certifique-se de informar um número inteiro.");
            }

            log = criarLog("alterarSituacaoPixExpiradoParaAguardandoPagamento", getEmpresaLogado());

            //Primeiro confirmar se o pix atende as condições para alteração
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT COUNT(*) AS count FROM pix WHERE codigo = ")
                    .append(codigoPix)
                    .append(" AND status ILIKE 'EXPIRADA';");

            try (Statement stm = connection.createStatement()) {
                try (ResultSet detailedRs = stm.executeQuery(sql.toString())) {
                    if (detailedRs.next()) {
                        int totalPixIdentificados = detailedRs.getInt("count");
                        if (totalPixIdentificados < 1) {
                            throw new Exception("Não foi identificado Pix de código: " + codigoPix +
                                    " , em situação de Expirada.");
                        }
                    }
                }
            }

            //Agora sim, alterar o registro
            StringBuilder sql2 = new StringBuilder();
            sql2.append("UPDATE pix SET status = 'ATIVA' WHERE codigo = ")
                    .append(codigoPix)
                    .append(" AND status ILIKE 'EXPIRADA';");

            stm2 = connection.createStatement();
            try {
                numeroLinhasAfetadas = stm2.executeUpdate(sql2.toString());
            } catch (Exception ex) {
                throw new Exception("Erro executar sql alterar Pix: " + ex.getMessage());
            }

            if (UteisValidacao.emptyNumber(numeroLinhasAfetadas)) {
                throw new Exception("Nenhum Pix foi encontrada para alterar.");
            }

            LogVO obj = new LogVO();
            obj.setChavePrimaria("0");
            obj.setPessoa(0);
            obj.setNomeEntidade("CONFIGURACAOSISTEMA-ALTERARSTATUS-PIXEXPIRADA-PIXATIVA");
            obj.setNomeEntidadeDescricao("Configurações");
            obj.setOperacao("Alterar status do Pix Expirado para Ativa (Processo)");
            obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
            obj.setUserOAMD(getUsuarioLogado().getUserOamd());
            obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            obj.setNomeCampo("");
            obj.setValorCampoAnterior("");
            String msg = "Alterado status do Pix  " + codigoPix + " de Expirada para Aguardando Pagamento.";
            obj.setValorCampoAlterado(msg);
            getFacade().getLog().incluir(obj);

            manutencaoAjusteGeralTO.setSucesso(true);
            manutencaoAjusteGeralTO.setMsgResultado(msg);
        } catch (Exception ex) {
            montarErro(ex);
            Uteis.logar(ex, ConfiguracaoSistema.class);
            manutencaoAjusteGeralTO.setErro(true);
            manutencaoAjusteGeralTO.setMsgResultado(ex.getMessage());
        } finally {
            finalizarLogProcesso(log);
            connection = null;
            stm2 = null;
        }
    }


    public void processoMigrarTurmaParaAulas() {
        try {
            limparMsg();
            this.manutencaoAjusteGeralTO.setSucesso(false);
            this.manutencaoAjusteGeralTO.setErro(false);
            this.manutencaoAjusteGeralTO.setMsgResultado("");

            ProcessoMigrarTurmaParaAulasPratique processoMigrarTurmaParaAulasPratique = new ProcessoMigrarTurmaParaAulasPratique(Conexao.getFromSession(), getUsuarioLogado());
            List<String> resultados = processoMigrarTurmaParaAulasPratique.processarTurmas(manutencaoAjusteGeralTO.getCodigosTurmasMigrarParaAulas());

            boolean sucesso = true;
            for (String r: resultados) {
                if (r.toLowerCase().contains("erro")) {
                    sucesso = false;
                }
            }
            this.manutencaoAjusteGeralTO.setSucesso(sucesso);
            this.manutencaoAjusteGeralTO.setMsgResultado(String.join("\n", resultados));

        } catch (Exception ex) {
            this.manutencaoAjusteGeralTO.setSucesso(false);
            this.manutencaoAjusteGeralTO.setMsgResultado("Erro ao executar o processo: " + ex.getMessage());
        }
    }

    public void processarCancelamentoBoletosCaixaOnline() {
        limparMsg();
        manutencaoAjusteGeralTO.setSucesso(false);
        manutencaoAjusteGeralTO.setMsgResultado("");
        LogProcessoSistemaVO log = null;
        CaixaService service;
        Boleto boletoDAO;
        Connection con;
        try {
            log = criarLog("processarCancelamentoBoletosCaixaOnline", getEmpresaLogado());
            limparMsg();
            setOnComplete("");

            if (UteisValidacao.emptyNumber(manutencaoAjusteGeralTO.getEmpresaVOBoletoCancelar().getCodigo())) {
                throw new Exception("Selecione uma empresa para não cancelar os boletos de outra unidade com o mesmo valor de Nosso Número.");
            }

            if (UteisValidacao.emptyString(getManutencaoAjusteGeralTO().getListaNossoNumeroBoletoCaixaOnlineCancelar())) {
                throw new Exception("Nenhum Nosso Número foi informado");
            }

            con = Conexao.getFromSession();
            List<BoletoVO> boletosCancelar = new ArrayList<>();
            List<String> listaNossoNumero = Stream
                    .of(getManutencaoAjusteGeralTO().getListaNossoNumeroBoletoCaixaOnlineCancelar().split(","))
                    .map(String::trim)
                    .filter(nn -> !nn.isEmpty())
                    .collect(Collectors.toList());

            boletoDAO = new Boleto(con);
            for (String nossoNumero : listaNossoNumero) {
                boletosCancelar.addAll(boletoDAO.consultarPorNossoNumero(nossoNumero, manutencaoAjusteGeralTO.getEmpresaVOBoletoCancelar().getCodigo(),
                        Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            }
            if (boletosCancelar.isEmpty()) {
                throw new Exception("Nenhum boleto encontrado para os Nosso Número mencionadas.");
            }

            int qtdBoletosForamCanceladosSucesso = 0;
            int qtdParcelasErro = 0;

            for (BoletoVO boleto : boletosCancelar) {
                try {
                    if (!boleto.getSituacao().equals(SituacaoBoletoEnum.PAGO)) {
                        service = new CaixaService(con, boleto.getEmpresaVO().getCodigo(), boleto.getConvenioCobrancaVO().getCodigo());
                        service.cancelarBoletoViaProcesso(boleto, getUsuarioLogado(), "Cancelamento pelo ajuste do processo geral", true,
                                this.manutencaoAjusteGeralTO.isTentarCancelarBoletosCaixaSemRegistro());

                        if (boleto.getSituacao().equals(SituacaoBoletoEnum.CANCELADO)) {
                            qtdBoletosForamCanceladosSucesso++;
                        } else if (boleto.getSituacao().equals(SituacaoBoletoEnum.ERRO)) {
                            qtdParcelasErro++;
                        }
                        service = null;
                    } else {
                        qtdParcelasErro++;
                    }
                } catch (Exception e) {
                    qtdParcelasErro++;
                }
            }

            StringBuilder resposta = new StringBuilder();
            resposta.append("Verifiquei um a um, os " + boletosCancelar.size() + " boletos encontrados para cancelar... Veja o resultado dos boletos, \n");
            resposta.append("Qtd. que conseguimos cancelar com sucesso: " + qtdBoletosForamCanceladosSucesso + "\n");
            resposta.append("Qtd. que tentamos cancelar porem deu erro: " + qtdParcelasErro + "\n");
            manutencaoAjusteGeralTO.setSucesso(true);
            manutencaoAjusteGeralTO.setMsgResultado(resposta.toString());
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
            manutencaoAjusteGeralTO.setSucesso(false);
            manutencaoAjusteGeralTO.setMsgResultado(ex.getMessage());
        } finally {
            finalizarLogProcesso(log);
            service = null;
            boletoDAO = null;
            con = null;
        }
    }

    public void reprocessarDadosIntegracaoLegacySesiCE() {
        LogProcessoSistemaVO log = null;
        try {
            log = criarLog("reprocessarDadosIntegracaoLegacySesiCE", getEmpresaLogado());
            this.manutencaoAjusteGeralTO.setMsgResultado("");
            StringBuilder mensagemDoProcessamento = new StringBuilder();

            if (validarParametrosReprocessarDadosIntegracaoLegacySesiCE()) {
                for (StatusMatriculaSesiCeEnum statusMatricula : StatusMatriculaSesiCeEnum.values()) {
                    mensagemDoProcessamento.append(consultarDadosDoAlunoEContratoNoSistemaLegacyParaDataInformada(
                            statusMatricula.getCodigo(), this.manutencaoAjusteGeralTO.getDataLancamento(),
                            this.manutencaoAjusteGeralTO.getCpf())).append(" <br/> ").append(" <br/> ");
                }

                mensagemDoProcessamento.append("Reprocessado com sucesso! ").append("<br/> ");
                this.manutencaoAjusteGeralTO.setMsgResultado(mensagemDoProcessamento.toString());

                gravarLogAdministrativoAoReprocessarDadosIntegracaoLegacySesiCE(true);
            }
        } catch (Exception e) {
            gravarLogAdministrativoAoReprocessarDadosIntegracaoLegacySesiCE(false);
            montarErro(e);
            manutencaoAjusteGeralTO.setErro(true);
            manutencaoAjusteGeralTO.setMsgResultado("Erro ao reprocessar: <br/> " + e.getMessage());
        } finally {
            finalizarLogProcesso(log);
        }
    }

    private String consultarDadosDoAlunoEContratoNoSistemaLegacyParaDataInformada(Integer statusMatricula, Date dataInicio, String cpf) throws Exception {
        String chave = getKey();

        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", "Bearer " + chave);

        JSONObject body = new JSONObject();
        body.put("status_matricula", statusMatricula.toString());
        if (getEmpresaLogado().getCodExternoUnidadeSesi() == null) {
            throw new Exception("Código externo da unidade SESI não encontrado para empresa logada!");
        }
        body.put("id_unidade", getEmpresaLogado().getCodExternoUnidadeSesi().toString());
        body.put("data_inicio", Uteis.getData(dataInicio, "bd"));
        body.put("chave", chave);
        body.put("matriculas", new JSONArray());
        JSONArray cpfs = new JSONArray();
        cpfs.put(cpf.replaceAll("[\\D]", ""));
        body.put("cpfs", cpfs);

        String urlBase = PropsService.getPropertyValue(PropsService.urlIntegracoesMs);
        if (urlBase == null || urlBase.isEmpty()) {
            throw new Exception("URL de integração não configurada");
        }
        String url = urlBase + "/integracao-sesi/alunos-matricula/";

        RequestHttpService httpService = new RequestHttpService();
        RespostaHttpDTO respostaHttpDTO = httpService.executeRequest(url, headers, null, body.toString(), MetodoHttpEnum.POST);
        JSONObject json = new JSONObject(respostaHttpDTO.getResponse());

        if (!respostaHttpDTO.getHttpStatus().equals(HttpStatus.SC_OK)) {
            String msg = json.optString("message", "Falha na chamada HTTP (" + respostaHttpDTO.getHttpStatus() + ")");

            throw new Exception(msg);
        }

        return json.optString("content", "");
    }

    private boolean validarParametrosReprocessarDadosIntegracaoLegacySesiCE() {
        boolean valido = false;

        if (UteisValidacao.emptyString(this.manutencaoAjusteGeralTO.getCpf())) {
            this.manutencaoAjusteGeralTO.setMsgResultado("Erro: informe o CPF.");
        } else if (this.manutencaoAjusteGeralTO.getCpf().replaceAll("[\\D]", "").length() != 11) {
            this.manutencaoAjusteGeralTO.setMsgResultado("Erro: informe um CPF válido.");
        } else if (this.manutencaoAjusteGeralTO.getDataLancamento() == null) {
            this.manutencaoAjusteGeralTO.setMsgResultado("Erro: informe a data de lançamento.");
        } else {
            valido = true;
        }

        return valido;
    }

    private void gravarLogAdministrativoAoReprocessarDadosIntegracaoLegacySesiCE(boolean sucesso) {
        try {
            LogVO obj = new LogVO();
            obj.setChavePrimaria("0");
            obj.setPessoa(0);
            obj.setNomeEntidade("CONFIGURACAOSISTEMA-MANUTENCAO-AJUSTESGERAIS-REPROCESSARINTEGRACAOLEGACYSESICE");
            obj.setNomeEntidadeDescricao("Configurações");
            obj.setOperacao("Reprocessar dados da integração Legacy (SESI CE)");
            obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
            obj.setUserOAMD(getUsuarioLogado().getUserOamd());
            obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            obj.setNomeCampo("");
            obj.setValorCampoAnterior("");
            String msg = sucesso ? "Reprocessado com sucesso: " : "Erro ao reprocessar: "
                    + "<br/> "
                    + "CPF do aluno " + this.manutencaoAjusteGeralTO.getCpf() + " e data de lançamento "
                    + this.manutencaoAjusteGeralTO.getDataLancamento() + ".";
            obj.setValorCampoAlterado(msg);
            getFacade().getLog().incluir(obj);
        } catch (Exception e) {
            montarErro(e);
            manutencaoAjusteGeralTO.setErro(true);
            manutencaoAjusteGeralTO.setMsgResultado(e.getMessage());
        }
    }

    // CONFIG - AJUSTE OPERADORAS STONE CONNECT
    public void ajusteOperadorasStoneConnect() {
        processarAjusteOperadoras(TipoConvenioCobrancaEnum.PINPAD_STONE_CONNECT);
    }

    // CONFIG - AJUSTE OPERADORAS STONE V5
    public void ajusteOperadorasStoneV5() {
        processarAjusteOperadoras(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5);
    }

    // CONFIG - AJUSTE OPERADORAS CIELO
    public void ajusteOperadorasCielo() {
        processarAjusteOperadoras(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE);
    }
    
    // METODO GENÉRICO PARA PROCESSAR AJUSTE DE OPERADORAS
    private void processarAjusteOperadoras(TipoConvenioCobrancaEnum processoEmExecucao) {
        Connection connection = null;
        Statement stm = null;
        ResultSet rs = null;
        PreparedStatement psConsulta = null;
        PreparedStatement psUpdate = null;
        PreparedStatement psDelete = null;
        LogProcessoSistemaVO log = null;
        try {
            String nomeProcesso = "ajusteOperadoras - " + processoEmExecucao.getDescricao();
            log = criarLog(nomeProcesso, getEmpresaLogado());
            connection = Conexao.getFromSession();

            setMsgAlert("");
            setErro(false);

            // SQL para buscar todas as operadoras criadas indevidamente
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT codigo, descricao, credito FROM operadoracartao ");
            if (processoEmExecucao.equals(TipoConvenioCobrancaEnum.PINPAD_STONE_CONNECT)) {
                sql.append("WHERE descricao LIKE '%STONE CONNECT%'");
            } else if (processoEmExecucao.equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5)) {
                sql.append("WHERE descricao LIKE '%STONE ONLINE V5%'");
            } else if (processoEmExecucao.equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE)) {
                sql.append("WHERE descricao LIKE '%CIELO%'");
            }

            int qtdSucesso = 0;
            int qtdErro = 0;

            stm = connection.createStatement();
            rs = stm.executeQuery(sql.toString());

            while (rs.next()) {
                Integer codigoOperadoraIndevida = rs.getInt("codigo");
                String descricaoCompleta = rs.getString("descricao");
                boolean credito = rs.getBoolean("credito");

                ResultSet rsConsulta = null;
                try {
                    // Extrair a primeira palavra da descrição (VISA, MASTERCARD, DINNERS, etc.)
                    String primeiraPalavra = descricaoCompleta.split(" ")[0].trim().toUpperCase();

                    // Criar descrição padrão baseada no tipo (crédito/débito)
                    String descricaoPadrao = primeiraPalavra + " (" + (credito ? "CRÉDITO" : "DÉBITO") + ")";

                    // Primeira tentativa: buscar operadora padrão equivalente (com acento)
                    String sqlConsulta = "SELECT codigo FROM operadoracartao WHERE ativo = true AND descricao ilike ? AND credito = ?";
                    psConsulta = connection.prepareStatement(sqlConsulta);
                    psConsulta.setString(1, "%" + descricaoPadrao + "%");
                    psConsulta.setBoolean(2, credito);

                    rsConsulta = psConsulta.executeQuery();
                    boolean encontrouOperadora = rsConsulta.next();

                    // Se não encontrou com acento, tentar sem acento
                    if (!encontrouOperadora) {
                        rsConsulta.close();
                        psConsulta.close();

                        String descricaoPadraoSemAcento = primeiraPalavra + " (" + (credito ? "CREDITO" : "DEBITO") + ")";
                        psConsulta = connection.prepareStatement(sqlConsulta);
                        psConsulta.setString(1, "%" + descricaoPadraoSemAcento + "%");
                        psConsulta.setBoolean(2, credito);
                        rsConsulta = psConsulta.executeQuery();
                        encontrouOperadora = rsConsulta.next();
                    }

                    if (encontrouOperadora) {
                        Integer codigoOperadoraPadrao = rsConsulta.getInt("codigo");

                        // Atualizar MovPagamento para usar a operadora padrão
                        String sqlUpdate = "UPDATE movpagamento SET operadoracartao = ? WHERE operadoracartao = ?";
                        psUpdate = connection.prepareStatement(sqlUpdate);
                        psUpdate.setInt(1, codigoOperadoraPadrao);
                        psUpdate.setInt(2, codigoOperadoraIndevida);
                        psUpdate.executeUpdate();

                        // Atualizar CartaoCredito para usar a operadora padrão
                        String sqlUpdateCartao = "UPDATE cartaocredito SET operadoracartao = ? WHERE operadoracartao = ?";
                        PreparedStatement psUpdateCartao = connection.prepareStatement(sqlUpdateCartao);
                        psUpdateCartao.setInt(1, codigoOperadoraPadrao);
                        psUpdateCartao.setInt(2, codigoOperadoraIndevida);
                        psUpdateCartao.executeUpdate();
                        psUpdateCartao.close();

                        qtdSucesso++;
                    } else {
                        qtdErro++;
                    }

                    // Excluir a operadora indevida (sempre, independente de ter encontrado padrão ou não)
                    String sqlDelete = "DELETE FROM operadoracartao WHERE codigo = ?";
                    psDelete = connection.prepareStatement(sqlDelete);
                    psDelete.setInt(1, codigoOperadoraIndevida);
                    psDelete.executeUpdate();

                } catch (Exception ex) {
                    qtdErro++;
                    ex.printStackTrace();
                } finally {
                    try {
                        if (rsConsulta != null) rsConsulta.close();
                        if (psConsulta != null) psConsulta.close();
                        if (psUpdate != null) psUpdate.close();
                        if (psDelete != null) psDelete.close();
                    } catch (Exception e) { /* ignore */ }
                }
            }

            setErro(false);
            StringBuilder resposta = new StringBuilder();
            resposta.append("Operadoras processadas com Sucesso: " + qtdSucesso + "\n");
            resposta.append("Operadoras processadas com Erro: " + qtdErro + "\n");
            setSucesso(true);
            setMensagem(resposta.toString());
        } catch (Exception e) {
            setErro(true);
            setMsgAlert("Erro ao rodar o processo: " + e.getMessage());
            e.printStackTrace();
        } finally {
            finalizarLogProcesso(log);
            try {
                if (rs != null) rs.close();
                if (stm != null) stm.close();
                if (psConsulta != null) psConsulta.close();
                if (psUpdate != null) psUpdate.close();
                if (psDelete != null) psDelete.close();
                if (connection != null) connection.close();
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.estoque;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;


import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import controle.arquitetura.FuncionalidadeSistemaEnum;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.estoque.PosicaoEstoqueTotalizadorTO;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import java.util.Map;
import javax.faces.model.SelectItem;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.estoque.ProdutoEstoqueVO;
import negocio.comuns.plano.CategoriaProdutoVO;

import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UteisValidacao;

/**
 *
 * <AUTHOR>
 */
public class RelatorioEstoqueProdutoControle extends SuperControleRelatorio {

    private String retorno = "";
    private Integer codigoEmpresa;
    private Integer codigoCategoria;
    protected List listaSelectItemEmpresa;
    protected List listaSelectItemCategoria;
    private int tipoVisualizacao = 1;
    private int statusProduto = 0;
    private int ordenacao = 1;
    private int valorImpresso = 1;


    public RelatorioEstoqueProdutoControle() throws Exception {
        montarListaSelectItemEmpresa();
        montarListaSelectItemCategoriaProduto();
        obterUsuarioLogado();
    }

    public String abrirTela(){
        limparMsg();
        return "";
    }

    public String abrirTelaPosicaoEstoque(){
        try {
            notificarRecursoEmpresa(RecursoSistema.fromDescricao(FuncionalidadeSistemaEnum.POSICAO_DO_ESTOQUE.toString()));
            abrirPosicaoEstoque();
            setMsgAlert("abrirPopup('relatorioEstoqueProduto.jsp', 'PosicaoEstoque', 1000, 650);");
            return abrirTela();
        } catch (Exception e) {
            montarMsgAlert(e.getMessage());
            return "";
        }
    }

    public void abrirPosicaoEstoque() throws Exception {
        setMensagemDetalhada("", "");
        if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
            if (getUsuarioLogado().getAdministrador()) {
                return;
            }
            throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
        }
        for (Object o : getUsuarioLogado().getUsuarioPerfilAcessoVOs()) {
            UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) o;
            if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().
                        consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                        getUsuarioLogado(), "VisualizarPosicaoEstoque", "12.08 - Visualizar Posição do Estoque");
            }
        }
    }

    public void limparMensagens() throws Exception{
        if (!getUsuarioLogado().getAdministrador()){
           this.codigoEmpresa = getEmpresaLogado().getCodigo();
        }
        setMensagemDetalhada("", "");
        setMensagem("");
        setMensagemID("msg_entre_prmrelatorio");
        setSucesso(true);
        setErro(false);
    }

    public void montarListaSelectItemEmpresa() throws Exception {
        List listaEmpresa = new ArrayList();
        listaEmpresa.add(new SelectItem(new Integer(0), ""));
        List listaConsulta = getFacade().getEmpresa().consultarPorNome("",true, false, Uteis.NIVELMONTARDADOS_TODOS);
        for (Object obj: listaConsulta){
            EmpresaVO empresa = (EmpresaVO) obj;
            listaEmpresa.add(new SelectItem(empresa.getCodigo(), empresa.getNome()));
        }
        setListaSelectItemEmpresa(listaEmpresa);
    }

    public void montarListaSelectItemCategoriaProduto() throws Exception {
        List lista = getFacade().getCategoriaProduto().consultarPorDescricao("", false, Uteis.NIVELMONTARDADOS_TODOS);
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), "Todos"));
        for(Object obj: lista) {
            CategoriaProdutoVO categoria = (CategoriaProdutoVO) obj;
            objs.add(new SelectItem(categoria.getCodigo(), categoria.getDescricao().toString()));
        }
        setListaSelectItemCategoria(objs);
    }


    public void imprimirRelatorioPDF() {
        try {
            if (getProcessandoOperacao() != null && getProcessandoOperacao()){
                setErro(true);
                throw new Exception("Operação em processamento, favor aguardar.");
            } else {
                setProcessandoOperacao(true);
                if (!getUsuarioLogado().getAdministrador()) {
                    this.codigoEmpresa = getEmpresaLogado().getCodigo();
                }
                if ((this.codigoEmpresa == null) || (this.codigoEmpresa <= 0)) {
                    throw new ConsistirException("É necessário informar a empresa para imprimir o relatório.");
                }
                EmpresaVO empresaVO = getFacade().getEmpresa().consultarPorChavePrimaria(this.codigoEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                if (this.tipoVisualizacao == 0) {
                    throw new ConsistirException("É necessário informar o tipo do relatório.");
                }
                this.codigoEmpresa.intValue();
                List<ProdutoEstoqueVO> listaProdutos = getFacade().getProdutoEstoque().consultarPosicaoEstoque(this.tipoVisualizacao, this.codigoEmpresa, this.codigoCategoria, this.statusProduto, this.ordenacao, getValorImpresso(), Uteis.NIVELMONTARDADOS_TELACONSULTA);
                if (listaProdutos.size() == 0)
                    throw new ConsistirException("Não há dados para serem exibidos ! verifique os parâmetros informados.");
                setListaRelatorio(new ArrayList());
                Map<String, Object> parametros = new HashMap<String, Object>();

                parametros.put("tipoVisualizacao", new Integer(this.tipoVisualizacao));
                if ((this.codigoCategoria != null) && (this.codigoCategoria > 0)) {
                    CategoriaProdutoVO categoria = getFacade().getCategoriaProduto().consultarPorChavePrimaria(this.codigoCategoria, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    parametros.put("nomeCategoria", categoria.getDescricao());
                }
                if (this.statusProduto > 0) {
                    parametros.put("statusProduto", this.statusProduto == 1 ? "Ativo" : "Inativo");
                }
                if (this.ordenacao == 1)
                    parametros.put("ordenacao", "Nome do Produto");
                else if (this.ordenacao == 2)
                    parametros.put("ordenacao", "Código do Produto");
                else
                    parametros.put("ordenacao", "Quantidade de Estoque");

                if (getValorImpresso() == 2) {
                    parametros.put("valorImpresso", "Custo da última compra");
                } else if (getValorImpresso() == 1) {
                    parametros.put("valorImpresso", "Preço de venda");
                } else {
                    parametros.put("valorImpresso", "Custo médio");
                }
                JRBeanCollectionDataSource totalizadores = new JRBeanCollectionDataSource(montarTotalizadoresCategoria(listaProdutos));

                parametros.put("listaObjetos", listaProdutos);
                parametros.put("totalizadores", totalizadores);
                parametros.put("nomeRelatorio", "RelatorioBalancoProduto");
                parametros.put("nomeEmpresa", empresaVO.getNome());
                parametros.put("nomeDesignIReport", getDesignIReportRelatorio());
                parametros.put("SUBREPORT_DIR", getCaminhoSubRelatorio());
                parametros.put("SUBREPORT_DIR1", getCaminhoSubRelatorio());
                parametros.put("SUBREPORT_DIR2", getCaminhoSubRelatorio());

                apresentarRelatorioObjetos(parametros);
                setMensagemDetalhada("", "");
                setMensagemID("msg_entre_prmrelatorio");
                setSucesso(true);
                setErro(false);
                setProcessandoOperacao(false);
            }
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        } finally {
            setProcessandoOperacao(false);
        }
    }
    public ArrayList<PosicaoEstoqueTotalizadorTO> montarTotalizadoresCategoria( List<ProdutoEstoqueVO> listaProdutos){

        ArrayList<PosicaoEstoqueTotalizadorTO> lista = new ArrayList<PosicaoEstoqueTotalizadorTO>();
        listaProdutos = Ordenacao.ordenarLista(listaProdutos, "ordernarCategoriaProduto");
        int categoria = -1;
        int index = -1;
        for(ProdutoEstoqueVO item :listaProdutos ){
            if(categoria == item.getProduto().getCategoriaProduto().getCodigo() ){
                PosicaoEstoqueTotalizadorTO totalizadorTO = lista.get(index);
                totalizadorTO.setEstoque(totalizadorTO.getEstoque()+item.getEstoque());
                totalizadorTO.setValorTotal(item.getValorTotalEstoque()+totalizadorTO.getValorTotal());
                lista.set(index,totalizadorTO);
            }else{
                index++;
                PosicaoEstoqueTotalizadorTO totalizadorTO = new PosicaoEstoqueTotalizadorTO();
                totalizadorTO.setValorTotal(item.getValorTotalEstoque());
                totalizadorTO.setEstoque(item.getEstoque());
                totalizadorTO.setDescricao(UteisValidacao.emptyNumber(item.getProduto().getCategoriaProduto().getCodigo()) ? "SEM CATEGORIA" : item.getProduto().getCategoriaProduto().getDescricao());
                categoria = item.getProduto().getCategoriaProduto().getCodigo();
                lista.add(index,totalizadorTO);
            }
        }

        return lista;

    }



    public String getIrPara() {
        return retorno;
    }

    public static String getDesignIReportRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio"
                + File.separator + "estoque" + File.separator + "RelatorioEstoqueProduto.jrxml");
    }


    public static String getCaminhoSubRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio"
                + File.separator + "estoque" + File.separator);
    }

    public String getRetorno() {
        return retorno;
    }

    public void setRetorno(String retorno) {
        this.retorno = retorno;
    }

    public int getTipoVisualizacao() {
        return tipoVisualizacao;
    }

    public void setTipoVisualizacao(int tipoVisualizacao) {
        this.tipoVisualizacao = tipoVisualizacao;
    }


    public Integer getCodigoEmpresa() {
        return codigoEmpresa;
    }

    public void setCodigoEmpresa(Integer codigoEmpresa) {
        this.codigoEmpresa = codigoEmpresa;
    }

    public List getListaSelectItemEmpresa() {
        return listaSelectItemEmpresa;
    }

    public void setListaSelectItemEmpresa(List listaSelectItemEmpresa) {
        this.listaSelectItemEmpresa = listaSelectItemEmpresa;
    }

    public Integer getCodigoCategoria() {
        return codigoCategoria;
    }

    public void setCodigoCategoria(Integer codigoCategoria) {
        this.codigoCategoria = codigoCategoria;
    }

    public List getListaSelectItemCategoria() {
        return listaSelectItemCategoria;
    }

    public void setListaSelectItemCategoria(List listaSelectItemCategoria) {
        this.listaSelectItemCategoria = listaSelectItemCategoria;
    }

    public int getStatusProduto() {
        return statusProduto;
    }

    public void setStatusProduto(int statusProduto) {
        this.statusProduto = statusProduto;
    }

    public int getOrdenacao() {
        return ordenacao;
    }

    public void setOrdenacao(int ordenacao) {
        this.ordenacao = ordenacao;
    }


    public int getValorImpresso() {
        return valorImpresso;
    }

    public void setValorImpresso(int valorImpresso) {
        this.valorImpresso = valorImpresso;
    }


}

//relatorio
package controle.plano;

import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import javax.servlet.*;
import javax.servlet.http.*;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.acesso.ColetorVO;
import negocio.comuns.acesso.LocalAcessoVO;
import negocio.comuns.acesso.enumerador.MeioIdentificacaoEnum;
import negocio.comuns.financeiro.AulaAvulsaDiariaVO;
import controle.arquitetura.SuperControle;
import negocio.comuns.acesso.AcessoClienteVO;
import negocio.comuns.contrato.ContratoAssinaturaDigitalVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.financeiro.VendaAvulsaVO;
import negocio.comuns.plano.PlanoTextoPadraoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Criptografia;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.acesso.AcessoCliente;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.ContratoAssinaturaDigital;
import negocio.facade.jdbc.contrato.ContratoTextoPadrao;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.financeiro.AulaAvulsaDiaria;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.ReciboPagamento;
import negocio.facade.jdbc.financeiro.VendaAvulsa;
import negocio.facade.jdbc.plano.PlanoTextoPadrao;
import negocio.facade.jdbc.plano.Produto;
import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRBeanArrayDataSource;
import net.sf.jasperreports.engine.data.JRXmlDataSource;
import net.sf.jasperreports.engine.util.JRLoader;
import servicos.discovery.ClientDiscoveryDataDTO;
import servicos.discovery.DiscoveryMsService;

import static controle.arquitetura.SuperControle.Crypt_ALGORITM;
import static controle.arquitetura.SuperControle.Crypt_KEY_Contrato;
import static negocio.comuns.utilitarias.Uteis.getUrl;
import static negocio.facade.jdbc.arquitetura.FacadeManager.getFacade;
import servlet.arquitetura.RecuperadorParametrosServlet;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.impl.autenticacaoMs.AutenticacaoMsService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

/**
 * Servlet responsável por gerar a visualização do relatório e apresentá-lo ao usuário.
 * É utilizado por todos os relatórios do sistema com esta finalidade. É capaz de receber os
 * dados do relatório e compilar o relatório final utilizando o JasperReport.
 */
public class VisualizarContrato extends HttpServlet {

    private HashMap parametrosRelatorio;
    private String nomeRelatorio;
    private String nomeEmpresa;
    private String nomeDesignIReport;
    private String caminhoParserXML;
    private String xmlDados;
    private String tipoRelatorio;
    private String mensagemRel;
    private String tipoImplementacao;
    private List listaObjetos;

    /**
     * Rotina responsável por obter o diretório real da aplicação Web em execução.
     * É importante acessar este diretório para que seja possível utilizar recursos
     * existentes nos pacotes da aplicação.
     */
    public String obterCaminhoBaseAplicacao(HttpServletRequest request, HttpServletResponse response) throws Exception {
        ServletContext servletContext = (ServletContext) this.getServletContext();
        String caminhoBaseAplicacao = servletContext.getRealPath(request.getContextPath());
        File caminhoBaseAplicacaoFile = new File(caminhoBaseAplicacao);
        caminhoBaseAplicacao = caminhoBaseAplicacaoFile.getParent() + File.separator + "WEB-INF" + File.separator + "classes";
        return caminhoBaseAplicacao;
    }

    /**
     * Rotina responsável por obter o diretório real da aplicação Web em execução.
     * É importante acessar este diretório para que seja possível utilizar recursos
     * existentes nos pacotes da aplicação.
     */
    public String obterCaminhoWebAplicacao(HttpServletRequest request, HttpServletResponse response) throws Exception {
        ServletContext servletContext = (ServletContext) this.getServletContext();
        String caminhoBaseAplicacao = servletContext.getRealPath(request.getContextPath());
        File caminhoBaseAplicacaoFile = new File(caminhoBaseAplicacao);
        caminhoBaseAplicacao = caminhoBaseAplicacaoFile.getParent();
        return caminhoBaseAplicacao;
    }

    public JasperPrint gerarRelatorioJasperPrintXML(HttpServletRequest request, HttpServletResponse response, String xmlDados, String caminhoParserXML, String nomeDesignIReport) throws Exception {
        JRXmlDataSource jrxmlds = new JRXmlDataSource(new ByteArrayInputStream(xmlDados.getBytes(), 0, xmlDados.length()), caminhoParserXML);

        //File arquivoIReport = new File(obterCaminhoBaseAplicacao(request, response) + File.separator + nomeDesignIReport);
        //JasperDesign jasperDesign = JRXmlLoader.load( arquivoIReport.getAbsoluteFile() );

        // Gerando relatório jasperReport com datasource do tipo XML
        //JasperReport jasperReport = JasperCompileManager.compileReport( jasperDesign );
        String nomeJasperReportDesignIReport = nomeDesignIReport.substring(0, nomeDesignIReport.lastIndexOf(".")) + ".jasper";
        File arquivoIReport = new File(obterCaminhoBaseAplicacao(request, response) + File.separator + nomeJasperReportDesignIReport);
        JasperReport jasperReport = (JasperReport) JRLoader.loadObject(arquivoIReport);
        JasperPrint print = JasperFillManager.fillReport(jasperReport, getParametrosRelatorio(), jrxmlds);
        return print;
    }

    public JasperPrint gerarRelatorioJasperPrintObjeto(HttpServletRequest request, HttpServletResponse response, String nomeDesignIReport) throws Exception {

        JRDataSource jr = new JRBeanArrayDataSource(getListaObjetos().toArray());
        String nomeJasperReportDesignIReport = nomeDesignIReport.substring(0, nomeDesignIReport.lastIndexOf(".")) + ".jasper";
        File arquivoIReport = new File(obterCaminhoBaseAplicacao(request, response) + File.separator + nomeJasperReportDesignIReport);

        JasperReport jasperReport = (JasperReport) JRLoader.loadObject(arquivoIReport);

        JasperPrint print = JasperFillManager.fillReport(jasperReport, getParametrosRelatorio(), jr);

        return print;
    }

//    protected void visualizarRelatorio( HttpServletRequest request, 
//                                            HttpServletResponse response, 
//                                            String texto) throws ServletException, IOException, Exception {
//        response.setContentType("text/html;charset=ISO-8859-1");
//        
//        // Gerar relatório no padrão HTML
//        JRHtmlExporter jrhtmlexporter = new JRHtmlExporter();
//        
//        Map imagesMap = new HashMap();
//        request.getSession().setAttribute("IMAGES_MAP", imagesMap);
//        jrhtmlexporter.setParameter(JRHtmlExporterParameter.IMAGES_MAP, imagesMap);
//        jrhtmlexporter.setParameter(JRHtmlExporterParameter.IMAGES_URI,"image?image=");
//        jrhtmlexporter.setParameter(JRExporterParameter.JASPER_PRINT, print);
//        jrhtmlexporter.setParameter(JRExporterParameter.OUTPUT_WRITER, response.getWriter() );
//        jrhtmlexporter.setParameter(JRExporterParameter.CHARACTER_ENCODING, "ISO-8859-1" );
//        jrhtmlexporter.exportReport();
//    }
    protected void visualizarRelatorio(HttpServletRequest request,
            HttpServletResponse response,
            String texto) throws ServletException, IOException, Exception {
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();
        out.println(texto);
//        out.println("<head>");
//        out.println("<title>Contrato</title>");
//        out.println("</head>");
//        
//        String nomePDF = getNomeRelatorio() + ".pdf";
//        String nomeRelPDF = "relatorio" + File.separator + nomePDF;
//        File pdfFile = new File(obterCaminhoWebAplicacao(request, response) + File.separator + nomeRelPDF);
//        
//        if (pdfFile.exists()) {
//            try {
//                pdfFile.delete();
//            }
//            catch (Exception e) {
//                DateFormat formatador = DateFormat.getDateInstance(DateFormat.MEDIUM);
//                String dataStr = formatador.format(negocio.comuns.utilitarias.Calendario.hoje());
//                nomePDF = getNomeRelatorio() + dataStr + ".pdf";
//                nomeRelPDF = "relatorios" + File.separator + nomePDF;
//                pdfFile = new File(obterCaminhoWebAplicacao(request, response) + File.separator + nomeRelPDF);
//            }
//        }
//        
//        JRPdfExporter jrpdfexporter = new JRPdfExporter();
//        jrpdfexporter.setParameter(JRExporterParameter.JASPER_PRINT, print);
//        jrpdfexporter.setParameter(JRExporterParameter.OUTPUT_FILE, pdfFile );
//        
//        jrpdfexporter.exportReport();
//        
//        out.println("<frameset cols=\"*\" frameborder=\"NO\" border=\"0\" framespacing=\"0\">");
//        String urlAplicacao = request.getRequestURI().toString();
//        urlAplicacao = urlAplicacao.substring(0, urlAplicacao.lastIndexOf("/"));
//        out.println("<frame src=\"" + urlAplicacao + "/relatorio/" + nomePDF + "\" name=\"mainFrame\">");
//        out.println("</frameset>");
//        out.println("<noframes><body>");
//        out.println("</body></noframes>");
//        out.println("</html>");
        out.close();
    }

    /** Processes requests for both HTTP <code>GET</code> and <code>POST</code> methods.
     * @param request servlet request
     * @param response servlet response
     */
    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException, Exception {
        String chave = request.getParameter("k");
        Integer contrato = UteisValidacao.converterInteiro(request.getParameter("c"));
        Boolean confirmacaoAssinatura = UteisValidacao.converterBooleano(request.getParameter("confirmacaoAssinatura"));
        String texto = "";
        Integer vendaAvulsa = UteisValidacao.converterInteiro(request.getParameter("va"));
        Integer aulaAvulsaDiaria = UteisValidacao.converterInteiro(request.getParameter("avd"));
        Integer produto = UteisValidacao.converterInteiro(request.getParameter("p"));
        String parcelas = request.getParameter("cp"); //comprovante de compra
        Boolean telaNova = UteisValidacao.converterBooleano(request.getParameter("telaNova"));
        Boolean exibirBotaoAssinatura = UteisValidacao.converterBooleano(request.getParameter("exibirBotaoAssinatura"));
        Integer aditivo = UteisValidacao.converterInteiro(request.getParameter("aditivo"));
        if (!UteisValidacao.emptyString(chave) &&
                !UteisValidacao.emptyString(parcelas)) {
            PlanoTextoPadrao planoTextoPadraoDAO = null;
            MovParcela movParcelaDAO = null;
            try (Connection con = new DAO().obterConexaoEspecifica(chave)) {
                //Conexao.guardarConexaoForJ2SE(chave, con);
                planoTextoPadraoDAO = new PlanoTextoPadrao(con);
                movParcelaDAO = new MovParcela(con);

                StringBuilder parceBuscar = new StringBuilder();
                for (String pac : parcelas.split("-")) {
                    if (!UteisValidacao.emptyString(pac)) {
                        parceBuscar.append(",").append(pac);
                    }
                }

                List<MovParcelaVO> listaParcelas = movParcelaDAO.consultar("select * from movparcela where codigo in (" + parceBuscar.toString().replaceFirst(",", "") + ") ", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                listaParcelas.forEach(movParcelaVO -> {
                    movParcelaVO.setParcelaEscolhida(true);
                });
                texto = planoTextoPadraoDAO.gerarComprovanteCompra(listaParcelas);
            } finally {
                planoTextoPadraoDAO = null;
                movParcelaDAO = null;
            }
        } else if (!UteisValidacao.emptyString(chave) &&
                !UteisValidacao.emptyNumber(contrato)) {
            ContratoTextoPadrao contratoTextoPadraoDAO = null;
            try (Connection con = new DAO().obterConexaoEspecifica(chave)) {
                //Conexao.guardarConexaoForJ2SE(chave, con);
                contratoTextoPadraoDAO = new ContratoTextoPadrao(con);
                texto = contratoTextoPadraoDAO.consultarHtmlContrato(contrato,true, true, telaNova, aditivo);
            } finally {
                contratoTextoPadraoDAO = null;
            }
        } else if (!UteisValidacao.emptyString(chave) &&
                (!UteisValidacao.emptyNumber(vendaAvulsa) || !UteisValidacao.emptyNumber(aulaAvulsaDiaria)) &&
                !UteisValidacao.emptyNumber(produto)) {
            VendaAvulsa vendaAvulsaDAO;
            AulaAvulsaDiaria aulaAvulsaDiariaDAO;
            PlanoTextoPadrao planoTextoPadraoDAO;
            Pessoa pessoaDAO;
            MovProduto movProdutoDAO;
            MovParcela movParcelaDAO;
            Empresa empresaDAO;
            Usuario usuarioDAO;
            MovPagamento movPagamentoDAO;
            Produto produtoDAO;
            try (Connection con = new DAO().obterConexaoEspecifica(chave)) {
                vendaAvulsaDAO = new VendaAvulsa(con);
                aulaAvulsaDiariaDAO = new AulaAvulsaDiaria(con);
                planoTextoPadraoDAO = new PlanoTextoPadrao(con);
                pessoaDAO = new Pessoa(con);
                movProdutoDAO = new MovProduto(con);
                movParcelaDAO = new MovParcela(con);
                empresaDAO = new Empresa(con);
                usuarioDAO = new Usuario(con);
                movPagamentoDAO = new MovPagamento(con);
                produtoDAO = new Produto(con);

                ProdutoVO prod = produtoDAO.consultarPorChavePrimaria(produto, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (!UteisValidacao.emptyNumber(vendaAvulsa)) {
                    VendaAvulsaVO vendaAvulsaVO = vendaAvulsaDAO.consultarPorChavePrimaria(vendaAvulsa, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
                    vendaAvulsaVO.setParcela(UteisValidacao.emptyList(vendaAvulsaVO.getMovParcelaVOs()) ? new MovParcelaVO() : vendaAvulsaVO.getMovParcelaVOs().get(0));
                    vendaAvulsaVO.setTextoPadrao(planoTextoPadraoDAO.consultarPorChavePrimaria(prod.getContratoTextoPadrao(), Uteis.NIVELMONTARDADOS_TODOS));
                    String contratoEpoca = vendaAvulsaDAO.obterContratoProdutoTextoPadrao(vendaAvulsaVO.getCodigo(), prod.getCodigo());

                    if (!UteisValidacao.emptyString(contratoEpoca)) {
                        vendaAvulsaVO.getTextoPadrao().setTexto(contratoEpoca);
                    }
                    vendaAvulsaVO.getParcela().setPessoa(pessoaDAO.consultarPorChavePrimaria(
                            vendaAvulsaVO.getParcela().getPessoa().getCodigo(),
                            Uteis.NIVELMONTARDADOS_TODOS));
                    vendaAvulsaVO.setMovProdutoVOs(movProdutoDAO.consultarPorCodigoParcela(vendaAvulsaVO.getParcela().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    vendaAvulsaVO.setEmpresa(
                            empresaDAO.consultarPorChavePrimaria(
                                    vendaAvulsaVO.getEmpresa().getCodigo(),
                                    Uteis.NIVELMONTARDADOS_TODOS));
                    vendaAvulsaVO.setResponsavel(
                            usuarioDAO.consultarPorChavePrimaria(
                                    vendaAvulsaVO.getResponsavel().getCodigo(),
                                    Uteis.NIVELMONTARDADOS_DADOSBASICOS));

                    List<MovPagamentoVO> pagamentos = movPagamentoDAO.consultarPagamentoDeUmaParcela(
                            vendaAvulsaVO.getParcela().getCodigo().intValue(), false,
                            Uteis.NIVELMONTARDADOS_TODOS);

                    vendaAvulsaVO.getTextoPadrao().substituirTagsTextoPadraoVendaAvulsa(chave, con, vendaAvulsaVO, null, pagamentos, vendaAvulsaVO.getEmpresa().getDescMoeda(), request, telaNova, false, prod.getCodigo());
                    texto = (String) request.getSession().getAttribute("textoRelatorio");
                } else {
                    AulaAvulsaDiariaVO aulaAvulsaDiariaVO = aulaAvulsaDiariaDAO.consultarPorChavePrimaria(aulaAvulsaDiaria, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    aulaAvulsaDiariaVO.setMovParcelaVOs(movParcelaDAO.consultarPorCodigoAulaAvulsaDiariaLista(aulaAvulsaDiaria, null, false, null, null, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    aulaAvulsaDiariaVO.setParcela(UteisValidacao.emptyList(aulaAvulsaDiariaVO.getMovParcelaVOs()) ? new MovParcelaVO() : aulaAvulsaDiariaVO.getMovParcelaVOs().get(0));
                    aulaAvulsaDiariaVO.setTextoPadrao(planoTextoPadraoDAO.consultarPorChavePrimaria(prod.getContratoTextoPadrao(), Uteis.NIVELMONTARDADOS_TODOS));
                    String contratoEpoca = aulaAvulsaDiariaDAO.obterContratoProdutoTextoPadraoDiaria(aulaAvulsaDiariaVO.getCodigo(), prod.getCodigo());

                    if (!UteisValidacao.emptyString(contratoEpoca)) {
                        aulaAvulsaDiariaVO.getTextoPadrao().setTexto(contratoEpoca);
                    }
                    aulaAvulsaDiariaVO.getParcela().setPessoa(pessoaDAO.consultarPorChavePrimaria(
                            aulaAvulsaDiariaVO.getParcela().getPessoa().getCodigo(),
                            Uteis.NIVELMONTARDADOS_TODOS));
                    aulaAvulsaDiariaVO.setMovProdutoVOs(movProdutoDAO.consultarPorCodigoParcela(aulaAvulsaDiariaVO.getParcela().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    aulaAvulsaDiariaVO.setEmpresa(
                            empresaDAO.consultarPorChavePrimaria(
                                    aulaAvulsaDiariaVO.getEmpresa().getCodigo(),
                                    Uteis.NIVELMONTARDADOS_TODOS));
                    aulaAvulsaDiariaVO.setResponsavel(
                            usuarioDAO.consultarPorChavePrimaria(
                                    aulaAvulsaDiariaVO.getResponsavel().getCodigo(),
                                    Uteis.NIVELMONTARDADOS_DADOSBASICOS));

                    List<MovPagamentoVO> pagamentos = movPagamentoDAO.consultarPagamentoDeUmaParcela(
                            aulaAvulsaDiariaVO.getParcela().getCodigo().intValue(), false,
                            Uteis.NIVELMONTARDADOS_TODOS);

                    aulaAvulsaDiariaVO.getTextoPadrao().substituirTagsTextoPadraoVendaAvulsa(chave, con, null, aulaAvulsaDiariaVO, pagamentos, aulaAvulsaDiariaVO.getEmpresa().getDescMoeda(), request, telaNova, false, prod.getCodigo());
                    texto = (String) request.getSession().getAttribute("textoRelatorio");
                }
            } finally {
                vendaAvulsaDAO = null;
                planoTextoPadraoDAO = null;
                pessoaDAO = null;
                movProdutoDAO = null;
                empresaDAO = null;
                usuarioDAO = null;
                movPagamentoDAO = null;
                produtoDAO = null;
                aulaAvulsaDiariaDAO = null;
                movParcelaDAO = null;
            }
        } else {
            texto = (String) request.getSession().getAttribute("textoRelatorio");
        }
        if (Boolean.TRUE.equals(exibirBotaoAssinatura)) {
            texto = adicionaBotaoAssinatura(texto, contrato, chave, request);
        }

        if (confirmacaoAssinatura) {
            String textoAtual = montarRelatorioConfirmacaoAssinatura(request, texto, chave, contrato);
            visualizarRelatorio(request, response, textoAtual);
        } else {
            visualizarRelatorio(request, response, texto);
        }
        //new ImprimirContrato(texto)
        //new ImprimirContrato(texto);
    }

    private String montarRelatorioConfirmacaoAssinatura(HttpServletRequest request, String texto, String chave, Integer contrato) throws Exception {
        texto = texto.replace("<input id=\"imprimir\"type=\"image\" src=\"./imagens/imprimirContrato.png\" name=\"imprimir\" alt=\"Imprimir Contrato\"onclick=\"window.print();\"/>", "");
        String geradoEmPor = "Gerado dia " + Uteis.getDataComHHMM(Calendario.getInstance(Long.parseLong(request.getParameter("geradoEm"))).getTime()).replace("-", "&agrave;s")+" pelo usu&aacute;rio " + request.getParameter("geradoPor");
        byte[] logoEmpresaByte;
        String nomeCliente;
        String periodoContrato;
        String valorContrato;
        String nomePlano;
        String assinouComoContratante;
        String autenticacaoTelefone;
        String ipDispositivoAssinou;
        String urlFotoDocumentoPessoa;
        String urlAssinaturaDigital;
        String historicoAcessos;
        String historicoProgramas;
        String historicoPagamentos;
        try (Connection con = new DAO().obterConexaoEspecifica(chave)) {
            ContratoTextoPadrao contratoTextoPadraoDAO = new ContratoTextoPadrao(con);
            Contrato contratoDAO = new Contrato(con);
            ContratoAssinaturaDigital contratoAssinaturaDigitalDAO = new ContratoAssinaturaDigital(con);
            ZillyonWebFacade zillyonWebFacade = new ZillyonWebFacade(con);
            ReciboPagamento reciboPagamentoDAO = new ReciboPagamento(con);
            MovPagamento movPagamentoDAO = new MovPagamento(con);
            ContratoAssinaturaDigitalVO contratoAssinaturaDigitalVO = contratoAssinaturaDigitalDAO.consultarPorCodigoContrato(contrato);
            ContratoVO cont = contratoDAO.consultarPorChavePrimaria(contrato, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
            logoEmpresaByte = zillyonWebFacade.getEmpresa().obterFoto(chave,
                    cont.getEmpresa().getCodigo(), MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO);
            nomeCliente = cont.getCliente().getNome_Apresentar();
            periodoContrato = Uteis.getData(cont.getVigenciaDe()) + " - " + Uteis.getData(cont.getVigenciaAteAjustada());
            valorContrato = "R$ "+Uteis.formatarValorEmReal(cont.getValorFinal());
            nomePlano = cont.getPlanoDescricao_Apresentar();
            assinouComoContratante = Uteis.getDataComHHMM(contratoAssinaturaDigitalVO.getLancamento());
            urlFotoDocumentoPessoa  = Uteis.getPaintFotoDaNuvem(contratoAssinaturaDigitalVO.getDocumentos());
            urlAssinaturaDigital = Uteis.getPaintFotoDaNuvem(contratoAssinaturaDigitalVO.getAssinatura());
            ipDispositivoAssinou = contratoAssinaturaDigitalVO.getIp();
            autenticacaoTelefone = contratoAssinaturaDigitalVO.getTipoAutenticacao();

            texto = contratoTextoPadraoDAO.montarContratoAditivoAssinaturaConfirmacao(texto, contrato);
            historicoAcessos = montarHistoricoAcessosConfirmacaoAssinatura(con, cont);
            historicoProgramas = montarHistoricoProgramasConfirmacaoAssinatura(chave, cont);
            historicoPagamentos = montarHistoricoPagamentosConfirmacaoAssinatura(reciboPagamentoDAO, movPagamentoDAO, cont);
        }

        String textoAtual = obterHtmlConfirmacaoAssinatura(!UteisValidacao.emptyString(historicoAcessos), !UteisValidacao.emptyString(historicoProgramas), !UteisValidacao.emptyString(historicoPagamentos))
                .replace("{logoEmpresaBase64}", Base64.getEncoder().encodeToString(logoEmpresaByte))
                .replace("{contratoHtml}", texto)
                .replace("{geradoEmPor}", geradoEmPor)
                .replace("{nome}", nomeCliente)
                .replace("{codigoContrato}", String.valueOf(contrato))
                .replace("{periodoContrato}", periodoContrato)
                .replace("{valorTotalContrato}", valorContrato)
                .replace("{nomePlano}", nomePlano)
                .replace("{assinouContratante}", assinouComoContratante)
                .replace("{telefoneAutenticacao}", autenticacaoTelefone != null ? autenticacaoTelefone : "")
                .replace("{ipDispositivoAssinou}", ipDispositivoAssinou != null ? ipDispositivoAssinou : "")
                .replace("{urlFotoDocumentoPessoa}", urlFotoDocumentoPessoa)
                .replace("{urlAssinaturaDigital}", urlAssinaturaDigital)
                .replace("{historicoAcessos}", historicoAcessos)
                .replace("{historicoProgramas}", historicoProgramas)
                .replace("{historicoPagamentos}", historicoPagamentos);
        return textoAtual;
    }

    private String montarHistoricoAcessosConfirmacaoAssinatura(Connection con, ContratoVO cont) throws Exception {
        List<AcessoClienteVO> acessosCliente = new ArrayList<>();
        String sql = "select codigo, sentido, localacesso, coletor, dthrentrada, dthrsaida, meioIdentificacaoEntrada from AcessoCliente "
                + "where (cliente = ?) "
                + "order by dthrentrada DESC, codigo ASC "
                + "offset 0 limit 100";
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            stm.setInt(1, cont.getCliente().getCodigo());
            try (ResultSet resultTabela = stm.executeQuery()) {
                while (resultTabela.next()) {
                    AcessoClienteVO obj = new AcessoClienteVO();
                    obj.setCodigo(resultTabela.getInt("codigo"));
                    obj.setSentido(resultTabela.getString("sentido"));
                    obj.setLocalAcesso(new LocalAcessoVO());
                    obj.getLocalAcesso().setCodigo(resultTabela.getInt("localacesso"));
                    obj.setColetor(new ColetorVO());
                    obj.getColetor().setCodigo(resultTabela.getInt("coletor"));
                    obj.setDataHoraEntrada(resultTabela.getTimestamp("dthrentrada"));
                    obj.setDataHoraSaida(resultTabela.getTimestamp("dthrsaida"));
                    obj.setMeioIdentificacaoEntrada(MeioIdentificacaoEnum.getMeioIdentificacao(resultTabela.getInt("meioIdentificacaoEntrada")));
                    acessosCliente.add(obj);
                }
            }
        }

        StringBuilder historialAcessos = new StringBuilder();
        if (acessosCliente != null && !acessosCliente.isEmpty()) {
            Ordenacao.ordenarListaReverse(acessosCliente, "dataHoraEntrada");
            for (AcessoClienteVO acesso : acessosCliente) {
                historialAcessos.append("<tr><td>")
                        .append(acesso.getCodigo()).append("</td><td>")
                        .append(acesso.getSentido()).append("</td><td>")
                        .append(acesso.getLocalAcesso().getDescricao()).append("</td><td>")
                        .append(acesso.getColetor().getDescricao()).append("</td><td>")
                        .append(acesso.getDataHoraEntrada() != null ? Uteis.getDataComHHMM(acesso.getDataHoraEntrada()) : "-").append("</td><td>")
                        .append(acesso.getDataHoraSaida() != null ? Uteis.getDataComHHMM(acesso.getDataHoraSaida()) : "-").append("</td><td>")
                        .append(acesso.getMeioIdentificacaoEntrada_Apresentar()).append("</td></tr>\n");
            }
        }
        return historialAcessos.toString();
    }

    private String montarHistoricoProgramasConfirmacaoAssinatura(String chave, ContratoVO cont) {
        StringBuilder historialProgramas = new StringBuilder();
        try {
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("Authorization", AutenticacaoMsService.token(chave));

            String urlTreino = getUrlTreino(chave) + "/prest/psec/fichas/fichasExecutadas/" + cont.getCliente().getCodigo();
            String response = ExecuteRequestHttpService.executeHttpRequest(urlTreino, null, headers, "GET", "UTF-8");
            JSONArray array = new JSONObject(response).getJSONArray("content");

            if(array.length() == 0) {
                return historialProgramas.toString();
            }

            for (int i = 0; i < array.length(); i++) {
                JSONObject ficha = array.getJSONObject(i);
                historialProgramas.append("<tr><td>")
                        .append(ficha.getString("nomePrograma")).append("</td><td>")
                        .append(ficha.getString("nome")).append("</td><td>")
                        .append(ficha.getString("ultimaExecucao")).append("</td></tr>\n");
            }
        } catch (Exception ignore) {
        }
        return historialProgramas.toString();
    }

    private String montarHistoricoPagamentosConfirmacaoAssinatura(ReciboPagamento reciboPagamentoDAO, MovPagamento movPagamentoDAO, ContratoVO cont) throws Exception {
        List<ReciboPagamentoVO> recibosCliente = reciboPagamentoDAO.consultarPorCodigoContrato(cont.getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
        StringBuilder historicoPagamentos = new StringBuilder();
        if (recibosCliente != null && !recibosCliente.isEmpty()) {
            Ordenacao.ordenarListaReverse(recibosCliente, "data");
            for (ReciboPagamentoVO recibo : recibosCliente) {
                List<MovPagamentoVO> pagamentosDesteRecibo = movPagamentoDAO.consultarPorCodigoRecibo(recibo.getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);
                historicoPagamentos.append("<tr><td>")
                        .append(cont.getCodigo()).append("</td><td>")
                        .append(recibo.getCodigo()).append("</td><td>")
                        .append(Uteis.getDataComHHMM(recibo.getData())).append("</td><td>")
                        .append(pagamentosDesteRecibo.stream().map(p -> p.getFormaPagamento().getDescricao()).collect(Collectors.joining(", "))).append("</td><td>")
                        .append(pagamentosDesteRecibo.stream().map(p ->
                                ( p.getOperadoraCartaoVO() != null && !UteisValidacao.emptyString(p.getOperadoraCartaoVO().getDescricao()) ? (p.getOperadoraCartaoVO().getDescricao() + ", AUT: "+ p.getAutorizacaoCartao()) : "-")).collect(Collectors.joining("; "))).append("</td><td>")
                        .append(pagamentosDesteRecibo.stream().map(p -> !UteisValidacao.emptyString(p.getNsu()) ? p.getNsu() : "-").collect(Collectors.joining(", "))).append("</td><td>")
                        .append(recibo.getValor_Apresentar()).append("</td></tr>\n");
            }
        }
        return historicoPagamentos.toString();
    }
    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">

    /** Handles the HTTP <code>GET</code> method.
     * @param request servlet request
     * @param response servlet response
     */
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        try {
            processRequest(request, response);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /** Handles the HTTP <code>POST</code> method.
     * @param request servlet request
     * @param response servlet response
     */
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        try {
            processRequest(request, response);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /** Returns a short description of the servlet.
     */
    public String getServletInfo() {
        return "Short description";
    }
    // </editor-fold>

    private String adicionaBotaoAssinatura(String texto, Integer codigoContrato, String chave, HttpServletRequest request) {
        try {
            ClientDiscoveryDataDTO dataDTO = DiscoveryMsService.urlsChave(chave);
            String urlSistema = dataDTO.getServiceUrls().getZwUrl();

            String token = "codigoContrato:" + codigoContrato + "; chave:" + chave;
            token = Criptografia.encrypt(token, Crypt_KEY_Contrato, Crypt_ALGORITM);

            String url = urlSistema + "/faces/assinaturaDigital.jsp?token=" + token + "&whats=true";
            String userAgent = request.getHeader("User-Agent");
            boolean isMobile = !UteisValidacao.emptyString(userAgent)
                    && userAgent.toLowerCase().contains("mobile");

            StringBuilder builder = new StringBuilder();
            builder.append(texto);
            builder.append("<div style='text-align: center; margin-top: 20px; " + (isMobile ? "margin-bottom: 100px;" : "") + "'>");
            builder.append("<a href='").append(url).append("' target='_blank'>");
            builder.append("<button style='padding: 10px 20px; font-size: " + (isMobile ? "32px" : "16px") + "; cursor: pointer;'>Assinar Contrato</button>");
            builder.append("</a>");
            builder.append("</div>");

            return builder.toString();
        } catch (Exception e) {
            e.printStackTrace();
            return texto;
        }
    }

    private String obterHtmlConfirmacaoAssinatura(boolean historicoAcessos, boolean historicoProgramas, boolean historicoPagamentos) {
        StringBuilder html = new StringBuilder();

        html.append("<!DOCTYPE html>\n");
        html.append("<html lang=\"pt-BR\">\n");
        html.append("<head>\n");
        html.append("  <meta charset=\"UTF-8\">\n");
        html.append("  <title>Comprovante de presta&ccedil;&atilde;o de servi&ccedil;o</title>\n");
        html.append("  <style>\n");
        html.append("    @media print { .no-print { display: none !important; } }\n");
        html.append("    body { font-family: Arial, sans-serif; margin: 40px; font-size: 14px; color: #333; }\n");
        html.append("    h1, h2, h3 { margin-bottom: 8px; }\n");
        html.append("    .logo { display: flex; justify-content: space-between; align-items: center; }\n");
        html.append("    .section { margin-top: 30px; }\n");
        html.append("    .section-title { font-size: 16px; font-weight: bold; margin-bottom: 10px; border-bottom: 1px solid #ccc; padding-bottom: 5px; }\n");
        html.append("    .grid { display: grid; grid-template-columns: 1fr; gap: 10px 40px; }\n");
        html.append("    .gerado { font-size: 12px; font-weight: 700; font-family: Nunito Sans; color: #94979E}\n");
        html.append("    .assinatura { margin-top: 20px; }\n");
        html.append("    .assinatura-campo { font-family: 'Courier New', monospace; padding: 5px; border: 1px solid #ccc; width: 300px; margin-top: 10px; }\n");
        html.append("    .foto-doc { height: 350px; width: 250px; margin-top: 10px; }\n");
        html.append("    .foto-ass { height: 50px; border-radius: 6px; }\n");
        html.append("    table { width: 100%; border-collapse: collapse; margin-top: 10px; font-size: 13px; }\n");
        html.append("    th, td { border: 1px solid #ccc; padding: 6px 8px; text-align: left; }\n");
        html.append("    th { background-color: #f5f5f5; }\n");
        html.append("  </style>\n");
        html.append("</head>\n");
        html.append("<body>\n");

        html.append("<div style=\"text-align: center\">\n");
        html.append("<button class=\"no-print\" onclick=\"window.print()\">\uD83D\uDDA8\uFE0F Imprimir PDF</button>");
        html.append("</div>\n");

        html.append("<div class=\"logo\">\n");
        html.append("  <img src=\"data:image/png;base64,{logoEmpresaBase64}\" alt=\"Logo Pacto\">\n");
        html.append("  <div style=\"margin-left: 10px\">\n");
        html.append("    <h2>Comprovante de presta&ccedil;&atilde;o de servi&ccedil;o</h2>\n");
        html.append("    <span class=\"gerado\">{geradoEmPor}</span>\n");
        html.append("  </div>\n");
        html.append("</div>\n");

        html.append("<div class=\"section\">\n");
        html.append("  <div class=\"section-title\">Informa&ccedil;&otilde;es do contrato</div>\n");
        html.append("  <div class=\"grid\">\n");
        html.append("    <span><strong>Cliente:</strong> {nome}</span>\n");
        html.append("    <span><strong>Contrato:</strong> {codigoContrato}</span>\n");
        html.append("    <span><strong>Per&iacute;odo do contrato:</strong> {periodoContrato}</span>\n");
        html.append("    <span><strong>Valor do contrato:</strong> {valorTotalContrato}</span>\n");
        html.append("    <span><strong>Plano:</strong> {nomePlano}</span>\n");
        html.append("    <span><strong>Assinou como contratante em:</strong> {assinouContratante}</span>\n");
        html.append("    <span><strong>Autentica&ccedil;&atilde;o via telefone:</strong> {telefoneAutenticacao}</span>\n");
        html.append("    <span><strong>IP do dispositivo de assinatura:</strong> {ipDispositivoAssinou}</span>\n");
        html.append("  </div>\n");
        html.append("  <img class=\"foto-doc\" src=\"{urlFotoDocumentoPessoa}\" alt=\"Foto documento\">\n");
        html.append("  <div class=\"assinatura\">\n");
        html.append("    <div class=\"assinatura-campo\">\n");
        html.append("    <span>Assinatura:</span>\n");
        html.append("    <div><img class=\"foto-ass\" src=\"{urlAssinaturaDigital}\" alt=\"Foto assinatura\"></div>\n");
        html.append("    </div>\n");
        html.append("  </div>\n");
        html.append("</div>\n");

        html.append("<div class=\"section\">\n");
        html.append("  <div class=\"section-title\">Contrato</div>\n");
        html.append("  {contratoHtml}\n");
        html.append("</div>\n");

        if (historicoAcessos) {
            html.append("<div class=\"section\">\n");
            html.append("  <div class=\"section-title\">Hist&oacute;rico de acessos</div>\n");
            html.append("  <table>\n");
            html.append("    <thead><tr><th>C&oacute;digo</th><th>Sentido da catraca</th><th>Local</th><th>Coletor</th><th>Entrada</th><th>Saida</th><th>Meio de Identifica&ccedil;&atilde;o</th></tr></thead>\n");
            html.append("    <tbody>\n");
            html.append("      {historicoAcessos} ");
            html.append("    </tbody>\n");
            html.append("  </table>\n");
            html.append("</div>\n");
        }

        if (historicoProgramas) {
            html.append("<div class=\"section\">\n");
            html.append("  <div class=\"section-title\">Hist&oacute;rico de execu&ccedil;&atilde;o de treino</div>\n");
            html.append("  <table>\n");
            html.append("    <thead><tr><th>Nome do programa</th><th>Ficha</th><th>Execu&ccedil;&atilde;o</th></tr></thead>\n");
            html.append("    <tbody>\n");
            html.append("      {historicoProgramas} \n");
            html.append("    </tbody>\n");
            html.append("  </table>\n");
            html.append("</div>\n");
        }

        if (historicoPagamentos) {
            html.append("<div class=\"section\">\n");
            html.append("  <div class=\"section-title\">Hist&oacute;rico de pagamentos</div>\n");
            html.append("  <table>\n");
            html.append("    <thead><tr><th>Contrato</th><th>N&ordm; Recibo</th><th>Data de pagamento</th><th>Forma de pagamento</th><th>Dados do cart&atilde;o</th><th>NSU</th><th>Valor</th></tr></thead>\n");
            html.append("    <tbody>\n");
            html.append("      {historicoPagamentos} ");
            html.append("    </tbody>\n");
            html.append("  </table>\n");
            html.append("</div>\n");
        }

        html.append("<div style=\"text-align: center\">\n");
        html.append("<button class=\"no-print\" onclick=\"window.print()\">\uD83D\uDDA8\uFE0F Imprimir PDF</button>");
        html.append("</div>\n");

        html.append("</body>\n</html>");
        return html.toString();
    }

    public String getUrlTreino(String chave) {
        try {
            return PropsService.getPropertyValue(chave, PropsService.urlTreinoWeb);
        } catch (Exception ex) {
            Logger.getLogger(SuperControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return "";
    }

    public HashMap getParametrosRelatorio() {
        return parametrosRelatorio;
    }

    public void setParametrosRelatorio(HashMap parametrosRelatorio) {
        this.parametrosRelatorio = parametrosRelatorio;
    }

    public String getNomeDesignIReport() {
        return nomeDesignIReport;
    }

    public void setNomeDesignIReport(String nomeDesignIReport) {
        this.nomeDesignIReport = nomeDesignIReport;
    }

    public String getCaminhoParserXML() {
        return caminhoParserXML;
    }

    public void setCaminhoParserXML(String caminhoParserXML) {
        this.caminhoParserXML = caminhoParserXML;
    }

    public String getXmlDados() {
        return xmlDados;
    }

    public void setXmlDados(String xmlDados) {
        this.xmlDados = xmlDados;
    }

    public String getTipoRelatorio() {
        return tipoRelatorio;
    }

    public void setTipoRelatorio(String tipoRelatorio) {
        this.tipoRelatorio = tipoRelatorio;
    }

    public String getNomeRelatorio() {
        return nomeRelatorio;
    }

    public void setNomeRelatorio(String nomeRelatorio) {
        this.nomeRelatorio = nomeRelatorio;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public String getMensagemRel() {
        return mensagemRel;
    }

    public void setMensagemRel(String mensagemRel) {
        this.mensagemRel = mensagemRel;
    }

    public String getTipoImplementacao() {
        return tipoImplementacao;
    }

    public void setTipoImplementacao(String tipoImplementacao) {
        this.tipoImplementacao = tipoImplementacao;
    }

    public List getListaObjetos() {
        return listaObjetos;
    }

    public void setListaObjetos(List listaObjetos) {
        this.listaObjetos = listaObjetos;
    }
}

package negocio.comuns.financeiro;

import negocio.comuns.utilitarias.UteisValidacao;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

public class HistoricoImportacaoContaVO {

	private Integer codigo;

	private Date dataImportacao;
	private String usuario;
	private String origemArquivo;
	private Integer totalImportados;
	private Integer totalErros;
	private List<String> mensagensResultado = new ArrayList<>();
	private boolean error;
	private String mensagemError;

	private int pagina = 1;
	private int tamanhoPagina = 5;


	public HistoricoImportacaoContaVO() {
		this.mensagensResultado =new ArrayList<>();
		this.dataImportacao = new Date();
	}



	public Integer getCodigo() {
		return codigo;
	}

	public void setCodigo(Integer codigo) {
		this.codigo = codigo;
	}


	public Date getDataImportacao() {
		return dataImportacao;
	}

	public void setDataImportacao(Date dataImportacao) {
		this.dataImportacao = dataImportacao;
	}

	public String getUsuario() {
		return usuario;
	}

	public void setUsuario(String usuario) {
		this.usuario = usuario;
	}

	public String getOrigemArquivo() {
		return origemArquivo;
	}

	public void setOrigemArquivo(String origemArquivo) {
		this.origemArquivo = origemArquivo;
	}

	public Integer getTotalImportados() {
		return totalImportados;
	}

	public void setTotalImportados(Integer totalImportados) {
		this.totalImportados = totalImportados;
	}

	public Integer getTotalErros() {
		return totalErros;
	}

	public void setTotalErros(Integer totalErros) {
		this.totalErros = totalErros;
	}

	public List<String> getMensagensResultado() {
		return mensagensResultado;
	}

	public void setMensagensResultado(List<String> mensagensResultado) {
		this.mensagensResultado = mensagensResultado;
	}

	public boolean isError() {
		return error;
	}

	public void setError(boolean error) {
		this.error = error;
	}

	public String getMensagemError() {
		return mensagemError;
	}

	public void setMensagemError(String mensagemError) {
		this.mensagemError = mensagemError;
	}

	public void registrarException(Exception e) {
		setError(true);
		setMensagemError(e.getMessage());
		getMensagensResultado().add( e.getMessage());
	}

	public void registrarSucesso() {
        getMensagensResultado().add("Importação concluída com sucesso!");
	}

	public void registrarMensagem(String msg) {
		getMensagensResultado().add(msg);
	}

	public List<MensagemComIndiceVO> getPaginaMensagensComIndice() {
		List<MensagemComIndiceVO> resultado = new ArrayList<>();
		int fromIndex = (pagina - 1) * tamanhoPagina;
		int toIndex = Math.min(fromIndex + tamanhoPagina, mensagensResultado.size());

		for (int i = fromIndex; i < toIndex; i++) {
			resultado.add(new MensagemComIndiceVO(i + 1, mensagensResultado.get(i)));
		}

		return resultado;
	}

	public void proximaPagina() {
		if (pagina * tamanhoPagina < getMensagensResultado().size()) {
			pagina++;
		}
	}

	public void paginaAnterior() {
		if (pagina > 1) {
			pagina--;
		}
	}

	public int getPagina() {
		return pagina;
	}

	public void setPagina(int pagina) {
		this.pagina = pagina;
	}

	public int getTamanhoPagina() {
		return tamanhoPagina;
	}

	public void setTamanhoPagina(int tamanhoPagina) {
		this.tamanhoPagina = tamanhoPagina;
	}
}
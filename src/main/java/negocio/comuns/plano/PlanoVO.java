package negocio.comuns.plano;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.FKJson;
import annotations.arquitetura.ListJson;
import annotations.arquitetura.Lista;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.ce.comuns.enumerador.Mes;
import br.com.pactosolucoes.enumeradores.TipoHorarioCreditoTreinoEnum;
import io.opencensus.internal.Utils;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.ItemGestaoNotasTO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.plano.enumerador.TipoReajuste;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.PlanoTipoVO;
import negocio.facade.jdbc.financeiro.FormaPagamento;
import negocio.facade.jdbc.plano.PlanoRecorrencia;
import negocio.interfaces.basico.EmpresaInterfaceFacade;
import servicos.vendasonline.ContratoSiteService;

import java.sql.Connection;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.regex.Pattern;

/**
 * Reponsável por manter os dados da entidade Plano. Classe do tipo VO - Value
 * Object composta pelos atributos da entidade com visibilidade protegida e os
 * métodos de acesso a estes atributos. Classe utilizada para apresentar e
 * manter em memória os dados desta entidade.
 *
 * @see SuperVO
 */
public class PlanoVO extends SuperVO {

    /**
     * Atributo responsável por manter os objetos da classe
     * <code>PlanoModalidade</code>.
     */
    @ChavePrimaria
    private Integer codigo;
    private String descricao;
    private Double percentualMultaCancelamento;
    private Date vigenciaDe;
    private Date vigenciaAte;
    private Date ingressoAte;
    private Boolean permitePagarComBoleto;
    private Boolean bolsa;
    private Boolean recorrencia = false;
    @ChaveEstrangeira
    @FKJson
    private ProdutoVO produtoPadraoGerarParcelasContrato;
    @FKJson
    private ProdutoVO produtoTaxaCancelamento;
    @ChaveEstrangeira
    @FKJson
    private PlanoTextoPadraoVO planoTextoPadrao;
    @ChaveEstrangeira
    @FKJson
    private PlanoTextoPadraoVO reciboTextoPadrao;
    @Lista
    @ListJson(clazz = PlanoDuracaoVO.class)
    private List<PlanoDuracaoVO> planoDuracaoVOs;
    @Lista
    @ListJson(clazz = PlanoComposicaoVO.class)
    private List planoComposicaoVOs;
    @Lista
    @ListJson(clazz = PlanoModalidadeVO.class)
    private List<PlanoModalidadeVO> planoModalidadeVOs;
    /**
     * Atributo responsável por manter os objetos da classe
     * <code>PlanoHorario</code>.
     */
    @Lista
    @ListJson(clazz = PlanoHorarioVO.class)
    private List<PlanoHorarioVO> planoHorarioVOs;
    @ChaveEstrangeira
    @FKJson
    private EmpresaVO empresa;
    @Lista
    @ListJson(clazz = PlanoProdutoSugeridoVO.class)
    private List planoProdutoSugeridoVOs;
    @Lista
    private List planoProdutoPadraoVOs;
    private Boolean defasado;
    @NaoControlarLogAlteracao
    private List<String> listaDiasVencimento;
    private String diasVencimentoProrata;
    private boolean prorataObrigatorio;
    @FKJson
    private DescontoVO descontoAntecipado;
    @NaoControlarLogAlteracao
    private PlanoRecorrenciaVO planoRecorrencia;
    //atributo usado com plano de recorrência
    //ao marcar esse atributo na tela mostrará
    //a aba de recorrência e a aba de duração ficará invisível.
    private Boolean comissao = true;
    private Boolean regimeRecorrencia = false;
    @NaoControlarLogAlteracao
    private Boolean escolhido = false;
    @NaoControlarLogAlteracao
    private Double valorAcrescentar = 0.0;
    @NaoControlarLogAlteracao
    private Double porcentagemAcrescentar = 0.0;
    @NaoControlarLogAlteracao
    private Date dataVencimentoParcela = null;
    @NaoControlarLogAlteracao
    private Date dataLancamentoContratoInicio = null;
    @NaoControlarLogAlteracao
    private Date dataLancamentoContratoFim = null;
    @NaoControlarLogAlteracao
    private Boolean contratosMatricula = false;
    @NaoControlarLogAlteracao
    private Boolean contratosRematricula = false;
    @NaoControlarLogAlteracao
    private Boolean contratosRenovacao = false;
    @NaoControlarLogAlteracao
    private TipoReajuste tipoReajuste = TipoReajuste.VALOR;
    @NaoControlarLogAlteracao
    private String descricaoProgresso;
    @NaoControlarLogAlteracao
    private boolean selecionado = false;
    @NaoControlarLogAlteracao
    private String correspondenciaZD;
    private Boolean cobrarAdesaoSeparada = Boolean.FALSE;
    private Integer nrVezesParcelarAdesao;
    private Boolean cobrarProdutoSeparado = Boolean.FALSE;
    private Integer nrVezesParcelarProduto;
    @Lista
    @ListJson(clazz = PlanoExcecaoVO.class)
    private List<PlanoExcecaoVO> planoExcecaoVOs = new ArrayList<PlanoExcecaoVO>();
    private Boolean site = false;
    private Boolean permitirVendaPlanoSiteNoBalcao = false;
    private Boolean totem = false;
    private Boolean permitirVendaPlanoTotemNoBalcao = false;
    private Date inicioMinimoContrato = null;
    private Boolean parcelamentoOperadora = false;
    private boolean parcelamentoOperadoraDuracao = false;
    @NaoControlarLogAlteracao
    private String descricaoEncantamento = "";
    private Boolean renovavelAutomaticamente = false;
    private boolean renovarAutomaticamenteComDesconto = false;
    private Integer quantidadeMaximaFrequencia = 0;
    private Integer convidadosPorMes = 0;
    private Integer tipoFrequencia;
    private Boolean renovarProdutoObrigatorio = false;
    private boolean renovarAnuidadeAutomaticamente = false;
    private boolean vendaCreditoTreino = false;
    private boolean creditoTreinoNaoCumulativo = false;
    private boolean creditoSessao = false;
    private Integer diaDoMesDescontoBoletoPagAntecipado = 0;
    private Double porcentagemDescontoBoletoPagAntecipado = 0.0;
    private Boolean aceitaDescontoPorPlano = false;
    private Double valorDescontoBoletoPagAntecipado;
    @NaoControlarLogAlteracao
    public static String TIPO_ALTERACAO_DETALHADO = "DETALHADO";
    @NaoControlarLogAlteracao
    public static String TIPO_ALTERACAO_SIMPLES = "SIMPLES";
    @ChaveEstrangeira
    @FKJson
    private PlanoTextoPadraoVO termoAceite;
    private boolean restringirMarcacaoAulasColetivas = false;
    private int restringirQtdMarcacaoPorDia;
    private boolean faturar = true;
    private Integer pontos;
    private ConvenioCobrancaVO convenioCobrancaPrivateLabel;
    private Boolean dividirManutencaoParcelasEA = false;
    @Lista
    @ListJson(clazz = PlanoEmpresaVO.class)
    private List<PlanoEmpresaVO> empresas = new ArrayList<>();
    @NaoControlarLogAlteracao
    private boolean atualizarEmpresas;
    private boolean permitirAcessoSomenteNaEmpresaVendeuContrato = false;
    private PlanoTipoVO planoTipo;
    private boolean planoPersonal;
    private boolean apresentaVendaRapida;
    private boolean renovarAutomaticamenteApenasCondicaoPagamentoRecorrencia = false;
    protected Boolean permiteSituacaoAtestadoContrato = true;
    private Integer qtdSemanasAno = 48;
    private int maximoVezesParcelar = 1;
    private boolean naoRenovarContratoParcelaVencidaAberto = false;
    private Boolean aceitaDescontoExtra = true;
    private Boolean restringeVendaPorCategoria = false;
    private List<PlanoCategoriaVO> planoCategoriaVOs = new ArrayList<>();
    private int restringirQtdMarcacaoPorDiaGeral;
    private boolean permitirAcessoRedeEmpresa = false;
    private Integer quantidadeCompartilhamentos;

    private Boolean restringirMarcacaoAulaPorNrVezesModalidade;
    private Boolean apresentarPactoFlow;

    private Boolean renovarAutomaticamenteUtilizandoValorBaseContrato = false;
    private Boolean permitirTurmasVendasOnline = false;
    private String videoSiteUrl;
    private String observacaoSite;
    private Boolean obrigatorioInformarCartaoCreditoVenda = false;
    private boolean renovarComDescontoTotem = false;
    private boolean permitirCompartilharPLanoNoSite = false;
    private Integer diasBloquearCompraMesmoPlano;
    private Integer planoDiferenteRenovacao;
    private Integer horarioPlanoDiferenteRenovacao;
    private String modalidadesPlanoDiferenteRenovacao;
    private Integer duracaoPlanoDiferenteRenovacao;
    private Integer condicaoPagPlanoDiferenteRenovacao;
    private boolean acessoRedeEmpresasEspecificas = false;
    private boolean bloquearRecompra = false;

    private Boolean permitirTransferenciaDeCredito = false;
    private Integer quantidadeATransferirPermitidaPorAluno = 0;
    private Date contratosEncerramDia;
    private Boolean habilitarIa = false;


    public PlanoVO() {
        super();
        inicializarDados();
    }

    public String getEmpresa_Apresentar() {
        return getEmpresa().getNome();
    }

    public static void validarDados(PlanoVO obj) throws Exception {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getEmpresa() == null || obj.getEmpresa().getCodigo() == 0) {
            throw new ConsistirException("O campo EMPRESA (Plano) deve ser informado.");
        }
        if (obj.getDescricao().trim().equals("")) {
            throw new ConsistirException("O campo DESCRIÇÃO (Plano) deve ser informado.");
        }
        if (obj.getVigenciaDe() == null) {
            throw new ConsistirException("O campo VIGÊNCIA DE (Dados Básicos) deve ser informado.", "dadosPlano");
        }
        if (obj.getVigenciaAte() == null) {
            throw new ConsistirException("O campo VIGÊNCIA ATÉ (Dados Básicos) deve ser informado.", "dadosPlano");
        }
        SimpleDateFormat formatador = new SimpleDateFormat();
        formatador.applyPattern("yyyy");
        String ano = formatador.format(obj.getVigenciaAte());
        if (new Long(ano) > 2099) {
            throw new ConsistirException("O campo VIGÊNCIA ATÉ (Dados Básicos) não pode ser maior que 2099.", "dadosPlano");
        }
        if (obj.getVigenciaAte().before(obj.getVigenciaDe())) {
            throw new ConsistirException("O campo VIGÊNCIA ATÉ (Dados Básicos) não pode ser antes da data do campo de  VIGÊNCIA DE.", "dadosPlano");
        }
        if (obj.getIngressoAte() == null) {
            throw new ConsistirException("O campo INGRESSO ATÉ (Dados Básicos) deve ser informado.", "dadosPlano");
        }
        if (obj.getIngressoAte().before(obj.getVigenciaDe())) {
            throw new ConsistirException("O campo INGRESSO ATÉ (Dados Básicos) não pode ser ANTES da data do campo de  VIGÊNCIA DE.", "dadosPlano");
        }
        if (obj.getIngressoAte().after(obj.getVigenciaAte())) {
            throw new ConsistirException("O campo INGRESSO ATÉ (Dados Básicos) não pode ser DEPOIS da data do campo de  VIGÊNCIA ATÉ.", "dadosPlano");
        }
        if (obj.getProdutoPadraoGerarParcelasContrato() == null || obj.getProdutoPadraoGerarParcelasContrato().getCodigo() == null || obj.getProdutoPadraoGerarParcelasContrato().getCodigo() == 0) {
            throw new ConsistirException("O campo PRODUTO PADRÃO GERAR PARCELAS CONTRATO (Dados Básicos) deve ser informado.", "dadosPlano");
        }
        if (obj.getPlanoTextoPadrao() == null || obj.getPlanoTextoPadrao().getCodigo() == 0) {
            throw new ConsistirException("O campo PLANO TEXTO PADRÃO (Dados Básicos) deve ser informado.", "dadosPlano");
        }

        if (obj.getProdutoTaxaCancelamento() == null || obj.getProdutoTaxaCancelamento().getCodigo() == 0) {
            throw new ConsistirException("O campo PRODUTO TAXA CANCELAMENTO (Configurações Gerais) deve ser informado.", "dadosConfiguracoesGerais");
        }
        //João Alcides: não faz sentido colocar esta validação, muitos clientes não informam esta taxa, mas como
        // são obrigados, colocam 0,*********, e o sistema grava uma notação matemática, gerando inconsistência no cadastro
//        if (obj.getPercentualMultaCancelamento() == null || obj.getPercentualMultaCancelamento() == 0) {
//            throw new ConsistirException("O campo PERCENTUAL DA MULTA CANCELAMENTO (Configurações Gerais) deve ser informado.");
//        }
        if (obj.getPermitePagarComBoleto()) {
            FormaPagamentoVO formaPagamento = new FormaPagamento().consultarPorTipoFormaPagamento("BB", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (formaPagamento == null) {
                throw new ConsistirException("Antes de Gravar um Plano que PERMITE PAGAR COM BOLETO deve Cadastrar uma FORMA DE PAGAMENTO DO TIPO BOLETO", "dadosPlano");
            }
        }
        if (obj.getPlanoModalidadeVOs().isEmpty() && !obj.isPlanoPersonal()) {
            throw new ConsistirException("O PLANO deve ter no MÍNIMO uma MODALIDADE CADASTRADA.(Modalidades) ", "dadosModalidade");
        }
        //Com regime de recorrência não é incluída duração
        if (!obj.getRegimeRecorrencia()) {
            if (obj.getPlanoDuracaoVOs().isEmpty()) {
                throw new ConsistirException("O PLANO deve ter no MÍNIMO uma DURAÇÃO CADASTRADA. (Durações)", "dadosDuracao");
            } else {
                Iterator i = obj.getPlanoDuracaoVOs().iterator();
                while (i.hasNext()) {
                    PlanoDuracaoVO planoDuracaoVO = (PlanoDuracaoVO) i.next();

                    if (planoDuracaoVO.getValorTotal() < 0.0) {
                        throw new ConsistirException("A DURAÇÃO de " + planoDuracaoVO.getNumeroMeses() + " meses está com valor total negativo. (Durações)", "dadosDuracao");
                    }
                    if (planoDuracaoVO.getPlanoCondicaoPagamentoVOs().isEmpty()) {
                        throw new ConsistirException("A DURAÇÃO de " + planoDuracaoVO.getNumeroMeses() + " meses deve ter no MÍNIMO uma CONDIÇÃO DE PAGAMENTO. (Durações)", "dadosDuracao");
                    }
                }
            }
        }
        if (obj.getPlanoHorarioVOs().isEmpty() && !obj.isPlanoPersonal()) {
            throw new ConsistirException("O PLANO deve ter um HORÁRIO CADASTRADO. (Horário)", "dadosHorario");
        }
        if (obj.getPlanoProdutoSugeridoVOs().isEmpty()) {
            throw new ConsistirException("É necessário Adicionar 3 produtos sugeridos. MATRÍCULA, REMATRÍCULA e RENOVAÇÃO.", "dadosProdutoSugerido");
        }
        if ((obj.getQuantidadeMaximaFrequencia() != null) && (obj.getQuantidadeMaximaFrequencia() > 0)){
            if (obj.getQuantidadeMaximaFrequencia() > 7)
                throw new ConsistirException("Informe um valor menor ou igual a 7 para o campo 'permitir acesso à academia somente x dias por semana' (Dados Básicos).", "dadosPlano");
        }
        boolean existeMatricula = false;
        boolean existeRematricula = false;
        boolean existeRenovacao = false;
        Iterator e = obj.getPlanoProdutoSugeridoVOs().iterator();
        while (e.hasNext()) {
            PlanoProdutoSugeridoVO planoProdudoSugerido = (PlanoProdutoSugeridoVO) e.next();
            if (planoProdudoSugerido.getProduto().getTipoProduto().equals("MA")) {
                existeMatricula = true;
            }
            if (planoProdudoSugerido.getProduto().getTipoProduto().equals("RE")) {
                existeRematricula = true;
            }
            if (planoProdudoSugerido.getProduto().getTipoProduto().equals("RN")) {
                existeRenovacao = true;
            }
        }
        if (!existeMatricula) {
            throw new ConsistirException("É necessário adicionar nos PRODUTOS SUGERIDOS o produto MATRÍCULA.", "dadosProdutoSugerido");
        }
        if (!existeRematricula) {
            throw new ConsistirException("É necessário adicionar nos PRODUTOS SUGERIDOS o produto REMATRÍCULA.", "dadosProdutoSugerido");
        }
        if (!existeRenovacao) {
            throw new ConsistirException("É necessário adicionar nos PRODUTOS SUGERIDOS o produto RENOVAÇÃO.", "dadosProdutoSugerido");
        }
        if (obj.isProrataObrigatorio() && obj.getDiasVencimentoProrata().trim().isEmpty()) {
            throw new ConsistirException("É necessário adicionar pelo Menos uma Data de Vencimento para o Plano.", "dadosConfiguracoesGerais");
        }
        if (
                (obj.getPlanoDuracaoVOs().size() > 0 && obj.getRegimeRecorrencia() == false)
                        && (obj.getPorcentagemDescontoBoletoPagAntecipado() > 0 && obj.getValorDescontoBoletoPagAntecipado() > 0))
        {
            throw new ConsistirException("É permitido configurar apenas um valor de desconto para pagamento atecipado do boleto.");
        }

        List<PlanoExcecaoVO> excecoes = obj.getPlanoExcecaoVOs();
        for (PlanoExcecaoVO exc : excecoes) {
            if (UteisValidacao.emptyNumber(exc.getCodigo())) {
                exc.setPlano(obj);
            }
            if (UteisValidacao.emptyString(exc.getModalidade().getNome())
                    && UteisValidacao.emptyString(exc.getPacote().getDescricao())) {
                throw new ConsistirException(String.format("PLANO EXCEÇÃO deve possuir uma MODALIDADE ou PACOTE"), "abaExcecaoModalidade");
            }
            if (!UteisValidacao.emptyString(exc.getModalidade().getNome())
                    && UteisValidacao.emptyNumber(exc.getVezesSemana())) {
                throw new ConsistirException(String.format("PLANO EXCEÇÃO deve possuir um número de VEZES/SEMANA"), "abaExcecaoModalidade");
            }
            if (UteisValidacao.emptyNumber(exc.getDuracao())) {
                throw new ConsistirException(String.format("PLANO EXCEÇÃO deve possuir uma DURAÇÃO em Meses"), "abaExcecaoModalidade");
            }
            if (!UteisValidacao.emptyNumber(exc.getCodigo())
                    && UteisValidacao.emptyNumber(exc.getPlano().getCodigo())) {
                throw new ConsistirException(String.format("PLANO EXCEÇÃO da Modalidade %s %s Vezes/Semana deve possuir um PLANO relacionado", exc.getModalidade().getNome(), exc.getVezesSemana()), "abaExcecaoModalidade");
            }
            if (UteisValidacao.emptyNumber(exc.getValor())) {
                throw new ConsistirException(String.format("PLANO EXCEÇÃO da Modalidade %s %s Vezes/Semana deve possuir um VALOR de Referência para o Mensal válido", exc.getModalidade().getNome(), exc.getVezesSemana()), "abaExcecaoModalidade");
            }
        }

        if (obj.isVendaCreditoTreino()){
            int totHorarioLivre = 0;
            int totHorarioTurma = 0;
            for (PlanoHorarioVO planoHorarioVO: obj.getPlanoHorarioVOs()){
               if (planoHorarioVO.getHorario().isLivre())
                 totHorarioLivre++;
               else
                 totHorarioTurma++;
            }
            boolean configurouCreditoLivre = false;
            boolean configurouCreditoTurma = false;
            if (!obj.isCreditoSessao()) {
                for (PlanoDuracaoVO planoDuracaoVO : obj.getPlanoDuracaoVOs()) {
                    if (planoDuracaoVO.getListaPlanoDuracaoCreditoTreino() == null || planoDuracaoVO.getListaPlanoDuracaoCreditoTreino().isEmpty()) {
                        throw new ConsistirException(String.format("PLANO DURAÇÃO de %s Meses deve possuir pelo menos uma Configuração de Créditos treino", planoDuracaoVO.getNumeroMeses()), "abaDuracao");
                    }
                    for (PlanoDuracaoCreditoTreinoVO planoDuracaoCreditoTreinoVO : planoDuracaoVO.getListaPlanoDuracaoCreditoTreino()) {
                        if (planoDuracaoCreditoTreinoVO.getTipoHorarioCreditoTreinoEnum() == TipoHorarioCreditoTreinoEnum.HORARIO_TURMA) {
                            configurouCreditoTurma = true;
                        } else {
                            configurouCreditoLivre = true;
                        }
                    }
                }
            }

            if (obj.isCreditoSessao()) {

                if (obj.getPlanoHorarioVOs().size() > 1) {
                    throw new ConsistirException("Plano de crédito por sessão é necessário incluir somente um horário da turma na aba 'Horários'.", "abaHorario");
                }

                for (PlanoHorarioVO planoHorarioVO: obj.getPlanoHorarioVOs()){
                    if (planoHorarioVO.getHorario().isLivre()) {
                        throw new ConsistirException("Para plano de crédito por sessão é o horário deve ser um horário da turma na aba 'Horários'.", "abaHorario");
                    }
                }
            }

            if (configurouCreditoTurma) {
                if (totHorarioTurma == 0){
                    throw new ConsistirException("Foi configurado valores para Crédito com horário da turma, portanto é necessário incluir o horário da turma na aba 'Horários'. ", "abaHorario");
                }
                if ((!configurouCreditoLivre) && (totHorarioLivre > 0)){
                    throw new ConsistirException("Foi configurado apenas valores para Crédito com horário da turma, portanto é necessário incluir somente um horário da turma na aba 'Horários'. ", "abaHorario");
                }
            }
            if (configurouCreditoLivre) {
                if (totHorarioLivre == 0){
                    throw new ConsistirException("Foi configurado valores para Crédito com horário livre, portanto é necessário incluir o horário livre na aba 'Horários'. ", "abaHorario");
                }
                if ((!configurouCreditoTurma) && (totHorarioTurma > 0)){
                    throw new ConsistirException("Foi configurado apenas valores para Crédito com horário livre, portanto é necessário incluir somente um horário livre na aba 'Horários'. ", "abaHorario");
                }
            }
            if (obj.getPlanoHorarioVOs().size() > 2){
                if ((configurouCreditoLivre) && (configurouCreditoTurma)) {
                    throw new ConsistirException("Foi configurado valores para crédito com horário livre e horário da turma, portanto é necessário incluir somente dois horários, sendo um 'Horário Livre' e outro 'Horário da Turma'. ", "abaHorario");
                }else if(configurouCreditoLivre)
                    throw new ConsistirException("Foi configurado somente valores para Crédito com horário livre, portanto é necessário incluir somente um horário livre na aba 'Horários'. ", "abaHorario");
                else
                    throw new ConsistirException("Foi configurado somente valores para Crédito com horário da turma, portanto é necessário incluir somente um horário da turma na aba 'Horários'. ", "abaHorario");
            }

            if (obj.isCreditoTreinoNaoCumulativo()) {
                for (PlanoDuracaoVO planoDuracaoVO: obj.getPlanoDuracaoVOs()){
                    for (PlanoDuracaoCreditoTreinoVO planoDuracaoCreditoTreinoVO: planoDuracaoVO.getListaPlanoDuracaoCreditoTreino()){
                        if (UteisValidacao.emptyNumber(planoDuracaoCreditoTreinoVO.getQuantidadeCreditoMensal())){
                            throw new ConsistirException("A DURAÇÃO de " + planoDuracaoVO.getNumeroMeses() + " meses não está com a quantidade de crédito mensal.", "abaDuracao");
                        }
                    }
                }
            }
        }

        if(null != obj.getBolsa() && !obj.isPlanoPersonal() && !obj.getBolsa().booleanValue()){
            boolean existeModalidadePaga = false;
            for (Object o : obj.getPlanoModalidadeVOs()) {
                PlanoModalidadeVO pm = (PlanoModalidadeVO) o;
                if (pm.getModalidade().getValorMensalFinal() > 0) {
                    existeModalidadePaga = true;
                    break;
                }
            }

            if(!existeModalidadePaga){
                throw new ConsistirException("Você adicionou modalidade com valor zerado na aba \"modalidades\". Como este plano não foi marcado como \"PLANO BOLSA\", esta operação não é permitida. Para prosseguir, remova a modalidade zerada do plano, ou então adicione mais uma modalidade junto que tenha valor, ou então coloque valor no cadastro dela.", "dadosModalidade");
            }

        }

    }

    public void realizarUpperCaseDados() {
        setDescricao(getDescricao().toUpperCase());
    }

    public void inicializarDados() {
        setCodigo(0);
        setDescricao("");
        setPercentualMultaCancelamento(0.0);
        setVigenciaDe(Calendario.hoje());
        setVigenciaAte(Uteis.somarCampoData(Calendario.hoje(),Calendar.MONTH,12));
        setIngressoAte(Uteis.somarCampoData(Calendario.hoje(),Calendar.MONTH,12));;
        setPermitePagarComBoleto(false);
        setBolsa(false);
        setProdutoPadraoGerarParcelasContrato(new ProdutoVO());
        setProdutoTaxaCancelamento(new ProdutoVO());
        setPlanoTextoPadrao(new PlanoTextoPadraoVO());
        setReciboTextoPadrao(new PlanoTextoPadraoVO());
        setPlanoDuracaoVOs(new ArrayList<>());
        setPlanoComposicaoVOs(new ArrayList<>());
        setPlanoModalidadeVOs(new ArrayList<>());
        setPlanoHorarioVOs(new ArrayList<>());
        setPlanoProdutoSugeridoVOs(new ArrayList<>());
        setPlanoProdutoPadraoVOs(new ArrayList<>());
        setDefasado(false);
        setDiasVencimentoProrata("");
        setListaDiasVencimento(new ArrayList<>());
        setProrataObrigatorio(false);
        setDescontoAntecipado(new DescontoVO());
        //atributo usado para recorrencia
        setPlanoRecorrencia(new PlanoRecorrenciaVO());
        setPontos(0);
    }

    public void adicionarObjPlanoProdutoSugeridoVOs(PlanoProdutoSugeridoVO obj) throws Exception {
        PlanoProdutoSugeridoVO.validarDados(obj);
        int index = 0;
        Iterator i = getPlanoProdutoSugeridoVOs().iterator();
        while (i.hasNext()) {
            PlanoProdutoSugeridoVO objExistente = (PlanoProdutoSugeridoVO) i.next();
            if (objExistente.getProduto().getCodigo().equals(obj.getProduto().getCodigo())) {
                getPlanoProdutoSugeridoVOs().set(index, obj);
                return;
            }
            if (objExistente.getProduto().getTipoProduto().equals(TipoProduto.MATRICULA.getCodigo())
                    && obj.getProduto().getTipoProduto().equals(TipoProduto.MATRICULA.getCodigo())) {
                throw new ConsistirException("Já tem um produto de matricula adicionado ao plano.");
            }
            if (objExistente.getProduto().getTipoProduto().equals(TipoProduto.REMATRICULA.getCodigo())
                    && obj.getProduto().getTipoProduto().equals(TipoProduto.REMATRICULA.getCodigo())) {
                throw new ConsistirException("Já tem um produto de rematricula adicionado ao plano.");
            }
            if (objExistente.getProduto().getTipoProduto().equals(TipoProduto.RENOVACAO.getCodigo())
                    && obj.getProduto().getTipoProduto().equals(TipoProduto.RENOVACAO.getCodigo())) {
                throw new ConsistirException("Já tem um produto de renovação adicionado ao plano.");
            }
            if (getSite()) {
                if (!obj.isObrigatorio()) {
                    throw new ConsistirException("O Plano Site só permite produtos obrigatórios!");
                }
            }
            if(getSite() && getRegimeRecorrencia() && obj.getProduto().getTipoProduto().equals(TipoProduto.MATRICULA.getCodigo()) && obj.getValorProduto() > 0.0){
                throw new ConsistirException("Você está configurando um plano que é de \"Regime Recorrência\" e ao mesmo tempo \"Site\",portanto o valor da matrícula não pode ser maior que zero.");
            }
            index++;
        }
        getPlanoProdutoSugeridoVOs().add(obj);
    }

    public void adicionarObjPlanoProdutoPadraoVOs(PlanoProdutoSugeridoVO obj) throws Exception {
        PlanoProdutoSugeridoVO.validarDados(obj);
        int index = 0;
        Iterator i = getPlanoProdutoPadraoVOs().iterator();
        while (i.hasNext()) {
            PlanoProdutoSugeridoVO objExistente = (PlanoProdutoSugeridoVO) i.next();
            if (objExistente.getProduto().getCodigo().equals(obj.getProduto().getCodigo())) {
                getPlanoProdutoPadraoVOs().set(index, obj);
                return;
            }
            index++;
        }
        getPlanoProdutoPadraoVOs().add(obj);
    }

    public void excluirObjPlanoProdutoSugeridoVOs(Integer produto) throws Exception {
        int index = 0;
        Iterator i = getPlanoProdutoSugeridoVOs().iterator();
        while (i.hasNext()) {
            PlanoProdutoSugeridoVO objExistente = (PlanoProdutoSugeridoVO) i.next();
            if (objExistente.getProduto().getCodigo().equals(produto)) {
                getPlanoProdutoSugeridoVOs().remove(index);
                return;
            }
            index++;
        }
    }

    public PlanoProdutoSugeridoVO consultarObjPlanoProdutoSugeridoVO(Integer produto) throws Exception {
        Iterator i = getPlanoProdutoSugeridoVOs().iterator();
        while (i.hasNext()) {
            PlanoProdutoSugeridoVO objExistente = (PlanoProdutoSugeridoVO) i.next();
            if (objExistente.getProduto().getCodigo().equals(produto)) {
                return objExistente;
            }
        }
        return null;
    }

    public void adicionarObjPlanoHorarioVOs(PlanoHorarioVO obj) throws Exception {
        PlanoHorarioVO.validarDados(obj);
        int index = 0;
        Iterator i = getPlanoHorarioVOs().iterator();
        while (i.hasNext()) {
            PlanoHorarioVO objExistente = (PlanoHorarioVO) i.next();
            if (objExistente.getHorario().getCodigo().equals(obj.getHorario().getCodigo())) {
                getPlanoHorarioVOs().set(index, obj);
                return;
            }
            index++;
        }
        getPlanoHorarioVOs().add(obj);
    }

    public void excluirObjPlanoHorarioVOs(Integer horario) throws Exception {
        int index = 0;
        Iterator i = getPlanoHorarioVOs().iterator();
        while (i.hasNext()) {
            PlanoHorarioVO objExistente = (PlanoHorarioVO) i.next();
            if (objExistente.getHorario().getCodigo().equals(horario)) {
                getPlanoHorarioVOs().remove(index);
                return;
            }
            index++;
        }
    }

    public PlanoHorarioVO consultarObjPlanoHorarioVO(Integer horario) throws Exception {
        Iterator i = getPlanoHorarioVOs().iterator();
        while (i.hasNext()) {
            PlanoHorarioVO objExistente = (PlanoHorarioVO) i.next();
            if (objExistente.getHorario().getCodigo().equals(horario)) {
                return objExistente;
            }
        }
        return null;
    }

    public void adicionarObjPlanoModalidadeVOs(PlanoModalidadeVO obj) throws Exception {
        PlanoModalidadeVO.validarDadosEspecial(obj);
        int index = 0;
        for (PlanoModalidadeVO objExistente : getPlanoModalidadeVOs()) {
            if (objExistente.getModalidade().getCodigo().equals(obj.getModalidade().getCodigo())) {
                getPlanoModalidadeVOs().set(index, obj);
                return;
            }
            index++;
        }
        getPlanoModalidadeVOs().add(obj);
    }

    public void adicionarPlanoExcecaoVOs(PlanoExcecaoVO obj) throws Exception {
        int index = 0;
        for (PlanoExcecaoVO objExistente : getPlanoExcecaoVOs()) {
            if (objExistente.getModalidade().getCodigo().equals(obj.getModalidade().getCodigo())
                    && objExistente.getPacote().getCodigo().equals(obj.getPacote().getCodigo())
                    && objExistente.getVezesSemana().equals(obj.getVezesSemana())
                    && objExistente.getDuracao().equals(obj.getDuracao())
                    && objExistente.getHorario().getCodigo().equals(obj.getHorario().getCodigo())) {
                getPlanoExcecaoVOs().set(index, obj);
                return;
            }
            index++;
        }
        getPlanoExcecaoVOs().add(obj);
    }

    public void excluirObjPlanoModalidadeVOs(Integer modalidade) throws Exception {
        int index = 0;
        Iterator i = getPlanoModalidadeVOs().iterator();
        while (i.hasNext()) {
            PlanoModalidadeVO objExistente = (PlanoModalidadeVO) i.next();
            if (objExistente.getModalidade().getCodigo().equals(modalidade)) {
                getPlanoModalidadeVOs().remove(index);
                return;
            }
            index++;
        }
    }

    public PlanoModalidadeVO consultarObjPlanoModalidadeVO(Integer modalidade) throws Exception {
        Iterator i = getPlanoModalidadeVOs().iterator();
        while (i.hasNext()) {
            PlanoModalidadeVO objExistente = (PlanoModalidadeVO) i.next();
            if (objExistente.getModalidade().getCodigo().equals(modalidade)) {
                return objExistente;
            }
        }
        return null;
    }

    public void adicionarObjPlanoComposicaoVOs(PlanoComposicaoVO obj) throws Exception {
        PlanoComposicaoVO.validarDados(obj);
        int index = 0;
        Iterator i = getPlanoComposicaoVOs().iterator();
        while (i.hasNext()) {
            PlanoComposicaoVO objExistente = (PlanoComposicaoVO) i.next();
            if (objExistente.getComposicao().getCodigo().equals(obj.getComposicao().getCodigo())) {
                getPlanoComposicaoVOs().set(index, obj);
                return;
            }
            index++;
        }
        getPlanoComposicaoVOs().add(obj);
    }

    public void adicionarObjPlanoCategoriaVOs(PlanoCategoriaVO obj) throws Exception {
        PlanoCategoriaVO.validarDados(obj);
        int index = 0;
        Iterator i = getPlanoCategoriaVOs().iterator();
        while (i.hasNext()) {
            PlanoCategoriaVO objExistente = (PlanoCategoriaVO) i.next();
            if (objExistente.getCategoria().getCodigo().equals(obj.getCategoria().getCodigo())) {
                getPlanoCategoriaVOs().set(index, obj);
                return;
            }
            index++;
        }
        getPlanoCategoriaVOs().add(obj);
    }

    public void excluirObjPlanoComposicaoVOs(Integer composicao) throws Exception {
        int index = 0;
        Iterator i = getPlanoComposicaoVOs().iterator();
        while (i.hasNext()) {
            PlanoComposicaoVO objExistente = (PlanoComposicaoVO) i.next();
            if (objExistente.getComposicao().getCodigo().equals(composicao)) {
                getPlanoComposicaoVOs().remove(index);
                return;
            }
            index++;
        }
    }

    public void excluirObjPlanoCategoriaVOs(Integer categoria) throws Exception {
        int index = 0;
        Iterator i = getPlanoCategoriaVOs().iterator();
        while (i.hasNext()) {
            PlanoCategoriaVO objExistente = (PlanoCategoriaVO) i.next();
            if (objExistente.getCategoria().getCodigo().equals(categoria)) {
                getPlanoCategoriaVOs().remove(index);
                return;
            }
            index++;
        }
    }

    public PlanoComposicaoVO consultarObjPlanoComposicaoVO(Integer composicao) throws Exception {
        Iterator i = getPlanoComposicaoVOs().iterator();
        while (i.hasNext()) {
            PlanoComposicaoVO objExistente = (PlanoComposicaoVO) i.next();
            if (objExistente.getComposicao().getCodigo().equals(composicao)) {
                return objExistente;
            }
        }
        return null;
    }


    public void adicionarObjPlanoDuracaoVOs(PlanoDuracaoVO obj) throws Exception {
        PlanoDuracaoVO.validarDados(obj);
        int index = 0;
        Iterator i = getPlanoDuracaoVOs().iterator();
        while (i.hasNext()) {
            PlanoDuracaoVO objExistente = (PlanoDuracaoVO) i.next();
            if (objExistente.getNumeroMeses().equals(obj.getNumeroMeses())) {
                getPlanoDuracaoVOs().set(index, obj);
                return;
            }
            index++;
        }
        getPlanoDuracaoVOs().add(obj);
    }

    public void excluirObjPlanoDuracaoVOs(PlanoDuracaoVO obj) throws Exception {
        int index = 0;
        Iterator i = getPlanoDuracaoVOs().iterator();
        while (i.hasNext()) {
            PlanoDuracaoVO objExistente = (PlanoDuracaoVO) i.next();
            if (objExistente.getNumeroMeses().equals(obj.getNumeroMeses())) {
                getPlanoDuracaoVOs().remove(index);
                return;
            }
            index++;
        }
    }

    public PlanoDuracaoVO consultarObjPlanoDuracaoVO(Integer codigo) throws Exception {
        Iterator i = getPlanoDuracaoVOs().iterator();
        while (i.hasNext()) {
            PlanoDuracaoVO objExistente = (PlanoDuracaoVO) i.next();
            if (objExistente.getCodigo().equals(codigo)) {
                return objExistente;
            }
        }
        return null;
    }

    public EmpresaVO getEmpresa() {
        if (empresa == null) {
            empresa = new EmpresaVO();
        }
        return empresa;
    }

    public void setEmpresa(EmpresaVO obj) {
        this.empresa = obj;
    }

    public List<PlanoHorarioVO> getPlanoHorarioVOs() {
        return planoHorarioVOs;
    }

    public void filtrarPlanoHorarioHorarioAtivo() {
        List<PlanoHorarioVO> planoHorarioVOAtivos = new ArrayList<>();
        this.getPlanoHorarioVOs().forEach(
                planoHorarioVO -> {
                    if (planoHorarioVO.getHorario().getAtivo()) {
                        planoHorarioVOAtivos.add(planoHorarioVO);
                    }
                }
        );
        this.setPlanoHorarioVOs(planoHorarioVOAtivos);
    }

    public void setPlanoHorarioVOs(List<PlanoHorarioVO> planoHorarioVOs) {
        this.planoHorarioVOs = planoHorarioVOs;
    }

    public List<PlanoModalidadeVO> getPlanoModalidadeVOs() {
        return planoModalidadeVOs;
    }

    public void setPlanoModalidadeVOs(List<PlanoModalidadeVO> planoModalidadeVOs) {
        this.planoModalidadeVOs = planoModalidadeVOs;
    }

    public List getPlanoComposicaoVOs() {
        return (planoComposicaoVOs);
    }

    public void setPlanoComposicaoVOs(List planoComposicaoVOs) {
        this.planoComposicaoVOs = planoComposicaoVOs;
    }

    public List<PlanoDuracaoVO> getPlanoDuracaoVOs() {
        return planoDuracaoVOs;
    }

    public void setPlanoDuracaoVOs(List<PlanoDuracaoVO> planoDuracaoVOs) {
        this.planoDuracaoVOs = planoDuracaoVOs;
    }

    public PlanoTextoPadraoVO getPlanoTextoPadrao() {
        if (planoTextoPadrao == null) {
            planoTextoPadrao = new PlanoTextoPadraoVO();
        }
        return planoTextoPadrao;
    }

    public String getPlanoTextoPadrao_Apresentar() {
        return getPlanoTextoPadrao().getDescricao();
    }


    public void setPlanoTextoPadrao(PlanoTextoPadraoVO planoTextoPadrao) {
        this.planoTextoPadrao = planoTextoPadrao;
    }

    public ProdutoVO getProdutoPadraoGerarParcelasContrato() {
        return produtoPadraoGerarParcelasContrato;
    }

    public void setProdutoPadraoGerarParcelasContrato(ProdutoVO produtoPadraoGerarParcelasContrato) {
        this.produtoPadraoGerarParcelasContrato = produtoPadraoGerarParcelasContrato;
    }

    public Date getIngressoAte() {
        return (ingressoAte);
    }

    public String getIngressoAte_Apresentar() {
        return (Uteis.getData(getIngressoAte()));
    }

    public void setIngressoAte(Date ingressoAte) {
        this.ingressoAte = ingressoAte;
    }

    public Date getVigenciaAte() {
        return (vigenciaAte);
    }

    public String getVigenciaAte_Apresentar() {
        return (Uteis.getData(getVigenciaAte()));
    }

    public void setVigenciaAte(Date vigenciaAte) {
        this.vigenciaAte = vigenciaAte;
    }

    public Date getVigenciaDe() {
        return (vigenciaDe);
    }

    public String getVigenciaDe_Apresentar() {
        return (Uteis.getData(getVigenciaDe()));
    }

    public void setVigenciaDe(Date vigenciaDe) {
        this.vigenciaDe = vigenciaDe;
    }

    public String getDescricao() {
        return (descricao);
    }
    public String getDescricao_Apresentar() {
        if(UteisValidacao.emptyString(descricao)){
            return " - ";
        }
        return (descricao);
    }
    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getBolsa_Apresentar() {
        return getBolsa() ? "Sim" : "Não";
    }

    public Boolean getBolsa() {
        return bolsa;
    }

    public void setBolsa(Boolean bolsa) {
        this.bolsa = bolsa;
    }

    public List getPlanoProdutoSugeridoVOs() {
        return planoProdutoSugeridoVOs;
    }

    @SuppressWarnings("unchecked")
    public void filtrarPlanoProdutoSugeridoAtivoPlano() {
        List<PlanoProdutoSugeridoVO> planoHorarioVOAtivos = new ArrayList<>();
        ((List<PlanoProdutoSugeridoVO>) this.getPlanoProdutoSugeridoVOs()).forEach(
                planoProdutoSugeridoVO -> {
                    if (planoProdutoSugeridoVO.getAtivoPlano()) {
                        planoHorarioVOAtivos.add(planoProdutoSugeridoVO);
                    }
                }
        );
        this.setPlanoProdutoSugeridoVOs(planoHorarioVOAtivos);
    }

    public void setPlanoProdutoSugeridoVOs(List planoProdutoSugeridoVOs) {
        this.planoProdutoSugeridoVOs = planoProdutoSugeridoVOs;
    }

    public Boolean getPermitePagarComBoleto() {
        return permitePagarComBoleto;
    }

    public void setPermitePagarComBoleto(Boolean permitePagarComBoleto) {
        this.permitePagarComBoleto = permitePagarComBoleto;
    }

    public Double getPercentualMultaCancelamento() {
        return percentualMultaCancelamento;
    }

    public void setPercentualMultaCancelamento(Double percentualMultaCancelamento) {
        this.percentualMultaCancelamento = percentualMultaCancelamento;
    }

    public ProdutoVO getProdutoTaxaCancelamento() {
        return produtoTaxaCancelamento;
    }

    public void setProdutoTaxaCancelamento(ProdutoVO produtoTaxaCancelamento) {
        this.produtoTaxaCancelamento = produtoTaxaCancelamento;
    }

    public Boolean getDefasado() {
        return defasado;
    }

    public void setDefasado(Boolean defasado) {
        this.defasado = defasado;
    }

    public void setReciboTextoPadrao(PlanoTextoPadraoVO reciboTextoPadrao) {
        this.reciboTextoPadrao = reciboTextoPadrao;
    }

    public PlanoTextoPadraoVO getReciboTextoPadrao() {
        return reciboTextoPadrao;
    }

    public List getPlanoProdutoPadraoVOs() {
        return planoProdutoPadraoVOs;
    }

    public void setPlanoProdutoPadraoVOs(List planoProdutoPadraoVOs) {
        this.planoProdutoPadraoVOs = planoProdutoPadraoVOs;
    }

    public String getDiasVencimentoProrata() {
        return diasVencimentoProrata;
    }

    public void setDiasVencimentoProrata(String diasVencimentoProrata) {
        this.diasVencimentoProrata = diasVencimentoProrata;
    }

    public List<String> getListaDiasVencimento() {
        if (listaDiasVencimento == null) {
            listaDiasVencimento = new ArrayList<String>();
        }
        return listaDiasVencimento;
    }

    public List<String> getListaPreenchidaVencimentos() {
        List<String> aux = new ArrayList<>();
        aux.add("");
        if (UteisValidacao.emptyString(getDiasVencimentoProrata())) {
            if (!UteisValidacao.emptyNumber(getConvenioCobrancaPrivateLabel().getCodigo())) {
                aux.add("1");
                aux.add("5");
                aux.add("10");
                aux.add("15");
                aux.add("20");
                aux.add("25");
            } else {
                for (int i = 1; i <= 31; i++) {
                    aux.add(String.valueOf(i));
                }
            }
        } else {
            aux.addAll(Arrays.asList(getDiasVencimentoProrata().split(",")));
        }
        return aux;
    }

    public void setDiaVencimento(String novoDia) {
        listaDiasVencimento.add(novoDia);
        ordenarDiaVencimento();
    }

    private void ordenarDiaVencimento() {
        if (!UteisValidacao.emptyList(listaDiasVencimento)) {
            List<ItemGestaoNotasTO> listaInteiros = new ArrayList<ItemGestaoNotasTO>();
            for (String dia : listaDiasVencimento) {
                ItemGestaoNotasTO item  = new ItemGestaoNotasTO();
                item.setQuantidade(Integer.parseInt(dia));
                listaInteiros.add(item);
            }

            Ordenacao.ordenarLista(listaInteiros, "quantidade");
            listaDiasVencimento.clear();

            for (ItemGestaoNotasTO diaInt : listaInteiros) {
                listaDiasVencimento.add(diaInt.getQuantidade().toString());
            }
        }
    }

    public void setListaDiasVencimento(List<String> listaDiasVencimento) {
        this.listaDiasVencimento = listaDiasVencimento;
    }

    public void setListaDiasVencimento(String vencimentos) {
        if (vencimentos.trim().isEmpty()) {
            listaDiasVencimento = new ArrayList<String>();
        } else {
            if (prorataObrigatorio) {
                listaDiasVencimento = new ArrayList(Arrays.asList(vencimentos.split(Pattern.quote(","))));
            } else {
                listaDiasVencimento = new ArrayList<String>();
                // adiciona-se naturalmente uma opção vazia para ser usada na tela de negociação
                // para usar no plano deve-se retirar esta opção vazia listaDiasVencimento.remove("");
                // optei por adicionar aqui para a lista já ser criada com o elemento vazio na primeira posição
                listaDiasVencimento.add("");
                listaDiasVencimento.addAll(Arrays.asList(vencimentos.split(Pattern.quote(","))));
            }
        }
    }

    public boolean isProrataObrigatorio() {
        return prorataObrigatorio;
    }

    public void setProrataObrigatorio(boolean prorataObrigatorio) {
        this.prorataObrigatorio = prorataObrigatorio;
    }

    public void atualizaStringVencimentos() {
        int i = 0;
        StringBuilder vencimentos = new StringBuilder();
        inicializarDiasProRataParaRecorrencia();
        for (String aux : listaDiasVencimento) {
            vencimentos.append(i++ == 0 ? "" : ",").append(aux);
        }
        diasVencimentoProrata = vencimentos.toString();
    }

    private void inicializarDiasProRataParaRecorrencia() {
        if (this.getRegimeRecorrencia()) {
            if (!UteisValidacao.emptyNumber(getConvenioCobrancaPrivateLabel().getCodigo())) {
                setListaDiasVencimento(new ArrayList<>());
                listaDiasVencimento.add("1");
                listaDiasVencimento.add("5");
                listaDiasVencimento.add("10");
                listaDiasVencimento.add("15");
                listaDiasVencimento.add("20");
                listaDiasVencimento.add("25");
            } else if (UteisValidacao.emptyList(getListaDiasVencimento())) {
                for (int i = 1; i <= 31; i++) {
                    listaDiasVencimento.add(String.valueOf(i));
                }
            }
        }
    }

    public DescontoVO getDescontoAntecipado() {
        return descontoAntecipado;
    }

    public void setDescontoAntecipado(DescontoVO descontoAntecipado) {
        this.descontoAntecipado = descontoAntecipado;
    }

    /**
     * Início metodos usados em recorrencia
     */
    /**
     * @return the planoRecorrencia
     */
    public PlanoRecorrenciaVO getPlanoRecorrencia() {
        return planoRecorrencia;
    }

    /**
     * @param planoRecorrencia the planoRecorrencia to set
     */
    public void setPlanoRecorrencia(PlanoRecorrenciaVO planoRecorrencia) {
        this.planoRecorrencia = planoRecorrencia;
    }

    /**
     * @return the regimeRecorrencia
     */
    public Boolean getRegimeRecorrencia() {
        return regimeRecorrencia;
    }

    /**
     * @param regimeRecorrencia the regimeRecorrencia to set
     */
    public void setRegimeRecorrencia(Boolean regimeRecorrencia) {
        this.regimeRecorrencia = regimeRecorrencia;
    }

    /**
     * @return Sim(true) ou Não(false) para ser mostrado na tela
     */
    public String getRegimeRecorrencia_Apresentar() {
        return getRegimeRecorrencia() ? "Sim" : "Não";
    }

    /**
     * Fim metodos usados em recorrencia
     */
    public void setEscolhido(Boolean escolhido) {
        this.escolhido = escolhido;
    }

    public Boolean getEscolhido() {
        return escolhido;
    }

    public Double getValorAcrescentar() {
        return valorAcrescentar;
    }

    public void setValorAcrescentar(Double valorAcrescentar) {
        this.valorAcrescentar = valorAcrescentar;
    }

    public Double getPorcentagemAcrescentar() {
        return porcentagemAcrescentar;
    }

    public void setPorcentagemAcrescentar(Double porcentagemAcrescentar) {
        this.porcentagemAcrescentar = porcentagemAcrescentar;
    }

    public Date getDataVencimentoParcela() {
        return dataVencimentoParcela;
    }

    public void setDataVencimentoParcela(Date dataVencimentoParcela) {
        this.dataVencimentoParcela = dataVencimentoParcela;
    }

    public TipoReajuste getTipoReajuste() {
        return tipoReajuste;
    }

    public void setTipoReajuste(TipoReajuste tipoReajuste) {
        this.tipoReajuste = tipoReajuste;
    }

    public String getDescricaoProgresso() {
        return descricaoProgresso;
    }

    public void setDescricaoProgresso(String descricaoProgresso) {
        this.descricaoProgresso = descricaoProgresso;
    }

    public boolean getSelecionado() {
        return selecionado;
    }

    public void setSelecionado(boolean selecionado) {
        this.selecionado = selecionado;
    }

    public String getCorrespondenciaZD() {
        return correspondenciaZD;
    }

    public void setCorrespondenciaZD(String correspondenciaZD) {
        this.correspondenciaZD = correspondenciaZD;
    }

    public Boolean getComissao() {
        return comissao;
    }

    public void setComissao(Boolean comissao) {
        this.comissao = comissao;
    }

    public Boolean getCobrarAdesaoSeparada() {
        return cobrarAdesaoSeparada;
    }

    public void setCobrarAdesaoSeparada(Boolean cobrarAdesaoSeparada) {
        this.cobrarAdesaoSeparada = cobrarAdesaoSeparada;
    }

    public Integer getNrVezesParcelarAdesao() {
        return nrVezesParcelarAdesao;
    }

    public void setNrVezesParcelarAdesao(Integer nrVezesParcelarAdesao) {
        this.nrVezesParcelarAdesao = nrVezesParcelarAdesao;
    }

    public List<PlanoExcecaoVO> getPlanoExcecaoVOs() {
        return planoExcecaoVOs;
    }

    public void setPlanoExcecaoVOs(List<PlanoExcecaoVO> planoExcecaoVOs) {
        this.planoExcecaoVOs = planoExcecaoVOs;
    }

    public Boolean getSite() {
        return site;
    }

    public void setSite(Boolean site) {
        this.site = site;
    }

    public Date getInicioMinimoContrato() {
        return inicioMinimoContrato;
    }

    public String getInicioMinimoContratoFomatado(){
        return Uteis.getDataAplicandoFormatacao(inicioMinimoContrato, "dd/MM/yyyy");
    }

    public String getInicioMinimoContratoFomatado_VendaRapida(){
        if (getInicioMinimoContrato() == null) {
            return "";
        } else if (Calendario.maior(getInicioMinimoContrato() , Calendario.hoje())) {
            return "* Contrato irá iniciar dia " + Uteis.getDataAplicandoFormatacao(getInicioMinimoContrato(), "dd/MM/yyyy");
        } else {
            return "* Contrato irá iniciar dia " + Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "dd/MM/yyyy");
        }
    }

    public void setInicioMinimoContrato(Date inicioMinimoContrato) {
        this.inicioMinimoContrato = inicioMinimoContrato;
    }

    public Boolean getParcelamentoOperadora() {
        return parcelamentoOperadora;
    }

    public void setParcelamentoOperadora(Boolean parcelamentoOperadora) {
        this.parcelamentoOperadora = parcelamentoOperadora;
    }

    public PlanoWS toWS(Connection con) throws Exception {
        return toWS(null, con);
    }

    public PlanoWS toWS(PlanoEmpresaVO planoEmpresaVO, Connection con) throws Exception{
        return toWS(planoEmpresaVO, con, false, false, false);
    }

    public PlanoWS toWS(PlanoEmpresaVO planoEmpresaVO, Connection con, boolean isRenovacao, boolean somentePlanosPactoFlow, boolean isRematricula) throws Exception {
        if (planoEmpresaVO != null && getPlanoRecorrencia() != null) {
            if (!UteisValidacao.emptyNumber(planoEmpresaVO.getValorMensal())) {
                getPlanoRecorrencia().setValorMensal(Uteis.arredondarForcando2CasasDecimais(planoEmpresaVO.getValorMensal()));
            }
            if (!UteisValidacao.emptyNumber(planoEmpresaVO.getTaxaAdesao())) {
                getPlanoRecorrencia().setTaxaAdesao(Uteis.arredondarForcando2CasasDecimais(planoEmpresaVO.getTaxaAdesao()));
            }
            if (!UteisValidacao.emptyNumber(planoEmpresaVO.getValorAnuidade())) {
                getPlanoRecorrencia().setValorAnuidade(Uteis.arredondarForcando2CasasDecimais(planoEmpresaVO.getValorAnuidade()));
            }
        }

        //Plano Site só pode ter UMA duração;
        PlanoWS plano = new PlanoWS();
        plano.setCodigoPlano(this.getCodigo());
        plano.setParcelamentoOperadora(this.parcelamentoOperadora);
        plano.setDescricao(this.getDescricao());
        plano.setDescricaoEncantamento(this.getDescricaoEncantamento());
        if (!this.getPlanoHorarioVOs().isEmpty()) {
            plano.setCodigoHorario(this.getPlanoHorarioVOs().get(0).getCodigo());
            plano.setDescricaoHorario(this.getPlanoHorarioVOs().get(0).getHorario().getDescricao());
        }
        if (!this.getPlanoDuracaoVOs().isEmpty() && !this.getPlanoDuracaoVOs().get(0).getPlanoCondicaoPagamentoVOs().isEmpty()) {
            plano.setCondicaoPagamento(this.getPlanoDuracaoVOs().get(0).getPlanoCondicaoPagamentoVOs().get(0).getCondicaoPagamento().getDescricao());
            plano.setCondicaoPagamentoPrePago(this.getPlanoDuracaoVOs().get(0).getPlanoCondicaoPagamentoVOs().get(0).getCondicaoPagamento().getRecebimentoPrePago());
        }
        plano.setPlanoPersonal(this.isPlanoPersonal());
        for (PlanoEmpresaVO planoEmpresa : this.getEmpresas()) {
            plano.getEmpresas().add(planoEmpresa.getEmpresa().getCodigo());
        }

        if (!UteisValidacao.emptyList(getListaDiasVencimento())) {
            plano.setDiasVencimento(new ArrayList<>());
            for (String d : getListaDiasVencimento()) {
                try {
                    plano.getDiasVencimento().add(Integer.valueOf(d));
                } catch (Exception e) {
                }
            }
        }

        try {
            if (this.getPermitirTurmasVendasOnline() && this.possuiModalidadeComTurma()) {
                plano.setVendaComTurma(true);
            }
        }catch (Exception ignore){};

        if (getPlanoRecorrencia() != null && getPlanoRecorrencia().getCodigo() != 0) {
            PlanoRecorrencia planoRecorrenciaDAO;
            try {
                planoRecorrenciaDAO = new PlanoRecorrencia(con);
                plano.setParcelas(planoRecorrenciaDAO.calcularParcelasRecorrenciaWS(getPlanoRecorrencia(), isRenovacao));
            } finally {
                planoRecorrenciaDAO = null;
            }

            plano.setParcelasAnuidade(new ArrayList<PlanoAnuidadeParcelaWS>());
            for (PlanoAnuidadeParcelaVO parcela : this.getPlanoRecorrencia().getParcelasAnuidade()) {
                plano.getParcelasAnuidade().add(new PlanoAnuidadeParcelaWS(parcela));
            }
        }

        if(!plano.getVendaComTurma()) {
            for (Object obj : getPlanoModalidadeVOs()) {
                PlanoModalidadeVO planoModalidadeVO = (PlanoModalidadeVO) obj;
                plano.getModalidades().add(planoModalidadeVO.toWS());
            }
        }

        if(somentePlanosPactoFlow) {
            for (Object obj : this.getPlanoProdutoSugeridoVOs()) {
                PlanoProdutoSugeridoVO planoProdudoSugerido = (PlanoProdutoSugeridoVO) obj;
                if(planoProdudoSugerido.getAtivoPlano() && planoProdudoSugerido.getObrigatorio()) {
                    plano.getProdutos().add(planoProdudoSugerido.toWS());
                    if (planoProdudoSugerido.getProduto().getTipoProduto().equals("MA")) {
                        plano.setValorMatricula(Uteis.arredondarForcando2CasasDecimais(planoProdudoSugerido.getValorProduto()));
                    } else if (planoProdudoSugerido.getProduto().getTipoProduto().equals("RE")) {
                        plano.setValorRematricula(Uteis.arredondarForcando2CasasDecimais(planoProdudoSugerido.getValorProduto()));
                    } else if (planoProdudoSugerido.getProduto().getTipoProduto().equals("RN")) {
                        plano.setValorRenovacao(Uteis.arredondarForcando2CasasDecimais(planoProdudoSugerido.getValorProduto()));
                    }
                }
            }
        } else {
            for (Object obj : this.getPlanoProdutoSugeridoVOs()) {
                PlanoProdutoSugeridoVO planoProdudoSugerido = (PlanoProdutoSugeridoVO) obj;
                if(planoProdudoSugerido.getAtivoPlano()) {
                    plano.getProdutos().add(planoProdudoSugerido.toWS());
                    if (isRenovacao && planoProdudoSugerido.getProduto().getTipoProduto().equals("RN")){
                        plano.setValorMatricula(Uteis.arredondarForcando2CasasDecimais(planoProdudoSugerido.getValorProduto()));
                    }else if (isRematricula && planoProdudoSugerido.getProduto().getTipoProduto().equals("RE")){
                        plano.setValorMatricula(Uteis.arredondarForcando2CasasDecimais(planoProdudoSugerido.getValorProduto()));
                    }else if ((!isRematricula && !isRenovacao ) && planoProdudoSugerido.getProduto().getTipoProduto().equals("MA")) {
                        plano.setValorMatricula(Uteis.arredondarForcando2CasasDecimais(planoProdudoSugerido.getValorProduto()));
                    }
                }
            }
        }

        if (isVendaCreditoTreino()) {
            if (getPlanoDuracaoVOs().size() == 1) {
                PlanoDuracaoVO planoDuracaoVO = getPlanoDuracaoVOs().get(0);
                if (planoDuracaoVO.getListaPlanoDuracaoCreditoTreino().size() == 1) {
                    PlanoDuracaoCreditoTreinoVO planoDuracaoCreditoTreinoVO = planoDuracaoVO.getListaPlanoDuracaoCreditoTreino().get(0);
                    plano.setValorMensal(planoDuracaoCreditoTreinoVO.getValorTotal() / planoDuracaoVO.getNumeroMeses());
                    plano.setDuracaoPlano(planoDuracaoVO.getNumeroMeses());
                    plano.setQuantidadeDiasExtra(this.getPlanoDuracaoVOs().get(0).getQuantidadeDiasExtra());
                    plano.setQtdCreditoPlanoCredito(planoDuracaoCreditoTreinoVO.getQuantidadeCreditoCompra());
                }
            }
        } else if(this.getRegimeRecorrencia()){ // Plano Recorrencia
            plano.setValorMensal(Uteis.arredondarForcando2CasasDecimais(this.getPlanoRecorrencia().getValorMensal()));
            plano.setTaxaAdesao(Uteis.arredondarForcando2CasasDecimais(this.getPlanoRecorrencia().getTaxaAdesao()));
            plano.setValorAnuidade(Uteis.arredondarForcando2CasasDecimais(this.getPlanoRecorrencia().getValorAnuidade()));
            plano.setDiaAnuidade(this.getPlanoRecorrencia().getDiaAnuidade());
            plano.setAnuidadeNaParcela(this.getPlanoRecorrencia().isAnuidadeNaParcela());

            if (plano.isAnuidadeNaParcela()) {
                plano.setParcelaAnuidade(this.getPlanoRecorrencia().getParcelaAnuidade());
            } else {
                for (Mes obj : Mes.values()) {
                    if (this.getPlanoRecorrencia().getMesAnuidade().equals(obj.getCodigo())) {
                        plano.setMesAnuidade(obj.getDescricao());
                        plano.setMesAnuidadeOrdinal(obj.getCodigo());
                    }
                }
            }
            if(getInicioMinimoContrato() != null){
                plano.setInicioMinimo(Uteis.getDataAplicandoFormatacao(getInicioMinimoContrato(), "dd/MM/yyyy"));
            }
            plano.setDuracaoPlano((this.getPlanoDuracaoVOs() != null && !this.getPlanoDuracaoVOs().isEmpty()) ?
                    this.getPlanoDuracaoVOs().get(0).getNumeroMeses() : getPlanoRecorrencia().getDuracaoPlano());

            if (plano.getVendaComTurma()) {
                for (Object obj : getPlanoModalidadeVOs()) {
                    PlanoModalidadeVO planoModalidadeVO = (PlanoModalidadeVO) obj;
                    PlanoModalidadeWS ws = planoModalidadeVO.toWS();
                    ws.setCodigo(((PlanoModalidadeVO) obj).getModalidade().getCodigo());
                    ws.setNrsVezesSemana(new ArrayList<>());
                    ws.setUtilizarTurma(((PlanoModalidadeVO) obj).getModalidade().getUtilizarTurma());
                    planoModalidadeVO.getPlanoModalidadeVezesSemanaVOs().forEach(pMVS ->{
                        ws.getNrsVezesSemana().add(((PlanoModalidadeVezesSemanaVO) pMVS).getNrVezes());
                    });
                    plano.getModalidades().add(ws);
                }
            }

        } else if(!this.getRegimeRecorrencia()) { // Planos comuns

            //Apenas para utilizar o metodo obter valor do mensal para planos comuns.
            ContratoSiteService contratoSiteService = new ContratoSiteService();
            ContratoVO contratoFake = new ContratoVO();
            contratoFake.setPlano(new PlanoVO());
            contratoFake.getPlano().setPlanoDuracaoVOs(this.getPlanoDuracaoVOs());
            contratoFake.getPlano().setPlanoModalidadeVOs(this.getPlanoModalidadeVOs());
            if (getPlanoExcecaoVOs() != null
                    && !getPlanoExcecaoVOs().isEmpty()) { // Se houver excecao, plano veio da tela nova. Ent?o olhar para o valor e modalidade da exce??o. "Permitido apenas 1 para vendas online".
                if (plano.getVendaComTurma()) {
                    plano.setModalidades(new ArrayList<>());

                    getPlanoExcecaoVOs().sort(Comparator.comparing(PlanoExcecaoVO::getVezesSemana));

                    getPlanoExcecaoVOs().forEach(pE -> {
                        AtomicBoolean existe = new AtomicBoolean(false);
                        plano.getModalidades().forEach(m -> {
                            if (m.getCodigo() == pE.getModalidade().getCodigo()) {
                                m.getNrsVezesSemana().add(pE.getVezesSemana());
                                existe.set(true);
                            }
                        });
                        if (!existe.get()) {
                            PlanoModalidadeWS planoModalidadeWSExcecao = new PlanoModalidadeWS();
                            planoModalidadeWSExcecao.setCodigo(pE.getModalidade().getCodigo());
                            planoModalidadeWSExcecao.setModalidade(pE.getModalidade().getNome());
                            planoModalidadeWSExcecao.setUtilizarTurma(pE.getModalidade().getUtilizarTurma());
                            if (planoModalidadeWSExcecao.getNrsVezesSemana() == null) {
                                planoModalidadeWSExcecao.setNrsVezesSemana(new ArrayList<>());
                            }
                            planoModalidadeWSExcecao.getNrsVezesSemana().add(pE.getVezesSemana());
                            plano.getModalidades().add(planoModalidadeWSExcecao);
                        }
                    });
                    plano.setValorMensal(Uteis.arredondarForcando2CasasDecimais(getPlanoExcecaoVOs().get(0).getValor()));
                } else {
                    PlanoModalidadeWS planoModalidadeWSExcecao = new PlanoModalidadeWS();
                    planoModalidadeWSExcecao.setCodigo(getPlanoExcecaoVOs().get(0).getModalidade().getCodigo());
                    planoModalidadeWSExcecao.setModalidade(getPlanoExcecaoVOs().get(0).getModalidade().getNome());
                    plano.setModalidades(new ArrayList<>());
                    plano.getModalidades().add(planoModalidadeWSExcecao);
                    plano.setValorMensal(Uteis.arredondarForcando2CasasDecimais(getPlanoExcecaoVOs().get(0).getValor()));
                }
            } else {
                plano.setValorMensal(Uteis.arredondarForcando2CasasDecimais(
                        contratoSiteService.obterValorMensalPlanoSiteNaoRecorrente(contratoFake, null)));
            }
            contratoSiteService = null;
            // FIM

            if(getInicioMinimoContrato() != null){
                plano.setInicioMinimo(Uteis.getDataAplicandoFormatacao(getInicioMinimoContrato(), "dd/MM/yyyy"));
            }
            if (!UteisValidacao.emptyList(this.getPlanoDuracaoVOs())) {
                plano.setDuracaoPlano(this.getPlanoDuracaoVOs().get(0).getNumeroMeses());
                plano.setQuantidadeDiasExtra(this.getPlanoDuracaoVOs().get(0).getQuantidadeDiasExtra());
            }
        }

        plano.setRenovavelAutomaticamente(this.getRenovavelAutomaticamente() != null && this.getRenovavelAutomaticamente());
        plano.setRegimeRecorrencia(this.getRegimeRecorrencia());
        plano.setMaximoVezesParcelar(this.getMaximoVezesParcelar());
        for(PlanoCategoriaVO planoCategoria : this.getPlanoCategoriaVOs()){
            plano.getCategorias().add(planoCategoria.getCategoria().getCodigo());
        }
        try {
            plano.setApresentarPactoFlow(this.apresentarPactoFlow);
        } catch (Exception ex) {}

        try {
            plano.setRenovarAutomaticamenteUtilizandoValorBaseContrato(this.getRenovarAutomaticamenteUtilizandoValorBaseContrato());
        } catch (Exception ignore){}

        try {
            plano.setVideoSiteUrl(this.getVideoSiteUrl());
        } catch (Exception ignore){}

        try {
            plano.setObservacaoSite(this.getObservacaoSite());
        } catch (Exception ignore){}

        plano.setObrigatorioInformarCartaoCreditoVenda(this.getObrigatorioInformarCartaoCreditoVenda());

        plano.setCobrarProdutoSeparado(this.getCobrarProdutoSeparado());
        plano.setCobrarAdesaoSeparado(this.getCobrarAdesaoSeparada());
        plano.setPermitirCompartilharPLanoNoSite(this.isPermitirCompartilharPLanoNoSite());
        plano.setBloquearRecompra(this.getBloquearRecompra());
        plano.setHabilitarIa(this.getHabilitarIa());
        return plano;
    }

    private boolean possuiModalidadeComTurma() {
        if (getPlanoModalidadeVOs() != null) {
            for (PlanoModalidadeVO planoModalidadeVO : getPlanoModalidadeVOs()) {
                if (planoModalidadeVO.getModalidade().isAtivo()
                        && planoModalidadeVO.getModalidade().isUtilizarTurma()) {
                    return true;
                }
            }
        } else if (getPlanoExcecaoVOs() != null) {
            for (PlanoExcecaoVO planoModalidadeVO : getPlanoExcecaoVOs()) {
                if (planoModalidadeVO.getModalidade().isAtivo()
                        && planoModalidadeVO.getModalidade().isUtilizarTurma()) {
                    return true;
                }
            }
        }
        return false;
    }

    public Date getDataLancamentoContratoInicio() {
        return dataLancamentoContratoInicio;
    }

    public void setDataLancamentoContratoInicio(Date dataLancamentoContratoinicio) {
        this.dataLancamentoContratoInicio = dataLancamentoContratoinicio;
    }

    public Date getDataLancamentoContratoFim() {
        return dataLancamentoContratoFim;
    }

    public void setDataLancamentoContratoFim(Date dataLancamentoContratofim) {
        this.dataLancamentoContratoFim = dataLancamentoContratofim;
    }

    public Boolean getContratosMatricula() {
        return contratosMatricula;
    }

    public void setContratosMatricula(Boolean contratosMatricula) {
        this.contratosMatricula = contratosMatricula;
    }

    public Boolean getContratosRematricula() {
        return contratosRematricula;
    }

    public void setContratosRematricula(Boolean contratosRematricula) {
        this.contratosRematricula = contratosRematricula;
    }

    public Boolean getContratosRenovacao() {
        return contratosRenovacao;
    }

    public void setContratosRenovacao(Boolean contratosRenovacao) {
        this.contratosRenovacao = contratosRenovacao;
    }

    public String getDescricaoEncantamento() {
        return descricaoEncantamento;
    }

    public void setDescricaoEncantamento(String descricaoEncantamento) {
        this.descricaoEncantamento = descricaoEncantamento;
    }

    public Boolean getRenovavelAutomaticamente() {
        return renovavelAutomaticamente;
    }

    public void setRenovavelAutomaticamente(Boolean renovavelAutomaticamente) {
        this.renovavelAutomaticamente = renovavelAutomaticamente;
    }

    public Integer getQuantidadeMaximaFrequencia() {
        return quantidadeMaximaFrequencia;
    }

    public void setQuantidadeMaximaFrequencia(Integer quantidadeMaximaFrequencia) {
        this.quantidadeMaximaFrequencia = quantidadeMaximaFrequencia;
    }

    public Integer getTipoFrequencia() {
        return tipoFrequencia;
    }

    public void setTipoFrequencia(Integer tipoFrequencia) {
        this.tipoFrequencia = tipoFrequencia;
    }

    public boolean isVendaCreditoTreino() {
        return vendaCreditoTreino;
    }

    public void setVendaCreditoTreino(boolean vendaCreditoTreino) {
        this.vendaCreditoTreino = vendaCreditoTreino;
    }

    public Boolean getRenovarProdutoObrigatorio() {
        return renovarProdutoObrigatorio;
    }

    public void setRenovarProdutoObrigatorio(Boolean renovarProdutoObrigatorio) {
        this.renovarProdutoObrigatorio = renovarProdutoObrigatorio;
    }

    public Integer getDiaDoMesDescontoBoletoPagAntecipado() {
        return diaDoMesDescontoBoletoPagAntecipado;
    }

    public void setDiaDoMesDescontoBoletoPagAntecipado(Integer diaDoMesDescontoBoletoPagAntecipado) {
        this.diaDoMesDescontoBoletoPagAntecipado = diaDoMesDescontoBoletoPagAntecipado;
    }

    public Double getPorcentagemDescontoBoletoPagAntecipado() {
        return porcentagemDescontoBoletoPagAntecipado;
    }

    public void setPorcentagemDescontoBoletoPagAntecipado(Double porcentagemDescontoBoletoPagAntecipado) {
        this.porcentagemDescontoBoletoPagAntecipado = porcentagemDescontoBoletoPagAntecipado;
    }

    public Boolean getAceitaDescontoPorPlano() {
        return aceitaDescontoPorPlano;
    }

    public void setAceitaDescontoPorPlano(Boolean aceitaDescontoPorPlano) {
        this.aceitaDescontoPorPlano = aceitaDescontoPorPlano;
    }

    public void inicializarValoresCalculoExcessaoPacotes(){
           for (PlanoExcecaoVO exc : this.getPlanoExcecaoVOs()) {
                if (!UteisValidacao.emptyNumber(exc.getPacote().getCodigo()) && UteisValidacao.emptyNumber(exc.getModalidade().getCodigo())) {
                    exc.setValorCalculo(0.0);
                    exc.setModalidadesAvaliadas(0);
                    exc.setModalidadesPacoteExcecaoPropria(new HashMap<Integer, PlanoExcecaoVO>());
                }
           }
           for (PlanoExcecaoVO excPacoteModalidade : this.getPlanoExcecaoVOs()) {
                if (!UteisValidacao.emptyNumber(excPacoteModalidade.getPacote().getCodigo()) && !UteisValidacao.emptyNumber(excPacoteModalidade.getModalidade().getCodigo())) {
                    for (PlanoExcecaoVO exc : this.getPlanoExcecaoVOs()) {
                        if (!UteisValidacao.emptyNumber(exc.getPacote().getCodigo()) 
                                && UteisValidacao.emptyNumber(exc.getModalidade().getCodigo()) 
                                && exc.getPacote().getCodigo().equals(excPacoteModalidade.getPacote().getCodigo())
                                && exc.getDuracao().equals(excPacoteModalidade.getDuracao())) {
                            exc.getModalidadesPacoteExcecaoPropria().put(excPacoteModalidade.getModalidade().getCodigo(), excPacoteModalidade);
                        }
                    }
                }
           }
    }

    public boolean isRenovarAnuidadeAutomaticamente() {
        return renovarAnuidadeAutomaticamente;
    }

    public void setRenovarAnuidadeAutomaticamente(boolean renovarAnuidadeAutomaticamente) {
        this.renovarAnuidadeAutomaticamente = renovarAnuidadeAutomaticamente;
    }

    public Boolean getCobrarProdutoSeparado() {
        return cobrarProdutoSeparado;
    }

    public void setCobrarProdutoSeparado(Boolean cobrarProdutoSeparado) {
        this.cobrarProdutoSeparado = cobrarProdutoSeparado;
    }

    public Integer getNrVezesParcelarProduto() {
        return nrVezesParcelarProduto;
    }

    public void setNrVezesParcelarProduto(Integer nrVezesParcelarProduto) {
        this.nrVezesParcelarProduto = nrVezesParcelarProduto;
    }

    public Integer getPontos() {
        if (pontos == null) {
            pontos = 0;
        }
        return pontos;
    }

    public void setPontos(Integer pontos) {
        this.pontos = pontos;
    }

    public PlanoTextoPadraoVO getTermoAceite() {
        if (termoAceite == null) {
            termoAceite = new PlanoTextoPadraoVO();
        }
        return termoAceite;
    }

    public void setTermoAceite(PlanoTextoPadraoVO termoAceite) {
        this.termoAceite = termoAceite;
    }

    public Boolean getTotem() {
        return totem;
    }

    public void setTotem(Boolean totem) {
        this.totem = totem;
    }

    public Boolean getPermitirVendaPlanoTotemNoBalcao() {
        if (totem == null) {
            return false;
        }
        if (!getTotem()) {
            return false;
        }
        return permitirVendaPlanoTotemNoBalcao;
    }

    public void setPermitirVendaPlanoTotemNoBalcao(Boolean permitirVendaPlanoTotemNoBalcao) {
        this.permitirVendaPlanoTotemNoBalcao = permitirVendaPlanoTotemNoBalcao;
    }

    public ConvenioCobrancaVO getConvenioCobrancaPrivateLabel() {
        if (convenioCobrancaPrivateLabel == null) {
            convenioCobrancaPrivateLabel = new ConvenioCobrancaVO();
        }
        return convenioCobrancaPrivateLabel;
    }

    public void setConvenioCobrancaPrivateLabel(ConvenioCobrancaVO convenioCobrancaPrivateLabel) {
        this.convenioCobrancaPrivateLabel = convenioCobrancaPrivateLabel;
    }

    public boolean isRestringirMarcacaoAulasColetivas() {
        return restringirMarcacaoAulasColetivas;
    }

    public void setRestringirMarcacaoAulasColetivas(boolean restringirMarcacaoAulasColetivas) {
        this.restringirMarcacaoAulasColetivas = restringirMarcacaoAulasColetivas;
    }

    public int getRestringirQtdMarcacaoPorDia() {
        return restringirQtdMarcacaoPorDia;
    }

    public void setRestringirQtdMarcacaoPorDia(int restringirQtdMarcacaoPorDia) {
        this.restringirQtdMarcacaoPorDia = restringirQtdMarcacaoPorDia;
    }

    public boolean isCreditoTreinoNaoCumulativo() {
        return creditoTreinoNaoCumulativo;
    }

    public void setCreditoTreinoNaoCumulativo(boolean creditoTreinoNaoCumulativo) {
        this.creditoTreinoNaoCumulativo = creditoTreinoNaoCumulativo;
    }

    public List<PlanoEmpresaVO> getEmpresas() {
        return empresas;
    }

    public void setEmpresas(List<PlanoEmpresaVO> empresas) {
        this.empresas = empresas;
    }

    public boolean isAtualizarEmpresas() {
        return atualizarEmpresas;
    }

    public void setAtualizarEmpresas(boolean atualizarEmpresas) {
        this.atualizarEmpresas = atualizarEmpresas;
    }

    public boolean isPermitirAcessoSomenteNaEmpresaVendeuContrato() {
        return permitirAcessoSomenteNaEmpresaVendeuContrato;
    }

    public void setPermitirAcessoSomenteNaEmpresaVendeuContrato(boolean permitirAcessoSomenteNaEmpresaVendeuContrato) {
        this.permitirAcessoSomenteNaEmpresaVendeuContrato = permitirAcessoSomenteNaEmpresaVendeuContrato;
    }

    public Integer getConvidadosPorMes() {
        if(convidadosPorMes == null){
            convidadosPorMes = 0;
        }
        return convidadosPorMes;
    }

    public void setConvidadosPorMes(Integer convidadosPorMes) {
        this.convidadosPorMes = convidadosPorMes;
    }

    public PlanoTipoVO getPlanoTipo() {
        if (planoTipo == null) {
            this.planoTipo = new PlanoTipoVO();
        }
        return planoTipo;
    }

    public void setPlanoTipo(PlanoTipoVO planoTipo) {
        this.planoTipo = planoTipo;
    }

    public boolean isSelecionado() {
        return selecionado;
    }

    public boolean isFaturar() {
        return faturar;
    }

    public void setFaturar(boolean faturar) {
        this.faturar = faturar;
    }

    public boolean isPlanoPersonal() {
        return planoPersonal;
    }

    public void setPlanoPersonal(boolean planoPersonal) {
        this.planoPersonal = planoPersonal;
    }

    public boolean isCreditoSessao() {
        return creditoSessao;
    }

    public void setCreditoSessao(boolean creditoSessao) {
        this.creditoSessao = creditoSessao;
    }

    public boolean isRenovarAutomaticamenteComDesconto() {
        return renovarAutomaticamenteComDesconto;
    }

    public void setRenovarAutomaticamenteComDesconto(boolean renovarAutomaticamenteComDesconto) {
        this.renovarAutomaticamenteComDesconto = renovarAutomaticamenteComDesconto;
    }

    public Boolean getRecorrencia() {
        if (recorrencia == null)
            recorrencia = false;
        return recorrencia;
    }

    public void setRecorrencia(Boolean recorrencia) {
        this.recorrencia = recorrencia;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PlanoVO planoVO = (PlanoVO) o;
        return Objects.equals(planoRecorrencia, planoVO.planoRecorrencia);
    }

    @Override
    public int hashCode() {
        return Objects.hash(planoRecorrencia);
    }

    public boolean isApresentaVendaRapida() {
        return apresentaVendaRapida;
    }

    public void setApresentaVendaRapida(boolean apresentaVendaRapida) {
        this.apresentaVendaRapida = apresentaVendaRapida;
    }

    public boolean isRenovarAutomaticamenteApenasCondicaoPagamentoRecorrencia() {
        return renovarAutomaticamenteApenasCondicaoPagamentoRecorrencia;
    }

    public void setRenovarAutomaticamenteApenasCondicaoPagamentoRecorrencia(boolean renovarAutomaticamenteApenasCondicaoPagamentoRecorrencia) {
        this.renovarAutomaticamenteApenasCondicaoPagamentoRecorrencia = renovarAutomaticamenteApenasCondicaoPagamentoRecorrencia;
    }

    public String getNomeEmpresaPlanoApresentar(){

        if (getEmpresa().getCodigo() != 0) {
            try {
                EmpresaInterfaceFacade empresaDAO = getFacade().getEmpresa();
                empresa = empresaDAO.consultarPorCodigo(getEmpresa().getCodigo(),Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                return empresa.getNome();
            }catch (Exception e){
                e.getMessage();
                return "";
            }
        }else {
            return "";
        }
    }

    public Boolean getPermiteSituacaoAtestadoContrato() {
        if (permiteSituacaoAtestadoContrato == null) {
            permiteSituacaoAtestadoContrato = false;
        }
        return (permiteSituacaoAtestadoContrato);
    }

    public void setPermiteSituacaoAtestadoContrato(Boolean permiteSituacaoAtestadoContrato) {
        this.permiteSituacaoAtestadoContrato = permiteSituacaoAtestadoContrato;
    }

    public Integer getQtdSemanasAno() {
        return qtdSemanasAno;
    }

    public void setQtdSemanasAno(Integer qtdSemanasAno) {
        this.qtdSemanasAno = qtdSemanasAno;
    }

    public int getMaximoVezesParcelar() {
        return maximoVezesParcelar;
    }

    public void setMaximoVezesParcelar(int maximoVezesParcelar) {
        this.maximoVezesParcelar = maximoVezesParcelar;
    }

    public boolean isNaoRenovarContratoParcelaVencidaAberto() {
        return naoRenovarContratoParcelaVencidaAberto;
    }

    public void setNaoRenovarContratoParcelaVencidaAberto(boolean naoRenovarContratoParcelaVencidaAberto) {
        this.naoRenovarContratoParcelaVencidaAberto = naoRenovarContratoParcelaVencidaAberto;
    }

    public Boolean getDividirManutencaoParcelasEA() {
        return dividirManutencaoParcelasEA;
    }

    public void setDividirManutencaoParcelasEA(Boolean dividirManutencaoParcelasEA) {
        this.dividirManutencaoParcelasEA = dividirManutencaoParcelasEA;
    }

    public boolean getApresentarListaPlanoEmpresas(){
        if(UteisValidacao.emptyList(this.empresas) || this.empresas.size() == 1){
            return false;
        }
        return true;
    }

    public Boolean getAceitaDescontoExtra() {
        return aceitaDescontoExtra;
    }

    public void setAceitaDescontoExtra(Boolean aceitaDescontoExtra) {
        this.aceitaDescontoExtra = aceitaDescontoExtra;
    }

    public Boolean getPermitirVendaPlanoSiteNoBalcao() {
        if(site == null){
            return false;
        }
        if(!getSite()){
            return false;
        }
        return permitirVendaPlanoSiteNoBalcao;
    }

    public void setPermitirVendaPlanoSiteNoBalcao(Boolean permitirVendaPlanoSiteNoBalcao) {
        this.permitirVendaPlanoSiteNoBalcao = permitirVendaPlanoSiteNoBalcao;
    }

    public Boolean getRestringeVendaPorCategoria() {
        return restringeVendaPorCategoria;
    }

    public void setRestringeVendaPorCategoria(Boolean restringeVendaPorCategoria) {
        this.restringeVendaPorCategoria = restringeVendaPorCategoria;
    }

    public List<PlanoCategoriaVO> getPlanoCategoriaVOs() {
        if(planoCategoriaVOs == null){
            planoCategoriaVOs = new ArrayList<>();
        }
        return planoCategoriaVOs;
    }

    public void setPlanoCategoriaVOs(List<PlanoCategoriaVO> planoCategoriaVOs) {
        this.planoCategoriaVOs = planoCategoriaVOs;
    }

    public PlanoEmpresaVO obterPlanoEmpresa(Integer codigoEmpresa) {
        if (getEmpresas() != null && !getEmpresas().isEmpty()) {
            for (PlanoEmpresaVO pEmpresaVO : getEmpresas()) {
                if (pEmpresaVO.getEmpresa().getCodigo().equals(codigoEmpresa)) {
                    return pEmpresaVO;
                }
            }
        }
        return null;
    }

    public int getRestringirQtdMarcacaoPorDiaGeral() {
        return restringirQtdMarcacaoPorDiaGeral;
    }

    public void setRestringirQtdMarcacaoPorDiaGeral(int restringirQtdMarcacaoPorDiaGeral) {
        this.restringirQtdMarcacaoPorDiaGeral = restringirQtdMarcacaoPorDiaGeral;
    }


    public Boolean getRestringirMarcacaoAulaPorNrVezesModalidade() {
        if (restringirMarcacaoAulaPorNrVezesModalidade == null){
            restringirMarcacaoAulaPorNrVezesModalidade = false;
        }
        return restringirMarcacaoAulaPorNrVezesModalidade;
    }

    public void setRestringirMarcacaoAulaPorNrVezesModalidade(Boolean restringirMarcacaoAulaPorNrVezesModalidade) {
        this.restringirMarcacaoAulaPorNrVezesModalidade = restringirMarcacaoAulaPorNrVezesModalidade;
    }

    public boolean isParcelamentoOperadoraDuracao() {
        return parcelamentoOperadoraDuracao;
    }

    public void setParcelamentoOperadoraDuracao(boolean parcelamentoOperadoraDuracao) {
        this.parcelamentoOperadoraDuracao = parcelamentoOperadoraDuracao;
    }

    public Double getValorDescontoBoletoPagAntecipado() {
        if (valorDescontoBoletoPagAntecipado == null) {
            valorDescontoBoletoPagAntecipado = 0.0;
        }
        return valorDescontoBoletoPagAntecipado;
    }

    public void setValorDescontoBoletoPagAntecipado(Double valorDescontoBoletoPagAntecipado) {
        this.valorDescontoBoletoPagAntecipado = valorDescontoBoletoPagAntecipado;
    }

    public boolean isPermitirAcessoRedeEmpresa() {
        return permitirAcessoRedeEmpresa;
    }

    public void setPermitirAcessoRedeEmpresa(boolean permitirAcessoRedeEmpresa) {
        this.permitirAcessoRedeEmpresa = permitirAcessoRedeEmpresa;
    }

    public Integer getQuantidadeCompartilhamentos() {
        if (quantidadeCompartilhamentos == null) {
            quantidadeCompartilhamentos = 0;
        }
        return quantidadeCompartilhamentos;
    }

    public void setQuantidadeCompartilhamentos(Integer quantidadeCompartilhamentos) {
        this.quantidadeCompartilhamentos = quantidadeCompartilhamentos;
    }

    public Boolean getApresentarPactoFlow() {
        return apresentarPactoFlow;
    }

    public void setApresentarPactoFlow(Boolean apresentarPactoFlow) {
        this.apresentarPactoFlow = apresentarPactoFlow;
    }

    public Boolean getRenovarAutomaticamenteUtilizandoValorBaseContrato() {
        return renovarAutomaticamenteUtilizandoValorBaseContrato;
    }

    public void setRenovarAutomaticamenteUtilizandoValorBaseContrato(Boolean renovarAutomaticamenteUtilizandoValorBaseContrato) {
        this.renovarAutomaticamenteUtilizandoValorBaseContrato = renovarAutomaticamenteUtilizandoValorBaseContrato;
    }

    public Boolean getPermitirTurmasVendasOnline() {
        if (permitirTurmasVendasOnline == null) {
            permitirTurmasVendasOnline = false;
        }
        return permitirTurmasVendasOnline;
    }

    public void setPermitirTurmasVendasOnline(Boolean permitirTurmasVendasOnline) {
        this.permitirTurmasVendasOnline = permitirTurmasVendasOnline;
    }

    public String getVideoSiteUrl() {
        return videoSiteUrl;
    }

    public void setVideoSiteUrl(String videoSiteUrl) {
        this.videoSiteUrl = videoSiteUrl;
    }

    public String getObservacaoSite() {
        return observacaoSite;
    }

    public void setObservacaoSite(String observacaoSite) {
        this.observacaoSite = observacaoSite;
    }

    public Boolean getObrigatorioInformarCartaoCreditoVenda() {
        if (obrigatorioInformarCartaoCreditoVenda == null) {
            obrigatorioInformarCartaoCreditoVenda = false;
        }
        return obrigatorioInformarCartaoCreditoVenda;
    }

    public void setObrigatorioInformarCartaoCreditoVenda(Boolean obrigatorioInformarCartaoCreditoVenda) {
        this.obrigatorioInformarCartaoCreditoVenda = obrigatorioInformarCartaoCreditoVenda;
    }

    public boolean isRenovarComDescontoTotem() {
        return renovarComDescontoTotem;
    }

    public void setRenovarComDescontoTotem(boolean renovarComDescontoTotem) {
        this.renovarComDescontoTotem = renovarComDescontoTotem;
    }

    public Integer getDiasBloquearCompraMesmoPlano() {
        return diasBloquearCompraMesmoPlano;
    }

    public void setDiasBloquearCompraMesmoPlano(Integer diasBloquearCompraMesmoPlano) {
        this.diasBloquearCompraMesmoPlano = diasBloquearCompraMesmoPlano;
    }

    public Integer getPlanoDiferenteRenovacao() {
        return planoDiferenteRenovacao;
    }

    public void setPlanoDiferenteRenovacao(Integer planoDiferenteRenovacao) {
        this.planoDiferenteRenovacao = planoDiferenteRenovacao;
    }

    public Integer getHorarioPlanoDiferenteRenovacao() {
        return horarioPlanoDiferenteRenovacao;
    }

    public void setHorarioPlanoDiferenteRenovacao(Integer horarioPlanoDiferenteRenovacao) {
        this.horarioPlanoDiferenteRenovacao = horarioPlanoDiferenteRenovacao;
    }

    public String getModalidadesPlanoDiferenteRenovacao() {
        return modalidadesPlanoDiferenteRenovacao;
    }

    public void setModalidadesPlanoDiferenteRenovacao(String modalidadesPlanoDiferenteRenovacao) {
        this.modalidadesPlanoDiferenteRenovacao = modalidadesPlanoDiferenteRenovacao;
    }

    public Integer getDuracaoPlanoDiferenteRenovacao() {
        return duracaoPlanoDiferenteRenovacao;
    }

    public void setDuracaoPlanoDiferenteRenovacao(Integer duracaoPlanoDiferenteRenovacao) {
        this.duracaoPlanoDiferenteRenovacao = duracaoPlanoDiferenteRenovacao;
    }

    public Integer getCondicaoPagPlanoDiferenteRenovacao() {
        return condicaoPagPlanoDiferenteRenovacao;
    }

    public void setCondicaoPagPlanoDiferenteRenovacao(Integer condicaoPagPlanoDiferenteRenovacao) {
        this.condicaoPagPlanoDiferenteRenovacao = condicaoPagPlanoDiferenteRenovacao;
    }

    public boolean isAcessoRedeEmpresasEspecificas() {
        return acessoRedeEmpresasEspecificas;
    }

    public void setAcessoRedeEmpresasEspecificas(boolean acessoRedeEmpresasEspecificas) {
        this.acessoRedeEmpresasEspecificas = acessoRedeEmpresasEspecificas;
    }

    public boolean isPermitirCompartilharPLanoNoSite() {
        return permitirCompartilharPLanoNoSite;
    }

    public void setPermitirCompartilharPLanoNoSite(boolean permitirCompartilharPLanoNoSite) {
        this.permitirCompartilharPLanoNoSite = permitirCompartilharPLanoNoSite;
    }

    public boolean getBloquearRecompra() {
        return bloquearRecompra;
    }

    public void setBloquearRecompra(boolean bloquearRecompra) {
        this.bloquearRecompra = bloquearRecompra;
    }

    public Boolean getPermitirTransferenciaDeCredito() {
        return permitirTransferenciaDeCredito;
    }

    public void setPermitirTransferenciaDeCredito(Boolean permitirTransferenciaDeCredito) {
        this.permitirTransferenciaDeCredito = permitirTransferenciaDeCredito;
    }

    public Integer getQuantidadeATransferirPermitidaPorAluno() {
        return quantidadeATransferirPermitidaPorAluno;
    }

    public void setQuantidadeATransferirPermitidaPorAluno(Integer quantidadeATransferirPermitidaPorAluno) {
        this.quantidadeATransferirPermitidaPorAluno = quantidadeATransferirPermitidaPorAluno;
    }

    public Date getContratosEncerramDia() {
        return contratosEncerramDia;
    }

    public void setContratosEncerramDia(Date contratosEncerramDia) {
        this.contratosEncerramDia = contratosEncerramDia;
    }

    public Boolean getHabilitarIa() {
        return habilitarIa;
    }

    public void setHabilitarIa(Boolean habilitarIa) {
        this.habilitarIa = habilitarIa;
    }
}

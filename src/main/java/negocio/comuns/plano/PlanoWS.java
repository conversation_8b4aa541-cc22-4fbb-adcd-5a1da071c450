package negocio.comuns.plano;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by glauco on 28/11/2014
 */
public class PlanoWS {

    private int codigoPlano;
    private boolean parcelamentoOperadora = false;
    private String descricao;
    private double valorMatricula;
    private double valorRematricula;
    private double valorRenovacao;
    private double nrVezesParcelarMatricula = 0.0;
    private int codigoHorario;
    private String descricaoHorario;
    private String condicaoPagamento;
    private String descricaoEncantamento;
    private List<PlanoModalidadeWS> modalidades = new ArrayList<PlanoModalidadeWS>();
    private List<Integer> diasVencimento = new ArrayList<>();
    private double valorMensal;
    private double taxaAdesao = 0.0;
    private double valorAnuidade = 0.0;
    private double valorTotalDoPlano = 0.0;
    private int diaAnuidade = 0;
    private String mesAnuidade = "";
    private int mesAnuidadeOrdinal = 0;
    private boolean anuidadeNaParcela;
    private int parcelaAnuidade=0;
    private int duracaoPlano = 0;
    private int quantidadeDiasExtra = 0;
    private int quantidadeCompartilhamentos = 0;
    private String msgValidacao = "";
    private boolean planoPersonal;
//    protected Integer qtdDiasAposVencimentoCancelamentoAutomatico = 0;
//    protected Boolean renovavelAutomaticamente = false;
//    protected Integer plano = 0;
    private List<PlanoProdutoWS> produtos = new ArrayList<PlanoProdutoWS>();
    private List<PlanoRecorrenciaParcelaWS> parcelas = new ArrayList<PlanoRecorrenciaParcelaWS>();
    private List<PlanoAnuidadeParcelaWS> parcelasAnuidade = new ArrayList<PlanoAnuidadeParcelaWS>();
    private List<Integer> empresas = new ArrayList<Integer>();
    private String inicioMinimo;
    private Boolean cobrarPrimeiraParcelaCompra = Boolean.TRUE;
    private int qtdCreditoPlanoCredito = 0;
    private boolean regimeRecorrencia;
    private boolean renovavelAutomaticamente = false;
    private int maximoVezesParcelar = 1;
    private List<Integer> categorias = new ArrayList<>();
    private boolean apresentarPactoFlow;
    private Boolean vendaComTurma = false;
    private String videoSiteUrl;
    private String observacaoSite;
    private Boolean renovarAutomaticamenteUtilizandoValorBaseContrato = false;
    private Boolean obrigatorioInformarCartaoCreditoVenda = false;
    private boolean cobrarProdutoSeparado = false;
    private boolean permitirCompartilharPLanoNoSite = false;
    private boolean cobrarAdesaoSeparado = false; //Utiliza a mesma configuração para adesão e matrícula, no plano Recorrente e no plano com Turma
    private boolean condicaoPagamentoPrePago = false; //Configuração da Condição de Pagamento que gera Parcela 1 e Parcela 2 com o mesmo vencimento
    private boolean bloquearRecompra;
    private boolean habilitarIa;

    public int getCodigoPlano() {
        return codigoPlano;
    }

    public void setCodigoPlano(int codigoPlano) {
        this.codigoPlano = codigoPlano;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public int getCodigoHorario() {
        return codigoHorario;
    }

    public void setCodigoHorario(int codigoHorario) {
        this.codigoHorario = codigoHorario;
    }

    public String getDescricaoHorario() {
        return descricaoHorario;
    }

    public void setDescricaoHorario(String descricaoHorario) {
        this.descricaoHorario = descricaoHorario;
    }

    public List<PlanoModalidadeWS> getModalidades() {
        return modalidades;
    }

    public void setModalidades(List<PlanoModalidadeWS> modalidades) {
        this.modalidades = modalidades;
    }

    public double getValorMensal() {
        return valorMensal;
    }

    public void setValorMensal(double valorMensal) {
        this.valorMensal = valorMensal;
    }

    public String getCondicaoPagamento() {
        return condicaoPagamento;
    }

    public void setCondicaoPagamento(String condicaoPagamento) {
        this.condicaoPagamento = condicaoPagamento;
    }

    public double getValorMatricula() {
        return valorMatricula;
    }

    public void setValorMatricula(double valorMatricula) {
        this.valorMatricula = valorMatricula;
    }

    public String getDescricaoEncantamento() {
        return descricaoEncantamento;
    }

    public void setDescricaoEncantamento(String descricaoEncantamento) {
        this.descricaoEncantamento = descricaoEncantamento;
    }

    public boolean isParcelamentoOperadora() {
        return parcelamentoOperadora;
    }

    public void setParcelamentoOperadora(boolean parcelamentoOperadora) {
        this.parcelamentoOperadora = parcelamentoOperadora;
    }

    public double getTaxaAdesao() {
        return taxaAdesao;
    }

    public void setTaxaAdesao(double taxaAdesao) {
        this.taxaAdesao = taxaAdesao;
    }

    public double getNrVezesParcelarMatricula() {
        return nrVezesParcelarMatricula;
    }

    public void setNrVezesParcelarMatricula(double nrVezesParcelarMatricula) {
        this.nrVezesParcelarMatricula = nrVezesParcelarMatricula;
    }

    public double getValorAnuidade() {
        return valorAnuidade;
    }

    public void setValorAnuidade(double valorAnuidade) {
        this.valorAnuidade = valorAnuidade;
    }

    public int getDiaAnuidade() {
        return diaAnuidade;
    }

    public void setDiaAnuidade(int diaAnuidade) {
        this.diaAnuidade = diaAnuidade;
    }

    public boolean isAnuidadeNaParcela() {
        return anuidadeNaParcela;
    }

    public void setAnuidadeNaParcela(boolean anuidadeNaParcela) {
        this.anuidadeNaParcela = anuidadeNaParcela;
    }

    public int getParcelaAnuidade() {
        return parcelaAnuidade;
    }

    public void setParcelaAnuidade(int parcelaAnuidade) {
        this.parcelaAnuidade = parcelaAnuidade;
    }

    public int getDuracaoPlano() {
        return duracaoPlano;
    }

    public void setDuracaoPlano(int duracaoPlano) {
        this.duracaoPlano = duracaoPlano;
    }

    public String getMesAnuidade() {
        return mesAnuidade;
    }

    public void setMesAnuidade(String mesAnuidade) {
        this.mesAnuidade = mesAnuidade;
    }

    public String getMsgValidacao() {
        return msgValidacao;
    }

    public void setMsgValidacao(String msgValidacao) {
        this.msgValidacao = msgValidacao;
    }

    public List<PlanoProdutoWS> getProdutos() {
        return produtos;
    }

    public void setProdutos(List<PlanoProdutoWS> produtos) {
        this.produtos = produtos;
    }

    public int getMesAnuidadeOrdinal() {
        return mesAnuidadeOrdinal;
    }

    public void setMesAnuidadeOrdinal(int mesAnuidadeOrdinal) {
        this.mesAnuidadeOrdinal = mesAnuidadeOrdinal;
    }

    public List<PlanoRecorrenciaParcelaWS> getParcelas() {
        return parcelas;
    }

    public void setParcelas(List<PlanoRecorrenciaParcelaWS> parcelas) {
        this.parcelas = parcelas;
    }

    public boolean isPlanoPersonal() {
        return planoPersonal;
    }

    public void setPlanoPersonal(boolean planoPersonal) {
        this.planoPersonal = planoPersonal;
    }

    public List<Integer> getEmpresas() {
        return empresas;
    }

    public void setEmpresas(List<Integer> empresas) {
        this.empresas = empresas;
    }

    public String getInicioMinimo() {
        return inicioMinimo;
    }

    public void setInicioMinimo(String inicioMinimo) {
        this.inicioMinimo = inicioMinimo;
    }

    public List<Integer> getDiasVencimento() {
        return diasVencimento;
    }

    public void setDiasVencimento(List<Integer> diasVencimento) {
        this.diasVencimento = diasVencimento;
    }

    public Boolean getCobrarPrimeiraParcelaCompra() {
        return cobrarPrimeiraParcelaCompra;
    }

    public void setCobrarPrimeiraParcelaCompra(Boolean cobrarPrimeiraParcelaCompra) {
        this.cobrarPrimeiraParcelaCompra = cobrarPrimeiraParcelaCompra;
    }

    public int getQtdCreditoPlanoCredito() {
        return qtdCreditoPlanoCredito;
    }

    public void setQtdCreditoPlanoCredito(int qtdCreditoPlanoCredito) {
        this.qtdCreditoPlanoCredito = qtdCreditoPlanoCredito;
    }

    public boolean isRegimeRecorrencia() {
        return regimeRecorrencia;
    }

    public void setRegimeRecorrencia(boolean regimeRecorrencia) {
        this.regimeRecorrencia = regimeRecorrencia;
    }

    public int getMaximoVezesParcelar() {
        return maximoVezesParcelar;
    }

    public void setMaximoVezesParcelar(int maximoVezesParcelar) {
        this.maximoVezesParcelar = maximoVezesParcelar;
    }

    public List<PlanoAnuidadeParcelaWS> getParcelasAnuidade() {
        return parcelasAnuidade;
    }

    public void setParcelasAnuidade(List<PlanoAnuidadeParcelaWS> parcelasAnuidade) {
        this.parcelasAnuidade = parcelasAnuidade;
    }

    public int getQuantidadeDiasExtra() { return quantidadeDiasExtra; }

    public void setQuantidadeDiasExtra(int quantidadeDiasExtra) { this.quantidadeDiasExtra = quantidadeDiasExtra; }

    public int getQuantidadeCompartilhamentos() {
        return quantidadeCompartilhamentos;
    }

    public void setQuantidadeCompartilhamentos(int quantidadeCompartilhamentos) {
        this.quantidadeCompartilhamentos = quantidadeCompartilhamentos;
    }

    public List<Integer> getCategorias() {
        return categorias;
    }

    public void setCategorias(List<Integer> categoria) {
        this.categorias = categoria;
    }

    public boolean isRenovavelAutomaticamente() {
        return renovavelAutomaticamente;
    }

    public void setRenovavelAutomaticamente(boolean renovavelAutomaticamente) {
        this.renovavelAutomaticamente = renovavelAutomaticamente;
    }

    public double getValorTotalDoPlano() {
        return valorTotalDoPlano;
    }

    public void setValorTotalDoPlano(double valorTotalDoPlano) {
        this.valorTotalDoPlano = valorTotalDoPlano;
    }

    public boolean isApresentarPactoFlow() {
        return apresentarPactoFlow;
    }

    public void setApresentarPactoFlow(boolean apresentarPactoFlow) {
        this.apresentarPactoFlow = apresentarPactoFlow;
    }

    public Boolean getRenovarAutomaticamenteUtilizandoValorBaseContrato() {
        return renovarAutomaticamenteUtilizandoValorBaseContrato;
    }

    public void setRenovarAutomaticamenteUtilizandoValorBaseContrato(Boolean renovarAutomaticamenteUtilizandoValorBaseContrato) {
        this.renovarAutomaticamenteUtilizandoValorBaseContrato = renovarAutomaticamenteUtilizandoValorBaseContrato;
    }

    public Boolean getVendaComTurma() {
        return vendaComTurma;
    }

    public void setVendaComTurma(Boolean vendaComTurma) {
        this.vendaComTurma = vendaComTurma;
    }

    public String getVideoSiteUrl() {
        return videoSiteUrl;
    }

    public void setVideoSiteUrl(String videoSiteUrl) {
        this.videoSiteUrl = videoSiteUrl;
    }

    public String getObservacaoSite() {
        return observacaoSite;
    }

    public void setObservacaoSite(String observacaoSite) {
        this.observacaoSite = observacaoSite;
    }

    public Boolean getObrigatorioInformarCartaoCreditoVenda() {
        if (obrigatorioInformarCartaoCreditoVenda == null) {
            obrigatorioInformarCartaoCreditoVenda = false;
        }
        return obrigatorioInformarCartaoCreditoVenda;
    }

    public void setObrigatorioInformarCartaoCreditoVenda(Boolean obrigatorioInformarCartaoCreditoVenda) {
        this.obrigatorioInformarCartaoCreditoVenda = obrigatorioInformarCartaoCreditoVenda;
    }

    public boolean isCobrarProdutoSeparado() {
        return cobrarProdutoSeparado;
    }

    public void setCobrarProdutoSeparado(boolean cobrarProdutoSeparado) {
        this.cobrarProdutoSeparado = cobrarProdutoSeparado;
    }

    public boolean isCobrarAdesaoSeparado() {
        return cobrarAdesaoSeparado;
    }

    public void setCobrarAdesaoSeparado(boolean cobrarAdesaoSeparado) {
        this.cobrarAdesaoSeparado = cobrarAdesaoSeparado;
    }

    public boolean isCondicaoPagamentoPrePago() {
        return condicaoPagamentoPrePago;
    }

    public void setCondicaoPagamentoPrePago(boolean condicaoPagamentoPrePago) {
        this.condicaoPagamentoPrePago = condicaoPagamentoPrePago;
    }
    public Boolean getBloquearRecompra() {
        return bloquearRecompra;
    }

    public void setBloquearRecompra(Boolean bloquearRecompra) {
        this.bloquearRecompra = bloquearRecompra;
    }

    public double getValorRematricula() {
        return valorRematricula;
    }

    public void setValorRematricula(double valorRematricula) {
        this.valorRematricula = valorRematricula;
    }

    public double getValorRenovacao() {
        return valorRenovacao;
    }

    public void setValorRenovacao(double valorRenovacao) {
        this.valorRenovacao = valorRenovacao;
    }
    public boolean isPermitirCompartilharPLanoNoSite() {
        return permitirCompartilharPLanoNoSite;
    }

    public void setPermitirCompartilharPLanoNoSite(boolean permitirCompartilharPLanoNoSite) {
        this.permitirCompartilharPLanoNoSite = permitirCompartilharPLanoNoSite;
    }

    public boolean isHabilitarIa() {
        return habilitarIa;
    }

    public void setHabilitarIa(boolean habilitarIa) {
        this.habilitarIa = habilitarIa;
    }
}


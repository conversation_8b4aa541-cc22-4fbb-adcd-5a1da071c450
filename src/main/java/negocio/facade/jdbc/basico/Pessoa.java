package negocio.facade.jdbc.basico;

import br.com.pactosolucoes.ce.comuns.to.NegociacaoEventoContratoTO;
import br.com.pactosolucoes.ce.comuns.to.PessoaTO;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import br.com.pactosolucoes.enumeradores.SituacaoClienteEnum;
import br.com.pactosolucoes.enumeradores.TipoEnderecoEnum;
import br.com.pactosolucoes.enumeradores.TipoTelefoneEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.CidadeVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.EstadoVO;
import negocio.comuns.basico.GrauInstrucaoVO;
import negocio.comuns.basico.ManutencaoAjusteGeralTO;
import negocio.comuns.basico.PaisVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.ProfissaoVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.basico.enumerador.CargoEnum;
import negocio.comuns.basico.enumerador.FuncaoEnum;
import negocio.comuns.basico.enumerador.TipoAssinaturaBiometricaEnum;
import negocio.comuns.basico.enumerador.TipoPessoa;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.PessoaBloqueioCobrancaTO;
import negocio.comuns.financeiro.PessoaCPFTO;
import negocio.comuns.financeiro.enumerador.TipoBloqueioCobrancaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.acesso.PessoaFotoLocalAcesso;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.PessoaInterfaceFacade;
import org.apache.commons.codec.binary.Base64;
import org.json.JSONObject;
import relatorio.negocio.jdbc.sad.SituacaoClienteSinteticoDW;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.propriedades.PropsService;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import static negocio.comuns.utilitarias.Uteis.NIVELMONTARDADOS_MINIMOS;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos
 * dados da classe
 * <code>PessoaVO</code>. Responsável por implementar operações como incluir,
 * alterar, excluir e consultar pertinentes a classe
 * <code>PessoaVO</code>. Encapsula toda a interação com o banco de dados.
 *
 * @see PessoaVO
 * @see SuperEntidade
 */
public class Pessoa extends SuperEntidade implements PessoaInterfaceFacade {

    private static final String colunas = "PREFIXOwebpage,PREFIXOgrauinstrucao,PREFIXOsexo,PREFIXOnaturalidade,"
            + "PREFIXOnacionalidade,PREFIXOestadocivil,PREFIXOpais,PREFIXOestado,PREFIXOcidade,PREFIXOrguf,PREFIXOrgorgao,PREFIXOrg,PREFIXOcfp,"
            + "PREFIXOnomemae,PREFIXOnomepai,PREFIXOdatanasc,PREFIXOnome,PREFIXOdatacadastro,PREFIXOprofissao,PREFIXOcodigo,PREFIXOsenhaacesso,"
            + "PREFIXOliberaSenhaAcesso,PREFIXOidexterno,PREFIXOfotokey,PREFIXOfoto,PREFIXOtipoPessoa,PREFIXOcnpj,PREFIXOinscEstadual,PREFIXOcfdf,"
            + "PREFIXOinscMunicipal, PREFIXOemitirNotaNomeMae, PREFIXOidvindi,PREFIXOdataAlteracaoVindi, PREFIXOidMaxiPago, PREFIXOdataAlteracaoMaxiPago,"
            + "PREFIXOemitirNomeTerceiro, PREFIXOnomeTerceiro, PREFIXOcpfCNPJTerceiro, PREFIXOobservacaoNota, PREFIXOrne, PREFIXOpassaporte, PREFIXOidGetNet,\n"
            + "PREFIXOdataAlteracaoGetNet, PREFIXOutilizaDotz, PREFIXOcontatoEmergencia, PREFIXOtelefoneEmergencia, PREFIXOcpfPai, PREFIXOcpfMae, PREFIXOinscEstadualTerceiro,\n"
            + "PREFIXOcfdfTerceiro, PREFIXOatualizarDados, PREFIXOaceiteTermosPacto, PREFIXOcargo, PREFIXOfuncao, PREFIXOreceberSMSPacto, PREFIXOreceberEmailNovidadesPacto, PREFIXOrgMae, \n"
            + "PREFIXOrgPai, PREFIXOidMundipagg, PREFIXOdataAlteracaoMundipagg, PREFIXOidPagarMe, PREFIXOdataAlteracaoPagarMe, PREFIXOvalorLimiteCaixaAbertoVendaAvulsa, \n"
            + "PREFIXOcustomerIdPagoLivre, PREFIXOidExternoIntegracao, PREFIXOgenero, PREFIXOemitirNotaNomeAluno, PREFIXOdataNascimentoResponsavel, PREFIXOidAsaas, \n"
            + "PREFIXOdataAlteracaoIdAsaas, PREFIXOemailMae, PREFIXOemailPai, PREFIXOcnpjClienteSesi, PREFIXOnomeResponsavelEmpresa, PREFIXOcpfResponsavelEmpresa, PREFIXOnomeRegistro, \n"
            + "PREFIXOnomeRespFinanceiro, PREFIXOemailRespFinanceiro, PREFIXOcpfRespFinanceiro, PREFIXOrgRespFinanceiro, PREFIXOidStone, PREFIXOdataAlteracaoStone ";


    private static String getColunas(final String prefixo) {
        return colunas.replaceAll("PREFIXO", prefixo);
    }
    private Hashtable enderecos;
    private Hashtable telefones;
    private Hashtable emails;

    public Pessoa() throws Exception {
        super();
        setEnderecos(new Hashtable());
        setTelefones(new Hashtable());
        setEmails(new Hashtable());
    }

    public Pessoa(Connection conexao) throws Exception {
        super(conexao);
        setEnderecos(new Hashtable());
        setTelefones(new Hashtable());
        setEmails(new Hashtable());
    }

    /**
     * Operação responsável por retornar um novo objeto da classe
     * <code>PessoaVO</code>.
     */
    public PessoaVO novo() throws Exception {
        incluir(getIdEntidade());
        return new PessoaVO();
    }

    public void incluirConexaoInicializada(PessoaVO obj, Connection con) throws Exception {
        incluirConexaoInicializada(obj, con, true);
    }

    public void incluirConexaoInicializada(PessoaVO obj, Connection con, boolean validarPermissao) throws Exception {

        if (validarPermissao) {
            incluir(getIdEntidade());
        }
        String sql = "INSERT INTO Pessoa(profissao, dataCadastro, nome, dataNasc, nomePai, nomeMae, cfp, rg, rgOrgao, rgUf, cidade, estado, pais, estadoCivil," +
                " nacionalidade, naturalidade, sexo, grauinstrucao, webPage, foto, tipoPessoa," +
                " cnpj, inscEstadual, cfdf, inscMunicipal, emitirNotaNomeMae, emitirNomeTerceiro, nomeTerceiro," +
                " cpfCNPJTerceiro, inscEstadualTerceiro, cfdfTerceiro, observacaoNota, rne, passaporte, utilizaDotz, contatoEmergencia, telefoneEmergencia, cpfPai, " +
                " cpfMae, rgPai, rgMae, valorLimiteCaixaAbertoVendaAvulsa, nomeconsulta, genero, emitirNotaNomeAluno, dataNascimentoResponsavel, emailmae, emailpai, cnpjclientesesi, nomeRegistro, " +
                "nomeRespFinanceiro, cpfRespFinanceiro, rgRespFinanceiro, emailRespFinanceiro) " +
                " VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, remove_acento_upper(?), ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            if (obj.getProfissao().getCodigo() != 0) {
                sqlInserir.setInt(1, obj.getProfissao().getCodigo());
            } else {
                sqlInserir.setNull(1, 0);
            }
            sqlInserir.setTimestamp(2, Uteis.getDataJDBCTimestamp(obj.getDataCadastro()));
            sqlInserir.setString(3, Uteis.getStringNormalizada(obj.getNome().toUpperCase()));
            sqlInserir.setDate(4, Uteis.getDataJDBC(obj.getDataNasc()));
            sqlInserir.setString(5, obj.getNomePai());
            sqlInserir.setString(6, obj.getNomeMae());
            sqlInserir.setString(7, obj.getCfp());
            sqlInserir.setString(8, obj.getRg());
            sqlInserir.setString(9, obj.getRgOrgao());
            sqlInserir.setString(10, obj.getRgUf());
            if (obj.getCidade().getCodigo() != 0) {
                sqlInserir.setInt(11, obj.getCidade().getCodigo());
            } else {
                sqlInserir.setNull(11, 0);
            }
            if (obj.getEstadoVO().getCodigo() != 0) {
                sqlInserir.setInt(12, obj.getEstadoVO().getCodigo());
            } else {
                sqlInserir.setNull(12, 0);
            }
            if (obj.getPais().getCodigo() != 0) {
                sqlInserir.setInt(13, obj.getPais().getCodigo());
            } else {
                sqlInserir.setNull(13, 0);
            }
            sqlInserir.setString(14, obj.getEstadoCivil());
            sqlInserir.setString(15, obj.getNacionalidade());
            sqlInserir.setString(16, obj.getNaturalidade());
            sqlInserir.setString(17, obj.getSexo());
            if (obj.getGrauInstrucao().getCodigo() != 0) {
                sqlInserir.setInt(18, obj.getGrauInstrucao().getCodigo());
            } else {
                sqlInserir.setNull(18, 0);
            }
            sqlInserir.setString(19, obj.getWebPage());
            sqlInserir.setNull(20, 0);
            sqlInserir.setInt(21, obj.getCategoriaPessoa().getCodigo());
            sqlInserir.setString(22, obj.getCnpj());
            sqlInserir.setString(23, obj.getInscEstadual());
            sqlInserir.setString(24, obj.getCfdf());
            sqlInserir.setString(25, obj.getInscMunicipal());
            sqlInserir.setBoolean(26, obj.isEmitirNotaNomeMae());
            sqlInserir.setBoolean(27, obj.isEmitirNomeTerceiro());
            sqlInserir.setString(28, obj.getNomeTerceiro());
            sqlInserir.setString(29, obj.getCpfCNPJTerceiro());

            sqlInserir.setString(30, obj.getInscEstadualTerceiro());
            sqlInserir.setString(31, obj.getCfdfTerceiro());

            sqlInserir.setString(32, obj.getObservacaoNota());
            sqlInserir.setString(33, obj.getRne());
            sqlInserir.setString(34, obj.getPassaporte());
            sqlInserir.setBoolean(35, obj.isUtilizaDotz());
            sqlInserir.setString(36, obj.getContatoEmergencia());
            sqlInserir.setString(37, obj.getTelefoneEmergencia());
            sqlInserir.setString(38, obj.getCpfPai());
            sqlInserir.setString(39, obj.getCpfMae());
            sqlInserir.setString(40, obj.getRgPai());
            sqlInserir.setString(41, obj.getRgMae());
            sqlInserir.setDouble(42, obj.getValorLimiteCaixaAbertoVendaAvulsa());
            sqlInserir.setString(43, Uteis.getStringNormalizada(obj.getNome().toUpperCase()));
            sqlInserir.setString(44, obj.getGenero());
            sqlInserir.setBoolean(45, obj.isEmitirNotaNomeAluno());
            if (obj.getDataNasc() != null) {
                sqlInserir.setDate(46, Uteis.getDataJDBC(obj.getDataNascimentoResponsavel()));
            } else {
                sqlInserir.setNull(46, 0);
            }
            sqlInserir.setString(47, obj.getEmailMae());
            sqlInserir.setString(48, obj.getEmailPai());
            sqlInserir.setString(49, obj.getCnpjSesi());
            sqlInserir.setString(50, obj.getNomeRegistro());
            sqlInserir.setString(51, obj.getNomeRespFinanceiro());
            sqlInserir.setString(52, obj.getCpfRespFinanceiro());
            sqlInserir.setString(53, obj.getRgRespFinanceiro());
            sqlInserir.setString(54, obj.getEmailRespFinanceiro());
            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
        Endereco endereco = new Endereco(con);
        endereco.incluirEnderecos(obj.getCodigo(), obj.getEnderecoVOs(), validarPermissao);
        endereco = null;

        Telefone telefone = new Telefone(con);
        telefone.incluirTelefones(obj.getCodigo(), obj.getTelefoneVOs(), validarPermissao);
        telefone = null;

        Email email = new Email(con);
        email.incluirEmails(obj.getCodigo(), obj.getEmailVOs(), validarPermissao);
        email = null;

    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe
     * <code>PessoaVO</code>. Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>PessoaVO</code> que será gravado no
     * banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    public void incluir(PessoaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            incluir(getIdEntidade());
            incluirConexaoInicializada(obj, con);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void incluirSemComit(PessoaVO obj) throws Exception {
        String sql = "INSERT INTO Pessoa(profissao, dataCadastro, nome, dataNasc, nomePai, nomeMae, cfp, rg, rgOrgao, rgUf, cidade, estado, pais," +
                " estadoCivil, nacionalidade, naturalidade, sexo, grauinstrucao, webPage, foto, emitirNotaNomeMae, emitirNomeTerceiro," +
                " nomeTerceiro, cpfCNPJTerceiro, inscEstadualTerceiro, cfdfTerceiro, observacaoNota, rne, passaporte, utilizaDotz, contatoEmergencia, " +
                "telefoneEmergencia, cpfPai, cpfMae, rgPai, rgMae, nomeconsulta, genero, emitirNotaNomeAluno, dataNascimentoResponsavel, cnpjclientesesi, nomeRegistro)" +
                " VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, remove_acento_upper(?), ?, ?, ?, ?, ?)";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            if (obj.getProfissao().getCodigo() != 0) {
                sqlInserir.setInt(1, obj.getProfissao().getCodigo());
            } else {
                sqlInserir.setNull(1, 0);
            }
            sqlInserir.setTimestamp(2, Uteis.getDataJDBCTimestamp(obj.getDataCadastro()));
            sqlInserir.setString(3, Uteis.getStringNormalizada(obj.getNome().toUpperCase()));
            sqlInserir.setDate(4, Uteis.getDataJDBC(obj.getDataNasc()));
            sqlInserir.setString(5, obj.getNomePai());
            sqlInserir.setString(6, obj.getNomeMae());
            sqlInserir.setString(7, obj.getCfp());
            sqlInserir.setString(8, obj.getRg());
            sqlInserir.setString(9, obj.getRgOrgao());
            sqlInserir.setString(10, obj.getRgUf());
            if (obj.getCidade().getCodigo() != 0) {
                sqlInserir.setInt(11, obj.getCidade().getCodigo());
            } else {
                sqlInserir.setNull(11, 0);
            }
            if (obj.getEstadoVO().getCodigo() != 0) {
                sqlInserir.setInt(12, obj.getEstadoVO().getCodigo());
            } else {
                sqlInserir.setNull(12, 0);
            }
            if (obj.getPais().getCodigo() != 0) {
                sqlInserir.setInt(13, obj.getPais().getCodigo());
            } else {
                sqlInserir.setNull(13, 0);
            }
            sqlInserir.setString(14, obj.getEstadoCivil());
            sqlInserir.setString(15, obj.getNacionalidade());
            sqlInserir.setString(16, obj.getNaturalidade());
            sqlInserir.setString(17, obj.getSexo());
            if (obj.getGrauInstrucao().getCodigo() != 0) {
                sqlInserir.setInt(18, obj.getGrauInstrucao().getCodigo());
            } else {
                sqlInserir.setNull(18, 0);
            }
            sqlInserir.setString(19, obj.getWebPage());
            sqlInserir.setNull(20, 0);
            sqlInserir.setBoolean(21, obj.isEmitirNotaNomeMae());
            sqlInserir.setBoolean(22, obj.isEmitirNomeTerceiro());
            sqlInserir.setString(23, obj.getNomeTerceiro());
            sqlInserir.setString(24, obj.getCpfCNPJTerceiro());

            sqlInserir.setString(25, obj.getInscEstadualTerceiro());
            sqlInserir.setString(26, obj.getCfdfTerceiro());

            sqlInserir.setString(27, obj.getObservacaoNota());
            sqlInserir.setString(28, obj.getRne());
            sqlInserir.setString(29, obj.getPassaporte());
            sqlInserir.setBoolean(30, obj.isUtilizaDotz());
            sqlInserir.setString(31, obj.getContatoEmergencia());
            sqlInserir.setString(32, obj.getTelefoneEmergencia());
            sqlInserir.setString(33, obj.getCpfPai());
            sqlInserir.setString(34, obj.getCpfMae());
            sqlInserir.setString(35, obj.getRgPai());
            sqlInserir.setString(36, obj.getRgMae());
            sqlInserir.setString(37, Uteis.getStringNormalizada(obj.getNome().toUpperCase()));
            sqlInserir.setString(38, obj.getGenero());
            sqlInserir.setBoolean(39, obj.isEmitirNotaNomeAluno());
            if (obj.getDataNasc() != null) {
                sqlInserir.setDate(40, Uteis.getDataJDBC(obj.getDataNascimentoResponsavel()));
            } else {
                sqlInserir.setNull(40, 0);
            }
            sqlInserir.setString(41, obj.getCnpjSesi());
            sqlInserir.setString(42, obj.getNomeRegistro());

            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);

        Endereco enderecoDAO = new Endereco(con);
        enderecoDAO.incluirEnderecos(obj.getCodigo(), obj.getEnderecoVOs());
        enderecoDAO = null;

        Telefone telefoneDAO = new Telefone(con);
        telefoneDAO.incluirTelefones(obj.getCodigo(), obj.getTelefoneVOs());
        telefoneDAO = null;

        Email emailDAO = new Email(con);
        emailDAO.incluirEmails(obj.getCodigo(), obj.getEmailVOs());
        emailDAO = null;
    }
    public void alterarConexaoInicializada(PessoaVO obj, Connection con) throws Exception {
        alterarConexaoInicializada(obj, con,true);
    }
    public void alterarConexaoInicializadaSemPermissao(PessoaVO obj, Connection con) throws Exception {
        alterarConexaoInicializada(obj, con,false);
    }
    public void alterarConexaoInicializada(PessoaVO obj, Connection con,boolean validarPermissao) throws Exception {
        if(validarPermissao) {
            alterar(getIdEntidade());
        }
        obj.realizarUpperCaseDados();
        String sql = "";
        sql = "UPDATE Pessoa set profissao=?, dataCadastro=?, nome=?, dataNasc=?, "
                + "nomePai=?, nomeMae=?, cfp=?, rg=?, rgOrgao=?, rgUf=?, cidade=?, "
                + "estado=?, pais=?, estadoCivil=?, nacionalidade=?, naturalidade=?, "
                + "sexo=?, grauinstrucao=?, webPage=? , tipoPessoa = ?, cnpj = ?, inscEstadual = ? , "
                + "cfdf = ?, inscMunicipal = ?, emitirNotaNomeMae = ?, emitirNomeTerceiro = ?, emitirNotaNomeAluno = ?, "
                + "nomeTerceiro = ?, cpfCNPJTerceiro = ?, inscEstadualTerceiro = ?, cfdfTerceiro = ?, observacaoNota = ?, "
                + "rne = ?, passaporte = ?, utilizaDotz = ?, contatoEmergencia = ?, telefoneEmergencia = ?, "
                + "cpfPai = ?, cpfMae = ?, rgPai = ?, rgMae = ?, valorLimiteCaixaAbertoVendaAvulsa = ?, "
                + "nomeconsulta = remove_acento_upper(?), genero = ?, dataNascimentoResponsavel = ?,"
                + "emailMae = ?, emailPai = ?, cnpjclientesesi = ?, nomeResponsavelEmpresa = ?, cpfResponsavelEmpresa = ?, "
                + "nomeRegistro = ? \n"
                + " WHERE codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            int i = 0;
            resolveFKNull(sqlAlterar, ++i, obj.getProfissao().getCodigo());
            sqlAlterar.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataCadastro()));
            sqlAlterar.setString(++i, Uteis.getStringNormalizada(obj.getNome().toUpperCase()));
            sqlAlterar.setDate(++i, Uteis.getDataJDBC(obj.getDataNasc()));
            sqlAlterar.setString(++i, obj.getNomePai());
            sqlAlterar.setString(++i, obj.getNomeMae());
            sqlAlterar.setString(++i, obj.getCfp());
            sqlAlterar.setString(++i, obj.getRg());
            sqlAlterar.setString(++i, obj.getRgOrgao());
            sqlAlterar.setString(++i, obj.getRgUf());
            resolveFKNull(sqlAlterar, ++i, obj.getCidade().getCodigo());
            resolveFKNull(sqlAlterar, ++i, obj.getEstadoVO().getCodigo());
            resolveFKNull(sqlAlterar, ++i, obj.getPais().getCodigo());
            sqlAlterar.setString(++i, obj.getEstadoCivil());
            sqlAlterar.setString(++i, obj.getNacionalidade());
            sqlAlterar.setString(++i, obj.getNaturalidade());
            sqlAlterar.setString(++i, obj.getSexo());
            resolveFKNull(sqlAlterar, ++i, obj.getGrauInstrucao().getCodigo());
            sqlAlterar.setString(++i, obj.getWebPage());
            sqlAlterar.setInt(++i, obj.getCategoriaPessoa().getCodigo());
            sqlAlterar.setString(++i, obj.getCnpj());
            sqlAlterar.setString(++i, obj.getInscEstadual());
            sqlAlterar.setString(++i, obj.getCfdf());
            sqlAlterar.setString(++i, obj.getInscMunicipal());
            sqlAlterar.setBoolean(++i, obj.isEmitirNotaNomeMae());
            sqlAlterar.setBoolean(++i, obj.isEmitirNomeTerceiro());
            sqlAlterar.setBoolean(++i, obj.isEmitirNotaNomeAluno());
            sqlAlterar.setString(++i, obj.getNomeTerceiro());
            sqlAlterar.setString(++i, obj.getCpfCNPJTerceiro());
            sqlAlterar.setString(++i, obj.getInscEstadualTerceiro());
            sqlAlterar.setString(++i, obj.getCfdfTerceiro());
            sqlAlterar.setString(++i, obj.getObservacaoNota());
            sqlAlterar.setString(++i, obj.getRne());
            sqlAlterar.setString(++i, obj.getPassaporte());
            sqlAlterar.setBoolean(++i, obj.isUtilizaDotz());
            sqlAlterar.setString(++i, obj.getContatoEmergencia());
            sqlAlterar.setString(++i, obj.getTelefoneEmergencia());
            sqlAlterar.setString(++i, obj.getCpfPai());
            sqlAlterar.setString(++i, obj.getCpfMae());
            sqlAlterar.setString(++i, obj.getRgPai());
            sqlAlterar.setString(++i, obj.getRgMae());
            sqlAlterar.setDouble(++i, obj.getValorLimiteCaixaAbertoVendaAvulsa());
            sqlAlterar.setString(++i, Uteis.getStringNormalizada(obj.getNome().toUpperCase()));
            sqlAlterar.setString(++i, obj.getGenero());
            if (obj.getDataNasc() != null) {
                sqlAlterar.setDate(++i, Uteis.getDataJDBC(obj.getDataNascimentoResponsavel()));
            } else {
                sqlAlterar.setNull(++i, 0);
            }
            sqlAlterar.setString(++i, obj.getEmailMae());
            sqlAlterar.setString(++i, obj.getEmailPai());
            sqlAlterar.setString(++i, obj.getCnpjSesi());
            sqlAlterar.setString(++i, obj.getNomeResponsavelEmpresa());
            sqlAlterar.setString(++i, obj.getCpfResponsavelEmpresa());
            sqlAlterar.setString(++i, obj.getNomeRegistro());

            sqlAlterar.setInt(++i, obj.getCodigo());
            sqlAlterar.execute();
        }
        Endereco enderecoDao = new Endereco(con);
        enderecoDao.alterarEnderecos(obj.getCodigo(), obj.getEnderecoVOs(), obj);
        enderecoDao = null;

        Telefone telefoneDao = new Telefone(con);
        telefoneDao.alterarTelefones(obj.getCodigo(), obj.getTelefoneVOs(), obj.getEmpresaInternacional());
        telefoneDao = null;

        Email emailDao = new Email(con);
        emailDao.alterarEmails(obj.getCodigo(), obj.getEmailVOs());
        emailDao = null;

        Placa placaDao = new Placa(con);
        placaDao.alterarPlacas(obj.getCodigo(), obj.getPlacaVOs());
        placaDao = null;
    }

    /**
     * Metodo usado no FINANCEIRO para cadastrar uma pessoa simplificado
     *
     * @param obj PessoaVO
     * @throws Exception
     */
    public void incluirPessoaSimplificado(PessoaVO obj) throws Exception {
        incluir(getIdEntidade());
        PessoaVO.validarDadosSimplificado(obj);
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO Pessoa(dataCadastro, nome, dataNasc, cfp, "
                + "cidade, estado, pais, sexo, cnpj, tipopessoa, nomepai, nomemae, "
                + "cpfpai, cpfmae, nomeconsulta, genero, rg, dataNascimentoResponsavel, nomeResponsavelEmpresa, cpfResponsavelEmpresa, nomeRegistro, passaporte) "
                + "VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, remove_acento_upper(?), ?, ?, ?, ?, ?, ?, ?)";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            int i = 0;

            sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataCadastro()));
            sqlInserir.setString(++i, Uteis.getStringNormalizada(obj.getNome()));
            if (obj.getDataNasc() != null) {
                sqlInserir.setDate(++i, Uteis.getDataJDBC(obj.getDataNasc()));
            } else {
                sqlInserir.setNull(++i, 0);
            }
            if (!obj.getCfp().isEmpty()) {
                sqlInserir.setString(++i, obj.getCfp());
            } else {
                sqlInserir.setNull(++i, 0);
            }
            if (obj.getCidade().getCodigo() != 0) {
                sqlInserir.setInt(++i, obj.getCidade().getCodigo());
            } else {
                sqlInserir.setNull(++i, 0);
            }
            if (obj.getEstadoVO().getCodigo() != 0) {
                sqlInserir.setInt(++i, obj.getEstadoVO().getCodigo());
            } else {
                sqlInserir.setNull(++i, 0);
            }
            if (obj.getPais().getCodigo() != 0) {
                sqlInserir.setInt(++i, obj.getPais().getCodigo());
            } else {
                sqlInserir.setNull(++i, 0);
            }
            if (!obj.getSexo().isEmpty()) {
                sqlInserir.setString(++i, obj.getSexo());
            } else {
                sqlInserir.setNull(++i, 0);
            }
            if (!UteisValidacao.emptyString(obj.getCnpj())) {
                sqlInserir.setString(++i, obj.getCnpj());
            } else {
                sqlInserir.setNull(++i, 0);
            }

            sqlInserir.setInt(++i, obj.getCategoriaPessoa().getCodigo());

            if (!UteisValidacao.emptyString(obj.getNomePai())) {
                sqlInserir.setString(++i, obj.getNomePai());
            } else {
                sqlInserir.setNull(++i, 0);
            }

            if (!UteisValidacao.emptyString(obj.getNomeMae())) {
                sqlInserir.setString(++i, obj.getNomeMae());
            } else {
                sqlInserir.setNull(++i, 0);
            }

            if (!UteisValidacao.emptyString(obj.getCpfPai())) {
                sqlInserir.setString(++i, obj.getCpfPai());
            } else {
                sqlInserir.setNull(++i, 0);
            }

            if (!UteisValidacao.emptyString(obj.getCpfMae())) {
                sqlInserir.setString(++i, obj.getCpfMae());
            } else {
                sqlInserir.setNull(++i, 0);
            }

            sqlInserir.setString(++i, Uteis.getStringNormalizada(obj.getNome()));

            if (!obj.getGenero().isEmpty()) {
                sqlInserir.setString(++i, obj.getGenero());
            } else {
                sqlInserir.setNull(++i, 0);
            }
            if (!UteisValidacao.emptyString(obj.getRg())) {
                sqlInserir.setString(++i, obj.getRg());
            } else {
                sqlInserir.setNull(++i, 0);
            }

            if (obj.getDataNasc() != null) {
                sqlInserir.setDate(++i, Uteis.getDataJDBC(obj.getDataNascimentoResponsavel()));
            } else {
                sqlInserir.setNull(++i, 0);
            }
            if (obj.getNomeResponsavelEmpresa() != null) {
                sqlInserir.setString(++i, obj.getNomeResponsavelEmpresa());
            } else {
                sqlInserir.setNull(++i, 0);
            }
            if (obj.getCpfResponsavelEmpresa() != null) {
                sqlInserir.setString(++i, obj.getCpfResponsavelEmpresa());
            } else {
                sqlInserir.setNull(++i, 0);
            }

            if (!obj.getNomeRegistro().isEmpty()) {
                sqlInserir.setString(++i, obj.getNomeRegistro());
            } else {
                sqlInserir.setNull(++i, 0);
            }

            if (!UteisValidacao.emptyString(obj.getPassaporte())) {
                sqlInserir.setString(++i, obj.getPassaporte());
            } else {
                sqlInserir.setNull(++i, 0);
            }

            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
        Endereco endereco = new Endereco(con);
        endereco.incluirEnderecos(obj.getCodigo(), obj.getEnderecoVOs());
        endereco = null;
        Telefone telefone = new Telefone(con);
        telefone.incluirTelefones(obj.getCodigo(), obj.getTelefoneVOs());
        telefone = null;
        Email email = new Email(con);
        email.incluirEmails(obj.getCodigo(), obj.getEmailVOs());
        email = null;

    }

    @Override
    public void alterarNomePessoaResponsavelAluno(PessoaVO obj) throws Exception {
        String sql = "UPDATE pessoa set nome = ? WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setString(1, obj.getNome());
            ps.setInt(2, obj.getCodigo());
            ps.executeUpdate();
        }
    }

    public PessoaVO incluirPessoaResponsavelAluno(PessoaVO obj) throws Exception {
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO Pessoa(nome, cfp, rg, nomeconsulta) "
                + "VALUES ( ?, ?, ?, remove_acento_upper(?))";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            int i = 0;
            sqlInserir.setString(++i, Uteis.getStringNormalizada(obj.getNome()));

            if (!UteisValidacao.emptyString(obj.getCfp())) {
                sqlInserir.setString(++i, obj.getCfp());
            } else {
                sqlInserir.setNull(++i, 0);
            }
            if (!UteisValidacao.emptyString(obj.getRg())) {
                sqlInserir.setString(++i, obj.getRg());
            } else {
                sqlInserir.setNull(++i, 0);
            }
            sqlInserir.setString(++i, Uteis.getStringNormalizada(obj.getNome()));

            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
        Endereco endereco = new Endereco(con);
        endereco.incluirEnderecos(obj.getCodigo(), obj.getEnderecoVOs());
        return obj;
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe
     * <code>PessoaVO</code>. Sempre utiliza a chave primária da classe como
     * atributo para localização do registro a ser alterado. Primeiramente
     * valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>alterar</code> da superclasse.
     *
     * @param obj Objeto da classe <code>PessoaVO</code> que será alterada no
     * banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    public void alterar(PessoaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterar(getIdEntidade());
            alterarConexaoInicializada(obj, con);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void alterarPessoaSiteSemCommit(Integer codigoPessoa,
                                           String endereco,
                                           String complemento,
                                           String numero,
                                           String bairro,
                                           String cep,
                                           final Integer codigoCidade,
                                           final Integer codigoEstado,
                                           String telCelular,
                                           String telResidencial,
                                           Connection con) throws Exception{
        String sql = "update pessoa set cidade = ?, estado=? where codigo = ? ";
        try (PreparedStatement pst = con.prepareStatement(sql)) {
            pst.setInt(1, codigoCidade);
            pst.setInt(2, codigoEstado);
            pst.setInt(3, codigoPessoa);
            pst.execute();
        }
        Telefone telefoneDao = new Telefone(con);
        Endereco enderecoDao = new Endereco(con);
        try{
            // Alterar/incluir telefone residencial
            if ((telResidencial != null) && (!telResidencial.trim().equals(""))){
                TelefoneVO telefoneVO = telefoneDao.consultar(codigoPessoa, TipoTelefoneEnum.RESIDENCIAL, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                if (telefoneVO == null){
                    telefoneVO = new TelefoneVO();
                    telefoneVO.setPessoa(codigoPessoa);
                    telefoneVO.setTipoTelefone(TipoTelefoneEnum.RESIDENCIAL.getCodigo());
                    telefoneVO.setNumero(telResidencial);
                    telefoneVO.setDescricao("");
                    telefoneDao.incluir(telefoneVO);
                }else{
                    String sqlUpdateTel = "update telefone set numero = ? where codigo = ?";
                    try (PreparedStatement pstTelRes = con.prepareStatement(sqlUpdateTel)) {
                        pstTelRes.setString(1, telResidencial);
                        pstTelRes.setInt(2, telefoneVO.getCodigo());
                        pstTelRes.execute();
                    }
                }
            }
            // Alterar/incluir telefone celular
            if ((telCelular != null) && (!telCelular.trim().equals(""))){
                TelefoneVO telefoneVO = telefoneDao.consultar(codigoPessoa, TipoTelefoneEnum.CELULAR, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                if (telefoneVO == null){
                    telefoneVO = new TelefoneVO();
                    telefoneVO.setPessoa(codigoPessoa);
                    telefoneVO.setTipoTelefone(TipoTelefoneEnum.CELULAR.getCodigo());
                    telefoneVO.setNumero(telResidencial);
                    telefoneVO.setDescricao("");
                    telefoneDao.incluir(telefoneVO);
                }else{
                    String sqlUpdateTel = "update telefone set numero = ? where codigo = ?";
                    try (PreparedStatement pstTelRes = con.prepareStatement(sqlUpdateTel)) {
                        pstTelRes.setString(1, telCelular);
                        pstTelRes.setInt(2, telefoneVO.getCodigo());
                        pstTelRes.execute();
                    }
                }
            }
            EnderecoVO enderecoVO = enderecoDao.consultar(codigoPessoa, TipoEnderecoEnum.RESIDENCIAL, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            if (enderecoVO != null){
                enderecoVO.setEndereco(endereco);
                enderecoVO.setComplemento(complemento);
                enderecoVO.setNumero(numero);
                enderecoVO.setBairro(bairro);
                enderecoVO.setCep(cep);
                enderecoDao.alterar(enderecoVO);
            }

        }finally {
            telefoneDao = null;
            enderecoDao = null;
        }
    }



    public void alterarSemComit(PessoaVO obj) throws Exception {
        obj.realizarUpperCaseDados();
        String sql = "";
        sql = "UPDATE Pessoa set profissao=?, dataCadastro=?, nome=?, dataNasc=?, "
                + "nomePai=?, nomeMae=?, cfp=?, rg=?, rgOrgao=?, rgUf=?, cidade=?, "
                + "estado=?, pais=?, estadoCivil=?, nacionalidade=?, naturalidade=?, "
                + "sexo=?, grauinstrucao=?, webPage=?, emitirNotaNomeMae = ?, emitirNomeTerceiro = ?, "
                + "nomeTerceiro = ?, cpfCNPJTerceiro = ?, inscestadualterceiro = ?, cfdfterceiro = ?, "
                + "emitirNotaNomeAluno = ?, observacaoNota = ?, rne = ?, passaporte = ?, utilizaDotz = ?, "
                + "dataNascimentoResponsavel = ?, cnpjclientesesi = ?\n"
                + "WHERE ((codigo = ?))";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            if (obj.getProfissao().getCodigo() != 0) {
                sqlAlterar.setInt(1, obj.getProfissao().getCodigo());
            } else {
                sqlAlterar.setNull(1, 0);
            }
            sqlAlterar.setTimestamp(2, Uteis.getDataJDBCTimestamp(obj.getDataCadastro()));
            sqlAlterar.setString(3, Uteis.getStringNormalizada(obj.getNome().toUpperCase()));
            sqlAlterar.setDate(4, Uteis.getDataJDBC(obj.getDataNasc()));
            sqlAlterar.setString(5, obj.getNomePai());
            sqlAlterar.setString(6, obj.getNomeMae());
            sqlAlterar.setString(7, obj.getCfp());
            sqlAlterar.setString(8, obj.getRg());
            sqlAlterar.setString(9, obj.getRgOrgao());
            sqlAlterar.setString(10, obj.getRgUf());
            if (obj.getCidade().getCodigo() != 0) {
                sqlAlterar.setInt(11, obj.getCidade().getCodigo());
            } else {
                sqlAlterar.setNull(11, 0);
            }
            if (obj.getEstadoVO().getCodigo() != 0) {
                sqlAlterar.setInt(12, obj.getEstadoVO().getCodigo());
            } else {
                sqlAlterar.setNull(12, 0);
            }
            if (obj.getPais().getCodigo() != 0) {
                sqlAlterar.setInt(13, obj.getPais().getCodigo());
            } else {
                sqlAlterar.setNull(13, 0);
            }
            sqlAlterar.setString(14, obj.getEstadoCivil());
            sqlAlterar.setString(15, obj.getNacionalidade());
            sqlAlterar.setString(16, obj.getNaturalidade());
            sqlAlterar.setString(17, obj.getSexo());
            if (obj.getGrauInstrucao().getCodigo() != 0) {
                sqlAlterar.setInt(18, obj.getGrauInstrucao().getCodigo());
            } else {
                sqlAlterar.setNull(18, 0);
            }
            sqlAlterar.setString(19, obj.getWebPage());
            sqlAlterar.setBoolean(20, obj.isEmitirNotaNomeMae());
            sqlAlterar.setBoolean(21, obj.isEmitirNomeTerceiro());
            sqlAlterar.setString(22, obj.getNomeTerceiro());
            sqlAlterar.setString(23, obj.getCpfCNPJTerceiro());

            sqlAlterar.setString(24, obj.getInscEstadualTerceiro());
            sqlAlterar.setString(25, obj.getCfdfTerceiro());

            sqlAlterar.setBoolean(26, obj.isEmitirNotaNomeAluno());
            sqlAlterar.setString(27, obj.getObservacaoNota());
            sqlAlterar.setString(28, obj.getRne());
            sqlAlterar.setString(29, obj.getPassaporte());
            sqlAlterar.setBoolean(30, obj.isUtilizaDotz());
            if (obj.getDataNasc() != null) {
                sqlAlterar.setDate(31, Uteis.getDataJDBC(obj.getDataNascimentoResponsavel()));
            } else {
                sqlAlterar.setNull(31, 0);
            }
            sqlAlterar.setString(32, obj.getCnpjSesi());
            sqlAlterar.setInt(33, obj.getCodigo());

            sqlAlterar.execute();
        }

        Endereco enderecoDAO;
        Telefone telefoneDAO;
        Email emailDAO;
        try {
            enderecoDAO = new Endereco(con);
            telefoneDAO = new Telefone(con);
            emailDAO = new Email(con);

            enderecoDAO.alterarEnderecos(obj.getCodigo(), obj.getEnderecoVOs());
            telefoneDAO.alterarTelefones(obj.getCodigo(), obj.getTelefoneVOs(), obj.getEmpresaInternacional());
            emailDAO.alterarEmails(obj.getCodigo(), obj.getEmailVOs());
        } finally {
            enderecoDAO = null;
            telefoneDAO = null;
            emailDAO = null;
        }
    }

    public void excluirConexaoInicializada(PessoaVO obj, Connection con) throws Exception {
        Endereco enderecoDAO;
        Telefone telefoneDAO;
        Email emailDAO;
        try {
            enderecoDAO = new Endereco(con);
            telefoneDAO = new Telefone(con);
            emailDAO = new Email(con);

            String sql = "DELETE FROM Pessoa WHERE ((codigo = ?))";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo());
                sqlExcluir.execute();
            }

            enderecoDAO.excluirEnderecos(obj.getCodigo());
            telefoneDAO.excluirTelefones(obj.getCodigo());
            emailDAO.excluirEmails(obj.getCodigo());
        } finally {
            enderecoDAO = null;
            telefoneDAO = null;
            emailDAO = null;
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe
     * <code>PessoaVO</code>. Sempre localiza o registro a ser excluído através
     * da chave primária da entidade. Primeiramente verifica a conexão com o
     * banco de dados e a permissão do usuário para realizar esta operacão na
     * entidade. Isto, através da operação
     * <code>excluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>PessoaVO</code> que será removido no
     * banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public void excluir(PessoaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluir(getIdEntidade());
            excluirConexaoInicializada(obj, con);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Pessoa</code> através do valor do atributo
     * <code>nome</code> da classe
     * <code>Cidade</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da classe <code>PessoaVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorNomeCidade(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT " + getColunas("Pessoa.") + " FROM Pessoa, Cidade WHERE Pessoa.cidade = Cidade.codigo and upper( Cidade.nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Cidade.nome";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    public List consultarPorNomeEmpresa(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT " + getColunas("Pessoa.") + " FROM Pessoa, Empresa WHERE Pessoa.empresa = Empresa.codigo and upper( Empresa.nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Pessoa.nome";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Pessoa</code> através do valor do atributo
     * <code>String cfp</code>. Retorna os objetos, com início do valor do
     * atributo idêntico ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>PessoaVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List<PessoaVO> consultarPorCfp(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT " + getColunas("") + " FROM Pessoa WHERE upper( cfp ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY cfp";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public PessoaVO consultarPorCPF(String cpf, int nivelMontarDados) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT " + getColunas("") + " FROM Pessoa where cfp = '").append(cpf).append("'");
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                if (rs.next()) {
                    return montarDados(rs, nivelMontarDados, con);
                }
            }
        }
        return null;
    }

    public PessoaVO consultarPorCnpj(String cnpj, int nivelMontarDados) throws Exception {
        String sql = "SELECT " + getColunas("") + " FROM Pessoa WHERE cnpj = ?";
        try (PreparedStatement pst = con.prepareStatement(sql)) {
            pst.setString(1, cnpj);
            try (ResultSet rs = pst.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs, nivelMontarDados, con);
                }
            }
        }
        return null;
    }

    public List consultarPorMatricula(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT " + getColunas("") + " FROM Pessoa WHERE upper( matricula ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY matricula";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public PessoaVO consultarPorMatriculaExterna(Integer matriculaExterna, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT p.* FROM Pessoa p JOIN cliente c on p.codigo = c.pessoa WHERE c.matriculaexterna = "  + matriculaExterna;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    PessoaVO obj = new PessoaVO();
                    return obj;
                }
                return montarDados(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    public PessoaVO consultarPorCPF(String valorConsulta, Integer codigoPessoa, boolean controlarAcesso) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT " + getColunas("Pessoa.") + " FROM Pessoa WHERE upper(Cfp) like('" + valorConsulta.toUpperCase() + "') AND codigo <> " + codigoPessoa + " ORDER BY Cfp";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return null;
                }
                return (montarDados(tabelaResultado, this.con));
            }
        }

    }

    public PessoaVO consultarPorNomeSemMatriculaExternaOuCpf(String nome, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sql = "SELECT p.* FROM Pessoa p JOIN cliente c on p.codigo = c.pessoa " +
                "WHERE coalesce(c.matriculaexterna, 0) = 0 and coalesce(p.cfp, '0') = '0' and nome ilike ? order by codigo";
        try (PreparedStatement sqlSelect = con.prepareStatement(sql)) {
            sqlSelect.setString(1, nome);

            try (ResultSet tabelaResultado = sqlSelect.executeQuery()) {
                if (!tabelaResultado.next()) {
                    PessoaVO obj = new PessoaVO();
                    return obj;
                }
                return montarDados(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    public PessoaVO montarDados(ResultSet dadosSQL, Connection con) throws Exception {
        return montarDados(dadosSQL, false, con);
    }

    public PessoaVO montarDadosCE(ResultSet dadosSQL) throws Exception {
        Email emailDAO;
        try {
            emailDAO = new Email(con);
            PessoaVO obj = new PessoaVO();
            obj.setCodigo(dadosSQL.getInt("codigo"));
            obj.setNome(dadosSQL.getString("nome"));
            obj.setDataNasc(dadosSQL.getDate("dataNasc"));
            obj.setCfp(dadosSQL.getString("cfp"));
            obj.setEmailVOs(emailDAO.consultarEmails(obj.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            return obj;
        } finally {
            emailDAO = null;
        }
    }

    public static PessoaVO montarDados(ResultSet dadosSQL, boolean somenteDadosBasicos, Connection con) throws Exception {
        if (somenteDadosBasicos) {
            return montarDados(dadosSQL, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        } else {
            return montarDados(dadosSQL, Uteis.NIVELMONTARDADOS_TODOS, con);
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Pessoa</code> através do valor do atributo
     * <code>String nome</code>. Retorna os objetos, com início do valor do
     * atributo idêntico ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>PessoaVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorNome(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT " + getColunas("Pessoa.") + " FROM Pessoa WHERE upper( nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY nome";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Pessoa</code> através do valor do atributo
     * <code>String nome</code>. Retorna os objetos, com início do valor do
     * atributo idêntico ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>PessoaVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
//    public PessoaVO consultarPorPessoaNome(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
//        SuperEntidade.consultar(getIdEntidade(), controlarAcesso);
//        String sqlStr = "SELECT " + colunas + " FROM Pessoa WHERE upper( nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY nome";
//        Statement stm = con.createStatement();
//        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
//        if (!tabelaResultado.next()) {
//            throw new ConsistirException("Dados Não Encontrados ( Pessoa ).");
//        }
//        return (montarDados(tabelaResultado, nivelMontarDados));
//    }
    /**
     * Responsável por realizar uma consulta de
     * <code>Pessoa</code> através do valor do atributo
     * <code>descricao</code> da classe
     * <code>Profissao</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da classe <code>PessoaVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorDescricaoProfissao(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT " + getColunas("Pessoa.") + " FROM Pessoa, Profissao WHERE Pessoa.profissao = Profissao.codigo and upper( Profissao.descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Profissao.descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    public List consultarPorDescricaoGrauInstrucao(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT " + getColunas("Pessoa.") + " FROM Pessoa, Grauinstrucao WHERE Pessoa.grauinstrucao = Grauinstrucao.codigo and upper( Grauinstrucao.descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Grauinstrucao.descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Pessoa</code> através do valor do atributo
     * <code>String tipoPessoa</code>. Retorna os objetos, com início do valor
     * do atributo idêntico ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>PessoaVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorTipoPessoa(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT " + getColunas("") + "FROM Pessoa WHERE upper( tipoPessoa ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY tipoPessoa";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Pessoa</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou
     * superiores ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>PessoaVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT " + getColunas("") + " FROM Pessoa WHERE codigo >= " + valorConsulta + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public PessoaVO consultarPorCodigo(Integer valorConsulta, int nivelMontarDados) throws Exception {
        PessoaVO pessoa = null;
        String sqlStr = "SELECT " + getColunas("") + " FROM Pessoa WHERE codigo = " + valorConsulta;
        try (Statement stm = con.createStatement()) {
            try (ResultSet resultDados = stm.executeQuery(sqlStr)) {
                if (resultDados.next()) {
                    pessoa = montarDados(resultDados, nivelMontarDados, this.con);
                }
            }
        }
        return pessoa;

    }

    public List consultarPorNomePessoaComLimite(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        sqlStr = "SELECT " + getColunas("Pessoa.") + " FROM Pessoa WHERE upper(Pessoa.nome) like('" + valorConsulta.toUpperCase() + "%') ORDER BY pessoa.nome limit 50";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List consultarTodosPessoaComLimite(boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        sqlStr = "SELECT " + getColunas("Pessoa.") + " FROM Pessoa ORDER BY pessoa.nome limit 50";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public PessoaVO consultarPorNomePessoa(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT " + getColunas("") + " FROM Pessoa WHERE upper( nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY nome";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    PessoaVO obj = new PessoaVO();
                    return obj;
                }
                return montarDados(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    public List consultarTodosPessoaComLimiteFinanceiro(int codigoEmpresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sqlStr = new StringBuilder("select " + getColunas("p.") + " ,cli.pessoa as tipoCliente, "
                + " forn.pessoa as tipoFornecedor,  p.cfp as cfp, forn.cnpj as cnpj, col.pessoa as tipoColaborador "
                + " from pessoa p"
                + " left join cliente cli on cli.pessoa = p.codigo");
        sqlStr.append(" left join fornecedor forn on forn.pessoa = p.codigo");

        sqlStr.append(" left join colaborador col on col.pessoa = p.codigo ");
        if (!UteisValidacao.emptyNumber(codigoEmpresa)) {
            sqlStr.append("where cli.empresa = ").append(codigoEmpresa).append(" or col.empresa = ").append(codigoEmpresa).append(" or forn.codigo is not null ");
        }
        sqlStr.append(" order by p.nome limit 50");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List<PessoaVO> consultarPorNomePessoaComLimiteFinanceiro(int codigoEmpresa, String valorConsulta, boolean controlarAcesso, int limit, boolean consultarQuaquerParteDoNome, int nivelMontarDados) throws Exception {
        return consultarPorNomePessoaLimiteFinanceiro(codigoEmpresa, valorConsulta, controlarAcesso, limit, consultarQuaquerParteDoNome, false, nivelMontarDados);
    }
    public List<PessoaVO> consultarPorNomePessoaComLimiteFinanceiro(int codigoEmpresa, String valorConsulta, boolean controlarAcesso, boolean consultarQuaquerParteDoNome, int nivelMontarDados) throws Exception {
        return consultarPorNomePessoaLimiteFinanceiro(codigoEmpresa, valorConsulta, controlarAcesso, 50, consultarQuaquerParteDoNome, false, nivelMontarDados);
    }

    public List<PessoaVO> consultarPorNomePessoaComLimiteFinanceiro(int codigoEmpresa, String valorConsulta, boolean controlarAcesso, boolean consultarQuaquerParteDoNome, int nivelMontarDados,  boolean consultarColabInativo) throws Exception {
        return consultarPorNomePessoaLimiteFinanceiro(codigoEmpresa, valorConsulta, controlarAcesso, 50, consultarQuaquerParteDoNome, consultarColabInativo, nivelMontarDados);
    }

    public List<PessoaVO> consultarPorNomePessoaLimiteFinanceiro(int codigoEmpresa, String valorConsulta, boolean controlarAcesso, int limit,boolean consultarQuaquerParteDoNome, boolean consultarColabInativo, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sqlStr = new StringBuilder("select " + getColunas("p.") + ",cli.pessoa as tipoCliente, "
                + " e.nome as nomeEmpresa, forn.pessoa as tipoFornecedor,  p.cfp as cfp, forn.cnpj as cnpj, col.pessoa as tipoColaborador, cli.codigo as codCliente, col.situacao as situacaoColaborador "
                + " from pessoa p"
                + " left join cliente cli on cli.pessoa = p.codigo ");
        sqlStr.append(" left join fornecedor forn on forn.pessoa = p.codigo ");

        sqlStr.append("left join colaborador col on col.pessoa = p.codigo ");
        sqlStr.append(" left join empresa e on e.codigo = COALESCE(cli.empresa, col.empresa, forn.empresa) ");
        if (consultarQuaquerParteDoNome){
            sqlStr.append(" where  p.nome ilike '%").append(valorConsulta.toUpperCase()).append("%'");
        }else{
            sqlStr.append(" where  p.nome ilike '").append(valorConsulta.toUpperCase()).append("%'");
        }

        if (!consultarColabInativo){
            sqlStr.append(" and ((col.pessoa is null or (col.situacao = 'AT')) or ");

            //essa linha é para considerar uma pessoa que tem cadastro de aluno e de colaborador e mesmo com o colaborador inativo ele deverá priorizar o aluno caso exista o aluno
            //Lá dentro do montar dados ele tem a mesma regra para definir o tipo String a exibir também
            sqlStr.append(" (col.pessoa is not null and col.situacao = 'NA' and cli.codigo is not null)) ");
        }
        if (!UteisValidacao.emptyNumber(codigoEmpresa)) {
            sqlStr.append("and (cli.empresa = ").append(codigoEmpresa).append(" or col.empresa = ").append(codigoEmpresa).append(" or forn.codigo is not null or(forn.codigo is null and cli.codigo is null and col.codigo is null) )");
        }
        sqlStr.append(" order by p.nome ");
        if(!UteisValidacao.emptyNumber(limit)){
            sqlStr.append(" limit ").append(limit);
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    public List<PessoaVO> consultarPessoaColaboradorComLimite(int codigoEmpresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        sqlStr = "select " + getColunas("p.") + " from pessoa p inner join colaborador col on col.pessoa = p.codigo ";
        if (!UteisValidacao.emptyNumber(codigoEmpresa)) {
            sqlStr += "left join empresa emp on emp.codigo = col.empresa  "
                    + " where emp.codigo = " + codigoEmpresa;
        }
        sqlStr += " order by p.nome limit 50";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List<PessoaVO> consultarPessoaPorNomeColaboradorComLimite(int codigoEmpresa, String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "select " + getColunas("p.") + " from pessoa p inner join colaborador col on col.pessoa = p.codigo ";
        if (!UteisValidacao.emptyNumber(codigoEmpresa)) {
            sqlStr += " left join empresa emp on emp.codigo = col.empresa  ";
        }
        sqlStr += " where p.nome ilike '" + valorConsulta.toUpperCase() + "%'";
        if (!UteisValidacao.emptyNumber(codigoEmpresa)) {
            sqlStr += " and emp.codigo = " + codigoEmpresa + "";
        }
        sqlStr += " order by p.nome limit 50";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List<PessoaVO> consultarPessoaClienteComLimite(int codigoEmpresa, boolean controlarAcesso,
            boolean clienteVisitante, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        sqlStr = "select " + getColunas("p.") + " from pessoa p inner join cliente cli on cli.pessoa = p.codigo ";
        if (!UteisValidacao.emptyNumber(codigoEmpresa)) {
            sqlStr += "left join empresa emp on emp.codigo = cli.empresa  "
                    + " where emp.codigo = " + codigoEmpresa;
        }
        if (clienteVisitante) {
            sqlStr += "and cli.situacao ilike 'VI' ";
        } else {
            sqlStr += " and cli.situacao not ilike 'VI' ";
        }
        sqlStr += " order by p.nome limit 50";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List<PessoaVO> consultarPessoaPorNomeClienteComLimite(int codigoEmpresa, String valorConsulta,
            boolean clienteVisitante, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "select " + getColunas("p.") + " from pessoa p inner join cliente cli on cli.pessoa = p.codigo ";
        if (!UteisValidacao.emptyNumber(codigoEmpresa)) {
            sqlStr += " left join empresa emp on emp.codigo = cli.empresa";
        }
        sqlStr += " where p.nome ilike '" + valorConsulta.toUpperCase() + "%' ";
        if (clienteVisitante) {
            sqlStr += "and cli.situacao ilike 'VI' ";
        } else {
            sqlStr += " and cli.situacao not ilike 'VI' ";
        }
        if (!UteisValidacao.emptyNumber(codigoEmpresa)) {
            sqlStr += " and emp.codigo = " + codigoEmpresa + "";
        }
        sqlStr += " order by p.nome limit 50";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List<PessoaVO> consultarClientes(int codigoEmpresa, String nomePessoa, int nivelMontarDados) throws Exception{
        String sqlStr = "select " + getColunas("p.") + " from pessoa p inner join cliente cli on cli.pessoa = p.codigo ";
        if (!UteisValidacao.emptyNumber(codigoEmpresa)) {
            sqlStr += " left join empresa emp on emp.codigo = cli.empresa";
        }
        sqlStr += " where upper(p.nome) like '%" + nomePessoa.toUpperCase() + "%' ";
        if (!UteisValidacao.emptyNumber(codigoEmpresa)) {
            sqlStr += " and emp.codigo = " + codigoEmpresa + "";
        }
        sqlStr += " order by p.nome limit 50";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List<PessoaVO> consultarPessoaClienteVisitantePorDataBVComLimite(int codigoEmpresa, Date dataInicial, Date dataFinal, String horarioInicial, String horarioFinal,
            int nivelMontarDados) throws Exception {
        String sqlStr = "select " + getColunas("pessoa.") + " from cliente "
                + "inner join pessoa on pessoa.codigo = cliente.pessoa "
                + "left join contrato as con on con.pessoa= cliente.pessoa "
                + "left join historicocontrato as hist on hist.contrato = con.codigo "
                + "inner join questionariocliente as bv on bv.cliente = cliente.codigo "
                + "where  pessoa.datacadastro between '" + Uteis.getDataJDBC(dataInicial) + " " + horarioInicial + "' and  '" + Uteis.getDataJDBC(dataFinal) + " " + horarioFinal + "' "
                + "and bv.data between  '" + Uteis.getDataJDBC(dataInicial) + " " + horarioInicial + "' and  '" + Uteis.getDataJDBC(dataFinal) + " " + horarioFinal + "' "
                + "and (hist.datainiciosituacao between '" + Uteis.getDataJDBC(dataInicial) + " " + horarioInicial + "' and  '" + Uteis.getDataJDBC(dataFinal) + " " + horarioFinal + "' "
                + "or hist.codigo is null)";
        if (!UteisValidacao.emptyNumber(codigoEmpresa)) {
            sqlStr += " and cliente.empresa =  " + codigoEmpresa;
        }
        sqlStr += " order by pessoa.nome limit 50";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List<PessoaVO> consultarPessoaPorNomeClienteVisitantePorDataBVComLimite(int codigoEmpresa, String valorConsulta,
            Date dataInicial, Date dataFinal, String horarioInicial, String horarioFinal, int nivelMontarDados) throws Exception {
        String sqlStr = "select " + getColunas("pessoa.") + " from cliente "
                + "inner join pessoa on pessoa.codigo = cliente.pessoa "
                + "left join contrato as con on con.pessoa= cliente.pessoa "
                + "left join historicocontrato as hist on hist.contrato = con.codigo "
                + "inner join questionariocliente as bv on bv.cliente = cliente.codigo "
                + " where pessoa.nome ilike '" + valorConsulta.toUpperCase() + "%' "
                + "and  pessoa.datacadastro between '" + Uteis.getDataJDBC(dataInicial) + " " + horarioInicial + "' and  '" + Uteis.getDataJDBC(dataFinal) + " " + horarioFinal + "' "
                + "and bv.data between  '" + Uteis.getDataJDBC(dataInicial) + " " + horarioInicial + "' and  '" + Uteis.getDataJDBC(dataFinal) + " " + horarioFinal + "' "
                + "and (hist.datainiciosituacao between '" + Uteis.getDataJDBC(dataInicial) + " " + horarioInicial + "' and  '" + Uteis.getDataJDBC(dataFinal) + " " + horarioFinal + "' "
                + "or hist.codigo is null)";
        if (!UteisValidacao.emptyNumber(codigoEmpresa)) {
            sqlStr += " and cliente.empresa =  " + codigoEmpresa;
        }
        sqlStr += " order by pessoa.nome limit 50";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List<PessoaVO> consultarPessoaComLimite(int codigoEmpresa,
            int nivelMontarDados) throws Exception {
        String sqlStr = "select " + getColunas("pessoa.") + " from pessoa "
                + "left join cliente on cliente.pessoa = pessoa.codigo "
                + "left join colaborador on colaborador.pessoa = pessoa.codigo where ";
        if (!UteisValidacao.emptyNumber(codigoEmpresa)) {
            sqlStr += "(cliente.empresa =  " + codigoEmpresa + " or colaborador.empresa =" + codigoEmpresa + ")";
        }
        sqlStr += " order by pessoa.nome limit 50";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List<PessoaVO> consultarPessoaPorNomeComLimite(int codigoEmpresa, String valorConsulta,
            int nivelMontarDados) throws Exception {
        String sqlStr = "select " + getColunas("pessoa.") + " from pessoa "
                + "left join cliente on cliente.pessoa = pessoa.codigo "
                + "left join colaborador on colaborador.pessoa = pessoa.codigo where 1 = 1 ";
        if (!UteisValidacao.emptyNumber(codigoEmpresa)) {
            sqlStr += " and (cliente.empresa =  " + codigoEmpresa + " or colaborador.empresa =" + codigoEmpresa + ")";
        }
        sqlStr += " and pessoa.nome ilike '" + valorConsulta + "%'";
        sqlStr += " order by pessoa.nome limit 50";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

//    public static void consultarFoto(PessoaVO obj) throws SQLException {
//        obj.setDestinoFoto(obj.getDestinoFoto() + obj.getNomeFoto());
//        obj.setDestinoFoto(obj.getDestinoFoto().replace("\\", "/"));
//        String sql = "SELECT lo_export(foto, '" + obj.getDestinoFoto() + "') " +
//                "FROM pessoa " +
//                "WHERE codigo = " + obj.getCodigo().intValue();
//        Statement stm = con.createStatement();
//        stm.executeQuery(sql);
//
//    }
    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma
     * consulta ao banco de dados (
     * <code>ResultSet</code>). Faz uso da operação
     * <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vários objetos da classe <code>PessoaVO</code>
     * resultantes da consulta.
     */
    public static List<PessoaVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<PessoaVO> vetResultado = new ArrayList<PessoaVO>();
        while (tabelaResultado.next()) {
            PessoaVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public List<PessoaVO> montarDadosConsultaCE(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List<PessoaVO> vetResultado = new ArrayList<PessoaVO>();
        while (tabelaResultado.next()) {
            PessoaVO obj = montarDadosCE(tabelaResultado);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static PessoaVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        PessoaVO obj = new PessoaVO();
        obj.setNovoObj(false);
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setNome(dadosSQL.getString("nome"));
        obj.getProfissao().setCodigo(dadosSQL.getInt("profissao"));
        obj.setDataCadastro(dadosSQL.getTimestamp("dataCadastro"));
        obj.setDataNasc(dadosSQL.getDate("dataNasc"));
        obj.setNomePai(dadosSQL.getString("nomePai"));
        obj.setNomeMae(dadosSQL.getString("nomeMae"));
        obj.setCfp(dadosSQL.getString("cfp"));
        if (Uteis.resultSetContemColuna(dadosSQL, "cnpjClienteSesi")) {
            obj.setCnpjSesi(dadosSQL.getString("cnpjClienteSesi"));
        }
        obj.setContatoEmergencia(dadosSQL.getString("contatoEmergencia"));
        obj.setTelefoneEmergencia(dadosSQL.getString("telefoneEmergencia"));
        obj.setLiberaSenhaAcesso(dadosSQL.getBoolean("liberaSenhaAcesso"));
        obj.setRne(dadosSQL.getString("rne"));
        obj.setPassaporte(dadosSQL.getString("passaporte"));
        obj.setRg(dadosSQL.getString("rg"));
        obj.setRgOrgao(dadosSQL.getString("rgOrgao"));
        obj.setRgUf(dadosSQL.getString("rgUf"));
        obj.getCidade().setCodigo(dadosSQL.getInt("cidade"));
        obj.getEstadoVO().setCodigo(dadosSQL.getInt("estado"));
        obj.getPais().setCodigo(dadosSQL.getInt("pais"));
        obj.setEstadoCivil(dadosSQL.getString("estadoCivil"));
        obj.setNacionalidade(dadosSQL.getString("nacionalidade"));
        obj.setNaturalidade(dadosSQL.getString("naturalidade"));
        obj.setSexo(dadosSQL.getString("sexo"));
        obj.setWebPage(dadosSQL.getString("webPage"));
        obj.getGrauInstrucao().setCodigo(dadosSQL.getInt("grauInstrucao"));
        obj.setCategoriaPessoa(TipoPessoa.obterPorCodigo(dadosSQL.getInt("tipoPessoa")));
        obj.setFotoKey(dadosSQL.getString("fotokey"));
        obj.setIdVindi(dadosSQL.getInt("idvindi"));
        obj.setDataAlteracaoVindi(dadosSQL.getTimestamp("dataalteracaovindi"));
        obj.setIdMaxiPago(dadosSQL.getString("idMaxiPago"));
        obj.setDataAlteracaoMaxiPago(dadosSQL.getTimestamp("dataAlteracaoMaxiPago"));
        obj.setIdGetNet(dadosSQL.getString("idGetNet"));
        obj.setDataAlteracaoGetNet(dadosSQL.getTimestamp("dataAlteracaoGetNet"));
        obj.setRgMae(dadosSQL.getString("rgMae"));
        obj.setRgPai(dadosSQL.getString("rgPai"));
        obj.setIdMundiPagg(dadosSQL.getString("idmundipagg"));
        obj.setDataAlteracaoMundiPagg(dadosSQL.getTimestamp("dataAlteracaoMundipagg"));
        obj.setIdPagarMe(dadosSQL.getString("idpagarme"));
        obj.setIdStone(dadosSQL.getString("idStone"));
        obj.setDataAlteracaoPagarMe(dadosSQL.getTimestamp("dataAlteracaopagarme"));
        obj.setDataAlteracaoStone(dadosSQL.getTimestamp("dataAlteracaoStone"));
        try{
            obj.setFoto(dadosSQL.getBytes("foto"));
        } catch (Exception ignored) {
        }
        try {
            obj.setCategoriaPessoa(TipoPessoa.obterPorCodigo(dadosSQL.getInt("tipoPessoa")));
            obj.setCnpj(dadosSQL.getString("cnpj"));
            obj.setInscEstadual(dadosSQL.getString("inscEstadual"));
            obj.setInscMunicipal(dadosSQL.getString("inscMunicipal"));
            obj.setCfdf(dadosSQL.getString("cfdf"));
            obj.setEmitirNotaNomeMae(dadosSQL.getBoolean("emitirNotaNomeMae"));
            obj.setEmitirNotaNomeAluno(dadosSQL.getBoolean("emitirNotaNomeAluno"));
            obj.setEmitirNomeTerceiro(dadosSQL.getBoolean("emitirNomeTerceiro"));
            obj.setNomeTerceiro(dadosSQL.getString("nomeTerceiro"));
            obj.setCpfCNPJTerceiro(dadosSQL.getString("cpfCNPJTerceiro"));

            obj.setInscEstadualTerceiro(dadosSQL.getString("inscEstadualTerceiro"));
            obj.setCfdfTerceiro(dadosSQL.getString("cfdfTerceiro"));

            obj.setObservacaoNota(dadosSQL.getString("observacaoNota"));
            obj.setUtilizaDotz(dadosSQL.getBoolean("utilizaDotz"));
            obj.setCpfPai(dadosSQL.getString("cpfPai"));
            obj.setCpfMae(dadosSQL.getString("cpfMae"));
            obj.setEmailMae(dadosSQL.getString("emailMae"));
            obj.setEmailPai(dadosSQL.getString("emailPai"));
            obj.setAtualizarDados(dadosSQL.getBoolean("atualizarDados"));
            obj.setAceiteTermosPacto(dadosSQL.getBoolean("aceiteTermosPacto"));
            obj.setCargoEnum(CargoEnum.obterPorCodigo(dadosSQL.getInt("cargo")));
            obj.setFuncaoEnum(FuncaoEnum.obterPorCodigo(dadosSQL.getInt("funcao")));
            obj.setReceberSMSPacto(dadosSQL.getBoolean("receberSMSPacto"));
            obj.setReceberEmailNovidadesPacto(dadosSQL.getBoolean("receberEmailNovidadesPacto"));
            obj.setValorLimiteCaixaAbertoVendaAvulsa(dadosSQL.getDouble("valorLimiteCaixaAbertoVendaAvulsa"));
            obj.setCustomerIdPagoLivre(dadosSQL.getString("customeridpagolivre"));
            obj.setIdExternoIntegracao(dadosSQL.getString("idExternoIntegracao"));
            obj.setGenero(dadosSQL.getString("genero"));
            obj.setDataNascimentoResponsavel(dadosSQL.getDate("dataNascimentoResponsavel"));
            obj.setIdAsaas(dadosSQL.getString("idAsaas"));
            obj.setDataAlteracaoIdAsaas(dadosSQL.getDate("dataAlteracaoIdAsaas"));
            obj.setNomeResponsavelEmpresa(dadosSQL.getString("nomeResponsavelEmpresa"));
            obj.setCpfResponsavelEmpresa(dadosSQL.getString("cpfResponsavelEmpresa"));
            obj.setNomeRegistro(dadosSQL.getString("nomeRegistro"));
        } catch (Exception ignored) {
        }

        try{
            obj.setNomeRespFinanceiro(dadosSQL.getString("nomeRespFinanceiro"));
            obj.setCpfRespFinanceiro(dadosSQL.getString("cpfRespFinanceiro"));
            obj.setRgRespFinanceiro(dadosSQL.getString("rgRespFinanceiro"));
            obj.setEmailRespFinanceiro(dadosSQL.getString("emailRespFinanceiro"));
        }catch (Exception ignored){}
        return obj;
    }

    private static void montarDadosEmail(PessoaVO pessoa) {
        Iterator k = pessoa.getEmailVOs().iterator();
        int passoCorrespondencia = 0;
        int passoSem = 0;
        while (k.hasNext()) {
            EmailVO objExistente = (EmailVO) k.next();
            if (objExistente.getEmailCorrespondencia() && (passoCorrespondencia == 0)) {
                pessoa.setEmail(objExistente.getEmail());
                passoCorrespondencia = 1;
                passoSem = 1;
            }
            if (!objExistente.getEmailCorrespondencia() && (passoSem == 0)) {
                pessoa.setEmail(objExistente.getEmail());
                passoSem = 1;
            }
        }
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de
     * dados (
     * <code>ResultSet</code>) em um objeto da classe
     * <code>PessoaVO</code>.
     *
     * @return O objeto da classe <code>PessoaVO</code> com os dados devidamente
     * montados.
     */
    public static PessoaVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONSULTA_WS ||
                nivelMontarDados == Uteis.NIVELMONTARDADOS_CONSULTA_WS_COM_BIOMETRIAS) {
            PessoaVO obj = new PessoaVO();
            obj.setNovoObj(false);
            obj.setCodigo(dadosSQL.getInt("codigo"));
            obj.setNome(dadosSQL.getString("nome"));
            obj.setDataNasc(dadosSQL.getDate("dataNasc"));
            obj.setSenhaAcesso(dadosSQL.getString("senhaacesso"));
            obj.setAtualizarDados(dadosSQL.getBoolean("atualizarDados"));
            obj.setFotoKey(dadosSQL.getString("fotokey"));
            obj.setCfp(dadosSQL.getString("cfp"));
            if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONSULTA_WS_COM_BIOMETRIAS) {
                Pessoa pessoaDAO = new Pessoa(con);
                obj.setAssinaturaBiometriaDigital(pessoaDAO.obterAssinaturaBiometriaDigital(obj.getCodigo()));
                obj.setAssinaturaBiometriaFacial(pessoaDAO.obterAssinaturaBiometriaFacial(obj.getCodigo()));
                pessoaDAO = null;
            }
            Email email = new Email(con);
            obj.setEmailVOs(email.consultarEmails(obj.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            email = null;
            Telefone telefone = new Telefone(con);
            obj.setTelefoneVOs(telefone.consultarTelefones(obj.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            telefone = null;
            return obj;
        }

        PessoaVO obj = montarDadosBasico(dadosSQL);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONSULTA_WS_COMPLETA) {
            obj.setSenhaAcesso(dadosSQL.getString("senhaacesso"));
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA) {
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_ROBO) {
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_MINIMOS_COM_BIOMETRIAS) {
            Pessoa pessoaDAO = new Pessoa(con);
            obj.setAssinaturaBiometriaDigital(pessoaDAO.obterAssinaturaBiometriaDigital(obj.getCodigo()));
            obj.setAssinaturaBiometriaFacial(pessoaDAO.obterAssinaturaBiometriaFacial(obj.getCodigo()));
            pessoaDAO = null;
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_SITUACAOCLIENTESINTETICODW) {
            montarDadosProfissao(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_PESSOA_PACTO_PAY) {
            Email email = new Email(con);
            obj.setEmailVOs(email.consultarEmails(obj.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            email = null;
            Telefone telefone = new Telefone(con);
            obj.setTelefoneVOs(telefone.consultarTelefones(obj.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            telefone = null;
            Endereco endereco = new Endereco(con);
            obj.setEnderecoVOs(endereco.consultarEnderecos(obj.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            endereco = null;
            if (obj.getCidade() != null && obj.getCidade().getCodigo() > 0) {
                Cidade cidade = new Cidade(con);
                obj.setCidade(cidade.consultarPorChavePrimaria(obj.getCidade().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                cidade = null;
            }
            if (obj.getEstadoVO() != null && obj.getEstadoVO().getCodigo() > 0) {
                Estado estado = new Estado(con);
                obj.setEstadoVO(estado.consultarPorChavePrimaria(obj.getEstadoVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                estado = null;
            }
            if (obj.getPais() != null && obj.getPais().getCodigo() > 0) {
                Pais pais = new Pais(con);
                obj.setPais(pais.consultarPorChavePrimaria(obj.getPais().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                pais = null;
            }
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TELACONSULTA) {
            Email email = new Email(con);
            obj.setEmailVOs(email.consultarEmails(obj.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            email = null;
        }

        if(nivelMontarDados == Uteis.NIVELMONTARDADOS_PESSOAJURIDICA){
            try {
                obj.setCategoriaPessoa(TipoPessoa.obterPorCodigo(dadosSQL.getInt("tipoPessoa")));
                obj.setCnpj(dadosSQL.getString("cnpj"));
                obj.setInscEstadual(dadosSQL.getString("inscEstadual"));
                obj.setInscMunicipal(dadosSQL.getString("inscMunicipal"));
            }catch (Exception erro){}
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_LOGIN) {
            Telefone telefone = new Telefone(con);
            obj.setTelefoneVOs(telefone.consultarTelefones(obj.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            telefone = null;
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_MINIMOS) {
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONSULTA_DADOS_BOLETO) {
            Email email = new Email(con);
            obj.setEmailVOs(email.consultarEmails(obj.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            email = null;
            Telefone telefone = new Telefone(con);
            obj.setTelefoneVOs(telefone.consultarTelefones(obj.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            telefone = null;
            Endereco endereco = new Endereco(con);
            obj.setEnderecoVOs(endereco.consultarEnderecos(obj.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            endereco = null;
            Placa placa = new Placa(con);
            obj.setPlacaVOs(placa.consultarPlacas(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            placa = null;
            if (obj.getCidade() != null && obj.getCidade().getCodigo() > 0) {
                Cidade cidade = new Cidade(con);
                obj.setCidade(cidade.consultarPorChavePrimaria(obj.getCidade().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                cidade = null;
            }
            if (obj.getEstadoVO() != null && obj.getEstadoVO().getCodigo() > 0) {
                Estado estado = new Estado(con);
                obj.setEstadoVO(estado.consultarPorChavePrimaria(obj.getEstadoVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                estado = null;
            }
            montarDadosProfissao(obj, nivelMontarDados, con);
            montarDadosEmail(obj);
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            Email email = new Email(con);
            obj.setEmailVOs(email.consultarEmails(obj.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            email = null;
            Telefone telefone = new Telefone(con);
            obj.setTelefoneVOs(telefone.consultarTelefones(obj.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            telefone = null;
            Endereco endereco = new Endereco(con);
            obj.setEnderecoVOs(endereco.consultarEnderecos(obj.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            endereco = null;
            Placa placa = new Placa(con);
            obj.setPlacaVOs(placa.consultarPlacas(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            placa = null;
            montarDadosEmail(obj);
            return obj;
        }
        Endereco endereco = new Endereco(con);
        Telefone telefone = new Telefone(con);
        Email email = new Email(con);
        Placa placa = new Placa(con);
        obj.setEnderecoVOs(endereco.consultarEnderecos(obj.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        obj.setTelefoneVOs(telefone.consultarTelefones(obj.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        obj.setEmailVOs(email.consultarEmails(obj.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        obj.setPlacaVOs(placa.consultarPlacas(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        email = null;
        endereco = null;
        telefone = null;
        placa = null;
        montarDadosEmail(obj);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS) {
            montarDadosEstado(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            montarDadosCidade(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TIPOPESSOA) {
            if (dadosSQL.getString("nomeEmpresa") != null) {
                obj.setNomeEmpresa(dadosSQL.getString("nomeEmpresa"));
            }
            if (dadosSQL.getString("tipoCliente") != null) {
                obj.setTipoPessoa("Cliente");
                obj.setCfp(dadosSQL.getString("cfp"));
            } else if (dadosSQL.getString("tipoFornecedor") != null) {
                obj.setTipoPessoa("Fornecedor");
                obj.setCfp(dadosSQL.getString("cnpj"));
            }
            if (dadosSQL.getString("tipoColaborador") != null) {
                //essa variável é para considerar uma pessoa que tem cadastro de aluno e de colaborador e mesmo com o colaborador inativo ele deverá priorizar o aluno caso exista o aluno
                boolean desconsiderarCadastroDeColaborador = false;
                try {
                    desconsiderarCadastroDeColaborador = !UteisValidacao.emptyString(dadosSQL.getString("tipoColaborador")) &&
                            !UteisValidacao.emptyString(dadosSQL.getString("situacaoColaborador")) &&
                            dadosSQL.getString("situacaoColaborador").equalsIgnoreCase("NA") &&
                            !UteisValidacao.emptyString(dadosSQL.getString("codCliente"));
                } catch (Exception ex) {
                }
                if (!obj.getTipoPessoa().isEmpty()) {
                    if (!desconsiderarCadastroDeColaborador) {
                        obj.setTipoPessoa(obj.getTipoPessoa() + "/Colaborador");
                    }
                } else {
                    obj.setTipoPessoa("Colaborador");
                }
                obj.setCfp(dadosSQL.getString("cfp"));
            }
            return obj;
        }
        montarDadosProfissao(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        montarDadosCidade(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        montarDadosEstado(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        montarDadosPais(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        montarDadosGrauInstrucao(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        return obj;

    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>PaisVO</code> relacionado ao objeto
     * <code>PessoaVO</code>. Faz uso da chave primária da classe
     * <code>PaisVO</code> para realizar a consulta.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosPais(PessoaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getPais().getCodigo() == 0) {
            obj.setPais(new PaisVO());
            return;
        }
        Pais pais = new Pais(con);
        obj.setPais(pais.consultarPorChavePrimaria(obj.getPais().getCodigo(), nivelMontarDados));
        pais = null;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>CidadeVO</code> relacionado ao objeto
     * <code>PessoaVO</code>. Faz uso da chave primária da classe
     * <code>CidadeVO</code> para realizar a consulta.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosCidade(PessoaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getCidade().getCodigo() == 0) {
            obj.setCidade(new CidadeVO());
            return;
        }
        Cidade cidade = new Cidade(con);
        obj.setCidade(cidade.consultarPorChavePrimaria(obj.getCidade().getCodigo(), nivelMontarDados));
        cidade = null;
    }

    public static void montarDadosEstado(PessoaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getEstadoVO().getCodigo() == 0) {
            obj.setEstadoVO(new EstadoVO());
            return;
        }
        Estado estado = new Estado(con);
        obj.setEstadoVO(estado.consultarPorChavePrimaria(obj.getEstadoVO().getCodigo(), nivelMontarDados));
        estado = null;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>ProfissaoVO</code> relacionado ao objeto
     * <code>PessoaVO</code>. Faz uso da chave primária da classe
     * <code>ProfissaoVO</code> para realizar a consulta.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosProfissao(PessoaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getProfissao().getCodigo() == 0) {
            obj.setProfissao(new ProfissaoVO());
            return;
        }
        Profissao profissao = new Profissao(con);
        obj.setProfissao(profissao.consultarPorChavePrimaria(obj.getProfissao().getCodigo(), nivelMontarDados));
        profissao = null;
    }

    public static void montarDadosGrauInstrucao(PessoaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getGrauInstrucao().getCodigo() == 0) {
            obj.setGrauInstrucao(new GrauInstrucaoVO());
            return;
        }
        GrauInstrucao grauInstrucao = new GrauInstrucao(con);
        obj.setGrauInstrucao(grauInstrucao.consultarPorChavePrimaria(obj.getGrauInstrucao().getCodigo(), nivelMontarDados));
        grauInstrucao = null;
    }

    /**
     * Operação responsável por adicionar um objeto da
     * <code>TelefoneVO</code> no Hashtable
     * <code>Telefones</code>. Neste Hashtable são mantidos todos os objetos de
     * Telefone de uma determinada Pessoa.
     *
     * @param obj Objeto a ser adicionado no Hashtable.
     */
    public void adicionarObjTelefones(TelefoneVO obj) throws Exception {
        getTelefones().put(obj.getNumero() + "", obj);
        //adicionarObjSubordinadoOC
    }

    /**
     * Operação responsável por remover um objeto da classe
     * <code>TelefoneVO</code> do Hashtable
     * <code>Telefones</code>. Neste Hashtable são mantidos todos os objetos de
     * Telefone de uma determinada Pessoa.
     *
     * @param Numero Atributo da classe <code>TelefoneVO</code> utilizado como
     * apelido (key) no Hashtable.
     */
    public void excluirObjTelefones(String Numero) throws Exception {
        getTelefones().remove(Numero + "");
        //excluirObjSubordinadoOC
    }

    /**
     * Operação responsável por adicionar um objeto da
     * <code>EnderecoVO</code> no Hashtable
     * <code>Enderecos</code>. Neste Hashtable são mantidos todos os objetos de
     * Endereco de uma determinada Pessoa.
     *
     * @param obj Objeto a ser adicionado no Hashtable.
     */
    public void adicionarObjEnderecos(EnderecoVO obj) throws Exception {
        getEnderecos().put(obj.getCodigo() + "", obj);
        //adicionarObjSubordinadoOC
    }

    /**
     * Operação responsável por remover um objeto da classe
     * <code>EnderecoVO</code> do Hashtable
     * <code>Enderecos</code>. Neste Hashtable são mantidos todos os objetos de
     * Endereco de uma determinada Pessoa.
     *
     * @param Codigo Atributo da classe <code>EnderecoVO</code> utilizado como
     * apelido (key) no Hashtable.
     */
    public void excluirObjEnderecos(Integer Codigo) throws Exception {
        getEnderecos().remove(Codigo + "");
        //excluirObjSubordinadoOC
    }

    /**
     * Operação responsável por localizar um objeto da classe
     * <code>PessoaVO</code> através de sua chave primária.
     *
     * @exception Exception Caso haja problemas de conexão ou localização do
     * objeto procurado.
     */
    public PessoaVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        PessoaVO eCache = (PessoaVO) obterFromCache(codigoPrm);
        if (eCache != null){
            return eCache;
        }
        String sql = "SELECT " + getColunas("") + " FROM Pessoa WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( Pessoa ).");
                }
                eCache = (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
        putToCache(eCache);
        return eCache;
    }

    public Hashtable getTelefones() {
        return (telefones);
    }

    public void setTelefones(Hashtable telefones) {
        this.telefones = telefones;
    }

    public Hashtable getEnderecos() {
        return (enderecos);
    }

    public void setEnderecos(Hashtable enderecos) {
        this.enderecos = enderecos;
    }

    public Hashtable getEmails() {
        return emails;
    }

    @Override
    public byte[] obterFotoBanco(final Integer codigoPessoa) throws SQLException {
        String sql = "SELECT foto FROM pessoa WHERE ((codigo = ?))";
        try (PreparedStatement sqlFoto = con.prepareStatement(sql)) {
            sqlFoto.setInt(1, codigoPessoa);
            try (ResultSet rs = sqlFoto.executeQuery()) {

                if (rs.next()) {
                    return rs.getBytes(1);
                } else {
                    return null;
                }
            }
        }
    }

    public void setEmails(Hashtable emails) {
        this.emails = emails;
    }

    @Override
    public byte[] obterFoto(final String chave, final Integer codigoPessoa) throws Exception {
        if (PropsService.isTrue(PropsService.fotosParaNuvem)) {
            byte[] foto = null;
            if (!UteisValidacao.emptyNumber(codigoPessoa)) {
                foto = MidiaService.getInstance().downloadObjectAsByteArray(chave,
                        MidiaEntidadeEnum.FOTO_PESSOA, codigoPessoa.toString(), null);
            }
            if (foto == null) {
                foto = MidiaService.getInstance().downloadObjectAsByteArray(chave,
                        MidiaEntidadeEnum.FOTO_PESSOA, null, null);
            } else {
                return foto;
            }
            return foto;
        } else {
            return obterFotoBanco(codigoPessoa);
        }

    }

    private String obterCPF(Integer codigoPessoa) throws Exception {
        String sql = "SELECT cfp FROM pessoa WHERE codigo = ?";
        try (PreparedStatement sqlFoto = con.prepareStatement(sql)) {
            sqlFoto.setInt(1, codigoPessoa);
            try (ResultSet rs = sqlFoto.executeQuery()) {
                if (rs.next()) {
                    return rs.getString(1);
                } else {
                    return "";
                }
            }
        }
    }

    @Override
    public String obterFotoKey(final Integer codigoPessoa) throws Exception {
        String sql = "SELECT fotokey FROM pessoa WHERE ((codigo = ?))";
        try (PreparedStatement sqlFoto = con.prepareStatement(sql)) {
            sqlFoto.setInt(1, codigoPessoa);
            try (ResultSet rs = sqlFoto.executeQuery()) {
                if (rs.next()) {
                    return rs.getString(1);
                } else {
                    return null;
                }
            }
        }
    }

    /**
     * Consulta cliente para o central de eventos
     *
     * @param nome - String
     * @param cpf - String
     * @param dataNasc - java.util.date
     * @param controlarAcesso - Boolean
     * @param nivelMontarDados - int
     * @return List (de PessoaVO)
     * @throws Exception caso ocorram erros
     */
    public List consultarCliente(String nome, String cpf, Date dataNasc, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sb = new StringBuilder();

        // variavel irá controlar a adição do "and" na consulta sql
        boolean campo = false;
        sb.append("SELECT ").append(getColunas("")).append(" FROM Pessoa WHERE ");

        // verifica se o nome está nulo ou em branco
        if (nome != null && !nome.equals("")) {
            // VALIDAÇÃO DE SEGURANÇA: Verificar se nome é seguro
            if (!Uteis.isValidStringValue(nome)) {
                throw new SecurityException("Nome contém caracteres não permitidos");
            }
            campo = true;
            // Escapar aspas simples para prevenir SQL injection
            String nomeEscapado = nome.replace("'", "''");
            sb.append("upper( nome ) like('").append(nomeEscapado.toUpperCase()).append("%')");
        }
        if (cpf != null && !cpf.equals("")) {
            // VALIDAÇÃO DE SEGURANÇA: Verificar se CPF é seguro
            if (!Uteis.isValidStringValue(cpf)) {
                throw new SecurityException("CPF contém caracteres não permitidos");
            }
            if (campo) {
                sb.append(" and ");
            }
            // Escapar aspas simples para prevenir SQL injection
            String cpfEscapado = cpf.replace("'", "''");
            sb.append(" upper( cfp ) like('").append(cpfEscapado.toUpperCase()).append("%') ");
            campo = true;
        }
        if (dataNasc != null) {
            if (campo) {
                sb.append(" and ");
            }
            sb.append(" ( datanasc ) = ('").append(Uteis.getDataJDBC(dataNasc)).append("')");
        }
        String sqlStr = sb.toString();
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List consultarClienteCE(String nome, String cpf, String telefones, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sb = new StringBuilder();

        // variavel irá controlar a adição do "and" na consulta sql
        boolean campo = false;
        sb.append("SELECT DISTINCT ").append(getColunas("")).append(" ");
        sb.append("from  pessoa ");
        sb.append("WHERE ");

        // verifica se o nome está nulo ou em branco
        if (nome != null && !nome.equals("")) {
            campo = true;
            sb.append("upper( nome ) like('").append(nome.toUpperCase()).append("%') ");
        }
        if (cpf != null && !cpf.equals("")) {
            if (campo) {
                sb.append(" and ");
            }
            sb.append(" upper( cfp ) like('").append(cpf.toUpperCase()).append("%') ");
            campo = true;
        }
        String sqlStr = sb.toString();
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsultaCE(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public void removerFoto(final String key, final int codigoPessoa) throws Exception {
        Pessoa.executarConsulta("update pessoa set foto = null, fotokey=null where codigo = " + codigoPessoa, con);
        PessoaFotoLocalAcesso fotoLocal = new PessoaFotoLocalAcesso(con);
        fotoLocal.excluirFotoPessoaLocalAcesso(codigoPessoa);
        fotoLocal = null;

        try {
            deletarFoto(key, codigoPessoa);
        } catch (Exception e){
            Uteis.logar(null, "Problema ao remover foto do Mídia Server");
        }

    }

    public void alterarSenhaAcesso(int codigoPessoa,String senha) throws Exception {
        alterarSenhaAcesso(codigoPessoa,senha,true);
    }

    public void definirCPFComoSenhaAcesso(int codigoPessoa, int codigoEmpresa) throws Exception{
        Empresa empresa = new Empresa(con);
        EmpresaVO empresaVO = empresa.consultarPorChavePrimaria(codigoEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if ((empresaVO.isDefinirCpfComoSenhaCatraca()) && (empresaVO.isSenhaAcessoOnzeDigitos())){
            PessoaVO pessoaVO = consultarPorCodigo(codigoPessoa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if ((pessoaVO.getCfp() != null) && (!pessoaVO.getCfp().trim().equals(""))) {
                if ((pessoaVO.getSenhaAcesso() == null) || (pessoaVO.getSenhaAcesso().trim().equals(""))){
                    // Este processo roda somente para as pessoas que ainda não tem senhas de acesso, pois o cliente pode já ter alterado a sua senha.
                    String cpfSomenteNumeros =  StringUtilities.formatarCpfCnjp(pessoaVO.getCfp(), 11);
                    if (cpfSomenteNumeros.length() == 11){
                        String senhaEncriptada = Uteis.encriptar(cpfSomenteNumeros);
                        if (!(senhaAcessoJaUtilizada(0, pessoaVO.getCodigo(), senhaEncriptada))) {
                            alterarSenhaAcesso(pessoaVO.getCodigo(), cpfSomenteNumeros);
                        }
                    }
                }
            }
        }

    }

    public void alterarSenhaAcesso(int codigoPessoa, String senha,boolean atualizarBaseOffline) throws Exception {
        SituacaoClienteSinteticoDW situacaoClienteSinteticoDWDAO;
        try {
            situacaoClienteSinteticoDWDAO = new SituacaoClienteSinteticoDW(con);

            String senhaEncriptada = Uteis.encriptar(senha);
            try (Statement st = con.createStatement()) {
                st.execute("update pessoa set senhaAcesso = '" + senhaEncriptada + "'"
                        + " where codigo = " + codigoPessoa);
            }
            String chave = (String) JSFUtilities.getFromSession(JSFUtilities.KEY);
            if (!UteisValidacao.emptyString(chave) && atualizarBaseOffline) {
                situacaoClienteSinteticoDWDAO.atualizarBaseOffLineZillyonAcesso(chave, codigoPessoa);
            }
        } finally {
            situacaoClienteSinteticoDWDAO = null;
        }
    }

    public void liberarRemoverSenhaAcesso(PessoaVO pessoaVO) throws Exception {
        Pessoa.executarConsulta("update pessoa set liberasenhaacesso ="+pessoaVO.getLiberaSenhaAcesso()+", senhaacesso = null  where codigo = " + pessoaVO.getCodigo(), con);
    }

    public void alterarCPF(Integer codigoPessoa, String cpf, String descricaoLog, UsuarioVO usuarioVO, boolean controlaTransacao) throws Exception {
        try {
            if (controlaTransacao) {
                con.setAutoCommit(false);
            }

            String cpfAnterior = obterCPF(codigoPessoa);
            String sql = "UPDATE pessoa set cfp=? WHERE codigo = ?";
            try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
                sqlAlterar.setString(1, cpf);
                sqlAlterar.setInt(2, codigoPessoa);
                sqlAlterar.execute();
            }
            gerarLogAlterarCPF(codigoPessoa, cpf, cpfAnterior, descricaoLog, usuarioVO);
            if (controlaTransacao) {
                con.commit();
            }
        } catch (Exception ex) {
            if (controlaTransacao) {
                con.rollback();
            }
            throw ex;
        } finally {
            if (controlaTransacao) {
                con.setAutoCommit(true);
            }
        }
    }

    private void gerarLogAlterarCPF(Integer pessoa, String cpfNovo, String cpfAnterior, String descricaoLog, UsuarioVO usuarioVO) throws Exception {
        Log logDAO;
        try {
            if (usuarioVO == null) {
                return;
            }
            logDAO = new Log(con);

            LogVO log = new LogVO();
            log.setNomeEntidade("PESSOA");
            log.setNomeEntidadeDescricao("PESSOA");
            log.setDescricao("ALTERAÇÃO - CPF");
            log.setChavePrimaria(pessoa.toString());
            log.setDataAlteracao(Calendario.hoje());
            log.setOperacao("ALTERAÇÃO");
            log.setNomeCampo("CPF");
            log.setUsuarioVO(usuarioVO);
            log.setResponsavelAlteracao(usuarioVO.getNomeAbreviado());
            log.setUserOAMD(usuarioVO.getUserOamd());
            log.setPessoa(pessoa);
            log.setValorCampoAnterior(cpfAnterior);
            if (!UteisValidacao.emptyString(descricaoLog)) {
                log.setValorCampoAlterado(descricaoLog += "\n\nNovo CPF: " + cpfNovo);
            } else {
                log.setValorCampoAlterado(cpfNovo);
            }

            logDAO.incluirSemCommit(log);
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logar(null, "Erro ao gerarLogAlterarCPF Pessoa " + ex.getMessage());
            throw ex;
        } finally {
            logDAO = null;
        }
    }

    public List<PessoaVO> consultar(String sql, final int nivelMontarDados)
            throws SQLException, Exception {
        if ((sql == null) || (sql.trim().equals(""))) {
            return null;
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List<Integer> consultarPorDataNascECPF(String cpf, java.sql.Date dataNasc) throws SQLException, Exception {
        String sqlStr = "SELECT Pessoa.codigo as codigo FROM Pessoa WHERE cfp = '" + cpf + "' and dataNasc = '" + dataNasc + "' ORDER BY pessoa.codigo";
        List<Integer> lista;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                lista = new ArrayList<Integer>();
                while (tabelaResultado.next()) {
                    lista.add(tabelaResultado.getInt("codigo"));
                }
            }
        }
        return lista;
    }

    public List<PessoaVO> consultarPorNomeAlunoProfessorComLimite(String valorConsulta, int professor) throws Exception {
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append(" SELECT distinct (pessoa.codigo), pessoa.nome from matriculaalunohorarioturma ");
        sqlStr.append("inner join pessoa on matriculaalunohorarioturma.pessoa= pessoa.codigo ");
        sqlStr.append("inner join horarioturma on horarioturma.codigo = matriculaalunohorarioturma.horarioturma ");
        sqlStr.append(" where horarioturma.professor =  " + professor);
        sqlStr.append(" and pessoa.nome ilike('" + valorConsulta.toUpperCase() + "%') ORDER BY pessoa.nome limit 30");
        ArrayList<PessoaVO> listaPessoas;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                listaPessoas = new ArrayList<PessoaVO>();
                while (tabelaResultado.next()) {
                    PessoaVO pessoaVO = new PessoaVO();
                    pessoaVO.setCodigo(tabelaResultado.getInt("codigo"));
                    pessoaVO.setNome(tabelaResultado.getString("nome"));
                    listaPessoas.add(pessoaVO);
                }
            }
        }
        return listaPessoas;
    }

    public PessoaVO consultarPessoaEmpresaFinan(EmpresaVO empresa) throws Exception {
        Empresa empresaDAO;
        try {
            empresaDAO = new Empresa(con);

            PessoaVO pessoa = new PessoaVO();
            if (!UteisValidacao.emptyNumber(empresa.getCodigo())) {
                String sql = "SELECT p.cfp, p.nome, p.codigo, p.cnpj FROM Pessoa p "
                        + "INNER JOIN empresa ON empresa.pessoafinan = p.codigo WHERE empresa.codigo = " + empresa.getCodigo();
                ResultSet consulta = criarConsulta(sql, con);
                if (consulta.next()) {
                    pessoa.setNome(consulta.getString("nome"));
                    pessoa.setCfp(consulta.getString("cfp"));
                    pessoa.setCodigo(consulta.getInt("codigo"));
                    pessoa.setCnpj(consulta.getString("cnpj"));

                    empresa = empresaDAO.consultarPorChavePrimaria(empresa.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    atualizarDadosCadastraisPessoaEmpresa(pessoa, empresa);

                    return pessoa;
                }
                empresa = empresaDAO.consultarPorChavePrimaria(empresa.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                executarConsultaUpdate("INSERT INTO pessoa(nome) VALUES('" + empresa.getNome() + "')", con);
                int codigoGeradoTabela = obterValorChavePrimariaCodigo();
                executarConsultaUpdate("UPDATE empresa SET pessoafinan = '" + codigoGeradoTabela + "' where codigo = " + empresa.getCodigo(), con);
                pessoa.setNome(empresa.getNome());
                pessoa.setCodigo(codigoGeradoTabela);
                atualizarDadosCadastraisPessoaEmpresa(pessoa, empresa);
            }
            return pessoa;
        } finally {
            empresaDAO = null;
        }
    }

    public void atualizarDadosCadastraisPessoaEmpresa(PessoaVO pessoaVO, EmpresaVO empresaVO)throws Exception{
        Endereco enderecoDAO;
        Email emailDAO;
        try {
            enderecoDAO = new Endereco(con);
            emailDAO = new Email(con);

            boolean incluirEndereco = false;
            // atualizar endereço.
            EnderecoVO enderecoVO = enderecoDAO.consultar(pessoaVO.getCodigo(), TipoEnderecoEnum.COMERCIAL, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (enderecoVO == null) {
                enderecoVO = new EnderecoVO();
                incluirEndereco = true;
            }
            enderecoVO.setPessoa(pessoaVO.getCodigo());
            enderecoVO.setTipoEndereco(TipoEnderecoEnum.COMERCIAL.getCodigo());
            enderecoVO.setCep(empresaVO.getCEP());
            enderecoVO.setBairro(empresaVO.getSetor());
            enderecoVO.setNumero(empresaVO.getNumero());
            enderecoVO.setComplemento(empresaVO.getComplemento());
            enderecoVO.setEndereco(empresaVO.getEndereco());
            enderecoVO.setEnderecoCorrespondencia(true);
            if (incluirEndereco) {
                enderecoDAO.incluir(enderecoVO);
            } else {
                enderecoDAO.alterar(enderecoVO);
            }
            // atualizar email.
            List<EmailVO> listaEmailVO = emailDAO.consultarEmails(pessoaVO.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if ((listaEmailVO != null) && (listaEmailVO.size() > 0)) {
                EmailVO emailVO = listaEmailVO.get(0);
                emailVO.setEmail(empresaVO.getEmail());
                emailDAO.alterar(emailVO);
            } else {
                EmailVO emailVO = new EmailVO();
                emailVO.setPessoa(pessoaVO.getCodigo());
                emailVO.setEmailCorrespondencia(true);
                emailVO.setEmail(empresaVO.getEmail());
                emailDAO.incluir(emailVO);
            }
            StringBuilder sql = new StringBuilder();
            sql.append("update pessoa set tipoPessoa = 1, cnpj = '").append(empresaVO.getCNPJ()).append("' ");
            if (UtilReflection.objetoMaiorQueZero(empresaVO, "getCidade().getCodigo()")) {
                sql.append(",cidade = ").append(empresaVO.getCidade().getCodigo());
            }
            sql.append(" where codigo = ").append(pessoaVO.getCodigo());
            try (Statement st = con.createStatement()) {
                st.execute(sql.toString());
            }
        } finally {
            enderecoDAO = null;
            emailDAO = null;
        }
    }

    public static void atualizarNomePessoaEmpresaFinan(EmpresaVO empresa, Connection con) throws Exception {
        String sql = "UPDATE pessoa SET nome = '" + empresa.getNome()
                + "' WHERE codigo = (SELECT pessoafinan FROM empresa WHERE codigo = " + empresa.getCodigo() + ")";
        executarConsulta(sql, con);
    }

    public void incluirPessoaSimplificadoImportacao(PessoaVO obj) throws Exception {
        incluir(getIdEntidade());
        PessoaVO.validarDadosSimplificado(obj);
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO Pessoa(dataCadastro, nome, dataNasc, cfp, "
                + "cidade, estado, pais, sexo, rg, idExternoIntegracao, nomeconsulta) "
                + "VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            sqlInserir.setTimestamp(1, Uteis.getDataJDBCTimestamp(obj.getDataCadastro()));
            sqlInserir.setString(2, Uteis.getStringNormalizada(obj.getNome().toUpperCase()));
            if (obj.getDataNasc() != null) {
                sqlInserir.setDate(3, Uteis.getDataJDBC(obj.getDataNasc()));
            } else {
                sqlInserir.setNull(3, 0);
            }
            if (!obj.getCfp().isEmpty()) {
                sqlInserir.setString(4, obj.getCfp());
            } else {
                sqlInserir.setNull(4, 0);
            }
            if (obj.getCidade().getCodigo() != 0) {
                sqlInserir.setInt(5, obj.getCidade().getCodigo());
            } else {
                sqlInserir.setNull(5, 0);
            }
            if (obj.getEstadoVO().getCodigo() != 0) {
                sqlInserir.setInt(6, obj.getEstadoVO().getCodigo());
            } else {
                sqlInserir.setNull(6, 0);
            }
            if (obj.getPais().getCodigo() != 0) {
                sqlInserir.setInt(7, obj.getPais().getCodigo());
            } else {
                sqlInserir.setNull(7, 0);
            }
            if (!obj.getSexo().isEmpty()) {
                sqlInserir.setString(8, obj.getSexo());
            } else {
                sqlInserir.setNull(8, 0);
            }
            sqlInserir.setString(9, obj.getRg());
            sqlInserir.setString(10, obj.getIdExternoIntegracao());
            sqlInserir.setString(11, Uteis.getStringNormalizada(obj.getNome().toUpperCase()));
            sqlInserir.execute();
        }

        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
        Endereco en = new Endereco(con);
        en.incluirEnderecos(obj.getCodigo(), obj.getEnderecoVOs());
        Telefone tel = new Telefone(con);
        tel.incluirTelefones(obj.getCodigo(), obj.getTelefoneVOs());
        Email e = new Email(con);
        e.incluirEmails(obj.getCodigo(), obj.getEmailVOs());

    }

    @Override
    public Integer obterCodigoColaboradorComCodigoAlternativo(final String codAcessoAlternativo) throws Exception {
        Integer codigo = (Integer) consultarValorColunaTop(String.format("select codigo from colaborador where codacessoalternativo = '%s'",
                codAcessoAlternativo), con);
        return codigo == null ? 0 : codigo;
    }

    @Override
    public Integer obterCodigoClienteComCodigoAlternativo(final String codAcessoAlternativo) throws Exception {
        Integer codigo = (Integer) consultarValorColunaTop(String.format("select codigo from cliente where codacessoalternativo = '%s'",
                codAcessoAlternativo), con);
        return codigo == null ? 0 : codigo;
    }

    public String consultarJSON(Integer empresa) throws Exception {
        StringBuilder json;
        boolean dados;
        try (ResultSet rs = getPS(empresa).executeQuery()) {
            json = new StringBuilder();
            json.append("{\"aaData\":[");
            dados = false;
            while (rs.next()) {
                dados = true;
                json.append("[\"").append(rs.getString("codigo")).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("nome"))).append("\",");
                json.append("\"").append(rs.getDate("datanasc")).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("cfp"))).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("cidade"))).append("\"],");
            }
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    private PreparedStatement getPS(Integer empresa) throws SQLException {
        StringBuilder sql = new StringBuilder("select p.codigo, p.nome, p.cfp, p.datanasc, c.nome as cidade from pessoa p \n");
        sql.append(" LEFT JOIN cidade c on c.codigo = p.cidade ");
        sql.append(" INNER JOIN empresa emp ON emp.codigo = ?");
        sql.append(" left join cliente cli on cli.pessoa = p.codigo  \n");
        sql.append(" left join colaborador co on co.pessoa = p.codigo  \n");
        sql.append(" left join fornecedor  f on f.pessoa = p.codigo  \n");
        sql.append(" left join empresa em on em.pessoafinan = p.codigo  \n");
        sql.append(" where cli.codigo is null  \n");
        sql.append(" and co.codigo is null and f.codigo is null and em.codigo is null  \n");

        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        if (empresa != 0) {
            sqlConsultar.setInt(1, empresa);
        }
        return sqlConsultar;
    }

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa) throws SQLException {
        List lista;
        try (ResultSet rs = getPS(empresa).executeQuery()) {
            lista = new ArrayList();

            while (rs.next()) {
                PessoaVO pessoa = new PessoaVO();
                String geral = rs.getString("codigo") + rs.getString("nome") + rs.getString("datanasc")
                        + rs.getString("cfp") + rs.getString("cidade");
                if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                    pessoa.setCodigo(rs.getInt("codigo"));
                    pessoa.setNome(rs.getString("nome"));
                    pessoa.setDataNasc(rs.getDate("datanasc"));
                    pessoa.setCfp(rs.getString("cfp"));
                    pessoa.getCidade().setNome(rs.getString("cidade"));
                    lista.add(pessoa);
                }
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Nome")) {
            Ordenacao.ordenarLista(lista, "nome");
        } else if (campoOrdenacao.equals("Data de Nascimento")) {
            Ordenacao.ordenarLista(lista, "dataNasc_Apresentar");
        } else if (campoOrdenacao.equals("CPF")) {
            Ordenacao.ordenarLista(lista, "cfp");
        } else if (campoOrdenacao.equals("Cidade")) {
            Ordenacao.ordenarLista(lista, "cidade_Apresentar");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }

    public List<PessoaVO> consultarTodosPessoaComLimiteCRM(int codigoEmpresa, String valorConsulta, boolean controlarAcesso) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);

        List<PessoaVO> listaRetornar = new ArrayList<PessoaVO>();

        StringBuilder sqlStr = new StringBuilder();
        // Primeira subconsulta
        sqlStr.append("SELECT ")
                .append("p.codigo, ")
                .append("p.nome, ")
                .append("cli.pessoa AS tipoCliente, ")
                .append("NULL AS tipoColaborador ")
                .append("FROM pessoa p ")
                .append("LEFT JOIN cliente cli ON cli.pessoa = p.codigo ")
                .append("WHERE 1 = 1 \n");

        if (valorConsulta != null) {
            sqlStr.append("AND p.nome ILIKE '").append(valorConsulta.toUpperCase()).append("%' \n");
        }

        if (!UteisValidacao.emptyNumber(codigoEmpresa)) {
            sqlStr.append("AND cli.empresa = ").append(codigoEmpresa).append(" \n");
        }

        // UNION ALL para combinar os resultados
        sqlStr.append("UNION ALL \n");

        // Segunda subconsulta
        sqlStr.append("SELECT ")
                .append("p.codigo, ")
                .append("p.nome, ")
                .append("NULL AS tipoCliente, ")
                .append("col.pessoa AS tipoColaborador ")
                .append("FROM pessoa p ")
                .append("LEFT JOIN colaborador col ON col.pessoa = p.codigo ")
                .append("WHERE 1 = 1 \n");

        if (valorConsulta != null) {
            sqlStr.append("AND p.nome ILIKE '").append(valorConsulta.toUpperCase()).append("%' \n");
        }

        if (!UteisValidacao.emptyNumber(codigoEmpresa)) {
            sqlStr.append("AND col.empresa = ").append(codigoEmpresa).append(" \n");
        }

        // Ordem e limite final
        sqlStr.append("ORDER BY nome asc \n")
                .append("LIMIT 50;");

        try (PreparedStatement pst = con.prepareStatement(sqlStr.toString())) {
            try (ResultSet dadosSQL = pst.executeQuery()) {

                while (dadosSQL.next()) {
                    PessoaVO pessoaVO = new PessoaVO();
                    pessoaVO.setCodigo(dadosSQL.getInt("codigo"));
                    pessoaVO.setNome(dadosSQL.getString("nome"));

                    if (dadosSQL.getString("tipoCliente") != null) {
                        pessoaVO.setTipoPessoa("Cliente");
                    } else if (dadosSQL.getString("tipoColaborador") != null) {
                        pessoaVO.setTipoPessoa("Colaborador");
                    }
                    listaRetornar.add(pessoaVO);
                }
            }
        }

        return listaRetornar;
    }

    @Override
    public Integer consultarCodigoPessoaPorCodigoContrato(Integer codigo) throws Exception {
        try (PreparedStatement pst = con.prepareStatement("select pessoa from contrato where codigo=?")) {
            pst.setInt(1, codigo);
            try (ResultSet dadosSQL = pst.executeQuery()) {
                if (dadosSQL.next())
                    return dadosSQL.getInt("pessoa");
            }
        }

        return -1;
    }

    public List<NegociacaoEventoContratoTO> consultarEventosPessoa(Integer pessoa, Integer limit) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT nec.dataevento, nec.nomeevento, nec.eventointeresse, \n");
        sql.append(" ( select SUM(valorparcela) FROM movparcela WHERE codigo IN  \n");
        sql.append(" ( select parcela from negociacaoeventocontratoparcelas where contrato = nec.codigo)) as valor \n");
        sql.append(" FROM negociacaoeventocontrato nec \n");
        sql.append(" WHERE pessoa = ").append(pessoa);
        if(!UteisValidacao.emptyNumber(limit)){
            sql.append(" LIMIT ").append(limit);
}
        List<NegociacaoEventoContratoTO> lista = new ArrayList<NegociacaoEventoContratoTO>();
        try (ResultSet rs = criarConsulta(sql.toString(), con)) {
            while (rs.next()) {
                NegociacaoEventoContratoTO negc = new NegociacaoEventoContratoTO();
                negc.setDataEvento(rs.getDate("dataevento"));
                negc.setNomeEvento(rs.getString("nomeevento"));
                negc.setCodigoEvento(rs.getInt("eventointeresse"));
                negc.setValorFinal(rs.getDouble("valor"));
                lista.add(negc);
            }
        }
        return lista;
    }

    public List<PessoaTO> consultarPessoaColaboradorAtivo(Integer codigoEmpresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT p.*,col.codigo as codigoColaborador from Pessoa p\n");
        sql.append("INNER JOIN Colaborador col ON col.pessoa = p.codigo\n");
        sql.append(" WHERE situacao = 'AT' and col.empresa = ").append(codigoEmpresa);
        List<PessoaTO> lista = new ArrayList<PessoaTO>();
        try (ResultSet rs = criarConsulta(sql.toString(), con)) {
            while (rs.next()) {
                PessoaTO obj = new PessoaTO();
                obj.setNomeCompleto(rs.getString("nome"));
                obj.setCodigo(rs.getInt("codigo"));
                obj.setCodigoColaborador(rs.getString("codigoColaborador"));
                obj.setCpf(rs.getString("cfp"));
                lista.add(obj);
            }
        }
        return lista;
    }

    public void atualizarNomeFotoKey(Integer codigo, String nome, String fotokey) throws  Exception{
        String sql = "UPDATE pessoa SET nome = ?, fotokey = ? WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setString(1, nome);
            ps.setString(2, fotokey);
            ps.setInt(3, codigo);
            ps.executeUpdate();
        }
    }

    @Override
    public void alterarIdVindi(PessoaVO pessoa) throws Exception {
        String sql = "UPDATE pessoa set idvindi = ?, dataAlteracaoVindi = ? WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            if (!UteisValidacao.emptyNumber(pessoa.getIdVindi())) {
                ps.setInt(1, pessoa.getIdVindi());
            } else {
                ps.setNull(1, 0);
            }
            ps.setTimestamp(2, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            ps.setInt(3, pessoa.getCodigo());
            ps.executeUpdate();
        }
    }

    public void alterarIdVindiComLog(PessoaVO pessoa, UsuarioVO usuarioVO) throws Exception {
        try {
            con.setAutoCommit(false);

            PessoaVO pessoaAnterior = consultarPorChavePrimaria(pessoa.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

            alterarIdVindi(pessoa);
            gerarLogAlterarIdVindi(pessoa.getCodigo(), pessoaAnterior.getIdVindi(), pessoa.getIdVindi(), usuarioVO);

            con.commit();
        } catch (Exception ex) {
            con.rollback();
            con.setAutoCommit(true);
            throw ex;
        } finally {
            con.setAutoCommit(true);
        }
    }

    private void gerarLogAlterarIdVindi(Integer pessoa, Integer valorAnterior, Integer valorAtual, UsuarioVO usuarioVO) throws Exception {
        try {
            LogVO log = new LogVO();
            log.setNomeEntidade("PESSOA");
            log.setNomeEntidadeDescricao("PESSOA");
            log.setDescricao("ALTERAÇÃO - IDVINDI");
            log.setChavePrimaria(pessoa.toString());
            log.setDataAlteracao(Calendario.hoje());
            log.setUsuarioVO(usuarioVO);
            log.setResponsavelAlteracao(usuarioVO.getNomeAbreviado());
            log.setOperacao("ALTERAÇÃO");
            log.setNomeCampo("IdVindi");
            log.setUserOAMD(usuarioVO.getUserOamd());
            log.setPessoa(pessoa);

            if (UteisValidacao.emptyNumber(valorAnterior)) {
                log.setValorCampoAnterior("");
            } else {
                log.setValorCampoAnterior(valorAnterior.toString());
            }

            if (UteisValidacao.emptyNumber(valorAtual)) {
                log.setValorCampoAlterado("");
            } else {
                log.setValorCampoAlterado(valorAtual.toString());
            }

            Log logDAO = new Log(con);
            logDAO.incluirSemCommit(log);
            logDAO = null;
        } catch (Exception ex) {
            Uteis.logar(null, "Erro ao gerarLogAlterarIdVindi " + ex.getMessage());
            ex.printStackTrace();
            throw ex;
        }
    }

    @Override
    public void atualizaFotoKey(Integer codigo, String fotokey) throws Exception{
        String sql = "UPDATE pessoa SET fotokey = ? WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setString(1, fotokey);
            ps.setInt(2, codigo);
            ps.executeUpdate();
        }
    }


    @Override
    public void deletarFoto(final String chave, final Integer codigoPessoa) throws Exception {
        if (PropsService.isTrue(PropsService.fotosParaNuvem)) {
            if (!UteisValidacao.emptyNumber(codigoPessoa)) {
                MidiaService.getInstance().deleteObject(chave, MidiaEntidadeEnum.FOTO_PESSOA, codigoPessoa.toString());
            }
        }
    }

    @Override
    public void alterarIdMaxiPago(PessoaVO pessoa) throws Exception {
        String sql = "UPDATE pessoa set idMaxiPago = ?, dataAlteracaoMaxiPago = ? WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setString(1, pessoa.getIdMaxiPago());
            ps.setDate(2, Uteis.getDataJDBC(Calendario.hoje()));
            ps.setInt(3, pessoa.getCodigo());
            ps.executeUpdate();
        }
    }

    public PessoaVO consultarPorCodigoEEmpresa(Integer codigo, Integer codigoEmpresa, int nivelMontarDados)throws Exception{

        StringBuilder sqlStr = new StringBuilder("select " + getColunas("p.") + ",cli.pessoa as tipoCliente, "
                + " forn.pessoa as tipoFornecedor,  p.cfp as cfp, forn.cnpj as cnpj, col.pessoa as tipoColaborador "
                + " from pessoa p"
                + " left join cliente cli on cli.pessoa = p.codigo ");
        sqlStr.append(" left join fornecedor forn on forn.pessoa = p.codigo ");
        sqlStr.append("left join colaborador col on col.pessoa = p.codigo ");
        sqlStr.append("where p.codigo = ").append(codigo);
        if (!UteisValidacao.emptyNumber(codigoEmpresa)) {
            sqlStr.append("and (cli.empresa = ").append(codigoEmpresa).append(" or col.empresa = ").append(codigoEmpresa).append(" or forn.codigo is not null or(forn.codigo is null and cli.codigo is null and col.codigo is null) )");
        }
        sqlStr.append(" order by p.nome ");

        try (Statement st = con.createStatement()) {
            try (ResultSet rs = st.executeQuery(sqlStr.toString())) {
                if (rs.next()) {
                    return montarDados(rs, nivelMontarDados, con);
                }
            }
        }
        return null;
    }
    
    @Override
    public boolean senhaAcessoJaUtilizada(int codigoAutorizacao,int codigoPessoa, String senhaEncriptada) throws Exception {
        StringBuilder sqlStr = new StringBuilder("select exists (");
        sqlStr.append(" select codigo from autorizacaoacessogrupoempresarial auto where auto.senhaacesso = ? ");
        if(!UteisValidacao.emptyNumber(codigoAutorizacao)){
            sqlStr.append(" AND auto.codigo <> ? ");
        }
        sqlStr.append(" Union all ");
        sqlStr.append(" select codigo from pessoa pes where pes.senhaacesso = ? ");
        if(!UteisValidacao.emptyNumber(codigoPessoa)){
            sqlStr.append(" AND pes.codigo <> ? ");
        }
        sqlStr.append(") as existe");
        try (PreparedStatement stm = con.prepareStatement(sqlStr.toString())) {
            int i = 1;
            stm.setString(i++, senhaEncriptada);
            if (!UteisValidacao.emptyNumber(codigoAutorizacao)) {
                stm.setInt(i++, codigoAutorizacao);
            }
            stm.setString(i++, senhaEncriptada);
            if (!UteisValidacao.emptyNumber(codigoPessoa)) {
                stm.setInt(i++, codigoPessoa);
            }
            try (ResultSet tabelaResultado = stm.executeQuery()) {
                tabelaResultado.next();
                return tabelaResultado.getBoolean("existe");
            }
        }
    }

    public PessoaVO consultarPessoaAvulsaPorCPF(String cpf, int nivelMontarDados) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select p.* from pessoa p left join colaborador col on col.pessoa = p.codigo left join cliente cli on cli.pessoa = p.codigo where col.codigo is null and cli.codigo is null and p.cfp = '").append(cpf).append("'");
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                if (rs.next()) {
                    return montarDados(rs, nivelMontarDados, con);
                }
            }
        }
        return new PessoaVO();
    }

    @Override
    public PessoaVO consultarPessoaAvulsaPorRNE(String rne, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select p.* from pessoa p\n")
                .append("left join colaborador col on col.pessoa = p.codigo\n")
                .append("left join cliente cli on cli.pessoa = p.codigo\n")
                .append("where col.codigo is null and cli.codigo is null and p.rne = '").append(rne).append("'");
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                if (rs.next()) {
                    return montarDados(rs, nivelMontarDados, con);
                }
            }
        }
        return new PessoaVO();
    }

    @Override
    public void alterarIdGetNet(PessoaVO pessoa) throws Exception {
        String sql = "UPDATE pessoa set idGetNet = ?, dataAlteracaoGetNet = ? WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setString(1, pessoa.getIdGetNet());
            ps.setTimestamp(2, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            ps.setInt(3, pessoa.getCodigo());
            ps.executeUpdate();
        }
    }

    public void atualizarAssinaturaBiometriaDigital(Integer codigo, String assinatura) throws SQLException {
        String sql = "UPDATE pessoa set assinaturaDigitalBiometria = ? WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setString(1, assinatura);
            ps.setInt(2, codigo);
            ps.executeUpdate();
        }
    }

    public void atualizarAssinaturaBiometriaFacial(Integer codigo, String assinatura) throws SQLException {
        String sql = "UPDATE pessoa set assinaturaBiometriaFacial = ? WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setString(1, assinatura);
            ps.setInt(2, codigo);
            ps.executeUpdate();
        }
    }

    public String obterAssinaturaBiometriaDigital(Integer pessoa) throws SQLException {
        String sql = "select assinaturaDigitalBiometria from pessoa WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, pessoa);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    String assinaturaBiometriaDigital = rs.getString("assinaturaDigitalBiometria");
                    if (!UteisValidacao.emptyString(assinaturaBiometriaDigital)) {
                        return assinaturaBiometriaDigital;
                    }
                }
            }
        }
        return "";
    }

    public String obterAssinaturaBiometriaFacial(Integer pessoa) throws SQLException {
        String sql = "select assinaturaBiometriaFacial from pessoa WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, pessoa);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    String assinaturaBiometriaFacial = rs.getString("assinaturaBiometriaFacial");
                    if (!UteisValidacao.emptyString(assinaturaBiometriaFacial)) {
                        return assinaturaBiometriaFacial;
                    }
                }
                return "";
            }
        }
    }

    public boolean verificaAssinaturaBiometriaFacial(Integer pessoa) throws SQLException {
        String sql = "select (coalesce(assinaturabiometriafacial,'') <> '') as temAssinaturabiometriafacial from pessoa WHERE codigo =?";
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setInt(1, pessoa);
        ResultSet rs = ps.executeQuery();
        if (rs.next()) {
            return rs.getBoolean("temAssinaturabiometriafacial");
        }
        return false;
    }

    public boolean verificaAssinaturaBiometriaDigital(Integer pessoa) throws SQLException {
        String sql = "select  (coalesce(assinaturaDigitalBiometria,'') <> '') as temAssinaturaDigitalBiometria from pessoa WHERE codigo =?";
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setInt(1, pessoa);
        ResultSet rs = ps.executeQuery();
        if (rs.next()) {
            return rs.getBoolean("temAssinaturaDigitalBiometria");
        }
        return false;
    }

    public PessoaVO getPessoaPactoBR() throws Exception {
        try (PreparedStatement ps = con.prepareStatement(new StringBuilder()
                .append("    SELECT pessoa.*                                                            ")

                .append("      FROM      pessoa      pessoa                                             ")
                .append("INNER JOIN colaborador colaborador ON  colaborador.pessoa = pessoa.codigo      ")
                .append("INNER JOIN     usuario     usuario ON usuario.colaborador = colaborador.codigo ")

                .append("     WHERE  usuario.username ilike ?                                           ")
                .toString()
        )) {

            ps.setString(1, "PACTOBR");

            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs, NIVELMONTARDADOS_MINIMOS, null);
                }
            }
        }

        return new PessoaVO();
    }

    public TipoPessoa consultarTipoPessoa(int codigoPessoa) throws Exception {
        String sqlStr = "select tipopessoa from pessoa where codigo = " + codigoPessoa;
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sqlStr)) {
                while (rs.next()) {
                    return TipoPessoa.obterPorCodigo(rs.getInt("tipopessoa"));
                }
            }
        }
        return null;
    }

    public List<PessoaTO> consultarNomeSimples(int codigoEmpresa, String nomePessoa) throws Exception{
        String sqlStr = "select cli.codigo, nome from pessoa p inner join cliente cli on cli.pessoa = p.codigo ";
        sqlStr += " where upper(p.nome) like '%" + nomePessoa.toUpperCase() + "%' ";
        if (!UteisValidacao.emptyNumber(codigoEmpresa)) {
            sqlStr += " and cli.empresa = " + codigoEmpresa + "";
        }
        sqlStr += " order by p.nome limit 50";
        List<PessoaTO> pessoas;
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sqlStr)) {
                pessoas = new ArrayList<PessoaTO>();
                while (rs.next()) {
                    PessoaTO pessoa = new PessoaTO();
                    pessoa.setCodigo(rs.getInt("codigo"));
                    pessoa.setNomeCompleto(rs.getString("nome"));
                    pessoas.add(pessoa);
                }
            }
        }
        return pessoas;
    }

    public List<Integer> getCodigosPessoaEmbaralhar() throws  Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT c.pessoa ");
        sql.append("FROM ( ");
        sql.append("SELECT distinct t.pessoa, 0 as ordenacao ");
        sql.append("FROM (SELECT pessoa ");
        sql.append("FROM cliente ");
        sql.append("WHERE situacao = 'AT' ");
        sql.append("UNION ALL ");
        sql.append("SELECT pessoa ");
        sql.append("FROM colaborador ");
        sql.append("WHERE situacao = 'AT') as t ");
        sql.append("UNION  ");
        sql.append("SELECT distinct t.pessoa, 1 as ordenacao ");
        sql.append("FROM (SELECT pessoa ");
        sql.append("FROM cliente ");
        sql.append("WHERE situacao <> 'AT' ");
        sql.append("UNION ALL ");
        sql.append("SELECT pessoa ");
        sql.append("FROM colaborador ");
        sql.append("WHERE situacao <> 'AT') as t) as c ");
        sql.append("ORDER BY c.ordenacao ");
        List<Integer> codigos = new ArrayList<Integer>();
        ResultSet rs = criarConsulta(sql.toString(), con);
        while(rs.next()){
            codigos.add(rs.getInt("pessoa"));
        }
        return codigos;
    }

    public void excluirAssinaturaBiometriaComLog(PessoaVO pessoaVO, TipoAssinaturaBiometricaEnum tipoAssinaturaBiometricaEnum, UsuarioVO usuarioVO) throws SQLException {
        if (tipoAssinaturaBiometricaEnum.equals(TipoAssinaturaBiometricaEnum.DIGITAL)) {
            atualizarAssinaturaBiometriaDigital(pessoaVO.getCodigo(), "");
            gerarLogAlteracaoAssinaturaBiometria(pessoaVO.getCodigo(), tipoAssinaturaBiometricaEnum, pessoaVO.getAssinaturaBiometriaDigital(),"", usuarioVO);
            pessoaVO.setAssinaturaBiometriaDigital("");
        } else if (tipoAssinaturaBiometricaEnum.equals(TipoAssinaturaBiometricaEnum.FACIAL)) {
            atualizarAssinaturaBiometriaFacial(pessoaVO.getCodigo(), "");
            gerarLogAlteracaoAssinaturaBiometria(pessoaVO.getCodigo(), tipoAssinaturaBiometricaEnum, pessoaVO.getAssinaturaBiometriaFacial(),"", usuarioVO);
            pessoaVO.setAssinaturaBiometriaFacial("");
        }
    }

    private void gerarLogAlteracaoAssinaturaBiometria(Integer pessoa,
                                                     TipoAssinaturaBiometricaEnum tipoAssinaturaBiometricaEnum,
                                                     String assinaturaAnterior,
                                                     String assinaturaNova,
                                                     UsuarioVO usuarioVO) {
        try {
            if (usuarioVO == null) {
                return;
            }

            LogVO log = new LogVO();
            log.setNomeEntidade("PESSOA");
            log.setNomeEntidadeDescricao("PESSOA");
            log.setDescricao("ALTERAÇÃO - ASSINATURA " + tipoAssinaturaBiometricaEnum.getDescricao());
            log.setChavePrimaria(pessoa.toString());
            log.setDataAlteracao(Calendario.hoje());
            log.setUsuarioVO(usuarioVO);
            log.setResponsavelAlteracao(usuarioVO.getNomeAbreviado());
            log.setOperacao("ALTERAÇÃO");
            log.setNomeCampo(tipoAssinaturaBiometricaEnum.getDescricao());
            log.setUserOAMD(usuarioVO.getUserOamd());
            log.setPessoa(pessoa);
            log.setValorCampoAnterior(assinaturaAnterior);
            log.setValorCampoAlterado(assinaturaNova);

            Log logDAO = new Log(con);
            logDAO.incluirSemCommit(log);
            logDAO = null;
        } catch (Exception e) {
            Uteis.logar(null, "Erro ao gerarLogAlteracaoAssinaturaBiometria " + e.getMessage());
            e.printStackTrace();
        }
    }

    public void gravarModalAtualizacaoCadastral(PessoaVO obj, UsuarioVO usuarioVO) throws Exception {
        try {
            con.setAutoCommit(false);

            obj.realizarUpperCaseDados();
            String sql = "UPDATE Pessoa set nome = ?, atualizarDados = ?, aceiteTermosPacto = ?,\n " +
                    "cargo = ?, funcao = ?, receberSMSPacto = ?, receberEmailNovidadesPacto = ?,\n " +
                    "ipAceiteTermosPacto = ?, infoBrowserTermosPacto = ?, dataAceiteTermosPacto = ?\n" +
                    "WHERE codigo = ?";
            try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
                int i = 0;
                sqlAlterar.setString(++i, obj.getNome() + " " + obj.getSobreNome());
                sqlAlterar.setBoolean(++i, obj.isAtualizarDados());
                sqlAlterar.setBoolean(++i, obj.isAceiteTermosPacto());
                sqlAlterar.setInt(++i, obj.getCargoEnum().getCodigo());
                sqlAlterar.setInt(++i, obj.getFuncaoEnum().getCodigo());
                sqlAlterar.setBoolean(++i, obj.isReceberSMSPacto());
                sqlAlterar.setBoolean(++i, obj.isReceberEmailNovidadesPacto());
                sqlAlterar.setString(++i, obj.getIpAceiteTermosPacto());
                sqlAlterar.setString(++i, obj.getInfoBrowserTermosPacto());
                sqlAlterar.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataAceiteTermosPacto()));
                sqlAlterar.setInt(++i, obj.getCodigo());
                sqlAlterar.execute();
            }

            Telefone telefoneDAO = new Telefone(con);
            telefoneDAO.alterarTelefones(obj.getCodigo(), obj.getTelefoneVOs(), obj.getEmpresaInternacional(), false);
            telefoneDAO = null;

            Email emailDAO = new Email(con);
            emailDAO.alterarEmails(obj.getCodigo(), obj.getEmailVOs(), false);
            emailDAO = null;

            obj.setAtualizarDados(false);

            usuarioVO.setNome(obj.getNome() + " " + obj.getSobreNome());
            try (PreparedStatement psUsuario = con.prepareStatement("UPDATE usuario set nome = ? WHERE codigo = ?")) {
                int i = 0;
                psUsuario.setString(++i, usuarioVO.getNome());
                psUsuario.setInt(++i, usuarioVO.getCodigo());
                psUsuario.execute();
            }

            con.commit();
        } catch (Exception ex) {
            con.rollback();
            throw ex;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void alterarSpiviClientID(final Integer codigoPessoa,
                                           final Integer spiviClientID) throws Exception{
        String sql = "update pessoa set spiviClientID = ? where codigo = ? ";
        try (PreparedStatement pst = con.prepareStatement(sql)) {
            pst.setInt(1,spiviClientID);
            pst.setInt(2, codigoPessoa);
            pst.execute();
        }
    }

    public void incluirPessoaImportacao(PessoaVO obj) throws Exception {
        PessoaVO.validarDadosSimplificado(obj);
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO Pessoa(dataCadastro, nome, dataNasc, cfp, "
                + "cidade, estado, pais, sexo, nomepai, cpfpai, nomemae, cpfmae, estadocivil, rg, grauinstrucao, rgOrgao, "
                + "nacionalidade, naturalidade, profissao, rguf, idexterno, passaporte) "
                + "VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        PreparedStatement ps = con.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);
        int i = 0;
        resolveTimestampNull(ps, ++i, obj.getDataCadastro());
        ps.setString(++i, Uteis.getStringNormalizada(obj.getNome()));
        resolveDateNull(ps, ++i, obj.getDataNasc());
        ps.setString(++i, obj.getCfp());
        resolveIntegerNull(ps, ++i, obj.getCidade().getCodigo());
        resolveIntegerNull(ps, ++i, obj.getEstadoVO().getCodigo());
        resolveIntegerNull(ps, ++i, obj.getPais().getCodigo());
        ps.setString(++i, obj.getSexo());
        ps.setString(++i, obj.getNomePai());
        ps.setString(++i, obj.getCpfPai());
        ps.setString(++i, obj.getNomeMae());
        ps.setString(++i, obj.getCpfMae());
        ps.setString(++i, obj.getEstadoCivil());
        ps.setString(++i, obj.getRg());
        resolveIntegerNull(ps, ++i, obj.getGrauInstrucao().getCodigo());
        ps.setString(++i, obj.getRgOrgao());
        ps.setString(++i, obj.getNacionalidade());
        ps.setString(++i, obj.getNaturalidade());
        resolveIntegerNull(ps, ++i, obj.getProfissao().getCodigo());
        ps.setString(++i, obj.getRgUf());
        resolveLongNull(ps, ++i, obj.getIdExterno());
        resolveStringNull(ps, ++i, obj.getPassaporte());
        ps.execute();

        ResultSet rs = ps.getGeneratedKeys();
        if (rs.next()) {
            obj.setCodigo(rs.getInt("codigo"));
        }
//        obj.setCodigo(Conexao.obterUltimoCodigoGeradoTabela(con, "pessoa"));

        obj.setNovoObj(false);

        Endereco endereco = new Endereco(con);
        endereco.incluirEnderecos(obj.getCodigo(), obj.getEnderecoVOs());
        endereco = null;
        Telefone telefone = new Telefone(con);
        telefone.incluirTelefones(obj.getCodigo(), obj.getTelefoneVOs());
        telefone = null;
        Email email = new Email(con);
        email.incluirEmails(obj.getCodigo(), obj.getEmailVOs());
        email = null;
    }

    public void alterarIdMundiPagg(PessoaVO pessoa) throws Exception {
        String sql = "UPDATE pessoa set idMundipagg = ?, dataAlteracaoMundipagg = ? WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setString(1, pessoa.getIdMundiPagg());
            ps.setTimestamp(2, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            ps.setInt(3, pessoa.getCodigo());
            ps.executeUpdate();
        }
    }

    public void alterarIdPagarMe(PessoaVO pessoa) throws Exception {
        String sql = "UPDATE pessoa set idPagarme = ?, dataAlteracaoPagarme = ? WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setString(1, pessoa.getIdPagarMe());
            ps.setTimestamp(2, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            ps.setInt(3, pessoa.getCodigo());
            ps.executeUpdate();
        }
    }

    public void alterarIdStone(PessoaVO pessoa) throws Exception {
        String sql = "UPDATE pessoa set idStone = ?, dataAlteracaoStone = ? WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setString(1, pessoa.getIdStone());
            ps.setTimestamp(2, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            ps.setInt(3, pessoa.getCodigo());
            ps.executeUpdate();
        }
    }

    public void alterarCustomerIdPagoLivre(PessoaVO pessoa) throws Exception {
        String sql = "UPDATE pessoa set customeridpagolivre = ? WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setString(1, pessoa.getCustomerIdPagoLivre());
            ps.setInt(2, pessoa.getCodigo());
            ps.executeUpdate();
        }
    }

    public void alterarIdAsaas(PessoaVO pessoa) throws Exception {
        String sql = "UPDATE pessoa set idAsaas = ?, dataAlteracaoIdAsaas = ? WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setString(1, pessoa.getIdAsaas());
            ps.setTimestamp(2, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            ps.setInt(3, pessoa.getCodigo());
            ps.executeUpdate();
        }
    }

    public void obterInformacoesDeBloqueioCobrancaAutomatica(PessoaVO pessoaVO) throws Exception {
        if (pessoaVO != null && !UteisValidacao.emptyNumber(pessoaVO.getCodigo())) {
            String sql = "select dataBloqueioCobrancaAutomatica, tipoBloqueioCobrancaAutomatica from pessoa where codigo = " + pessoaVO.getCodigo();
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sql)) {
                    if (rs.next()) {
                        pessoaVO.setDataBloqueioCobrancaAutomatica(rs.getTimestamp("dataBloqueioCobrancaAutomatica"));
                        pessoaVO.setTipoBloqueioCobrancaAutomatica(TipoBloqueioCobrancaEnum.obterTipoBloqueioCobrancaEnum(rs.getInt("tipoBloqueioCobrancaAutomatica")));
                    }
                }
            }
        }
    }

    public void obterInformacoesDeBloqueioCobrancaAutomatica(PessoaVO pessoaVO, boolean validarNull) throws Exception {
        if (pessoaVO != null && !UteisValidacao.emptyNumber(pessoaVO.getCodigo())) {
            String sql = "select dataBloqueioCobrancaAutomatica, tipoBloqueioCobrancaAutomatica from pessoa where codigo = " + pessoaVO.getCodigo();
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sql)) {
                    if (rs.next()) {
                        pessoaVO.setDataBloqueioCobrancaAutomatica(rs.getTimestamp("dataBloqueioCobrancaAutomatica"));

                        if (validarNull) {
                            String tipoBloqueioCobrancaAutomatica = rs.getString("tipoBloqueioCobrancaAutomatica");
                            if (tipoBloqueioCobrancaAutomatica != null) {
                                pessoaVO.setTipoBloqueioCobrancaAutomatica(TipoBloqueioCobrancaEnum.obterTipoBloqueioCobrancaEnum(rs.getInt("tipoBloqueioCobrancaAutomatica")));
                            }
                        } else {
                            pessoaVO.setTipoBloqueioCobrancaAutomatica(TipoBloqueioCobrancaEnum.obterTipoBloqueioCobrancaEnum(rs.getInt("tipoBloqueioCobrancaAutomatica")));
                        }
                    }
                }
            }
        }
    }

    public void alterarDataBloqueioCobrancaAutomatica(Date dataBloqueioCobrancaAutomatica, TipoBloqueioCobrancaEnum tipoBloqueioCobrancaEnum,
                                                      Integer pessoa, UsuarioVO usuarioVO, boolean controlaTransacao, String mensagemAdicionalLog) throws Exception {

        try {
            if (controlaTransacao) {
                con.setAutoCommit(false);
            }

            String update = "update pessoa set dataBloqueioCobrancaAutomatica = ?, tipoBloqueioCobrancaAutomatica = ? where codigo = ?";
            try (PreparedStatement pst = con.prepareStatement(update)) {
                pst.setTimestamp(1, Uteis.getDataJDBCTimestamp(dataBloqueioCobrancaAutomatica));
                if (dataBloqueioCobrancaAutomatica == null || tipoBloqueioCobrancaEnum == null) {
                    pst.setNull(2, 0);
                } else {
                    pst.setInt(2, tipoBloqueioCobrancaEnum.getCodigo());
                }
                pst.setInt(3, pessoa);
                pst.execute();
            }
            gerarLogAlterarDataBloqueioCobrancaAutomatica(dataBloqueioCobrancaAutomatica, tipoBloqueioCobrancaEnum, pessoa, usuarioVO, mensagemAdicionalLog);

            if (controlaTransacao) {
                con.commit();
            }
        } catch (Exception e) {
            if (controlaTransacao) {
                con.rollback();
                con.setAutoCommit(true);
            }
            e.printStackTrace();
            throw e;
        } finally {
            if (controlaTransacao) {
                con.setAutoCommit(true);
            }
        }
    }

    private void gerarLogAlterarDataBloqueioCobrancaAutomatica(Date dataBloqueioCobrancaAutomatica, TipoBloqueioCobrancaEnum tipoBloqueioCobrancaEnum,
                                                               Integer pessoa, UsuarioVO usuarioVO, String mensagemAdicionalLog) throws Exception {
        try {
            LogVO log = new LogVO();
            log.setNomeEntidade("PESSOA");
            log.setNomeEntidadeDescricao("PESSOA");
            log.setDescricao("PESSOA-BLOQUEIO-COBRANCAS");
            log.setChavePrimaria(pessoa.toString());
            log.setDataAlteracao(Calendario.hoje());
            log.setUsuarioVO(usuarioVO);
            log.setResponsavelAlteracao(usuarioVO.getNomeAbreviado());
            log.setOperacao("ALTERAÇÃO");
            log.setNomeCampo("PESSOA-BLOQUEIO-COBRANCAS");
            log.setUserOAMD(usuarioVO.getUserOamd());
            log.setPessoa(pessoa);
            log.setValorCampoAnterior("");
            JSONObject json = new JSONObject();
            json.put("pessoa", pessoa);
            if (dataBloqueioCobrancaAutomatica == null) {
                json.put("dataBloqueioCobrancaAutomatica", "Bloqueio de cobranças automáticas removida");
            } else {
                json.put("dataBloqueioCobrancaAutomatica", Calendario.getDataAplicandoFormatacao(dataBloqueioCobrancaAutomatica, "dd/MM/yyyy"));
                json.put("tipoBloqueioCobranca", tipoBloqueioCobrancaEnum.getCodigo() + " - " + tipoBloqueioCobrancaEnum.getDescricao());
            }
            if (!UteisValidacao.emptyString(mensagemAdicionalLog)) {
                json.put("mensagemAdicional", mensagemAdicionalLog);
            }
            log.setValorCampoAlterado(json.toString());
            Log logDAO = new Log(con);
            logDAO.incluirSemCommit(log);
            logDAO = null;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public Map<Integer, PessoaVO> obterMapaPessoasBloqueioCobrancaAutomatica() throws Exception {
        Map<Integer, PessoaVO> mapa = new HashMap<>();
        String sql = "select codigo, dataBloqueioCobrancaAutomatica, tipoBloqueioCobrancaAutomatica from pessoa where dataBloqueioCobrancaAutomatica is not null";
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql)) {
                while (rs.next()) {
                    Integer pessoa = rs.getInt("codigo");
                    PessoaVO pessoaVO = new PessoaVO();
                    pessoaVO.setCodigo(pessoa);
                    pessoaVO.setDataBloqueioCobrancaAutomatica(rs.getTimestamp("dataBloqueioCobrancaAutomatica"));
                    pessoaVO.setTipoBloqueioCobrancaAutomatica(TipoBloqueioCobrancaEnum.obterTipoBloqueioCobrancaEnum(rs.getInt("tipoBloqueioCobrancaAutomatica")));
                    mapa.put(pessoa, pessoaVO);
                }
            }
        }
        return mapa;
    }

    public boolean podeCobrarParcelaBloqueioCobrancaAutomatica(MovParcelaVO movParcelaVO, Map<Integer, PessoaVO> mapaPessoaBloqueio) {
        try {
            //by Luiz Felipe 28/04/2020

            if (movParcelaVO == null) {
                throw new Exception("MovParcela null!");
            }

            Integer codPessoa = movParcelaVO.getPessoa().getCodigo();
            if (UteisValidacao.emptyNumber(codPessoa)) {
                codPessoa = descobrirCodigoPessoa(null, null, movParcelaVO.getCodigo(), movParcelaVO.getContrato().getCodigo());
            }
            if (UteisValidacao.emptyNumber(codPessoa)) {
                Uteis.logar(null, "Pessoa não encontrada! " + movParcelaVO.getCodigo());
                return false;
            }

            PessoaVO pessoaVO = mapaPessoaBloqueio.get(codPessoa);
            if (pessoaVO == null) {
                //não tem a pessoa no mapa então ela não tem bloqueio!
                //está liberado a cobrança
                return true;
            }

            Date dataBloqueioCobrancaAutomatica = pessoaVO.getDataBloqueioCobrancaAutomatica();
            TipoBloqueioCobrancaEnum tipoBloqueioCobrancaAutomatica = pessoaVO.getTipoBloqueioCobrancaAutomatica();


            //É O PRINCIPAL PARA SABER SE A PESSOA ESTÁ BLOQUEADA OU NÃO
            //se não tiver data de bloqueio está liberado a cobrança
            if (dataBloqueioCobrancaAutomatica == null) {
                return true;
            }


            //bloqueio de todas as parcelas
            if (tipoBloqueioCobrancaAutomatica != null &&
                    tipoBloqueioCobrancaAutomatica.equals(TipoBloqueioCobrancaEnum.TODAS_PARCELAS)) {
                return false;
            }

            // se for somente parcelas futuras
            // se for parcela com data menor ou igual a data do bloqueio pode cobrar
            if (tipoBloqueioCobrancaAutomatica != null &&
                    tipoBloqueioCobrancaAutomatica.equals(TipoBloqueioCobrancaEnum.PARCELAS_FUTURAS) &&
                    Calendario.menorOuIgual(movParcelaVO.getDataVencimento(), dataBloqueioCobrancaAutomatica)) {
                return true;
            }

            return false;
        } catch (Exception ex) {
            //em casos de exceção retornar TRUE para evitar problemas de cobranças não realizadas
            ex.printStackTrace();
            return true;
        }
    }

    public Integer descobrirCodigoPessoa(Integer codigoCliente, Integer codigoColaborador,
                                         Integer codigoMovParcela, Integer codigoContrato) throws Exception {

        if (!UteisValidacao.emptyNumber(codigoCliente)) {
            Integer codigoPessoa = obterPessoaCliente(codigoCliente);
            if (!UteisValidacao.emptyNumber(codigoPessoa)) {
                return codigoPessoa;
            }
        }
        if (!UteisValidacao.emptyNumber(codigoColaborador)) {
            Integer codigoPessoa = obterPessoaColaborador(codigoColaborador);
            if (!UteisValidacao.emptyNumber(codigoPessoa)) {
                return codigoPessoa;
            }
        }
        if (!UteisValidacao.emptyNumber(codigoMovParcela)) {
            Integer codigoPessoa = obterPessoaMovParcela(codigoMovParcela);
            if (!UteisValidacao.emptyNumber(codigoPessoa)) {
                return codigoPessoa;
            }
        }
        if (!UteisValidacao.emptyNumber(codigoContrato)) {
            Integer codigoPessoa = obterPessoaContrato(codigoContrato);
            if (!UteisValidacao.emptyNumber(codigoPessoa)) {
                return codigoPessoa;
            }
        }
        return null;
    }

    private Integer obterPessoaMovParcela(Integer movparcela) throws Exception {
        String sql = "SELECT pessoa FROM movparcela WHERE codigo = " + movparcela;
        try (ResultSet rs = con.prepareStatement(sql).executeQuery()) {
            return rs.next() ? rs.getInt("pessoa") : null;
        }
    }

    public Integer obterPessoaCliente(Integer cliente) throws Exception {
        String sql = "SELECT pessoa FROM cliente WHERE codigo = " + cliente;
        try (ResultSet rs = con.prepareStatement(sql).executeQuery()) {
            return rs.next() ? rs.getInt("pessoa") : null;
        }
    }

    public Integer obterPessoaColaborador(Integer colaborador) throws Exception {
        String sql = "SELECT pessoa FROM colaborador WHERE codigo = " + colaborador;
        try (ResultSet rs = con.prepareStatement(sql).executeQuery()) {
            return rs.next() ? rs.getInt("pessoa") : null;
        }
    }

    private Integer obterPessoaContrato(Integer contrato) throws Exception {
        String sql = "SELECT pessoa FROM contrato WHERE codigo = " + contrato;
        try (ResultSet rs = con.prepareStatement(sql).executeQuery()) {
            return rs.next() ? rs.getInt("pessoa") : null;
        }
    }

    public List<PessoaBloqueioCobrancaTO> consultarPessoasComCobrancaBloqueada(Integer empresa, Date inicio, Date fim) throws Exception {

        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("p.codigo as pessoa, \n");
        sql.append("p.nome, \n");
        sql.append("cl.codigo as cliente, \n");
        sql.append("cl.matricula, \n");
        sql.append("p.dataBloqueioCobrancaAutomatica, \n");
        sql.append("p.tipoBloqueioCobrancaAutomatica, \n");
        sql.append("array_to_string(array(SELECT email FROM email WHERE email.pessoa = p.codigo), '; ', '') AS emails, \n");
        sql.append("array_to_string(array(SELECT numero FROM telefone WHERE telefone.pessoa = p.codigo), ', ', '') AS telefones, \n");
        sql.append("e.codigo as empresa, \n");
        sql.append("e.nome as empresaNome \n");
        sql.append("from pessoa p \n");
        sql.append("left join cliente cl on p.codigo = cl.pessoa \n");
        sql.append("left join empresa e on e.codigo = cl.empresa \n");
        sql.append("where p.dataBloqueioCobrancaAutomatica is not null \n");

        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append(" and cl.empresa = ").append(empresa).append(" \n");
        }
        if (inicio != null) {
            sql.append("and p.dataBloqueioCobrancaAutomatica::date >= '").append(Uteis.getDataFormatoBD(inicio)).append("' \n");
        }
        if (fim != null) {
            sql.append("and p.dataBloqueioCobrancaAutomatica::date <= '").append(Uteis.getDataFormatoBD(fim)).append("' \n");
        }
        sql.append("order by p.nome \n");

        List<PessoaBloqueioCobrancaTO> lista = new ArrayList<>();

        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                while (rs.next()) {
                    PessoaBloqueioCobrancaTO obj = new PessoaBloqueioCobrancaTO();
                    obj.setPessoa(rs.getInt("pessoa"));
                    obj.setNome(rs.getString("nome"));
                    obj.setCliente(rs.getInt("cliente"));
                    obj.setMatricula(rs.getString("matricula"));
                    obj.setDataBloqueio(rs.getDate("dataBloqueioCobrancaAutomatica"));
                    obj.setTipoBloqueioCobrancaEnum(TipoBloqueioCobrancaEnum.obterTipoBloqueioCobrancaEnum(rs.getInt("tipoBloqueioCobrancaAutomatica")));
                    obj.setEmpresa(rs.getInt("empresa"));
                    obj.setEmpresaNome(rs.getString("empresaNome"));
                    obj.setEmails(rs.getString("emails"));
                    obj.setTelefones(rs.getString("telefones"));
                    lista.add(obj);
                }
            }
        }
        return lista;
    }

    public List<PessoaBloqueioCobrancaTO> consultarPessoasParaBloqueio(boolean desbloquear, ManutencaoAjusteGeralTO obj) throws Exception {
        List<PessoaBloqueioCobrancaTO> lista = new ArrayList<>();

        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("distinct \n");
        sql.append("p.codigo as pessoa, \n");
        sql.append("p.nome, \n");
        sql.append("cl.codigo as cliente, \n");
        sql.append("cl.matricula, \n");
        sql.append("p.dataBloqueioCobrancaAutomatica, \n");
        sql.append("p.tipoBloqueioCobrancaAutomatica, \n");
        sql.append("e.codigo as empresa, \n");
        sql.append("e.nome as empresaNome \n");
        sql.append("from pessoa p \n");
        sql.append("left join cliente cl on p.codigo = cl.pessoa \n");
        sql.append("left join situacaoclientesinteticodw sw on sw.codigopessoa = p.codigo \n");
        sql.append("left join contrato co on co.codigo = sw.codigocontrato \n");
        sql.append("left join contratomodalidade com on com.contrato = co.codigo \n");
        sql.append("left join empresa e on e.codigo = cl.empresa \n");
        sql.append("where 1 = 1 \n");

        if (!UteisValidacao.emptyString(obj.getSituacao())) {

            //buscar pela situaacao do aluno
            if (obj.getSituacao().equalsIgnoreCase(SituacaoClienteEnum.ATIVO.getCodigo()) ||
                    obj.getSituacao().equalsIgnoreCase(SituacaoClienteEnum.CANCELADO.getCodigo()) ||
                    obj.getSituacao().equalsIgnoreCase(SituacaoClienteEnum.TRANCADO.getCodigo()) ||
                    obj.getSituacao().equalsIgnoreCase(SituacaoClienteEnum.INATIVO.getCodigo()) ||
                    obj.getSituacao().equalsIgnoreCase(SituacaoClienteEnum.VISITANTE.getCodigo())) {
                sql.append("and sw.situacao = '").append(obj.getSituacao().toUpperCase()).append("' \n");
            } else {
                //buscar pela situacao do contrato
                sql.append("and sw.situacaocontrato = '").append(obj.getSituacao().toUpperCase()).append("' \n");
            }
        }

        if (!UteisValidacao.emptyNumber(obj.getPlanoVO().getCodigo())) {
            sql.append("and co.plano = ").append(obj.getPlanoVO().getCodigo()).append(" \n");
        }

        if (!UteisValidacao.emptyNumber(obj.getModalidadeVO().getCodigo())) {
            sql.append("and com.modalidade = ").append(obj.getModalidadeVO().getCodigo()).append(" \n");
        }

        if (!UteisValidacao.emptyNumber(obj.getEmpresaVO().getCodigo())) {
            sql.append("and cl.empresa = ").append(obj.getEmpresaVO().getCodigo()).append(" \n");
        }

        if (!UteisValidacao.emptyNumber(obj.getConvenioCobrancaVO().getCodigo())) {
            sql.append("and exists(select codigo from autorizacaocobrancacliente  where ativa and cliente = cl.codigo and conveniocobranca = ").append(obj.getConvenioCobrancaVO().getCodigo()).append(") \n");
        }

        if (desbloquear) {
            sql.append("and p.dataBloqueioCobrancaAutomatica is not null \n");
        } else {
            if (obj.isPessoasSemDataBloqueioCobrancaAutomatica()) {
                sql.append("and p.dataBloqueioCobrancaAutomatica is null \n");
            }
        }
        sql.append("order by p.nome \n");

        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                while (rs.next()) {
                    PessoaBloqueioCobrancaTO pessoaBloqueioCobrancaTO = new PessoaBloqueioCobrancaTO();
                    pessoaBloqueioCobrancaTO.setPessoa(rs.getInt("pessoa"));
                    pessoaBloqueioCobrancaTO.setNome(rs.getString("nome"));
                    pessoaBloqueioCobrancaTO.setCliente(rs.getInt("cliente"));
                    pessoaBloqueioCobrancaTO.setMatricula(rs.getString("matricula"));
                    pessoaBloqueioCobrancaTO.setDataBloqueio(rs.getDate("dataBloqueioCobrancaAutomatica"));
                    pessoaBloqueioCobrancaTO.setTipoBloqueioCobrancaEnum(TipoBloqueioCobrancaEnum.obterTipoBloqueioCobrancaEnum(rs.getInt("tipoBloqueioCobrancaAutomatica")));
                    pessoaBloqueioCobrancaTO.setEmpresa(rs.getInt("empresa"));
                    pessoaBloqueioCobrancaTO.setEmpresaNome(rs.getString("empresaNome"));
                    lista.add(pessoaBloqueioCobrancaTO);
                }
            }
            return lista;
        }
    }

    public List<PessoaVO> consultarPorCNPJ(String cnpj, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select * from pessoa \n");
        sql.append("where cnpj = '").append(Uteis.formatarCpfCnpj(cnpj, true)).append("' or cnpj = '").append(Uteis.formatarCpfCnpj(cnpj, false)).append("' ");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    public void atualizarDados(Boolean atualizarDados, Integer codPessoa) throws SQLException {
        String sql = "UPDATE pessoa SET atualizarDados = ? WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setBoolean(1, atualizarDados);
            ps.setInt(2, codPessoa);
            ps.execute();
        }
    }

    public List<String> nomesColaboradores(List<Integer> codigosColaboradores) throws SQLException {
        List<String> nomesColaboradores = new ArrayList<>();
        String sql = "select nome from  pessoa " +
                "inner join colaborador on pessoa.codigo = colaborador.pessoa " +
                "where colaborador.codigo in (" + Uteis.montarListaIN(codigosColaboradores) + ")";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ResultSet resultSet = ps.executeQuery();
            while (resultSet.next()){
                nomesColaboradores.add(resultSet.getString("nome"));
            }
        }
        return nomesColaboradores;
    }

    public PessoaCPFTO obterCpfValidandoIdade(PessoaVO pessoaVO) throws Exception {
        Cliente clienteDAO;
        try {
            clienteDAO = new Cliente(con);

            if (pessoaVO == null || UteisValidacao.emptyNumber(pessoaVO.getCodigo())) {
                return null;
            }

            PessoaVO pessoaVOCompleta = consultarPorChavePrimaria(pessoaVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            PessoaCPFTO pessoaCPFTO = new PessoaCPFTO();
            pessoaCPFTO.setPessoa(pessoaVOCompleta.getCodigo());
            pessoaCPFTO.setNome(pessoaVOCompleta.getNome());

            String nomeResponsavel = "";
            String cpfResponsavel = "";
            if (pessoaVOCompleta.getDataNasc() != null && pessoaVOCompleta.getIdade() < 18) {

                PessoaVO pessoaResponsavel = clienteDAO.obterPessoaResponsavelCliente(null, pessoaVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                if (pessoaResponsavel != null && !UteisValidacao.emptyString(pessoaResponsavel.getCfp().trim())) {
                    nomeResponsavel = pessoaResponsavel.getNome();
                    cpfResponsavel = pessoaResponsavel.getCfp();
                } else if (!UteisValidacao.emptyString(pessoaVOCompleta.getNomeMae().trim()) && !UteisValidacao.emptyString(pessoaVOCompleta.getCpfMae().trim())) {
                    nomeResponsavel = pessoaVOCompleta.getNomeMae();
                    cpfResponsavel = pessoaVOCompleta.getCpfMae();
                } else if (!UteisValidacao.emptyString(pessoaVOCompleta.getNomePai().trim()) && !UteisValidacao.emptyString(pessoaVOCompleta.getCpfPai().trim())) {
                    nomeResponsavel = pessoaVOCompleta.getNomePai();
                    cpfResponsavel = pessoaVOCompleta.getCpfPai();
                } else {
                    throw new ConsistirException(String.format("Cliente %s não possui responsável cadastrado.", pessoaVOCompleta.getNome()));
                }
            } else {
                nomeResponsavel = pessoaVOCompleta.getNome();
                cpfResponsavel = pessoaVOCompleta.getCfp();
            }

            pessoaCPFTO.setNomeResponsavel(nomeResponsavel);
            pessoaCPFTO.setCpfResponsavel(Formatador.removerMascara(cpfResponsavel));
            return pessoaCPFTO;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            clienteDAO = null;
        }
    }

    @Override
    public int obterPessoaPorIdExternoIntegracao(String idExternoIntegracao) throws Exception {
        String sql = "SELECT codigo FROM pessoa WHERE idexternointegracao ILIKE '" + idExternoIntegracao + "' ";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("codigo");
                } else {
                    throw new ConsistirException("Pessoa não encontrada.");
                }
            }
        }
    }

    @Override
    public String uploadTemplateFotoPessoa(String key, int codigoPessoa, String templateFoto) throws Exception {
        try {
            final String identificador = codigoPessoa + "_" + MidiaEntidadeEnum.TEMPLATE_FACIAL_FOTO_PESSOA.name();
            final String chave = DAO.resolveKeyFromConnection(con);
            templateFoto = templateFoto.replaceAll("data:image", "");
            templateFoto = templateFoto.replaceAll("/(png|jpg|jpeg);base64,", "");
            MidiaService.getInstance().uploadObjectFromByteArray(chave, MidiaEntidadeEnum.TEMPLATE_FACIAL_FOTO_PESSOA, identificador, Base64.decodeBase64(templateFoto));
            return "Sucesso ao realizar upload da foto";
        } catch (Exception ex) {
            throw new ConsistirException ("Erro ao realizar upload da foto");
        }
    }

    public Integer codigoPessoaResponsavel(String nome) throws Exception {
        String sql = "SELECT codigo FROM pessoa WHERE nome = '" + nome + "'";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("codigo");
                }
            }
        }
        return 0;
    }

    public String obtemNumeroTelefone(Integer codigoPessoa) throws Exception{
        String sql = "SELECT numero FROM telefone WHERE pessoa = " + codigoPessoa;

        try (PreparedStatement stm = con.prepareStatement(sql);
             ResultSet tabelaResultado = stm.executeQuery()) {
            if (tabelaResultado.next()) {
                return tabelaResultado.getString("numero");
            }
        }
        return "";
    }

    public String obterCodigoViteo(Integer codConsultor) throws Exception {
        String sql = "SELECT codigoAfiliadoVitio FROM colaborador WHERE codigo = " + codConsultor + " AND codigoAfiliadoVitio is not null";

        try (PreparedStatement stm = con.prepareStatement(sql);
             ResultSet tabelaResultado = stm.executeQuery()) {
            if (tabelaResultado.next()) {
                return tabelaResultado.getString("codigoAfiliadoVitio");
            }
        }
        return "";
    }

    public Boolean existeContratoParaPessoa(Integer pessoa) throws Exception {
        String sql = "SELECT * FROM contrato WHERE pessoa = " + pessoa;

        try (PreparedStatement stm = con.prepareStatement(sql);
             ResultSet tabelaResultado = stm.executeQuery()) {
            if (tabelaResultado.next()) {
                return true;
            }
        }
        return false;
    }

    public PessoaVO consultarPessoaEmpresaFinanSemAlterar(EmpresaVO empresa) throws Exception {
        PessoaVO pessoa = new PessoaVO();
        if (!UteisValidacao.emptyNumber(empresa.getCodigo())) {
            String sql = "SELECT p.cfp, p.nome, p.codigo, p.cnpj FROM Pessoa p "
                    + "INNER JOIN empresa ON empresa.pessoafinan = p.codigo WHERE empresa.codigo = " + empresa.getCodigo();
            ResultSet consulta = criarConsulta(sql, con);
            if (consulta.next()) {
                pessoa.setNome(consulta.getString("nome"));
                pessoa.setCfp(consulta.getString("cfp"));
                pessoa.setCodigo(consulta.getInt("codigo"));
                pessoa.setCnpj(consulta.getString("cnpj"));

                return pessoa;
            }
        }
        return pessoa;
    }

    public void separarPessoaColaborador(Integer codigoColaborador, PessoaVO pessoaColaborador, PessoaVO pessoaAluno) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemComit(pessoaAluno);
            incluirSemComit(pessoaColaborador);
            getFacade().getColaborador().atualizarNovaPessoa(codigoColaborador, pessoaColaborador.getCodigo());
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public PessoaVO consultarPessoaPorCliente(Integer codigoCliente) throws Exception {
        PessoaVO pessoa = new PessoaVO();
        String sql = "select p.codigo, p.nome, p.cfp\n" +
                "from pessoa p\n" +
                "         inner join cliente c on c.pessoa = p.codigo\n" +
                "where c.codigo = "+codigoCliente;

        ResultSet consulta = criarConsulta(sql, con);
        if (consulta.next()) {
            pessoa.setNome(consulta.getString("nome"));
            pessoa.setCfp(consulta.getString("cfp"));
            pessoa.setCodigo(consulta.getInt("codigo"));

            return pessoa;
        }
        return pessoa;
    }


    public int contarPessoas() throws Exception {
        String sql = "SELECT COUNT(*) AS total FROM pessoa";

        try (PreparedStatement stm = con.prepareStatement(sql);
             ResultSet rs = stm.executeQuery()) {

            if (rs.next()) {
                return rs.getInt("total");
            }
        }
        return 0;
    }

    public List<PessoaVO> consultarPorPassaporte(String passaporte, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT " + getColunas("") + " FROM Pessoa WHERE upper( passaporte) = upper('" + passaporte + "')";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }
}

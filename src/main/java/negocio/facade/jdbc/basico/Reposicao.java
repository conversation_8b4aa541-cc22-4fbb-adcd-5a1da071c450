/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.basico;

import br.com.pacto.priv.utils.Tricks;
import br.com.pactosolucoes.agendatotal.json.AgendaTotalJSON;
import br.com.pactosolucoes.agendatotal.json.AgendadoJSON;
import br.com.pactosolucoes.ce.comuns.enumerador.DiaSemana;
import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import br.com.pactosolucoes.enumeradores.TipoAgendaEnum;
import br.com.pactosolucoes.enumeradores.TipoOperacaoCreditoTreinoEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.SuperControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.PresencaVO;
import negocio.comuns.basico.ReposicaoVO;
import negocio.comuns.contrato.*;
import negocio.comuns.conviteaulaexperimental.ConviteAulaExperimentalVO;
import negocio.comuns.crm.AgendaVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.crm.HistoricoContatoVO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.TurmaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.arquitetura.ControleAcesso;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.Permissao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.ControleCreditoTreino;
import negocio.facade.jdbc.contrato.MatriculaAlunoHorarioTurma;
import negocio.facade.jdbc.crm.Agenda;
import negocio.facade.jdbc.crm.HistoricoContato;
import negocio.facade.jdbc.plano.HorarioTurma;
import negocio.facade.jdbc.plano.Modalidade;
import negocio.facade.jdbc.plano.Turma;
import negocio.interfaces.arquitetura.UsuarioInterfaceFacade;
import negocio.interfaces.basico.AulaDesmarcadaInterfaceFacade;
import negocio.interfaces.basico.ClienteInterfaceFacade;
import negocio.interfaces.basico.PresencaInterfaceFacade;
import negocio.interfaces.basico.ReposicaoInterfaceFacade;
import negocio.interfaces.contrato.ContratoInterfaceFacade;
import negocio.interfaces.contrato.ControleCreditoTreinoInterfaceFacade;
import negocio.interfaces.contrato.MatriculaAlunoHorarioTurmaInterfaceFacade;
import negocio.interfaces.plano.HorarioTurmaInterfaceFacade;
import negocio.interfaces.plano.TurmaInterfaceFacade;
import org.json.JSONObject;
import relatorio.negocio.jdbc.sad.SituacaoClienteSinteticoDW;
import servicos.integracao.sms.Message;
import servicos.integracao.sms.SmsController;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 *
 * <AUTHOR>
 */
public class Reposicao extends SuperEntidade implements ReposicaoInterfaceFacade {

    private SituacaoClienteSinteticoDW situacaoClienteSinteticoDW;
    private ControleAcesso controleAcesso;
    private Permissao permissao;
    private PresencaInterfaceFacade presenca;
    private AulaDesmarcadaInterfaceFacade aulaDesmarcada;
    private MatriculaAlunoHorarioTurmaInterfaceFacade matriculaAlunoHorarioTurma;

    public AulaDesmarcadaInterfaceFacade getAulaDesmarcada() throws Exception{
        if(aulaDesmarcada == null){
            aulaDesmarcada = new AulaDesmarcada(getCon());
        }
        return aulaDesmarcada;
    }

    public PresencaInterfaceFacade getPresenca() throws Exception{
        if(presenca == null){
            presenca = new Presenca(getCon());
        }
        return presenca;
    }

    public MatriculaAlunoHorarioTurmaInterfaceFacade getMatriculaAlunoHorarioTurma() throws Exception{
        if(matriculaAlunoHorarioTurma == null){
            matriculaAlunoHorarioTurma = new MatriculaAlunoHorarioTurma(getCon());
        }
        return matriculaAlunoHorarioTurma;
    }
    
    public Permissao getPermissao() throws Exception{
        if(permissao == null){
            permissao = new Permissao(getCon());
        }
        return permissao;
    }
    
    public SituacaoClienteSinteticoDW getSituacaoClienteSinteticoDW() throws Exception {
        if(situacaoClienteSinteticoDW == null){
            situacaoClienteSinteticoDW = new SituacaoClienteSinteticoDW(getCon());
        }
        return situacaoClienteSinteticoDW;
    }

    public ControleAcesso getControleAcesso() throws Exception {
        if(controleAcesso == null){
            controleAcesso = new ControleAcesso(getCon());
        }
        return controleAcesso;
    }
    
    
    
    public Reposicao() throws Exception {
    }

    public Reposicao(Connection con) throws Exception {

        super(con);

    }

    /**
     * Método inclui uma reposição esperando que esteja dentro de um bloco transacional, omitindo os métodos de 'commit' e 'rollback'
     * @param obj
     * @throws Exception 
     */
    @Override
    public void incluir(ReposicaoVO obj, ReposicaoVO reposicaoOrigem) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj, reposicaoOrigem);
            con.commit();

        } catch (Exception e) {
            con.rollback();
            throw e;
        }finally{
            con.setAutoCommit(true);
        }

    }

    public void incluirSemCommit(ReposicaoVO obj, ReposicaoVO reposicaoOrigem) throws Exception{
        incluirSemCommit(obj, reposicaoOrigem, false);
    }

    private Boolean teveAlteracaoContrato (Integer contrato) {
        try {
            Integer contar = contar("select count(codigo) from contratooperacao c where tipooperacao = 'AC' and contrato = " + contrato, con);
            return contar > 0;
        } catch (Exception ex) {
            ex.printStackTrace();
            return false;
        }
    }

    public void incluirSemCommit(ReposicaoVO obj, ReposicaoVO reposicaoOrigem, boolean diaria) throws Exception{
        String sql = "INSERT INTO reposicao(dataReposicao, dataPresenca, "
                + "dataLancamento, horarioTurma, turmaOrigem, turmaDestino, "
                + "cliente, contrato, horarioTurmaOrigem, dataOrigem, usuario, origemSistema,marcacaoaula, "
                + "spiviSeatID, spiviEventID) "
                + "VALUES (?,?,?,?,?,?,?,?,?,?,?, ?, ?, ?, ?)";

        try (PreparedStatement ps = con.prepareStatement(sql)) {

            if (!obj.isAulaExperimental()) {
                validarPermissaoIncluir(obj);
                obj.validarDados(diaria, obj.getContrato() == null ? false : teveAlteracaoContrato(obj.getContrato().getCodigo()));
            }

            validarRegras(obj, reposicaoOrigem == null || UteisValidacao.emptyNumber(reposicaoOrigem.getCodigo()) ? 0 : reposicaoOrigem.getCodigo(), diaria);
            int i = 1;
            ps.setDate(i++, Uteis.getDataJDBC(obj.getDataReposicao()));
            ps.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataPresenca()));
            ps.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataLancamento()));
            ps.setInt(i++, obj.getHorarioTurma().getCodigo());
            ps.setInt(i++, obj.getTurmaOrigem().getCodigo());
            ps.setInt(i++, obj.getTurmaDestino().getCodigo());
            if (UtilReflection.objetoMaiorQueZero(obj, "getCliente().getCodigo()")) {
                ps.setInt(i++, obj.getCliente().getCodigo());
            } else {
                ps.setNull(i++, Types.NULL);
            }
            if (UtilReflection.objetoMaiorQueZero(obj, "getContrato().getCodigo()")) {
                ps.setInt(i++, obj.getContrato().getCodigo());
            } else {
                ps.setNull(i++, Types.NULL);
            }
            ps.setInt(i++, obj.getHorarioTurmaOrigem().getCodigo());
            ps.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataOrigem()));
            resolveFKNull(ps, i++, obj.getUsuario().getCodigo());
            ps.setInt(i++, obj.getOrigemSistemaEnum().getCodigo());
            ps.setBoolean(i++, obj.isMarcacaoAula());

            Uteis.setInt(ps, obj.getSpiviSeatID(), i++);
            Uteis.setInt(ps, obj.getSpiviEventID(), i++);
            if(obj.getSpiviClientID() != null){
                getFacade().getPessoa().alterarSpiviClientID(obj.getCliente().getPessoa().getCodigo(), obj.getSpiviClientID());
            }

            ps.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);

        if (!obj.isAulaExperimental()) {
            incluirLogMarcacaoReposicao(obj);
        }

        if (obj.getContrato() != null && obj.getContrato().isVendaCreditoTreino()){
            ControleCreditoTreino controleCreditoTreino = new ControleCreditoTreino(this.con);
            ControleCreditoTreinoVO controleCreditoTreinoVO = new ControleCreditoTreinoVO();
            if(obj.isMarcacaoAula()){
                controleCreditoTreinoVO.setTipoOperacaoCreditoTreinoEnum(TipoOperacaoCreditoTreinoEnum.MARCOU_AULA);
            }else{
                controleCreditoTreinoVO.setTipoOperacaoCreditoTreinoEnum(TipoOperacaoCreditoTreinoEnum.REPOSICAO);
            }
            controleCreditoTreinoVO.setQuantidade(0);
            controleCreditoTreinoVO.setContratoVO(obj.getContrato());
            controleCreditoTreinoVO.setReposicaoVO(obj);
            controleCreditoTreinoVO.setUsuarioVO(obj.getUsuario());
            controleCreditoTreino.incluirSemCommit(controleCreditoTreinoVO, null, getSituacaoClienteSinteticoDW(), null);
            controleCreditoTreino = null;
        }

        if(obj.isAulaExperimental() || diaria ){
            Agenda agendaDao = new Agenda(this.con);
            TipoAgendaEnum tipoAgendaEnum = diaria ? TipoAgendaEnum.AULA_DIARIA : TipoAgendaEnum.AULA_EXPERIMENTAL;
            agendaDao.gravarAgendamentoAulaExperimental(obj, validarAlunoGynPass(obj.getCliente().getPessoa().getCodigo(), obj.getDataReposicao()), tipoAgendaEnum);
        }
    }

    public boolean validarAlunoGynPass(int codigoPessoa, Date data) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT EXISTS(SELECT codigo \n");
        sql.append("FROM periodoacessocliente \n");
        sql.append("WHERE COALESCE(tokengympass, '') <> '' \n");
        sql.append("AND pessoa = ? \n");
        sql.append("AND datainicioacesso::DATE = ? \n");
        sql.append("AND datafinalacesso::DATE = ?)");
        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            int i = 0;
            pst.setInt(++i, codigoPessoa);
            pst.setDate(++i, Uteis.getDataJDBC(data));
            pst.setDate(++i, Uteis.getDataJDBC(data));
            try (ResultSet rs = pst.executeQuery()) {
                if (rs.next()) {
                    return rs.getBoolean(1);
                }
                return false;
            }
        }
    }

    public void validarPermissaoExcluir(final ReposicaoVO obj) throws Exception {
        if(obj.getUsuario().getAdministrador()){
            return;
        }
        Iterator i = obj.getUsuario().getUsuarioPerfilAcessoVOs().iterator();
        while (i.hasNext()) {
            UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
            usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getPermissao().consultarPermissaos(
                    usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            if (obj.getContrato().getEmpresa().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo())) {
                getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                        obj.getUsuario(), "ExcluirReposicaoEmTurmas_Autorizar", "3.22 - Excluir Reposição em Turmas - Autorizar");
            }
        }
    }

    public void validarPermissaoIncluir(final ReposicaoVO obj) throws Exception {
        Iterator i = obj.getUsuario().getUsuarioPerfilAcessoVOs().iterator();
        while (i.hasNext()) {
            UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
            usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getPermissao().consultarPermissaos(
                    usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            if (obj.getContrato().getEmpresa().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo())) {
                getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                        obj.getUsuario(), "IncluirReposicaoEmTurmas_Autorizar", "3.21 - Incluir Reposição em Turmas - Autorizar");
            }
        }
    }

    @Override
    public void excluir(ReposicaoVO obj) throws Exception {
        try {
            validarPermissaoExcluir(obj);
            excluirSemValidarPermissao(obj);
        } catch (Exception e) {
            throw e;
        }

    }
    @Override
    public void excluirSemValidarPermissao(ReposicaoVO obj) throws Exception {
        excluirSemValidarPermissao(obj, null);
    }

    public void excluirSemValidarPermissao(ReposicaoVO obj, String parcela) throws Exception {
        // excluir a reposição do controle de credito de treino
        ControleCreditoTreinoInterfaceFacade controleDao = new ControleCreditoTreino(con);
        controleDao.ajustarOperacaoExcluirReposicao(obj, parcela);
        excluirEntidadesRelacionadas(obj);

        if (!UteisValidacao.emptyString(parcela)) {
            incluirLogExclusaoReposicao(obj, parcela);
        }

        // excluir a reposição
        String sqldelete = "delete from reposicao where codigo = " + obj.getCodigo();
        executarConsulta(sqldelete ,con);
        if(obj.isAulaExperimental()){
            return;
        }
        try (PreparedStatement stm = con.prepareStatement("UPDATE auladesmarcada SET datareposicao = null, reposicao = null WHERE datareposicao = ?"
                + " and contrato = ? and horarioturma = ? and reposicao = ? ")) {
            stm.setDate(1, Uteis.getDataJDBC(obj.getDataReposicao()));
            stm.setInt(2, obj.getContrato().getCodigo());
            stm.setInt(3, obj.getHorarioTurmaOrigem().getCodigo());
            stm.setInt(4, obj.getCodigo());
            stm.execute();
        }
    }

    public List<ReposicaoVO>consultarAulaMarcadaComCreditoExtra(Integer codigoContrato, Date dataBase, int nivelMontarDados)throws Exception{
        StringBuilder sql = new StringBuilder();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        sql.append("select to_date(to_char(re.dataReposicao, 'YYYY-MM-DD') ||' ' || ht.horaFinal,  'YYYY-MM-DD HH24:MI:SS') as dataHoraAula, re.* \n");
        sql.append("from reposicao re \n");
        sql.append("left join aulaDesmarcada ad on ad.reposicao = re.codigo \n");
        sql.append("inner join horarioTurma ht on ht.codigo = re.horarioTurma \n");
        sql.append("where ad.reposicao is null and re.contrato = ").append(codigoContrato).append(" \n");
        sql.append("and to_date(to_char(re.dataReposicao, 'YYYY-MM-DD') ||' ' || ht.horaFinal,  'YYYY-MM-DD HH24:MI:SS') >= '").append(sdf.format(dataBase)).append("' \n");
        try (Statement st = con.createStatement()) {
            try (ResultSet rs = st.executeQuery(sql.toString())) {
                return montarDadosConsulta(rs, nivelMontarDados, con);
            }
        }
    }


    private void validarRegras(ReposicaoVO obj, Integer codigoReposicaoOrigem) throws ConsistirException {
        validarRegras(obj, codigoReposicaoOrigem, false);
    }
    private void validarRegras(ReposicaoVO obj, Integer codigoReposicaoOrigem, boolean diaria) throws ConsistirException {
        String sqlExiteReposicaoIgual = String.format("select codigo from reposicao "
                + "where cliente = %s "
                + "and contrato = %s "
                + "and horarioturma = %s "
                + "and cast(datareposicao as date) = '%s' "
                + "and codigo <> %s",
                new Object[]{
                    obj.getCliente().getCodigo(), (obj.getContrato() == null ? null : obj.getContrato().getCodigo()),
                    obj.getHorarioTurma().getCodigo(),
                    Uteis.getDataFormatoBD(obj.getDataReposicao()),
                    codigoReposicaoOrigem
                });

        String sqlJaFoiRemarcada = String.format("select codigo from reposicao "
                + "where cliente = %s and contrato = %s and horarioturmaorigem = %s and cast(dataorigem as date) = '%s' and codigo <> %s",
                new Object[]{
                    obj.getCliente().getCodigo(),
                        (obj.getContrato() == null ? null : obj.getContrato().getCodigo()),
                    obj.getHorarioTurmaOrigem().getCodigo(),
                    Uteis.getDataFormatoBD(obj.getDataOrigem()),
                    codigoReposicaoOrigem
                });

        String sqlJaTevePresenca = String.format("select codigo from reposicao "
                + "where cliente = %s and contrato = %s and horarioturma = %s and cast(datareposicao as date) = '%s' "
                + "and datapresenca is not null and codigo <> %s",
                new Object[]{
                    obj.getCliente().getCodigo(),
                        (obj.getContrato() == null ? null : obj.getContrato().getCodigo()),
                    obj.getHorarioTurma().getCodigo(),
                    Uteis.getDataFormatoBD(obj.getDataReposicao()),
                    codigoReposicaoOrigem
                });


        try {
            if ((obj.getOrigemSistemaEnum().equals(OrigemSistemaEnum.APP_TREINO) || obj.getTurmaDestino().isBloquearReposicaoAcimaLimite()) &&  obj.getHorarioTurma().getNrAlunoMatriculado() >= obj.getHorarioTurma().getNrMaximoAluno()) {
                throw new ConsistirException("Número de vagas excedido! Consulte as turmas novamente e verifique as vagas disponíveis.");
            } else if (SuperFacadeJDBC.existe(sqlExiteReposicaoIgual, con) && codigoReposicaoOrigem > 0) {
                throw new ConsistirException("Já existe outra reposição com os dados informados.");
            } else if (SuperFacadeJDBC.existe(sqlJaFoiRemarcada, con) && codigoReposicaoOrigem > 0) {
                throw new ConsistirException("O Aluno já remarcou esta aula.");
            } else if (SuperFacadeJDBC.existe(sqlJaTevePresenca, con)) {
                throw new ConsistirException("O Aluno já esteve presente nesta aula.");
            }
            if(obj.isAulaExperimental() || diaria || (obj.getContrato() != null && obj.getContrato().isVendaCreditoTreino())){
                return;
            }
            //validar Quantidades de Reposições
            MatriculaAlunoHorarioTurmaVO  matriculaAlunoHorarioTurmaVO = 
                    getMatriculaAlunoHorarioTurma().
                    consultarMatriculaAtivaPorHorarioTurma(obj.getContrato().getCodigo(), obj.getHorarioTurmaOrigem().getCodigo(), Calendario.hoje(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            int contAulas = 0;
            int contAulasPossiveis = 0;
            int presencas = 0;
            int reposicoesPresentes = 0;
            int reposicoesFuturas = 0;
            int aulasDesmarcadas = 0;
            if (!UteisValidacao.emptyNumber(matriculaAlunoHorarioTurmaVO.getCodigo())) {
                Date dInicio = matriculaAlunoHorarioTurmaVO.getDataInicio();
                Date dFim = matriculaAlunoHorarioTurmaVO.getDataFim();

                contAulas = contAulas + Calendario.contarDiasDaSemanaEntre(dInicio, dFim, DiaSemana.getDiaSemana(obj.getHorarioTurmaOrigem().getDiaSemana()).getNumeral());

                contAulasPossiveis = contAulasPossiveis + Calendario.contarDiasDaSemanaEntre(dInicio, Calendario.hoje(),
                        DiaSemana.getDiaSemana(obj.getHorarioTurmaOrigem().getDiaSemana()).getNumeral());

                presencas = presencas + getPresenca().contarPresencasAluno(matriculaAlunoHorarioTurmaVO.getCodigo(), null, null);

                reposicoesPresentes += contarReposicoes(
                        obj.getContrato().getCodigo().toString(),
                        matriculaAlunoHorarioTurmaVO.getHorarioTurma().getTurma().toString(), matriculaAlunoHorarioTurmaVO.getHorarioTurma().getCodigo().toString(), false, null, null, true, true);

                reposicoesFuturas += contarReposicoes(
                        obj.getContrato().getCodigo().toString(),
                        matriculaAlunoHorarioTurmaVO.getHorarioTurma().getTurma().toString(), matriculaAlunoHorarioTurmaVO.getHorarioTurma().getCodigo().toString(), false, Calendario.hoje(), null, false, true);

                aulasDesmarcadas += getAulaDesmarcada().contarAulasDesmarcadas(obj.getContrato().getCodigo(), obj.getContrato().getEmpresa().getCodigo(), matriculaAlunoHorarioTurmaVO.getHorarioTurma().getTurma(),matriculaAlunoHorarioTurmaVO.getHorarioTurma().getCodigo(), null, null, null);


                int faltasPossiveis = contAulasPossiveis - presencas - reposicoesPresentes;
                int totalMarcacoes = faltasPossiveis + presencas + reposicoesPresentes + reposicoesFuturas - aulasDesmarcadas;
                if (totalMarcacoes >= contAulas) {
                    throw new ConsistirException(String.format("Não é possível realizar a Reposição. "
                            + "O aluno já possui somatório de Faltas, Presenças, Remarcações Futuras = %s. "
                            + "Que já é maior ou igual ao número de aulas previstas nesta turma = %s. ",
                            totalMarcacoes,
                            contAulas));
                }
            }
        } catch (SQLException ex) {
            throw new ConsistirException(ex.getMessage());
        } catch (Exception ex) {
            throw new ConsistirException(ex.getMessage());
        }

    }

    public static ReposicaoVO montarDadosBasico(ResultSet rs)throws Exception {
        ReposicaoVO obj = new ReposicaoVO();
        obj.setCodigo(rs.getInt("codigo"));
        obj.setDataPresenca(rs.getTimestamp("datapresenca"));
        obj.setDataOrigem(rs.getDate("dataorigem"));
        obj.setDataReposicao(rs.getDate("datareposicao"));
        obj.setDataLancamento(rs.getTimestamp("dataLancamento"));
        obj.setOrigemSistemaEnum(OrigemSistemaEnum.getOrigemSistema(rs.getInt("origemSistema")));
        ContratoVO contratoVO = new ContratoVO();
        contratoVO.setCodigo(rs.getInt("contrato"));
        obj.setContrato(contratoVO);
        ClienteVO clienteVO = new ClienteVO();
        clienteVO.setCodigo(rs.getInt("cliente"));
        obj.setCliente(clienteVO);
        UsuarioVO usuarioVO = new UsuarioVO();
        usuarioVO.setCodigo(rs.getInt("usuario"));
        obj.setUsuario(usuarioVO);
        HorarioTurmaVO horarioTurmaVO = new HorarioTurmaVO();
        horarioTurmaVO.setCodigo(rs.getInt("horarioTurma"));
        obj.setHorarioTurma(horarioTurmaVO);
        TurmaVO turmaOrigem = new TurmaVO();
        turmaOrigem.setCodigo(rs.getInt("turmaOrigem"));
        obj.setTurmaOrigem(turmaOrigem);
        TurmaVO turmaDestino = new TurmaVO();
        turmaDestino.setCodigo(rs.getInt("turmaDestino"));
        obj.setTurmaDestino(turmaDestino);
        HorarioTurmaVO horarioTurmaOrigem = new HorarioTurmaVO();
        horarioTurmaOrigem.setCodigo(rs.getInt("horarioTurmaOrigem"));
        obj.setHorarioTurmaOrigem(horarioTurmaOrigem);
        obj.setConviteAulaExperimentalVO(new ConviteAulaExperimentalVO());
        obj.getConviteAulaExperimentalVO().setCodigo(rs.getInt("conviteAulaExperimental"));
        obj.setMarcacaoAula(rs.getBoolean("marcacaoAula"));
        return obj;
    }

    public static ReposicaoVO montarDados(ResultSet ds, int nivelMontarDados, Connection con) throws Exception {
        ReposicaoVO obj = montarDadosBasico(ds);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS) {
            int codigoContrato = obj.getContrato().getCodigo();
            if (codigoContrato > 0) {
                Contrato contrato = new Contrato(con);
                obj.setContrato(contrato.consultarPorChavePrimaria(obj.getContrato().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                contrato = null;
            }
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONSULTA_WS) {
            HorarioTurma horarioTurmaDao = new HorarioTurma(con);
            obj.setHorarioTurma(horarioTurmaDao.consultarPorChavePrimaria(ds.getInt("horarioturma"), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            return obj;
        }
        if(nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS){
            HorarioTurma horarioTurmaDao = new HorarioTurma(con);
            obj.setHorarioTurma(horarioTurmaDao.consultarPorChavePrimaria(ds.getInt("horarioturma"), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            TurmaInterfaceFacade turmaDao = new Turma(con);
            obj.setTurmaDestino(turmaDao.consultarPorChavePrimaria(ds.getInt("turmadestino"), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_COM_CLIENTE) {
            int codigoCliente = ds.getInt("cliente");
            if (codigoCliente > 0){
                obj.setCliente(new Cliente(con).consultarPorChavePrimaria(ds.getInt("cliente"), Uteis.NIVELMONTARDADOS_MINIMOS));
            }
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TODOS) {
            HorarioTurmaInterfaceFacade horarioDao = new HorarioTurma(con);
            TurmaInterfaceFacade turmaDao = new Turma(con);
            ContratoInterfaceFacade contratoDao = new Contrato(con);
            ClienteInterfaceFacade clienteDao = new Cliente(con);
            UsuarioInterfaceFacade usuarioDao = new Usuario(con);
            
            obj.setHorarioTurma(horarioDao.consultarPorChavePrimaria(ds.getInt("horarioturma"), Uteis.NIVELMONTARDADOS_TODOS));
            obj.setTurmaDestino(turmaDao.consultarPorChavePrimaria(ds.getInt("turmadestino"), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            obj.setTurmaOrigem(turmaDao.consultarPorChavePrimaria(ds.getInt("turmaorigem"), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            int codigoCliente = ds.getInt("cliente");
            int codigoContrato = ds.getInt("contrato");
            if (codigoCliente > 0){
                obj.setCliente(clienteDao.consultarPorChavePrimaria(ds.getInt("cliente"), Uteis.NIVELMONTARDADOS_MINIMOS));
            }
            if (codigoContrato > 0){
                obj.setContrato(contratoDao.consultarPorChavePrimaria(ds.getInt("contrato"), Uteis.NIVELMONTARDADOS_MINIMOS));
            }
            if (ds.getInt("horarioTurmaOrigem") != 0) {
                obj.setHorarioTurmaOrigem(horarioDao.consultarPorChavePrimaria(ds.getInt("horarioTurmaOrigem"), Uteis.NIVELMONTARDADOS_TODOS));
            }

            if (ds.getInt("usuario") != 0) {
                obj.setUsuario(usuarioDao.consultarPorChavePrimaria(ds.getInt("usuario"), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            }
            return obj;
        }

        return obj;
    }

    public ReposicaoVO consultar(HorarioTurmaVO horarioTurmaOrigem, Date dataOrigem, Integer codigoContrato, int nivelMontarDados)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select *  \n");
        sql.append("from reposicao \n");
        sql.append("where contrato = ? and horarioTurmaOrigem = ? \n");
        sql.append("and dataOrigem = ? ");
        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            pst.setInt(1, codigoContrato);
            pst.setInt(2, horarioTurmaOrigem.getCodigo());
            pst.setDate(3, Uteis.getDataJDBC(Calendario.getDataComHoraZerada(dataOrigem)));
            try (ResultSet rs = pst.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs, nivelMontarDados, con);
                }
            }
        }
        return null;

    }

    public List<ReposicaoVO> consultar(Date dataReposicao, Integer codigoContrato, Integer codigoCliente, int nivelMontarDados)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select *  \n");
        sql.append("from reposicao \n");
        sql.append("where \n");
        if(!UteisValidacao.emptyNumber(codigoContrato)){
            sql.append(" contrato = ?  and ");
        }
        if(!UteisValidacao.emptyNumber(codigoCliente)){
            sql.append(" cliente = ?  and ");
        }
        sql.append(" dataReposicao = ? ");
        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            int i = 1;
            if (!UteisValidacao.emptyNumber(codigoContrato)) {
                pst.setInt(i++, codigoContrato);
            }
            if (!UteisValidacao.emptyNumber(codigoCliente)) {
                pst.setInt(i++, codigoCliente);
            }
            pst.setDate(i++, Uteis.getDataJDBC(Calendario.getDataComHoraZerada(dataReposicao)));
            try (ResultSet rs = pst.executeQuery()) {
                return montarDadosConsulta(rs, nivelMontarDados, con);
            }
        }
    }

    public List<ReposicaoVO> consultarReposicoesApartirDeUmaDataBase(Date dataBase, Integer codigoContrato, int nivelMontarDados)throws Exception{
        StringBuilder sql = new StringBuilder();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        sql.append("select to_date(to_char(r.dataReposicao, 'YYYY-MM-DD') ||' ' || ht.horaFinal,  'YYYY-MM-DD HH24:MI:SS') as dataHoraAula, r.* \n");
        sql.append("from reposicao r \n");
        sql.append("inner join horarioTurma ht on ht.codigo = r.horarioTurma \n");
        sql.append("where r.contrato = ").append(codigoContrato).append(" \n");
        sql.append("and to_date(to_char(r.dataReposicao, 'YYYY-MM-DD') ||' ' || ht.horaFinal,  'YYYY-MM-DD HH24:MI:SS') >= '").append(sdf.format(dataBase)).append("' \n");
        List<ReposicaoVO> lista;
        try (Statement st = con.createStatement()) {
            try (ResultSet rs = st.executeQuery(sql.toString())) {
                lista = new ArrayList<ReposicaoVO>();
                while (rs.next()) {
                    ReposicaoVO obj = montarDados(rs, nivelMontarDados, con);
                    obj.setDataHoraAula(rs.getTimestamp("dataHoraAula"));
                    lista.add(obj);
                }
            }
        }
        return lista;

    }

    public List<ReposicaoVO> consultarReposicaoContratoCreditoTreino(Date dataBase)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select rep.*, \n");
        sql.append("ht.codigo as codigoHorarioTurma, ht.diaSemana, ht.horaInicial as horarioTurmaInicial, ht.horaFinal as horarioTurmaFinal,  \n");
        sql.append("hto.codigo as codigoHorarioTurmaOrigem, hto.diaSemana as diasSemanaOrigem, hto.horaInicial as horarioTurmaInicialOrigem, hto.horaFinal as horarioTurmaFinalOrigem,  \n");
        sql.append("turmaOrigem.codigo as codigoTurmaOrigem, modOrigem.nome as nomeModalidadeOrigem, \n");
        sql.append("turmaDestino.codigo as codigoTurmaDestino, modDestino.nome as nomeModalidadeDestino \n");
        sql.append("from reposicao rep \n");
        sql.append("inner join contrato c on c.codigo = rep.contrato \n");
        sql.append("inner join SituacaoClienteSinteticoDW sintetico on sintetico.codigoContrato = c.codigo \n");
        sql.append("inner join horarioTurma ht on ht.codigo = rep.horarioTurma \n");
        sql.append("inner join turma turmaOrigem on turmaOrigem.codigo = rep.turmaOrigem \n");
        sql.append("inner join modalidade modOrigem on modOrigem.codigo = turmaOrigem.modalidade \n");
        sql.append("left join horarioTurma hto on hto.codigo = rep.horarioTurmaOrigem \n");
        sql.append("inner join turma turmaDestino on turmaDestino.codigo = rep.turmaDestino \n");
        sql.append("inner join modalidade modDestino on modDestino.codigo = turmaDestino.modalidade \n");
        sql.append("where c.vendaCreditoTreino = true \n");
        sql.append("and sintetico.situacaoContrato in ('NO','AV','VE') \n"); // como esse processo roda baseado no dia anterior, caso o aluno tenha uma ultima aula no ultimo dia do contrato, a situação dele vai estar VE quando processar o dia da ultima aula 
        sql.append(" and '").append(Uteis.getDataJDBC(Calendario.getDataComHoraZerada(dataBase))).append("' between  c.vigenciaDe and  c.vigenciaAteAjustada \n");
        sql.append("and rep.dataReposicao = '").append(Uteis.getDataJDBC(dataBase)).append("'");
        sql.append(" \n");
        List<ReposicaoVO> lista;
        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = pst.executeQuery()) {
                lista = new ArrayList<ReposicaoVO>();
                while (rs.next()) {
                    ReposicaoVO reposicaoVO = montarDadosBasico(rs);
                    reposicaoVO.getHorarioTurma().setDiaSemana(rs.getString("diaSemana"));
                    reposicaoVO.getHorarioTurma().setHoraInicial(rs.getString("horarioTurmaInicial"));
                    reposicaoVO.getHorarioTurma().setHoraFinal(rs.getString("horarioTurmaFinal"));
                    reposicaoVO.setTurmaOrigem(new TurmaVO());
                    reposicaoVO.getTurmaOrigem().setCodigo(rs.getInt("codigoTurmaOrigem"));
                    reposicaoVO.getTurmaOrigem().setModalidade(new ModalidadeVO());
                    reposicaoVO.getTurmaOrigem().getModalidade().setNome(rs.getString("nomeModalidadeOrigem"));

                    reposicaoVO.getHorarioTurmaOrigem().setDiaSemana(rs.getString("diasSemanaOrigem"));
                    reposicaoVO.getHorarioTurmaOrigem().setHoraInicial(rs.getString("horarioTurmaInicialOrigem"));
                    reposicaoVO.getHorarioTurmaOrigem().setHoraFinal(rs.getString("horarioTurmaFinalOrigem"));
                    reposicaoVO.setTurmaDestino(new TurmaVO());
                    reposicaoVO.getTurmaDestino().setCodigo(rs.getInt("codigoTurmaDestino"));
                    reposicaoVO.getTurmaDestino().setModalidade(new ModalidadeVO());
                    reposicaoVO.getTurmaDestino().getModalidade().setNome(rs.getString("nomeModalidadeDestino"));


                    lista.add(reposicaoVO);
                }
            }
        }
        return lista;
    }

    public static List<ReposicaoVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<ReposicaoVO> vetResultado = new ArrayList<ReposicaoVO>();
        while (tabelaResultado.next()) {
            ReposicaoVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    @Override
    public ReposicaoVO consultarPorChavePrimaria(final int codigo, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM reposicao WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigo);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException(String.format("Dados Não Encontrados ( Reposição %s)",
                            codigo));
                }
                return (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public ReposicaoVO consultarPorCodigo(final int codigo, int nivelMontarDados) throws Exception{
        String sql = "SELECT * FROM reposicao WHERE codigo = ?";
        try (PreparedStatement pst = con.prepareStatement(sql)) {
            pst.setInt(1, codigo);
            try (ResultSet rs = pst.executeQuery()) {
                if (rs.next()) {
                    return (montarDados(rs, nivelMontarDados, con));
                }
            }
        }
        return null;
    }

    @Override
    public List<ReposicaoVO> consulta(final String sql) throws Exception {
        return montarDadosConsulta(SuperFacadeJDBC.criarConsulta(sql, con), Uteis.NIVELMONTARDADOS_TODOS, con);
    }

    @Override
    public void atualizarPresenca(int codigoReposicao, final Date dataPresenca) throws Exception {
        try (PreparedStatement ps = con.prepareStatement("update reposicao set datapresenca = ? where codigo = ?")) {
            if (dataPresenca == null) {
                ps.setNull(1, Types.NULL);
            } else {
                ps.setDate(1, Uteis.getDataJDBC(dataPresenca));
            }
            ps.setInt(2, codigoReposicao);
            ps.execute();
        }
    }

    @Override
    public PresencaVO preencherPresencaEmFuncaoReposicao(int codigoPessoa, int codigoHorarioTurma, Date dataInicial, Date dataFinal) throws Exception {
        StringBuilder sb = new StringBuilder();
        sb.append("select * from reposicao ").
                append("inner join cliente on reposicao.cliente = cliente.codigo ").
                append("where cliente.pessoa = %s and horarioturma = %s ").
                append("AND ((cast(datapresenca as date) >= '%s' AND cast(datapresenca as date) <= '%s')").
                append("or (cast(datareposicao as date) >= '%s' AND cast(datareposicao as date) <= '%s')) ");
        List<ReposicaoVO> lista = consulta(String.format(sb.toString(), codigoPessoa,
                codigoHorarioTurma,
                Uteis.getDataFormatoBD(dataInicial),
                Uteis.getDataFormatoBD(dataFinal),
                Uteis.getDataFormatoBD(dataInicial),
                Uteis.getDataFormatoBD(dataFinal)));

        ReposicaoVO repo = lista.isEmpty() ? null : lista.get(0);

        if (repo == null) {
            return new PresencaVO();
        } else {
            PresencaVO presenca = new PresencaVO();
            presenca.setDataChamada(repo.getDataReposicao());
            presenca.setDataPresenca(repo.getDataPresenca());
            presenca.setRepo(repo);
            return presenca;
        }

    }

    @Override
    public int contarReposicoes(String contrato, String turma, String horarioturma,
            boolean pesquisarPorHorario, Date periodoInicial, Date periodoFinal, boolean presencas, boolean pesqHorarioOrigem) throws Exception {
        String condicaoPresenca = presencas ? " and r.datapresenca is not null " : "";
        if (periodoInicial != null && periodoFinal != null && presencas) {
            condicaoPresenca += " and r.datapresenca >= '" + Uteis.getDataJDBC(periodoInicial) + "' AND r.datapresenca <= '" + Uteis.getDataJDBC(periodoFinal) + "' ";
        } else if (!presencas && periodoInicial != null && periodoFinal != null) {
            condicaoPresenca += " and r.datareposicao >= '" + Uteis.getDataJDBC(periodoInicial) + "' AND r.datareposicao <= '" + Uteis.getDataJDBC(periodoFinal) + "' ";
        } else if (periodoInicial != null && periodoFinal == null) {
            condicaoPresenca += " and r.datareposicao >= '" + Uteis.getDataJDBC(periodoInicial) + "' and r.dataorigem >= '" + Uteis.getDataJDBC(periodoInicial) + "' "; // com data de origem menor já é contabilidado na falta. Sem essa condição uma aula é contada duas vezes
        }
        if (horarioturma.isEmpty()) {
            return SuperFacadeJDBC.contar(String.format(sqlReposicoesRealizadas(true, false, false),
                    new Object[]{contrato, turma, turma, turma, condicaoPresenca}), con);
        } else {
            return SuperFacadeJDBC.contar(String.format(sqlReposicoesRealizadas(true, pesquisarPorHorario, pesqHorarioOrigem),
                    new Object[]{contrato, turma, turma, turma, horarioturma, condicaoPresenca}), con);
        }
    }

    @Override
    public List<ReposicaoVO> consultarReposicoes(String contrato, String turma, String horarioturma, boolean pesquisarPorHorario, Date periodoInicial, Date periodoFinal, boolean presencas) throws Exception {
        String condicaoPresenca = presencas ? " and r.datapresenca is not null " : "";
        if (periodoInicial != null && periodoFinal != null) {
            condicaoPresenca += " and r.datapresenca >= '" + Uteis.getDataJDBC(periodoInicial) + "' AND r.datapresenca <= '" + Uteis.getDataJDBC(periodoFinal) + "' ";
        }
        return montarDadosConsulta(SuperFacadeJDBC.criarConsulta(String.format(sqlReposicoesRealizadas(false, pesquisarPorHorario,false),
                new Object[]{contrato, turma, turma, turma, horarioturma, condicaoPresenca}), con), Uteis.NIVELMONTARDADOS_TODOS, con);
    }

    @Override
    public int contarReposicoesSolicitadas(String contratos, String turma, String horarioTurma, boolean pesquisarPorHorario, Date dataLancamentoInicial, Date dataLancamentoFinal) throws Exception {
        return SuperFacadeJDBC.contar(String.format(sqlReposicoesSolicitadas(true, pesquisarPorHorario),
                new Object[]{contratos, turma, Uteis.getDataJDBC(dataLancamentoInicial), Uteis.getDataJDBC(dataLancamentoFinal), horarioTurma}), con);
    }

    @Override
    public List<ReposicaoVO> consultarReposicoesSolicitadas(String contratos, String turma, String horarioTurma, boolean pesquisarPorHorario, Date dataLancamentoInicial, Date dataLancamentoFinal) throws Exception {
        return montarDadosConsulta(SuperFacadeJDBC.criarConsulta(String.format(sqlReposicoesSolicitadas(false, pesquisarPorHorario),
                new Object[]{contratos, turma, Uteis.getDataJDBC(dataLancamentoInicial), Uteis.getDataJDBC(dataLancamentoFinal), horarioTurma}), con), Uteis.NIVELMONTARDADOS_TODOS, con);
    }

    private String sqlReposicoesSolicitadas(boolean contar, boolean horarioTurma) {
        String horario = "";
        if (horarioTurma) {
            horario = " and horarioturma in (%s) ";
        }
        String retornoConsulta = contar ? "count(codigo)" : " *";
        return "select " + retornoConsulta + "from reposicao where contrato in (%s) "
                + "and turmaorigem in (%s) and dataLancamento >= '%s 00:00:00' and dataLancamento <= '%s 23:59:59' " + horario;
    }

    private String sqlReposicoesRealizadas(boolean contar, boolean horarioTurma, boolean  horarioTurmaOrigem) {
        String horario = "";
        String retornoConsulta = contar ? "count(r.codigo)" : " r.*";
        if (horarioTurma) {
            horario = " and r.horarioturma in (%s) ";
        } else if (horarioTurmaOrigem) {
            horario = " and r.horarioTurmaOrigem in (%s) ";
        }
        return "select " + retornoConsulta + "from reposicao r " +
                "left join auladesmarcada a on a.reposicao = r.codigo " +
                "where r.contrato in (%s) and (r.turmaorigem in (%s) or r.turmadestino in (%s) or (a.turmadestino is not null and a.turmadestino in( %s))) " + horario + " %s";
    }

    @Override
    public void nrAlunosReposicao(HorarioTurmaVO obj, final String periodo) throws Exception {

        try (ResultSet rsEntraram = criarConsulta(
                String.format(
                        "SELECT COUNT(r.codigo) AS qtde FROM reposicao r "
                                + "inner JOIN horarioturma ht ON ht.codigo = r.horarioturma "
                                + "WHERE r.horarioturma = %s AND "
                                + "datareposicao between %s ",
                        new Object[]{
                                obj.getCodigo(),
                                periodo
                        }), con)) {
            if (rsEntraram.next()) {
                obj.setNrAlunoEntraramPorReposicao(rsEntraram.getInt("qtde"));
            }
        }

        try (ResultSet rsSairam = criarConsulta(String.format(
                "SELECT COUNT(r.codigo) AS qtde FROM reposicao r "
                        + "inner JOIN horarioturma ht ON ht.codigo = r.horarioturma "
                        + "WHERE r.horarioturmaorigem = %s AND "
                        + "dataorigem between %s ",
                new Object[]{
                        obj.getCodigo(),
                        periodo
                }), con)) {
            if (rsSairam.next()) {
                obj.setNrAlunoSairamPorReposicao(rsSairam.getInt("qtde"));
            }
        }
    }

    @Override
    public int contarReposicoesDoAluno(final int cliente, final HorarioTurmaVO obj,
            final String periodo, boolean presentes) throws Exception {

        try (ResultSet rsEntraram = criarConsulta(
                String.format(
                        "SELECT COUNT(r.codigo) AS qtde FROM reposicao r "
                                + "inner JOIN horarioturma ht ON ht.codigo = r.horarioturma "
                                + "WHERE r.horarioturma = %s AND "
                                + "cliente = %s "
                                + (presentes ? " AND datapresenca between %s AND " : " AND ")
                                + "datareposicao between %s ",
                        new Object[]{
                                obj.getCodigo(),
                                cliente,
                                periodo,
                                periodo
                        }), con)) {
            if (rsEntraram.next()) {
                return rsEntraram.getInt("qtde");
            } else {
                return 0;
            }
        }
    }

    @Override
    public boolean existeReposicaoAlunoNaqueleDia(final int pessoa, final HorarioTurmaVO ht,
            final Date dia) throws Exception {

        return existe(String.format(
                "SELECT r.codigo FROM reposicao r "
                        + "inner JOIN horarioturma  ht  ON ht.codigo = r.horarioturma "
                        + "inner JOIN cliente       cli ON cli.codigo = r.cliente "
                        + "WHERE r.horarioturma = %s "
                        + "AND   cli.pessoa = %s "
                        + "AND   r.datareposicao = '%s' ",
                new Object[]{
                        ht.getCodigo(),
                        pessoa,
                        Uteis.getDataFormatoBD(dia)
                }), con);
    }

    @Override
    public boolean existeDesmarcacaoAlunoNaqueleDia(final int pessoa, final HorarioTurmaVO ht,
            final Date dia) throws Exception {

        return existe(String.format(
                "SELECT r.codigo FROM auladesmarcada r "
                + "inner JOIN horarioturma ht ON ht.codigo = r.horarioturma "
                + "inner JOIN cliente cli ON cli.codigo = r.cliente "
                + "inner join pessoa p on p.codigo = cli.pessoa "
                + "WHERE r.horarioturma = %s AND "
                + "cli.pessoa = %s and "
                + "dataorigem = '%s' ",
                new Object[]{
                    ht.getCodigo(),
                    pessoa,
                    Uteis.getDataFormatoBD(dia)
                }), con);
    }

    @Override
    public int contarDesmarcacoesDoAluno(final int cliente, final HorarioTurmaVO obj,
            final String periodo) throws Exception {
        try (ResultSet rsSairam = criarConsulta(String.format(
                "SELECT COUNT(ad.codigo) AS qtde FROM auladesmarcada ad "
                        + "inner JOIN horarioturma ht ON ht.codigo = ad.horarioturma "
                        + "WHERE ad.horarioturma = %s AND "
                        + "cliente = %s AND "
                        + "dataorigem between %s ",
                new Object[]{
                        obj.getCodigo(),
                        cliente,
                        periodo
                }), con)) {
            if (rsSairam.next()) {
                return rsSairam.getInt("qtde");
            } else {
                return 0;
            }
        }
    }

    public List<ReposicaoVO> consultarReposicoesPorHorarioTurmaOrigem(HorarioTurmaVO obj, final String periodo, int nivelMontarDados, Integer codContrato) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM reposicao r "
                + "inner JOIN horarioturma ht ON ht.codigo = r.horarioturma "
                + "WHERE r.horarioturmaorigem = %s "
                + "AND datareposicao between %s ");
        if (codContrato != null) {
            sql.append("AND contrato = ").append(codContrato);
        }
        return  montarDadosConsulta(criarConsulta(String.format(sql.toString(), obj.getCodigo(), periodo), con), nivelMontarDados, con);
    }

    @Override
    public List<ReposicaoVO> consultarReposicoesPorHorarioTurma(final Integer codigoHorarioTurma,
                                                                boolean alunosQueSairam, final String periodo, int nivelMontarDados, Integer codContrato) throws Exception {

        StringBuilder sql = new StringBuilder();
        if (alunosQueSairam) {
            sql.append("SELECT * FROM reposicao r "
                    + "inner JOIN horarioturma ht ON ht.codigo = r.horarioturma "
                    + "WHERE r.horarioturmaorigem = %s "
                    + "AND dataorigem between %s ");
            if (codContrato != null) {
                sql.append("AND contrato = ").append(codContrato);
            }
        } else {
            sql.append("SELECT * FROM reposicao r "
                    + "inner JOIN horarioturma ht ON ht.codigo = r.horarioturma "
                    + "WHERE r.horarioturma = %s "
                    + "AND datareposicao between %s ");
            if (codContrato != null) {
                sql.append("AND contrato = ").append(codContrato);
            }
        }
        return montarDadosConsulta(criarConsulta(String.format(sql.toString(),
                codigoHorarioTurma,
                periodo), con), nivelMontarDados, con);
    }

    public boolean existemReposicoesPorHorarioTurmaOrigem(HorarioTurmaVO obj, final String periodo, Integer codContrato) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM reposicao r "
                + "inner JOIN horarioturma ht ON ht.codigo = r.horarioturma "
                + "WHERE r.horarioturmaorigem = %s "
                + "AND datareposicao between %s ");
        if (codContrato != null) {
            sql.append("AND contrato = ").append(codContrato);
        }
        return existe(String.format(sql.toString(), obj.getCodigo(), periodo), con);
    }

    public boolean existemReposicoesParaHorarioTurma(HorarioTurmaVO obj, boolean reposicoesFuturas) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT codigo FROM reposicao r\n");
        sql.append("WHERE 1 = 1\n");
        sql.append("AND (horarioturma = %s or horarioturmaorigem = %s)\n");
        if (reposicoesFuturas) {
            sql.append("AND datareposicao > now() ");
        }
        return existe(String.format(sql.toString(), obj.getCodigo(), obj.getCodigo()), con);
    }

    public Integer existemReposicoesPorContratoDataReposicaoPegarHorarioTurmaOrigem(final String periodo, final Integer codContrato) throws Exception {
        try (ResultSet rs = criarConsulta(String.format(
                "SELECT horarioturmaorigem FROM reposicao r "
                        + "inner JOIN horarioturma ht ON ht.codigo = r.horarioturma "
                        + "WHERE datareposicao between %s "
                        + "AND contrato = %s "
                        + "order by datalancamento desc limit 1",
                new Object[]{
                        periodo,
                        codContrato
                }), con)) {
            if (rs.next()) {
                return rs.getInt("horarioturmaorigem");
            } else {
                return 0;
            }
        }
    }

    public ReposicaoVO consultarReposicao(int contrato, int horarioTurmaOrigem, int turmaOrigem, Date dataOrigem, int nivelMontarDados) throws Exception {
       String  sql = "select * from reposicao where contrato = ? and dataorigem = ? and horarioturmaorigem = ? and turmaorigem = ?";
        try (PreparedStatement pst = con.prepareStatement(sql)) {
            pst.setInt(1, contrato);
            pst.setDate(2, Uteis.getDataJDBC(dataOrigem));
            pst.setInt(3, horarioTurmaOrigem);
            pst.setInt(4, turmaOrigem);
            try (ResultSet rs = pst.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs, nivelMontarDados, con);
                }
            }
        }
        return null;
    }


    public void verificarReposicaoJaLancada(int contrato, int horarioTurma, int turma, Date data) throws Exception {
        StringBuilder sql;
        try{
            sql = new StringBuilder("select * from reposicao where contrato = ? and dataorigem::date = ? and horarioturmaorigem = ? and turmaorigem = ?\n");
            try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
                pst.setInt(1, contrato);
                pst.setDate(2, Uteis.getDataJDBC(data));
                pst.setInt(3, horarioTurma);
                pst.setInt(4, turma);
                try (ResultSet tabelaResultado = pst.executeQuery()) {
                    if (tabelaResultado.next()) {
                        throw new ConsistirException("Existe uma Reposição com a mesma data e horário, lançada no dia: " + Uteis.getDataComHHMM(tabelaResultado.getTimestamp("datalancamento")));

                    }
                }
            }
        }catch (Exception e){
            throw e;
        }
    }

    @Override
    public List<AgendadoJSON> consultarReposicoesParaAgenda(Date inicio, Date fim, Integer empresa, Integer matricula, boolean usarDataOrigem, Integer modalidade, Integer tipomodalidade) throws Exception{
        inicio = Calendario.getDataComHoraZerada(inicio);
        fim = Calendario.getDataComHoraZerada(fim);
        List<AgendadoJSON> lista = new ArrayList<AgendadoJSON>();
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT r.datapresenca, sc.codigocliente as cliente, sc.matricula, sc.telefonescliente, cc.tipooperacaocreditotreino,  \n");
        sql.append(" sc.nomecliente as nome, sc.codigopessoa as pessoa, t.codigo as turma, r.codigo as reposicao, sc.situacaoContrato as situacaoContrato,  \n");
        sql.append(" ht.codigo as horarioturma, r.horarioturmaorigem, r.datareposicao, r.contrato, ag.gympass, coalesce(ag.tipoagendamento, '') as tipoagendamento, \n");
        sql.append(" r.spiviSeatID, r.spivieventid, p.spiviclientid, p.fotokey \n");
        sql.append("  FROM reposicao r INNER JOIN horarioturma ht ON ht.codigo = r.horarioturma \n");
        sql.append(" INNER JOIN turma t ON t.codigo = r.turmadestino \n");
        sql.append(" INNER JOIN modalidade m on m.codigo = t.modalidade \n");
        sql.append(" INNER JOIN cliente ON cliente.codigo = r.cliente \n");
        sql.append(" INNER JOIN situacaoclientesinteticodw sc ON sc.codigocliente = r.cliente \n");
        sql.append(" INNER JOIN pessoa p ON p.codigo = sc.codigopessoa \n");
        sql.append(" LEFT JOIN controlecreditotreino cc ON cc.reposicao = r.codigo \n");
        sql.append(" LEFT JOIN agenda  ag ON ag.reposicao = r.codigo \n");
        if(matricula == null){
            sql.append(" WHERE r.datareposicao >= '").append(Uteis.getDataJDBCTimestamp(inicio)).append("'");
            sql.append(" AND r.datareposicao <= '").append(Uteis.getDataJDBCTimestamp(fim)).append("'\n");
            sql.append(" AND ht.situacao = 'AT'  \n");
            sql.append(" AND t.empresa = ").append(empresa);
        }else{
            String campo = "datareposicao";
            if(usarDataOrigem){
               campo = "dataorigem"; 
            }
            sql.append(" WHERE (r.").append(campo).append(" >= '").append(Uteis.getDataJDBCTimestamp(inicio)).append("'");
            sql.append(" AND r.").append(campo).append(" <= '").append(Uteis.getDataJDBCTimestamp(fim)).append("')\n");
            sql.append(" AND sc.matricula = ").append(matricula);
        }

        if (UteisValidacao.notEmptyNumber(tipomodalidade)) {
            sql.append(" and m.tipo = ").append(tipomodalidade);
        } else if (UteisValidacao.notEmptyNumber(modalidade)) {
            sql.append(" and m.codigo = ").append(modalidade);
        }

        try (ResultSet rs = criarConsulta(sql.toString(), con)) {
            while (rs.next()) {
                AgendadoJSON agendado = new AgendadoJSON();
                agendado.setCodigoCliente(rs.getInt("cliente"));
                agendado.setCodigoContrato(rs.getInt("contrato"));
                agendado.setMatricula(rs.getString("matricula"));
                agendado.setNome(rs.getString("nome"));
                agendado.setTelefones(rs.getString("telefonescliente"));
                agendado.setCodigoPessoa(rs.getInt("pessoa"));
                agendado.setId_agendamento(usarDataOrigem ? String.valueOf(rs.getInt("horarioturmaorigem")) : String.valueOf(rs.getInt("horarioturma")));
                agendado.setInicio(Uteis.getDataAplicandoFormatacao(rs.getDate("datareposicao"), "dd/MM/yyyy"));
                agendado.setFim(Uteis.getDataAplicandoFormatacao(rs.getDate("datareposicao"), "dd/MM/yyyy"));
                agendado.setUsaSaldo(rs.getInt("tipooperacaocreditotreino") == TipoOperacaoCreditoTreinoEnum.MARCOU_AULA.getCodigo());
                agendado.setSituacaoContrato(rs.getString("situacaoContrato"));
                agendado.setConfirmado(rs.getDate("datapresenca") != null);
                agendado.setGymPass(rs.getBoolean("gymPass"));
                agendado.setDiaria(rs.getString("tipoagendamento").equalsIgnoreCase("DI"));
                agendado.setExperimental(rs.getString("tipoagendamento").equalsIgnoreCase("AE"));
                agendado.setSpiviSeat(rs.getInt("spiviseatid"));
                agendado.setSpiviEventID(rs.getInt("spivieventid"));
                agendado.setSpiviClientID(rs.getInt("spiviclientid"));
                agendado.setFotokey(rs.getString("fotokey"));
                lista.add(agendado);
            }
        }
        return lista;
    }

    public void excluirReposicaoDaAulaExperimental(Integer codigoHorarioTurma, Integer codigoConvite, Date dataAula) throws Exception{
        String sql = "delete from reposicao where conviteAulaExperimental = ? and horarioTurma = ? and dataReposicao = ?";
        try (PreparedStatement pst = con.prepareStatement(sql)) {
            pst.setInt(1, codigoConvite);
            pst.setInt(2, codigoHorarioTurma);
            pst.setDate(3, Uteis.getDataJDBC(dataAula));
            pst.execute();
        }
    }
    
    public ReposicaoVO consultarReposicaoJaLancada(int codigoCliente, int horarioTurma, int turma, Date dataOrigem, Date dataReposicao) throws Exception {
        StringBuilder sql;
        try {
            sql = new StringBuilder("select * from reposicao where cliente = ? ");
            if(dataOrigem != null){
                sql.append(" and dataorigem::date = ? \n");
            }
            if(dataReposicao != null){
                sql.append(" and datareposicao::date = ? \n");
            }
            sql.append("and horarioturma = ? and turmadestino = ?");
            try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
                int i = 1;
                pst.setInt(i++, codigoCliente);
                if (dataOrigem != null) {
                    pst.setDate(i++, Uteis.getDataJDBC(dataOrigem));
                }
                if (dataReposicao != null) {
                    pst.setDate(i++, Uteis.getDataJDBC(dataReposicao));
                }
                pst.setInt(i++, horarioTurma);
                pst.setInt(i++, turma);
                try (ResultSet tabelaResultado = pst.executeQuery()) {
                    if (tabelaResultado.next()) {
                        return montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS, con);
                    }
                }
            }
            return null;
        } catch (Exception e) {
            throw e;
        }
    }

    public Integer consultarTotalReposicao(Integer codigoHorarioTurma, Date dataReposicao, Set<Integer> codClientes)throws Exception{
        String sql = "";
        if(codClientes != null){
            sql = "select cliente from reposicao where dataReposicao = ? and horarioTurma =?";
        }else{
            sql = "select count(*) as total from reposicao where dataReposicao = ? and horarioTurma =?";
        }
        Integer total;
        try (PreparedStatement pst = con.prepareStatement(sql)) {
            pst.setDate(1, Uteis.getDataJDBC(Calendario.getDataComHoraZerada(dataReposicao)));
            pst.setInt(2, codigoHorarioTurma);
            try (ResultSet rs = pst.executeQuery()) {
                total = 0;
                if(codClientes != null){
                    while (rs.next()) {
                        codClientes.add(rs.getInt("cliente"));
                        total++;
                    }
                }else {
                    if (rs.next()) {
                        total = rs.getInt("total");
                    }
                }
            }
        }
        return total;

    }

    public String sqlConsultaMatriculaHorarioTurma(Integer matricula, boolean desmarcadas, Date agora, boolean somenteMesmoDia, Integer modalidade,  Integer tipoModalidade) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT t.codigo as turma, t.empresa, \n");
        sql.append(" t.identificador,t.aulaColetiva, m.codigo as modalidade, m.nome as nomemodalidade, \n");
        sql.append(" ht.diasemana,ht.professor,p.nome as nomeprofessor, ht.ambiente,   \n");
        sql.append(" nivt.descricao,   \n");
        sql.append(" ht.horainicial, ht.horafinal, ht.nrmaximoaluno, ht.codigo as horarioturma,r.datareposicao as datainicio,r.datareposicao as datafim, r.contrato,\n");
        sql.append(" exists(select codigo from demandahorarioturma dht where dht.cliente = cli.codigo AND dht.horarioturma = ht.codigo) as jaMarcouEuQuero,\n");
        sql.append(" p.fotokey as fotoProfessor,\n");
        sql.append(" a.descricao as nomeambiente\n");
        sql.append(" FROM reposicao r  \n");
        sql.append(" INNER JOIN cliente cli ON r.cliente = cli.codigo \n");
        sql.append(" INNER JOIN contrato sc ON cli.pessoa = sc.pessoa AND r.contrato = sc.codigo \n");
        sql.append(" INNER JOIN horarioturma ht ON ht.codigo = r.horarioturma \n");
        sql.append(" INNER JOIN colaborador c ON c.codigo = ht.professor \n");
        sql.append(" INNER JOIN pessoa p ON p.codigo = c.pessoa \n");
        sql.append(" INNER JOIN turma t ON t.codigo = ht.turma \n");
        sql.append(" INNER JOIN nivelturma nivt ON nivt.codigo = ht.nivelturma\n");
        sql.append(" INNER JOIN modalidade m ON t.modalidade = m.codigo \n");
        sql.append(" INNER JOIN contrato con ON con.codigo = r.contrato AND con.situacao = 'AT' \n");
        sql.append(" INNER JOIN ambiente a ON a.codigo = ht.ambiente \n");
        sql.append(" WHERE cli.codigomatricula = ").append(matricula);
        sql.append(" and r.datareposicao ").append(somenteMesmoDia ? "= '" : ">= '").append(Uteis.getDataJDBC(Calendario.getDataComHoraZerada(agora))).append("'");
        sql.append(" AND not t.aulacoletiva \n");
        if (UteisValidacao.notEmptyNumber(tipoModalidade)) {
            sql.append(" and m.tipo = ").append(tipoModalidade);
        } else if (UteisValidacao.notEmptyNumber(modalidade)){
            sql.append(" and m.codigo = ").append(modalidade);
        }
        sql.append(" ORDER BY r.datareposicao");
        return sql.toString();
    }
    @Override
    public List<AgendaTotalJSON> consultarProximasReposicoes(Integer matricula, Date agora, boolean somenteMesmoDia, Integer modalidade, Integer tipoModalidade)throws Exception{
        List<AgendaTotalJSON> aulas = new ArrayList<AgendaTotalJSON>();
        try (ResultSet rs = criarConsulta(sqlConsultaMatriculaHorarioTurma(matricula, false, agora, somenteMesmoDia, modalidade, tipoModalidade), con)) {
            while (rs.next()) {
                Date dataHoraRep = Calendario.getDataComHora(rs.getDate("datainicio"), rs.getString("horainicial"));
                if (dataHoraRep.before(agora) && Calendario.igual(Calendario.hoje(), Calendario.getDataComHoraZerada(agora))) {
                    continue;
                }
                AgendaTotalJSON dadosAgenda = MatriculaAlunoHorarioTurma.montarDadosAgenda(rs);
                MatriculaAlunoHorarioTurma.consultaOcupacoes(rs.getDate("datainicio") ,dadosAgenda, con);
                aulas.add(new AgendaTotalJSON(rs.getDate("datainicio"), dadosAgenda));
            }
        }
        return aulas;
    }

    public void excluirReposicoesFuturasContratoHorarioTurma(Integer contrato,Integer horarioTurma, Date agora) throws Exception {
        List<ReposicaoVO> reposicoes = consulta("select * from reposicao where contrato ="+ contrato +" and horarioturma = "+ horarioTurma+ " and datareposicao >= '"+Uteis.getDataFormatoBD(agora)+"'");
        for(ReposicaoVO reposicao: reposicoes){
            excluirSemValidarPermissao(reposicao);
        }
    }

    public void excluirReposicoesFuturasContrato(Integer contrato, Date agora, ContratoVO contratoNovo) throws Exception {
        List<ReposicaoVO> reposicoes = consulta("select * from reposicao where contrato ="+ contrato +" and datareposicao >= '"+Uteis.getDataFormatoBD(agora)+"' and datapresenca is null");
        for(ReposicaoVO reposicao: reposicoes){
            excluirSemValidarPermissao(reposicao);
        }
        try {
            try (ResultSet rs = SuperFacadeJDBC.criarConsulta("select mantermarcacoesfuturascreditorenovacao, vendaCreditoTreino from empresa e \n" +
                    "inner join contrato c on c.empresa = e.codigo \n" +
                    "where c.codigo = " + contratoNovo.getCodigo(), con)){
                if(rs.next() && rs.getBoolean("mantermarcacoesfuturascreditorenovacao") && rs.getBoolean("vendaCreditoTreino")){
                    for(ReposicaoVO reposicao: reposicoes){
                        reposicao.setContrato(contratoNovo);
                        incluirSemCommit(reposicao, null, false);
                    }
                }
            }
        }catch (Exception e){
            Uteis.logar(e, Reposicao.class);
        }
    }

    private void excluirEntidadesRelacionadas(ReposicaoVO reposicaoVO) throws Exception {
        try{
            Agenda agendaDAO = new Agenda(con);
            AgendaVO agendaVO = agendaDAO.consultarPorReposicao(reposicaoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if(UteisValidacao.notEmptyNumber(agendaVO.getCodigo())) {
                agendaDAO.excluirSemCommit(agendaVO.getCodigo());
                HistoricoContatoVO hisExclusao = new HistoricoContatoVO();
                HistoricoContato hisDAO = new HistoricoContato(con);
                hisExclusao.setClienteVO(agendaVO.getCliente());
                hisExclusao.setDia(Calendario.hoje());
                hisExclusao.setResponsavelCadastro(reposicaoVO.getUsuarioVO());//setado anteriormente com o usuário que foi passado na solicitação
                hisExclusao.setResultado("Exclusão: " + agendaVO.qualResultadoAgendamento());
                hisDAO.incluirSemCommitSemAtualizarSintetico(hisExclusao);
            }
            executarConsulta("delete from periodoacessocliente where reposicao = " + reposicaoVO.getCodigo(), con);
        } catch(Exception ignored) {
            
        }
       
    }
    
    public ReposicaoVO consultaReposicaoClienteDiaTurma(Integer cliente,Integer horarioTurma, Date data, int nivelMontarDados) throws Exception {
       String sql = "select * from reposicao " +
                    " where cliente = " +cliente+
                    " and horarioturma = "+horarioTurma+
                    " and datareposicao::date = '"+Uteis.getDataAplicandoFormatacao(data, "yyyy-MM-dd")+"'";
        try (ResultSet rs = criarConsulta(sql, con)) {
            if (rs.next()) {
                return montarDados(rs, nivelMontarDados, con);
            }
        }
        return null;
    }

    public StringBuilder validarConflitoComReposicoes(ContratoVO contratoVO, List<Integer> codigosHorarios) throws Exception {

        StringBuilder sb = new StringBuilder();
        List<ReposicaoVO> reposicoes = new ArrayList<ReposicaoVO>();
        if(UteisValidacao.emptyList(codigosHorarios)){ //validando da negociação de contratos. Na manutenção, apenas os horarios que serão adicionados devem ser validados e lista deve vir preenchida
            codigosHorarios = new ArrayList<Integer>();
            for (ContratoModalidadeVO contratoModalidadeVO : contratoVO.getContratoModalidadeVOs()) {
                for (ContratoModalidadeHorarioTurmaVO cmhtVO : contratoModalidadeVO.getListaContratoModalidadesHorarioTurmaVOs()) {
                    codigosHorarios.add(cmhtVO.getHorarioTurma().getCodigo());
                }
            }
        }
        for (Integer codigoHorarioTurma: codigosHorarios) {
            List<ReposicaoVO> reposicoesTmp = consultarReposicoesPorHorarioTurma(codigoHorarioTurma, false,
                    Calendario.periodoSQL(Calendario.getMaior(contratoVO.getVigenciaDe(), Calendario.hoje()), contratoVO.getVigenciaAteAjustada()), Uteis.NIVELMONTARDADOS_DADOSBASICOS, null);
            reposicoes.addAll(reposicoesTmp);
        }

        if (reposicoes.size() > 0) {
            if (contratoVO.getEmpresa().isImpedirVendaContratoPorConflitoReposicao()) {
                sb.append("Não é possível continuar esta negociação por existir ");
            } else {
                sb.append("A(s) turma(s) selecionada(s) para esse contrato tem ");
            }
            if (reposicoes.size() == 1) {
                ReposicaoVO reposicaoVO = reposicoes.get(0);
                sb.append("uma reposição futura para a data: ").append(reposicaoVO.getDataReposicao_Apresentar());
            } else {
                List<String> datas = new ArrayList<String>();
                Ordenacao.ordenarLista(reposicoes,"dataReposicao");
                int limit = 0;
                for (ReposicaoVO reposicaoVO : reposicoes) {
                    if (!datas.contains(reposicaoVO.getDataReposicao_Apresentar())) {
                        datas.add(reposicaoVO.getDataReposicao_Apresentar());
                        limit++;
                    }
                    if(limit == 10){
                        break;
                    }

                }
                sb.append(reposicoes.size());
                sb.append(" reposições futuras para as datas: ").append(Uteis.montarListaIN(datas));
                if(reposicoes.size()>10){
                    sb.append("... ");
                }
            }

            if (contratoVO.getEmpresa().isImpedirVendaContratoPorConflitoReposicao()) {
                throw new Exception(sb.toString());
            }
        }

        return sb;
    }

    public Boolean existeReposicaoTurma(Integer codigoTurma) throws Exception {
        String sql = "SELECT * FROM reposicao WHERE turmadestino = ? or turmaorigem = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoTurma);
            sqlConsultar.setInt(2, codigoTurma);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                return tabelaResultado.next();
            }
        }
    }

    public void excluirAulasExperimentaisFuturas(Integer pessoa , Integer horarioTurma, Date dataInicio, UsuarioVO usuarioVO) throws Exception {
        String sql = "SELECT r.* FROM reposicao r inner join cliente c on c.codigo = r.cliente WHERE c.pessoa = ? and horarioturma = ? and  datareposicao::date >= ? and contrato is null";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, pessoa);
            sqlConsultar.setInt(2, horarioTurma);
            sqlConsultar.setDate(3, Uteis.getDataJDBC(dataInicio));
            try (ResultSet rs = sqlConsultar.executeQuery()) {
                List<ReposicaoVO>  lista = montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, con);
                for (ReposicaoVO reposicaoVO : lista) {
                    reposicaoVO.setAulaExperimental(true);
                    reposicaoVO.setUsuarioVO(usuarioVO); //usado para gerar responsável historico
                    excluirSemValidarPermissao(reposicaoVO);
                }
            }
        }

    }

    public Boolean existeAulaExperimentalNoDiaParaAlunoHorarioTurma(Integer pessoa , Integer horarioTurma, Date dataPesquisa) throws Exception {
        String sql = "SELECT r.* FROM reposicao r inner join cliente c on c.codigo = r.cliente WHERE c.pessoa = ? and horarioturma = ? and  datareposicao::date = ? and contrato is null;";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, pessoa);
            sqlConsultar.setInt(2, horarioTurma);
            sqlConsultar.setDate(3, Uteis.getDataJDBC(dataPesquisa));
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                return tabelaResultado.next();
            }
        }
    }

    public boolean existeAulaExperimentalNoDiaParaAlunoHorariosDoContrato(Integer codigoPessoa, List<ContratoModalidadeVO> contratoModalidadeVOs, Date vigenciaDe) throws Exception {
        for (ContratoModalidadeVO cont : contratoModalidadeVOs) {
            if (cont.getModalidade().getUtilizarTurma()) {
                if (cont.getModalidade().getModalidadeEscolhida()) {
                    for (Object o1 : cont.getContratoModalidadeTurmaVOs()) {
                        ContratoModalidadeTurmaVO modTurma = (ContratoModalidadeTurmaVO) o1;
                        if (modTurma.getTurma().getTurmaEscolhida()) {
                            for (Object o2 : modTurma.getContratoModalidadeHorarioTurmaVOs()) {
                                ContratoModalidadeHorarioTurmaVO modalidadeHorarioTurma = (ContratoModalidadeHorarioTurmaVO) o2;
                                if (modalidadeHorarioTurma.getHorarioTurma().getHorarioTurmaEscolhida()) {
                                   if(existeAulaExperimentalNoDiaParaAlunoHorarioTurma(codigoPessoa,modalidadeHorarioTurma.getHorarioTurma().getCodigo(), vigenciaDe)){
                                       return true;
                                   }
                                }
                            }
                        }
                    }
                }
            }
        }
        return false;
    }

    public Boolean removerMarcacoesFuturasParcelasVencidas(String chave){
        try {
            Integer diasDesmarcarComParcelaVencida = diasDesmarcarComParcelaVencida(chave);
            if(diasDesmarcarComParcelaVencida > 0){
                System.out.println(chave + " - dias para desmarcar com parcela vencida :" + diasDesmarcarComParcelaVencida);
                excluirMarcacoesAlunoParcelasVencidas(diasDesmarcarComParcelaVencida);
            }
            return true;
        }catch (Exception e){
            Uteis.logar(e, Reposicao.class);
            return false;
        }
    }


    private Integer diasDesmarcarComParcelaVencida(String chave){
        try {
            String urlTreino = PropsService.getPropertyValue(chave, PropsService.urlTreino);
            String dados = ExecuteRequestHttpService.executeHttpRequestGETEncode(urlTreino + "/prest/config/"
                    + chave + "/getconfigs/DESMARCAR_AULAS_FUTURAS_PARCELA_ATRASADA", new HashMap<>(), "UTF-8");
            JSONObject json = new JSONObject(dados).getJSONObject("return").getJSONArray("configuracoes").getJSONObject(0);
            return Integer.valueOf(json.getString("valor"));
        }catch (Exception e){
            Uteis.logar(e, Reposicao.class);
        }
        return 0;
    }

    public void excluirMarcacoesAlunoParcelasVencidas(Integer diasDesmarcarParcelaVencida) throws Exception {

        try {
            Date hoje = Calendario.hoje();
            Date dataLimiteDesmarcarParcelaVencida = Uteis.somarDias(Calendario.hoje(), diasDesmarcarParcelaVencida * -1);
            List<ReposicaoVO> reposicoes = consulta("select * from reposicao r \n" +
                    "where datareposicao >= '"+Uteis.getDataFormatoBD(hoje)+"'\n" +
                    "and contrato in (select contrato from movparcela m \n" +
                    "where situacao = 'EA'\n" +
                    "and datavencimento <= '"+Uteis.getDataFormatoBD(dataLimiteDesmarcarParcelaVencida)+"'\n" +
                    "and contrato is not null)\n" +
                    "and datapresenca is null");
            for(ReposicaoVO reposicao: reposicoes){
                excluirSemValidarPermissao(reposicao,
                        descreverParcelasVencidasContratoDesmarcacao(reposicao.getContrato().getCodigo(), dataLimiteDesmarcarParcelaVencida));
            }
        }catch (Exception e){
            Uteis.logar(e, Reposicao.class);
        }
    }

    private String descreverParcelasVencidasContratoDesmarcacao(Integer contrato, Date dataLimiteDesmarcarParcelaVencida){
        String descricao = "";
        try {
            int cont = 0;
            ResultSet rs = SuperFacadeJDBC.criarConsulta("select codigo, datavencimento  from movparcela m \n" +
                    " where situacao = 'EA'\n" +
                    " and contrato = " + contrato +
                    " and datavencimento <= '" + Uteis.getDataFormatoBD(dataLimiteDesmarcarParcelaVencida) + "'", con);
            while(rs.next()){
                cont++;
                descricao += ", cód. " + rs.getInt("codigo") + " vencida em "
                        + Calendario.getDataAplicandoFormatacao(rs.getDate("datavencimento"), "dd/MM");
            }
            descricao = (cont > 1 ? " parcelas vencidas (" :  " parcela vencida (")
                    + descricao.replaceFirst(", ", "") + ")";
        }catch (Exception e){
            Uteis.logar(e, Reposicao.class);
        }
        return descricao;
    }

    private void incluirLogExclusaoReposicao(ReposicaoVO reposicaoVO, String motivoParcela) throws Exception {
        try {
            Integer codigoContrato = reposicaoVO.getContrato() != null && reposicaoVO.getContrato().getCodigo() != null
                    ? reposicaoVO.getContrato().getCodigo()
                    : 0;

            Log logDAO = new Log(con);
            LogVO logVO = new LogVO();
            logVO.setChavePrimaria(codigoContrato.toString());
            logVO.setNomeEntidade("CONTRATO");

            String operacao = UteisValidacao.emptyString(motivoParcela) ?
                "ALTERAÇÃO - EXCLUSÃO DE REPOSIÇÃO." :
                "ALTERAÇÃO - EXCLUSÃO AUTOMÁTICA DE REPOSIÇÃO.";

            logVO.setOperacao(operacao);
            logVO.setNomeEntidadeDescricao("Contrato - Reposição");

            if (reposicaoVO.getUsuarioVO() != null) {
                logVO.setResponsavelAlteracao(reposicaoVO.getUsuarioVO().getNome());
                logVO.setUserOAMD(reposicaoVO.getUsuarioVO().getUserOamd());
            } else {
                logVO.setResponsavelAlteracao("SISTEMA");
                logVO.setUserOAMD("SISTEMA");
            }

            logVO.setNomeCampo("Campo(s)");
            logVO.setDataAlteracao(Calendario.hoje());

            StringBuilder msgLog = new StringBuilder();
            msgLog.append("Reposição: ").append(reposicaoVO.getCodigo());
            msgLog.append("\nDados da reposição: ").append(reposicaoVO.getDescricaoDestinoCurta());

            if (!UteisValidacao.emptyString(motivoParcela)) {
                msgLog.append("\nMotivo:").append(motivoParcela);
            }

            logVO.setValorCampoAlterado(msgLog.toString());
            logVO.setPessoa(reposicaoVO.getCliente().getPessoa().getCodigo());

            logDAO.incluirSemCommit(logVO);

        } catch (Exception e) {
            Uteis.logar(e, Reposicao.class);
        }
    }

    private void incluirLogMarcacaoReposicao(ReposicaoVO reposicaoVO) throws Exception {
        try {
            Integer codigoContrato = reposicaoVO.getContrato() != null && reposicaoVO.getContrato().getCodigo() != null
                    ? reposicaoVO.getContrato().getCodigo()
                    : 0;

            Log logDAO = new Log(con);
            LogVO logVO = new LogVO();
            logVO.setChavePrimaria(codigoContrato.toString());
            logVO.setNomeEntidade("CONTRATO");
            logVO.setOperacao("ALTERAÇÃO - MARCAÇÃO DE REPOSIÇÃO.");
            logVO.setNomeEntidadeDescricao("Contrato - Reposição");
            logVO.setResponsavelAlteracao(reposicaoVO.getUsuario().getNome());
            logVO.setUserOAMD(reposicaoVO.getUsuario().getUserOamd());
            logVO.setNomeCampo("Campo(s)");
            logVO.setDataAlteracao(Calendario.hoje());

            StringBuilder msgLog = new StringBuilder();
            msgLog.append("Reposição: ").append(reposicaoVO.getCodigo());
            msgLog.append("\nDados da reposição: ").append(reposicaoVO.getDescricaoDestinoCurta());

            logVO.setValorCampoAlterado(msgLog.toString());
            logVO.setPessoa(reposicaoVO.getCliente().getPessoa().getCodigo());

            logDAO.incluirSemCommit(logVO);

        } catch (Exception e) {
            Uteis.logar(e, Reposicao.class);
        }
    }

    public String enviarEmail(final ReposicaoVO repo, boolean exclusao) throws Exception {
        try {
            UteisEmail uteisEmail = new UteisEmail();
            ConfiguracaoSistemaCRMVO configCRM = SuperControle.getConfiguracaoSMTPNoReply();
            uteisEmail.novo("", configCRM);
            if (configCRM.getMailServer() != null && !configCRM.getMailServer().isEmpty()) {
                if (!UteisValidacao.emptyString(repo.getCliente().getPessoa().getEmail())) {
                    String[] dest = new String[]{
                            repo.getCliente().getPessoa().getEmail()
                    };
                    //
                    uteisEmail.enviarEmailN(dest, repo.getResultadoReposicaoHTML(exclusao).toString(),
                            (exclusao ? "Aula Reposição CANCELADA: " : "Aula REPOSIÇÃO para: ") +
                                    repo.getDescricaoDestino(),
                            repo.getCliente().getEmpresa_Apresentar());
                    return "ok";
                }
            } else {
                throw new Exception("Configuração de email inválida");
            }
            throw new Exception("Email não enviado");
        } catch (Exception ex) {
            ex.printStackTrace();
            return ex.getMessage();
        }
    }

    public String enviarSMS(final ReposicaoVO repo, boolean exclusao) throws Exception {
        try {
            String telefones = repo.getCliente().getPessoa().getTelefones();
            String chave = DAO.resolveKeyFromConnection(this.con);
            if (!UteisValidacao.emptyString(telefones)) {
                String token = Tricks.obterValorCampoConfiguracaoSistema("tokencontasms", chave);
                if (token != null && !token.isEmpty()) {
                    SmsController smsController = new SmsController(token, chave, TimeZone.getTimeZone(repo.getCliente().getEmpresa().getTimeZoneDefault()));
                    String[] fones = telefones.split(";");
                    List<Message> beans = new ArrayList<Message>();
                    for (int i = 0; i < fones.length; i++) {
                        String numero = fones[i];
                        Message b = new Message();
                        b.setMsg(repo.getResultadoReposicaoTEXTO(exclusao).toString());
                        b.setNumero(numero);
                        beans.add(b);
                    }
                    if (!beans.isEmpty()) {
                        smsController.sendMessage(null, beans);
                        return "ok";
                    }
                } else {
                    throw new Exception("TokenSMS inválido");
                }
            } else {
                throw new Exception("Cliente sem telefone");
            }
            throw new Exception("SMS não enviado");
        } catch (Exception ex) {
            ex.printStackTrace();
            return ex.getMessage();
        }
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.facade.jdbc.estoque;

import br.com.pactosolucoes.controle.json.estoque.EstoqueJSON;
import br.com.pactosolucoes.controle.json.estoque.ListaEstoqueJSON;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.estoque.CompraVO;
import negocio.comuns.estoque.ProdutoEstoqueVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.plano.Produto;
import negocio.interfaces.estoque.ProdutoEstoqueInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import negocio.comuns.estoque.CompraItensVO;

/**
 *
 * <AUTHOR>
 */
public class ProdutoEstoque extends SuperEntidade implements ProdutoEstoqueInterfaceFacade {

    public ProdutoEstoque() throws Exception{
        super();
    }

    public ProdutoEstoque(Connection connection) throws Exception{
        super(connection);
    }

   /**
     * Operação responsável por retornar um novo objeto da classe <code>ProdutoEstoqueVO</code>.
     */
  public ProdutoEstoqueVO novo() throws Exception{
        setIdEntidade("ProdutoEstoque");
        //incluir(getIdEntidade());
        ProdutoEstoqueVO obj = new ProdutoEstoqueVO();
        return obj;
    }

    public ProdutoEstoqueVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = " select * from produtoEstoque WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( ProdutoEstoque ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));
    }

    private Date consultarUltimaConfiguracaoProduto(Integer codigoProdutoEstoque)throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select min(dataCadastro) as data from produtoEstoque_alteracaosit where produtoEstoque = ").append(codigoProdutoEstoque);
        PreparedStatement pst = con.prepareStatement(sql.toString());
        ResultSet rs = pst.executeQuery();
        if (rs.next())
          return rs.getTimestamp("data");
        return null;
    }

    public ResultSet consultarProdutoEstoqueMinimo(int codigoEmpresa) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append(" Select COUNT(*) as Qtd from Produtoestoque pe\n");
        sql.append(" WHERE pe.estoqueminimo >= pe.estoque");
        sql.append(" and pe.empresa = ").append(codigoEmpresa);
        PreparedStatement stm = con.prepareStatement(sql.toString());
        ResultSet resultado = stm.executeQuery();
        return resultado;
    }

    public ListaEstoqueJSON consultarEstoqueProdutosJSON(int codigoEmpresa) throws Exception {
        ListaEstoqueJSON listaEstoqueJSON = new ListaEstoqueJSON();
        listaEstoqueJSON.setProdutosEstoque(new ArrayList<>());
        StringBuilder sql = new StringBuilder();
        sql.append(" Select p.codigo as codigoProduto, p.descricao as nomeProduto, pe.estoque, pe.estoqueminimo from Produtoestoque pe\n");
        sql.append(" inner join produto p on p.codigo = pe.produto \n");
        sql.append(" WHERE pe.empresa = ").append(codigoEmpresa);
        PreparedStatement stm = con.prepareStatement(sql.toString());
        ResultSet resultado = stm.executeQuery();
        while (resultado.next()) {
            EstoqueJSON jsonObject = new EstoqueJSON();
            jsonObject.setCodigoProduto(resultado.getInt("codigoProduto"));
            jsonObject.setNomeProduto(resultado.getString("nomeProduto"));
            jsonObject.setEstoqueAtual(resultado.getInt("estoque"));
            jsonObject.setEstoqueMinimo(resultado.getInt("estoqueminimo"));
            listaEstoqueJSON.getProdutosEstoque().add(jsonObject);
        }
        return listaEstoqueJSON;
    }

   public ProdutoEstoqueVO consultarPorProduto(Integer codigoProduto, Integer codigoEmpresa, int nivelMontarDados) throws Exception{
        String sql = " select * from produtoEstoque WHERE produto = ? and empresa = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoProduto.intValue());
        sqlConsultar.setInt(2, codigoEmpresa.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (tabelaResultado.next()) {
            return (montarDados(tabelaResultado, nivelMontarDados, this.con));
        }
        return null;

   }

    public List<ProdutoEstoqueVO> consultar(Integer codigoEmpresa, Integer codigoProduto, Integer codigoCategoriaProduto, String situacao, int nivelMontarDados) throws Exception{
        consultar(getIdEntidade(), false);
        StringBuilder sql = new  StringBuilder();
        sql.append(" select pe.* ");
        sql.append(" from produtoEstoque pe ");
        sql.append(" inner join produto p on p.codigo = pe.produto ");
        sql.append(" left join categoriaProduto cat on cat.codigo = p.categoriaProduto ");
        sql.append("  where (0 = 0) ");
        if ((codigoEmpresa != null) && (codigoEmpresa.intValue() > 0)){
            sql.append(" AND pe.empresa = ").append(codigoEmpresa.intValue());
        }
        if ((codigoProduto != null) && (codigoProduto.intValue() > 0)){
            sql.append(" AND pe.produto = ").append(codigoProduto.intValue());
        }
        if ((codigoCategoriaProduto != null) && (codigoCategoriaProduto.intValue() > 0)){
            sql.append(" AND cat.codigo = ").append(codigoCategoriaProduto.intValue());
        }
        if (!situacao.equals("T"))
            sql.append(" and pe.situacao = '").append(situacao).append("'");

        sql.append(" order by p.descricao");

        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));

    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>ProdutoEstoqueVO</code> resultantes da consulta.
     */
    public List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List lista = new ArrayList();
        while (tabelaResultado.next()) {
            ProdutoEstoqueVO obj = new ProdutoEstoqueVO();
            obj = montarDados(tabelaResultado, nivelMontarDados, con);
            lista.add(obj);
        }
        return lista;
    }


    public List montarDadosConsultaPosicaoEstoque(ResultSet tabelaResultado, int nivelMontarDados, Connection con, int valorImpresso) throws Exception {
        List lista = new ArrayList();
        while (tabelaResultado.next()) {
            ProdutoEstoqueVO obj = new ProdutoEstoqueVO();
            obj = montarDados(tabelaResultado, nivelMontarDados, con);

            if (valorImpresso == 2){
                obj.getProduto().setValorFinal(tabelaResultado.getDouble("custoUnitario"));
            } else if (valorImpresso == 3){
                obj.getProduto().setValorFinal(tabelaResultado.getDouble("custoMedio"));
            }

            lista.add(obj);
        }
        return lista;
    }


    public ProdutoEstoqueVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        ProdutoEstoqueVO obj = new ProdutoEstoqueVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.getEmpresa().setCodigo(dadosSQL.getInt("empresa"));
        obj.getProduto().setCodigo(dadosSQL.getInt("produto"));
        obj.setEstoque(dadosSQL.getInt("estoque"));
        obj.setEstoqueMinimo(dadosSQL.getInt("estoqueMinimo"));
        obj.setSituacao(dadosSQL.getString("situacao"));
        obj.setPontos(dadosSQL.getInt("pontos") * obj.getEstoque());
        obj.setDataConfiguracaoEstoque(consultarUltimaConfiguracaoProduto(obj.getCodigo()));
        return obj;

    }

    private static void montarDadosFKs(ProdutoEstoqueVO obj, int nivelMontarDados, Connection con) throws Exception{
        montarDadosEmpresa(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        montarDadosProduto(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>ProdutoEstoqueVO</code>.
     * @return  O objeto da classe <code>ProdutoEstoqueVO</code> com os dados devidamente montados.
     */
    public ProdutoEstoqueVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        ProdutoEstoqueVO obj = montarDadosBasico(dadosSQL);
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TELACONSULTA) {
            montarDadosFKs(obj, nivelMontarDados, con);
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TODOS) {
            montarDadosFKs(obj, nivelMontarDados, con);
            return obj;
        }
        return obj;
    }


    public static void montarDadosEmpresa(ProdutoEstoqueVO obj, int nivelMontarDados, Connection con) throws Exception {
        if ((obj.getEmpresa() == null) || (obj.getEmpresa().getCodigo().intValue() == 0)) {
            obj.setEmpresa(new EmpresaVO());
            return;
        }
        Empresa empresa = new Empresa(con);
        obj.setEmpresa(empresa.consultarPorChavePrimaria(obj.getEmpresa().getCodigo(), nivelMontarDados));
        empresa = null;
    }

    public static void montarDadosProduto(ProdutoEstoqueVO obj, int nivelMontarDados, Connection con) throws Exception {
        if ((obj.getProduto() == null) || (obj.getProduto().getCodigo().intValue() == 0)) {
            obj.setProduto(new ProdutoVO());
            return;
        }
        Produto produto = new Produto();
        obj.setProduto(produto.consultarPorChavePrimaria(obj.getProduto().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        produto = null;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>ProdutoEstoqueVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(List<ProdutoEstoqueVO> listaProdutoEstoqueVO, UsuarioVO usuario) throws Exception {
        try {
            con.setAutoCommit(false);
            for (ProdutoEstoqueVO obj: listaProdutoEstoqueVO){
                ProdutoEstoqueVO.validarDados(obj);

                List<ProdutoEstoqueVO> listaProd = consultar(obj.getEmpresa().getCodigo(), obj.getProduto().getCodigo(), null, "T", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                int index = listaProd.indexOf(obj);
                if(index >= 0){
                    ProdutoEstoqueVO produtoEstoqueVO = listaProd.get(index);
                    if (produtoEstoqueVO.getSituacao().equals("C")){
                        throw new Exception("Este produto já está configurado no controle de estoque, porém está cancelado, por favor busque pelos produtos cancelados na opção situação e adicione novamente o produto ao estoque.");
                    }else{
                        throw new Exception("O Produto '" + obj.getProduto().getDescricao() + "' já foi adicionado ao controle de estoque.");
                    }
                }

                String sql = "INSERT INTO ProdutoEstoque( empresa, produto, estoqueMinimo, situacao,pontos) VALUES ( ?, ?, ?, ?,? )";

                PreparedStatement sqlInserir = con.prepareStatement(sql);
                sqlInserir.setInt(1, obj.getEmpresa().getCodigo());
                sqlInserir.setInt(2, obj.getProduto().getCodigo());
                if (obj.getEstoqueMinimo() == null)
                    sqlInserir.setNull(3, java.sql.Types.NULL);
                else
                    sqlInserir.setInt(3, obj.getEstoqueMinimo());
                sqlInserir.setString(4, "A");
                sqlInserir.setInt(5, obj.getPontos());
                sqlInserir.execute();
                obj.setCodigo(obterValorChavePrimariaCodigo());
                obj.setNovoObj(new Boolean(false));

                String sqlAlt = "INSERT INTO ProdutoEstoque_AlteracaoSit( produtoEstoque, datacadastro, usuario, situacao) VALUES ( ?, ?, ?, ? )";
                PreparedStatement sqlInserirAlt = con.prepareStatement(sqlAlt);
                // Incluir a primeira alteracao de situacao do ProdutoEstoque
                sqlInserirAlt.setInt(1, obj.getCodigo());
                sqlInserirAlt.setTimestamp(2, Uteis.getDataHoraJDBC(Calendario.getInstance().getTime(), Uteis.getHoraAtual()));
                sqlInserirAlt.setInt(3, usuario.getCodigo());
                sqlInserirAlt.setString(4, "A");
                sqlInserirAlt.execute();

            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            for (ProdutoEstoqueVO obj: listaProdutoEstoqueVO)
                obj.setNovoObj(new Boolean(true));
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }


    public void alterar(ProdutoEstoqueVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            CompraVO.validarDados(obj);
            String sql = "update ProdutoEstoque set estoqueMinimo = ?,pontos = ? where codigo = ? ";

            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            sqlAlterar.setInt(1, obj.getEstoqueMinimo());
            sqlAlterar.setInt(2, obj.getPontos());
            sqlAlterar.setInt(3, obj.getCodigo());
            sqlAlterar.execute();
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(new Boolean(true));
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void alterarSituacao(ProdutoEstoqueVO obj, UsuarioVO usuario) throws Exception {
        try {
            con.setAutoCommit(false);
            if (obj.getSituacao().equals("A"))
              obj.setSituacao("C");
            else
              obj.setSituacao("A");
            String sql = "update ProdutoEstoque set situacao =?, ape = 'trigger' where codigo = ? ";
            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            sqlAlterar.setString(1, obj.getSituacao());
            sqlAlterar.setInt(2, obj.getCodigo());
            sqlAlterar.execute();

            // retirar o valor do campo ape
            String sql2 = "update produtoEstoque set ape = null where codigo = " + obj.getCodigo();
            PreparedStatement sqlAlterar2 = con.prepareStatement(sql2);
            sqlAlterar2.execute();

            String sqlAlt = "INSERT INTO ProdutoEstoque_AlteracaoSit( produtoEstoque, datacadastro, usuario, situacao) VALUES ( ?, ?, ?, ? )";
            PreparedStatement sqlInserirAlt = con.prepareStatement(sqlAlt);
            // Incluir a alteração de situaçao do ProdutoEstoque
            sqlInserirAlt.setInt(1, obj.getCodigo());
            sqlInserirAlt.setTimestamp(2, Uteis.getDataHoraJDBC(Calendario.getInstance().getTime(), Uteis.getHoraAtual()));
            sqlInserirAlt.setInt(3, usuario.getCodigo());
            sqlInserirAlt.setString(4, obj.getSituacao());
            sqlInserirAlt.execute();

            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(new Boolean(true));
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public List<ProdutoEstoqueVO> consultarPosicaoEstoque(int tipoRelatorio,
                                                  Integer codigoEmpresa,
                                                  Integer codigoCategoria,
                                                  int statusProduto,
                                                  int ordenacao,
                                                  int valorImpresso,
                                                  int nivelMontarDados)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select pe.* ");
        if (valorImpresso == 2){
            sql.append(", (select ci.valorunitario from compraitens ci inner join compra on compra.codigo = ci.compra where ci.produto = pe.produto order by compra.datacadastro desc, compra.codigo desc limit 1) as custoUnitario " );
        } else if (valorImpresso == 3){
            sql.append(", (select (round(cast((sum(ci.total) / sum(ci.quantidade)) as numeric), 2)) from compraitens ci where ci.produto = pe.produto limit 1) as custoMedio " );
        }
        sql.append(" from produtoEstoque pe ");
        sql.append(" join produto p on p.codigo = pe.produto ") ;
        sql.append(" where pe.situacao = 'A' ");
        sql.append(" and pe.empresa = ").append(codigoEmpresa);
        if (tipoRelatorio == 2){
            // Listar somente os produtos com estoque abaixo do mínimo
            sql.append(" and (coalesce(pe.estoque,0) <= pe.estoqueMinimo) ");
        }
        if ((codigoCategoria != null) && (codigoCategoria > 0)){
            sql.append(" and p.categoriaProduto = ").append(codigoCategoria);
        }
        if (statusProduto > 0){
            sql.append(" and p.desativado = ").append(statusProduto == 1? "false" : "true");
        }
        if (ordenacao == 1)
            sql.append(" order by p.descricao ");
        else if (ordenacao == 2)
          sql.append(" order by p.codigo ");
        else
          sql.append(" order by pe.estoque ");

        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        return (montarDadosConsultaPosicaoEstoque(tabelaResultado, nivelMontarDados, this.con, valorImpresso));

    }


    public String consultarJSON(Integer empresa) throws Exception {
        ResultSet rs = getRS(empresa);
        ProdutoEstoqueVO produtoEstoqueVO = new ProdutoEstoqueVO();
        StringBuilder json = new StringBuilder();
        json.append("{\"aaData\":[");
        boolean dados = false;
        while (rs.next()) {
            dados = true;
            json.append("[\"").append(rs.getString("codigo")).append("\",");
            json.append("\"").append(rs.getString("produto").trim().replaceAll("\\\\", "\\\\\\\\").replaceAll("\"", "\\\\\"")).append("\",");
            produtoEstoqueVO.setSituacao(rs.getString("situacao"));
            json.append("\"").append(produtoEstoqueVO.getSituacao_Apresentar()).append("\",");
            json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("empresa"))).append("\",");
            json.append("\"").append(rs.getString("estoque")).append("\",");
            json.append("\"").append(rs.getString("estoqueminimo")).append("\",");
            json.append("\"").append(rs.getInt("pontos")).append("\"],");
        }
        if(dados) {
            json.deleteCharAt(json.toString().length()-1);
        }
        json.append("]}");
        return json.toString();
    }

    private ResultSet getRS(Integer empresa) throws SQLException {
        StringBuilder sql = new StringBuilder("SELECT pe.codigo, pd.descricao AS produto, emp.nome AS empresa, \n" + "pe.estoque, pe.estoqueminimo, pe.situacao, pe.pontos \n" + "FROM produtoestoque pe\n" + "  LEFT JOIN empresa emp ON pe.empresa = emp.codigo\n" + "  LEFT JOIN produto pd ON pe.produto = pd.codigo");
        if (empresa != 0) {
            sql.append("  WHERE pe.empresa = ?");
        }
        sql.append("  ORDER BY pd.descricao");
        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        if (empresa != 0) {
            sqlConsultar.setInt(1, empresa);
        }
        ResultSet rs = sqlConsultar.executeQuery();
        return rs;
    }
 public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa) throws SQLException {

        ResultSet rs = getRS(empresa);
        List lista = new ArrayList();

        while (rs.next()) {

            ProdutoEstoqueVO prod = new ProdutoEstoqueVO();
            String geral = rs.getString("codigo") + rs.getString("produto") + rs.getString("empresa") + rs.getString("estoque") + rs.getString("estoqueminimo") + rs.getString("situacao");
            if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                prod.getProduto().setCodigo(rs.getInt("codigo"));
                prod.getProduto().setDescricao(rs.getString("produto"));
                prod.getEmpresa().setNome(rs.getString("empresa"));
                prod.setEstoque(rs.getInt("estoque"));
                prod.setEstoqueMinimo(rs.getInt("estoqueminimo"));
                prod.setSituacao(rs.getString("situacao"));
                prod.setPontos(rs.getInt("pontos"));
                lista.add(prod);
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo_Apresentar");
        } else if (campoOrdenacao.equals("Produto")) {
            Ordenacao.ordenarLista(lista, "descricao_Apresentar");
        } else if (campoOrdenacao.equals("Situação")) {
            Ordenacao.ordenarLista(lista, "situacao_Apresentar");
        } else if (campoOrdenacao.equals("Empresa")) {
            Ordenacao.ordenarLista(lista, "empresa_Apresentar");
        } else if (campoOrdenacao.equals("Estoque Atual")) {
            Ordenacao.ordenarLista(lista, "estoque");
        } else if (campoOrdenacao.equals("Estoque Mínimo")) {
            Ordenacao.ordenarLista(lista, "estoqueMinimo");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }

    public Date pesquisarProdutoEstoqueComDataMaior(Date dataComparar, Integer codigoProduto, Integer codigoEmpresa)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select min(pes.dataCadastro) as dataCadastro \n");
        sql.append("from produtoEstoque_alteracaosit pes \n");
        sql.append("inner join produtoEstoque pe on pe.codigo = pes.produtoEstoque \n");
        sql.append("where produto = ").append(codigoProduto).append(" and empresa = ").append(codigoEmpresa);
        sql.append("and pes.dataCadastro > '").append(Calendario.getData(dataComparar, "yyyy-MM-dd HH:mm:ss")).append("'");
        PreparedStatement pst = con.prepareStatement(sql.toString());
        ResultSet rs = pst.executeQuery();
        if (rs.next()){
            return rs.getTimestamp("dataCadastro");
        }
        return null;

    }
    
    public void pesquisarDatasAlteracoesProdutoEstoque(CompraItensVO item, Integer codigoEmpresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select max(dataOperacao) as ultimaOperacao ,max(DataFinal) as saidaControle  from ( \n");
        sql.append("select sit.dataCadastro  as dataOperacao, null as DataFinal  \n");
        sql.append("from produtoEstoque_alteracaoSit sit inner join produtoEstoque pe on pe.codigo = sit.produtoEstoque \n");
        sql.append("where produto = ").append(item.getProduto().getCodigo()).append(" and empresa = ").append(codigoEmpresa);
        sql.append("and sit.situacao = 'A' \n");
        sql.append("union all  \n");
        sql.append("select null  as dataOperacao, sit.dataCadastro as DataFinal \n");
        sql.append("from produtoEstoque_alteracaoSit sit inner join produtoEstoque pe on pe.codigo = sit.produtoEstoque \n");
        sql.append("where produto = ").append(item.getProduto().getCodigo()).append(" and empresa = ").append(codigoEmpresa);
        sql.append("and sit.situacao = 'C' \n");
        sql.append("union all  \n");
        sql.append("(select cast (b.dataCadastro  as timestamp) as dataOperacao,null as DataFinal  \n");
        sql.append("from balancoItens bi inner join balanco b on b.codigo = bi.balanco \n");
        sql.append("where bi.produto = ").append(item.getProduto().getCodigo()).append(" and b.empresa = ").append(codigoEmpresa);
        sql.append("order by b.dataCadastro desc limit 1))  as foo  \n");
        PreparedStatement pst = con.prepareStatement(sql.toString());
        ResultSet rs = pst.executeQuery();
        if (rs.next()) {
            item.setDataUltimaOperacao(rs.getTimestamp("ultimaOperacao"));
            item.setDataSaidaControle(rs.getTimestamp("saidaControle"));
            if(item.getDataSaidaControle() != null  && item.getDataUltimaOperacao() != null && Calendario.menorOuIgualComHora(item.getDataSaidaControle(), item.getDataUltimaOperacao())){
                item.setDataSaidaControle(null); // controle voltou a ser vigente
            }
        }

    }
}

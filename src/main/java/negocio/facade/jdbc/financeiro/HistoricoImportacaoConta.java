package negocio.facade.jdbc.financeiro;

import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import negocio.comuns.financeiro.HistoricoImportacaoContaVO;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.HistoricoImportacaoContaInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

public class HistoricoImportacaoConta extends SuperEntidade implements HistoricoImportacaoContaInterfaceFacade {

    public HistoricoImportacaoConta() throws Exception {
        super();
        setIdEntidade("HistoricoImportacaoConta");
    }

    public HistoricoImportacaoConta(Connection con) throws Exception {
        super(con);
        setIdEntidade("HistoricoImportacaoConta");
    }

    @Override
    public void incluir(HistoricoImportacaoContaVO obj) throws Exception {
        String sql = "INSERT INTO historicoimportacaoconta (dataimportacao, usuario, origemarquivo, totalimportados, totalerros, mensagemresultado) " +
                "VALUES (?, ?, ?, ?, ?, ?  )";
        try (PreparedStatement pst = con.prepareStatement(sql)) {

            pst.setTimestamp(1, new java.sql.Timestamp(obj.getDataImportacao().getTime()));
            pst.setString(2, obj.getUsuario());
            pst.setString(3, obj.getOrigemArquivo());
            pst.setInt(4, obj.getTotalImportados());
            pst.setInt(5, obj.getTotalErros());

            List<String> mensagens = obj.getMensagensResultado();
            Gson gson = new Gson();
            String mensagensJson = gson.toJson(mensagens);
            pst.setString(6, mensagensJson);

            pst.executeUpdate();
        }
    }

    @Override
    public void alterar(HistoricoImportacaoContaVO obj) throws Exception {
        String sql = "UPDATE historicoimportacaoconta SET  dataimportacao = ?, usuario = ?, origemarquivo = ?, " +
                "totalimportados = ?, totalerros = ?, mensagemresultado = ? WHERE codigo = ?";
        try (PreparedStatement pst = con.prepareStatement(sql)) {
            pst.setTimestamp(1, new java.sql.Timestamp(obj.getDataImportacao().getTime()));
            pst.setString(2, obj.getUsuario());
            pst.setString(3, obj.getOrigemArquivo());
            pst.setInt(4, obj.getTotalImportados());
            pst.setInt(5, obj.getTotalErros());

            List<String> mensagens = obj.getMensagensResultado();
            Gson gson = new Gson();
            String mensagensJson = gson.toJson(mensagens);
            pst.setString(6, mensagensJson);
            pst.setInt(7, obj.getCodigo());
            pst.executeUpdate();
        }
    }

    @Override
    public void excluir(HistoricoImportacaoContaVO obj) throws Exception {
        String sql = "DELETE FROM historicoimportacaoconta WHERE codigo = ?";
        try (PreparedStatement pst = con.prepareStatement(sql)) {
            pst.setInt(1, obj.getCodigo());
            pst.executeUpdate();
        }
    }

    @Override
    public HistoricoImportacaoContaVO obterPorCodigo(int codigo) throws Exception {
        String sql = "SELECT codigo, dataimportacao, usuario, origemarquivo, totalimportados, totalerros, mensagemresultado " +
                "FROM historicoimportacaoconta WHERE codigo = ?";
        try (PreparedStatement pst = con.prepareStatement(sql)) {
            pst.setInt(1, codigo);
            try (ResultSet rs = pst.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs);
                }
            }
        }
        return null;
    }

    @Override
    public List<HistoricoImportacaoContaVO> consultar(HistoricoImportacaoContaVO filtro) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT codigo , dataimportacao, usuario, origemarquivo, totalimportados, totalerros, mensagemresultado  ")
                .append("FROM historicoimportacaoconta ");

        boolean addWhere = false;
        List<Object> parametros = new ArrayList<>();

        sql.append("ORDER BY dataimportacao DESC");

        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            for (int i = 0; i < parametros.size(); i++) {
                pst.setObject(i + 1, parametros.get(i));
            }

            try (ResultSet rs = pst.executeQuery()) {
                List<HistoricoImportacaoContaVO> lista = new ArrayList<>();
                while (rs.next()) {
                    lista.add(montarDados(rs));
                }
                return lista;
            }
        }
    }

    @Override
    public String consultarJSON() throws Exception {
        StringBuilder json = new StringBuilder();
        boolean dados = false;

        String sql = "SELECT codigo , dataimportacao, usuario, origemarquivo, totalimportados, totalerros, mensagemresultado " +
                "FROM historicoimportacaoconta";

        try (PreparedStatement pst = con.prepareStatement(sql);
             ResultSet rs = pst.executeQuery()) {

            json.append("{\"aaData\":[");
            while (rs.next()) {
                dados = true;
                json.append("[\"").append(rs.getInt("codigo")).append("\",");
                json.append("\"").append(rs.getDate("dataimportacao")).append("\",");
                json.append("\"").append(rs.getString("usuario")).append("\",");
                json.append("\"").append(rs.getString("origemarquivo")).append("\",");
                json.append("\"").append(rs.getInt("totalimportados")).append("\",");
                json.append("\"").append(rs.getInt("totalerros")).append("\",");
                json.append("\"").append(rs.getString("mensagemresultado")).append("\"],");

            }
            if (dados) {
                json.deleteCharAt(json.length() - 1); // Remover vírgula extra
            }
            json.append("]}");
        }

        return json.toString();
    }

    private HistoricoImportacaoContaVO montarDados(ResultSet rs) throws Exception {
        HistoricoImportacaoContaVO vo = new HistoricoImportacaoContaVO();
        vo.setCodigo(rs.getInt("codigo"));
        vo.setDataImportacao(rs.getTimestamp("dataimportacao"));
        vo.setUsuario(rs.getString("usuario"));
        vo.setOrigemArquivo(rs.getString("origemarquivo"));
        vo.setTotalImportados(rs.getInt("totalimportados"));
        vo.setTotalErros(rs.getInt("totalerros"));
        String mensagensJson = rs.getString("mensagemresultado");
        List<String> mensagens;

        try {
            mensagens = new Gson().fromJson(mensagensJson, new TypeToken<List<String>>() {}.getType());
            if (mensagens == null || !mensagensJson.trim().startsWith("[")) {
                throw new Exception("NÒo Ú lista");
            }
        } catch (Exception e) {
            mensagens = new ArrayList<>();
            for (String parte : mensagensJson.split("\\.")) {
                String msg = parte.trim();
                if (!msg.isEmpty()) {
                    mensagens.add(msg + ".");
                }
            }
        }

        vo.setMensagensResultado(mensagens);
        return vo;
    }
}


package negocio.facade.jdbc.plano;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.LogGenericoTipoEnum;
import br.com.pactosolucoes.enumeradores.TipoFrequenciaEnum;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.enumerador.TipoItemCampanhaEnum;
import negocio.comuns.contrato.MovProdutoModalidadeVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.plano.DescontoVO;
import negocio.comuns.plano.PlanoComposicaoVO;
import negocio.comuns.plano.PlanoCondicaoPagamentoVO;
import negocio.comuns.plano.PlanoDuracaoVO;
import negocio.comuns.plano.PlanoHorarioVO;
import negocio.comuns.plano.PlanoModalidadeVO;
import negocio.comuns.plano.PlanoRecorrenciaVO;
import negocio.comuns.plano.PlanoTextoPadraoVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.enumerador.TipoReajuste;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.PlanoTipo;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.contrato.MovProdutoModalidade;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.RemessaItem;
import negocio.interfaces.plano.PlanoInterfaceFacade;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.negocio.comuns.basico.ListaPaginadaTO;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos
 * dados da classe
 * <code>PlanoVO</code>. Responsável por implementar operações como incluir,
 * alterar, excluir e consultar pertinentes a classe
 * <code>PlanoVO</code>. Encapsula toda a interação com o banco de dados.
 *
 * @see PlanoVO
 * @see SuperEntidade
 */
public class Plano extends SuperEntidade implements PlanoInterfaceFacade {

    private Hashtable planoDuracaos;
    private Hashtable planoCondicaoPagamentos;
    private Hashtable planoComposicaos;
    private Hashtable planoModalidades;
    private Hashtable planoHorarios;

    public Plano() throws Exception {
        super();
        iniciarVariaveis();
    }

    public Plano(Connection conexao) throws Exception {
        super(conexao);
        iniciarVariaveis();
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma
     * consulta ao banco de dados (
     * <code>ResultSet</code>). Faz uso da operação
     * <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vários objetos da classe <code>PlanoVO</code>
     * resultantes da consulta.
     */
    public static List<PlanoVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<PlanoVO> vetResultado = new ArrayList<PlanoVO>();
        while (tabelaResultado.next()) {
            PlanoVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }
    public static PlanoVO montarDadosBasico(ResultSet dadosSQL, Connection con) throws Exception {
        PlanoVO obj = new PlanoVO();
        obj.setNovoObj(false);
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.setEmpresa(getCachedEmpresa(dadosSQL.getInt("empresa"), con));
        obj.setVigenciaDe(dadosSQL.getDate("vigenciaDe"));
        obj.setVigenciaAte(dadosSQL.getDate("vigenciaAte"));
        obj.setIngressoAte(dadosSQL.getDate("ingressoAte"));
        obj.setBolsa(dadosSQL.getBoolean("bolsa"));
        obj.setPermitePagarComBoleto(dadosSQL.getBoolean("permitepagarcomboleto"));
        obj.getProdutoPadraoGerarParcelasContrato().setCodigo(dadosSQL.getInt("produtoPadraoGerarParcelasContrato"));
        obj.getProdutoTaxaCancelamento().setCodigo(dadosSQL.getInt("produtoTaxaCancelamento"));
        obj.setPercentualMultaCancelamento(dadosSQL.getDouble("percentualMultaCancelamento"));
        obj.getPlanoTextoPadrao().setCodigo(dadosSQL.getInt("planoTextoPadrao"));
        obj.getReciboTextoPadrao().setCodigo(dadosSQL.getInt("reciboTextoPadrao"));
        obj.setProrataObrigatorio(dadosSQL.getBoolean("prorataObrigatorio"));
        obj.setDiasVencimentoProrata(dadosSQL.getString("diasVencimentoProrata"));
        obj.setListaDiasVencimento(dadosSQL.getString("diasVencimentoProrata"));
        obj.getDescontoAntecipado().setCodigo(dadosSQL.getInt("descontoAntecipado"));
        obj.setRegimeRecorrencia(dadosSQL.getBoolean("recorrencia"));
        obj.setCorrespondenciaZD(dadosSQL.getString("correspondencia_zd"));
        obj.setComissao(dadosSQL.getBoolean("comissao"));
        obj.setDescricaoEncantamento(dadosSQL.getString("descricaoencantamento"));
        obj.setQuantidadeMaximaFrequencia(dadosSQL.getInt("quantidadeMaximaFrequencia"));
        obj.setTipoFrequencia(dadosSQL.getInt("tipoFrequencia"));
        obj.setVendaCreditoTreino(dadosSQL.getBoolean("vendaCreditoTreino"));
        obj.setDiaDoMesDescontoBoletoPagAntecipado(dadosSQL.getInt("diaDoMesDescontoBoletoPagAntecipado"));
        obj.setPorcentagemDescontoBoletoPagAntecipado(dadosSQL.getDouble("porcentagemDescontoBoletoPagAntecipado"));
        obj.setAceitaDescontoPorPlano(dadosSQL.getBoolean("aceitadescontoporplano"));
        obj.setValorDescontoBoletoPagAntecipado(dadosSQL.getDouble("valordescontoboletopagantecipado"));
        obj.setQuantidadeATransferirPermitidaPorAluno(dadosSQL.getInt("quantidadeATransferirPermitidaPorAluno"));
        obj.setPermitirTransferenciaDeCredito(dadosSQL.getBoolean("permitirTransferenciaDeCredito"));
        try {
            obj.setCobrarAdesaoSeparada(dadosSQL.getBoolean("CobrarAdesaoSeparada"));
            obj.setNrVezesParcelarAdesao(dadosSQL.getInt("NrVezesParcelarAdesao"));
            obj.setCobrarProdutoSeparado(dadosSQL.getBoolean("cobrarProdutoSeparado"));
            obj.setNrVezesParcelarProduto(dadosSQL.getInt("nrVezesParcelarProduto"));

            obj.setPlanoPersonal(dadosSQL.getBoolean("planoPersonal"));
            obj.setSite(dadosSQL.getBoolean("site"));
            obj.setPermitirVendaPlanoSiteNoBalcao(dadosSQL.getBoolean("permitirVendaPlanoSiteNoBalcao"));
            obj.setRenovavelAutomaticamente(dadosSQL.getBoolean("renovavelAutomaticamente"));
            obj.setInicioMinimoContrato(dadosSQL.getDate("inicioMinimoContrato"));
            obj.setParcelamentoOperadora(dadosSQL.getBoolean("parcelamentoOperadora"));
            obj.setRenovarProdutoObrigatorio(dadosSQL.getBoolean("renovarprodutoobrigatorio"));
            obj.setRenovarAnuidadeAutomaticamente(dadosSQL.getBoolean("renovaranuidadeautomaticamente"));
            obj.setPontos(dadosSQL.getInt("pontos"));
            obj.getTermoAceite().setCodigo(dadosSQL.getInt("termoAceite"));
            obj.setTotem(dadosSQL.getBoolean("totem"));
            obj.setPermitirVendaPlanoTotemNoBalcao(dadosSQL.getBoolean("permitirVendaPlanoTotemNoBalcao"));
            obj.getConvenioCobrancaPrivateLabel().setCodigo(dadosSQL.getInt("convenioCobrancaPrivateLabel"));
            obj.setRestringirMarcacaoAulasColetivas(dadosSQL.getBoolean("restringirmarcacaoaulascoletivas"));
            obj.setRestringirQtdMarcacaoPorDia(dadosSQL.getInt("restringirqtdmarcacaoporpia"));
            obj.setCreditoTreinoNaoCumulativo(dadosSQL.getBoolean("creditoTreinoNaoCumulativo"));
            obj.setPermitirAcessoSomenteNaEmpresaVendeuContrato(dadosSQL.getBoolean("permitirAcessoSomenteNaEmpresaVendeuContrato"));
            obj.setCreditoSessao(dadosSQL.getBoolean("creditoSessao"));
            obj.setRenovarAutomaticamenteComDesconto(dadosSQL.getBoolean("renovarAutomaticamenteComDesconto"));
            obj.setConvidadosPorMes(dadosSQL.getInt("convidadospormes"));
            obj.setRecorrencia(dadosSQL.getBoolean("recorrencia"));
            obj.setApresentaVendaRapida(dadosSQL.getBoolean("apresentarvendarapida"));
            obj.setRenovarAutomaticamenteApenasCondicaoPagamentoRecorrencia(dadosSQL.getBoolean("renovarAutomaticamenteApenasCondicaoPagamentoRecorrencia"));
            obj.setPermiteSituacaoAtestadoContrato(dadosSQL.getBoolean("permiteSituacaoAtestadoContrato"));
            obj.setQtdSemanasAno(dadosSQL.getInt("qtdSemanasAno"));
            obj.setMaximoVezesParcelar(dadosSQL.getInt("maximoVezesParcelar"));
            obj.setNaoRenovarContratoParcelaVencidaAberto(dadosSQL.getBoolean("naorenovarcontratoparcelavencidaaberto"));
            obj.setDividirManutencaoParcelasEA(dadosSQL.getBoolean("dividirmanutencaoparcelasea"));
            obj.setAceitaDescontoExtra(dadosSQL.getBoolean("aceitaDescontoExtra"));
            obj.setRestringeVendaPorCategoria(dadosSQL.getBoolean("restringeVendaPorCategoria"));
            obj.setRestringirQtdMarcacaoPorDiaGeral(dadosSQL.getInt("restringirqtdmarcacaopordiageral"));
            obj.setParcelamentoOperadoraDuracao(dadosSQL.getBoolean("parcelamentoOperadoraDuracao"));
            obj.setPermitirAcessoRedeEmpresa(dadosSQL.getBoolean("permitirAcessoRedeEmpresa"));
            obj.setQuantidadeCompartilhamentos(dadosSQL.getInt("quantidadeCompartilhamentos"));
            obj.setRestringirMarcacaoAulaPorNrVezesModalidade(dadosSQL.getBoolean("restringirMarcacaoAulaPorNrVezesModalidade"));
            obj.setBloquearRecompra(dadosSQL.getBoolean("bloquearrecompra"));
            obj.setHabilitarIa(dadosSQL.getBoolean("habilitarIa"));
        } catch (Exception ignored) {
        }

        try {
            obj.setApresentarPactoFlow(dadosSQL.getBoolean("apresentarPactoFlow"));
        } catch (Exception ex) {}

        try {
            obj.setFaturar(dadosSQL.getBoolean("faturar"));
        } catch (Exception ignored) {
        }

        try {
            obj.setRenovarAutomaticamenteUtilizandoValorBaseContrato(dadosSQL.getBoolean("renovarAutomaticamenteUtilizandoValorBaseContrato"));
        } catch (Exception ignored){}

        try {
            obj.setPermitirTurmasVendasOnline(dadosSQL.getBoolean("permitirTurmasVendasOnline"));
        } catch (Exception ignored){}

        try {
            obj.setVideoSiteUrl(dadosSQL.getString("videoSiteUrl"));
        } catch (Exception ignored){}

        try {
            obj.setObservacaoSite(dadosSQL.getString("observacaoSite"));
        } catch (Exception ignored){}
        try {
            obj.setObrigatorioInformarCartaoCreditoVenda(dadosSQL.getBoolean("obrigatorioInformarCartaoCreditoVenda"));
        } catch (Exception ignored){}

        try {
            obj.setRenovarComDescontoTotem(dadosSQL.getBoolean("renovarComDescontoTotem"));
        } catch (Exception ignored){}
        try {
            obj.setAcessoRedeEmpresasEspecificas(dadosSQL.getBoolean("acessoRedeEmpresasEspecificas"));
        } catch (Exception ignored){}

        try {
            obj.setDiasBloquearCompraMesmoPlano(dadosSQL.getInt("diasBloquearCompraMesmoPlano"));
        } catch (Exception ignored){}

        try {
            obj.setPlanoDiferenteRenovacao(dadosSQL.getInt("planoDiferenteRenovacao"));
            obj.setModalidadesPlanoDiferenteRenovacao(dadosSQL.getString("modalidadesPlanoDiferenteRenovacao"));
            obj.setHorarioPlanoDiferenteRenovacao(dadosSQL.getInt("horarioPlanoDiferenteRenovacao"));
            obj.setDuracaoPlanoDiferenteRenovacao(dadosSQL.getInt("duracaoPlanoDiferenteRenovacao"));
            obj.setCondicaoPagPlanoDiferenteRenovacao(dadosSQL.getInt("condicaoPagPlanoDiferenteRenovacao"));
        } catch (Exception ignored){}

        try {
            obj.setPermitirCompartilharPLanoNoSite(dadosSQL.getBoolean("permitirCompartilharPLanoNoSite"));
        } catch (Exception ignored){}

        try {
            obj.setContratosEncerramDia(dadosSQL.getTimestamp("contratosEncerramDia"));
        } catch (Exception ignored){}

        return obj;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de
     * dados (
     * <code>ResultSet</code>) em um objeto da classe
     * <code>PlanoVO</code>.
     *
     * @return O objeto da classe <code>PlanoVO</code> com os dados devidamente
     * montados.
     */
    public static PlanoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        if (Uteis.NIVELMONTARDADOS_GESTAO_NOTAS == nivelMontarDados) {
            PlanoVO obj = new PlanoVO();
            obj.setNovoObj(false);
            obj.setCodigo(dadosSQL.getInt("codigo"));
            obj.setDescricao(dadosSQL.getString("descricao"));
            return obj;
        }

        PlanoVO obj = montarDadosBasico(dadosSQL, con);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA) {
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONSULTA_WS) {
            PlanoRecorrencia planoRecorrencia = new PlanoRecorrencia(con);
            obj.setPlanoRecorrencia(planoRecorrencia.consultarPlanoRecorrencia(obj.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            planoRecorrencia = null;
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_MINIMOS) {
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TELACONSULTA) {
            PlanoEmpresa planoEmpresa = new PlanoEmpresa(con);
            obj.setEmpresas(planoEmpresa.consultarTodas(obj.getCodigo()));
            montarDadosEmpresa(obj, Uteis.NIVELMONTARDADOS_TELACONSULTA, con);
            return obj;
        }
        montarDadosDescontoAntecipado(obj, con);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS) {
            PlanoDuracao planoDuracao = new PlanoDuracao(con);
            PlanoHorario planoHorario = new PlanoHorario(con);
            PlanoModalidade planoModalidade = new PlanoModalidade(con);
            obj.setPlanoDuracaoVOs(planoDuracao.consultarPlanoDuracaos(obj.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            obj.setPlanoHorarioVOs(planoHorario.consultarPlanoHorarios(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            obj.setPlanoModalidadeVOs(planoModalidade.consultarPlanoModalidades(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
            planoDuracao = null;
            planoHorario = null;
            planoModalidade = null;
            return obj;
        }
        PlanoDuracao planoDuracao = new PlanoDuracao(con);
        PlanoComposicao planoComposicao = new PlanoComposicao(con);
        PlanoCategoria planoCategoria = new PlanoCategoria(con);
        PlanoHorario planoHorario = new PlanoHorario(con);
        PlanoProdutoSugerido planoProdutoSugerido = new PlanoProdutoSugerido(con);
        PlanoRecorrencia planoRecorrencia = new PlanoRecorrencia(con);
        PlanoModalidade planoModalidade = new PlanoModalidade(con);
        PlanoExcecao planoExcecao = new PlanoExcecao(con);
        PlanoEmpresa planoEmpresa = new PlanoEmpresa(con);
        PlanoTipo planoTipo = new PlanoTipo(con);
        obj.setPlanoDuracaoVOs(planoDuracao.consultarPlanoDuracaos(obj.getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS));
        obj.setPlanoComposicaoVOs(planoComposicao.consultarPlanoComposicaos(obj.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        obj.setPlanoCategoriaVOs(planoCategoria.consultarPorPlano(obj.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        obj.setPlanoModalidadeVOs(planoModalidade.consultarPlanoModalidades(obj.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        obj.setPlanoHorarioVOs(planoHorario.consultarPlanoHorarios(obj.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        obj.setPlanoProdutoSugeridoVOs(planoProdutoSugerido.consultarPlanoProdutoSugeridos(obj.getCodigo(), true, Uteis.NIVELMONTARDADOS_TODOS));
        obj.setPlanoRecorrencia(planoRecorrencia.consultarPlanoRecorrencia(obj.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        obj.setPlanoExcecaoVOs(planoExcecao.consultarPorPlano(obj.getCodigo()));
        obj.setEmpresas(planoEmpresa.consultarTodas(obj.getCodigo()));
        obj.setPlanoTipo(planoTipo.consultarPorCodigo(dadosSQL.getInt("planotipo"), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS) {
            return obj;
        }
        planoDuracao = null;
        planoComposicao = null;
        planoHorario = null;
        planoRecorrencia = null;
        planoProdutoSugerido = null;
        planoModalidade = null;
        planoExcecao = null;
        planoTipo = null;
        montarDadosEmpresa(obj, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, con);
        montarDadosPadraoGerarParcelaContrato(obj, Uteis.NIVELMONTARDADOS_MINIMOS, con);
        montarDadosPlanoTextoPadrao(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        montarDadosReciboTextoPadrao(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        montarDadosRecorrencia(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>EmpresaVO</code> relacionado ao objeto
     * <code>PlanoVO</code>. Faz uso da chave primária da classe
     * <code>EmpresaVO</code> para realizar a consulta.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosEmpresa(PlanoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getEmpresa().getCodigo() == 0) {
            obj.setEmpresa(new EmpresaVO());
            return;
        }
        Empresa empresa = new Empresa(con);
        obj.setEmpresa(empresa.consultarPorChavePrimaria(obj.getEmpresa().getCodigo(), nivelMontarDados));
        empresa = null;
    }

    public static void montarDadosDescontoAntecipado(PlanoVO obj, Connection con) throws Exception {
        if (obj.getDescontoAntecipado().getCodigo() == 0) {
            obj.setDescontoAntecipado(new DescontoVO());
        } else {
            Desconto desconto = new Desconto(con);
            obj.setDescontoAntecipado(desconto.consultarPorChavePrimaria(obj.getDescontoAntecipado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            desconto = null;
        }
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>EmpresaVO</code> relacionado ao objeto
     * <code>PlanoVO</code>. Faz uso da chave primária da classe
     * <code>EmpresaVO</code> para realizar a consulta.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosPadraoGerarParcelaContrato(PlanoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getProdutoPadraoGerarParcelasContrato().getCodigo() == 0) {
            obj.setProdutoPadraoGerarParcelasContrato(new ProdutoVO());
            return;
        }
        Produto produto = new Produto(con);
        obj.setProdutoPadraoGerarParcelasContrato(produto.consultarPorChavePrimaria(obj.getProdutoPadraoGerarParcelasContrato().getCodigo(), nivelMontarDados));
        produto = null;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>EmpresaVO</code> relacionado ao objeto
     * <code>PlanoVO</code>. Faz uso da chave primária da classe
     * <code>EmpresaVO</code> para realizar a consulta.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosPlanoTextoPadrao(PlanoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getPlanoTextoPadrao().getCodigo() == 0) {
            obj.setPlanoTextoPadrao(new PlanoTextoPadraoVO());
            return;
        }
        PlanoTextoPadrao planoTextoPadrao = new PlanoTextoPadrao(con);
        obj.setPlanoTextoPadrao(planoTextoPadrao.consultarPorChavePrimaria(obj.getPlanoTextoPadrao().getCodigo(), nivelMontarDados));
        planoTextoPadrao = null;
    }

    public static void montarDadosReciboTextoPadrao(PlanoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getReciboTextoPadrao().getCodigo() == 0) {
            obj.setReciboTextoPadrao(new PlanoTextoPadraoVO());
            return;
        }
        PlanoTextoPadrao planoTextoPadrao = new PlanoTextoPadrao(con);
        obj.setReciboTextoPadrao(planoTextoPadrao.consultarPorChavePrimaria(obj.getReciboTextoPadrao().getCodigo(), nivelMontarDados));
        planoTextoPadrao = null;
    }

    public static void montarDadosRecorrencia(PlanoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getPlanoRecorrencia().getCodigo() == 0) {
            obj.setPlanoRecorrencia(new PlanoRecorrenciaVO());
            return;
        }
        PlanoRecorrencia planoRecorrencia = new PlanoRecorrencia(con);
        obj.setPlanoRecorrencia(planoRecorrencia.consultarPorChavePrimaria(obj.getPlanoRecorrencia().getCodigo(), nivelMontarDados));

        PlanoRecorrenciaParcela planoRecorrenciaParcela = new PlanoRecorrenciaParcela(con);
        planoRecorrenciaParcela.montarDados(obj.getPlanoRecorrencia());

        planoRecorrencia = null;
        planoRecorrenciaParcela = null;
    }

    private void iniciarVariaveis() {
        setPlanoDuracaos(new Hashtable());
        setPlanoCondicaoPagamentos(new Hashtable());
        setPlanoComposicaos(new Hashtable());
        setPlanoModalidades(new Hashtable());
        setPlanoHorarios(new Hashtable());
    }

    /**
     * Operação responsável por retornar um novo objeto da classe
     * <code>PlanoVO</code>.
     */
    public PlanoVO novo() throws Exception {
        incluir(getIdEntidade());
        return new PlanoVO();
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe
     * <code>PlanoVO</code>. Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>PlanoVO</code> que será gravado no
     *            banco de dados.
     * @throws Exception Caso haja problemas de conexão, restrição de acesso
     *                   ou validação de dados.
     */
    public void incluir(PlanoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(true);
            obj.setCodigo(new Integer(0));
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe
     * <code>PlanoVO</code>. Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>PlanoVO</code> que será gravado no
     *            banco de dados.
     * @throws Exception Caso haja problemas de conexão, restrição de acesso
     *                   ou validação de dados.
     */
    public void incluirSemCommit(PlanoVO obj) throws Exception {
        PlanoVO.validarDados(obj);
        incluir(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO Plano( descricao, empresa, vigenciaDe, vigenciaAte, "
                + "ingressoAte, produtoPadraoGerarParcelasContrato, planoTextoPadrao, bolsa, "
                + "permitepagarcomboleto, percentualMultaCancelamento, produtoTaxaCancelamento, "
                + "reciboTextoPadrao, prorataObrigatorio, diasVencimentoProrata, descontoAntecipado, "
                + "recorrencia, correspondencia_zd, comissao, NrVezesParcelarAdesao, "
                + "cobraradesaoseparada, site, permitirVendaPlanoSiteNoBalcao, descricaoencantamento, renovavelAutomaticamente, "
                + "quantidadeMaximaFrequencia,tipoFrequencia,vendaCreditoTreino, inicioMinimoContrato, "
                + "parcelamentoOperadora,renovarprodutoobrigatorio,diaDoMesDescontoBoletoPagAntecipado,porcentagemDescontoBoletoPagAntecipado, renovaranuidadeautomaticamente,cobrarProdutoSeparado,nrVezesParcelarProduto,"
                + "pontos, termoAceite, totem, convenioCobrancaPrivateLabel,restringirmarcacaoaulascoletivas,restringirqtdmarcacaoporpia, creditoTreinoNaoCumulativo, convidadosPorMes, planotipo, faturar, planoPersonal, "
                + "creditoSessao, renovarAutomaticamenteComDesconto, renovarAutomaticamenteApenasCondicaoPagamentoRecorrencia,permiteSituacaoAtestadoContrato, qtdSemanasAno, maximoVezesParcelar,"
                + "naorenovarcontratoparcelavencidaaberto,dividirmanutencaoparcelasea,aceitaDescontoExtra,restringeVendaPorCategoria, permitirVendaPlanoTotemNoBalcao,restringirqtdmarcacaopordiageral,"
                + "parcelamentoOperadoraDuracao, valordescontoboletopagantecipado, permitirAcessoRedeEmpresa, quantidadeCompartilhamentos, restringirMarcacaoAulaPorNrVezesModalidade,"
                + "renovarAutomaticamenteUtilizandoValorBaseContrato, renovarComDescontoTotem, bloquearrecompra) "
                + "VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? ,?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            int i = 1;
            sqlInserir.setString(i++, obj.getDescricao().trim());
            if (obj.getEmpresa().getCodigo() != 0) {
                sqlInserir.setInt(i++, obj.getEmpresa().getCodigo());
            } else {
                sqlInserir.setNull(i++, 0);
            }
            sqlInserir.setDate(i++, Uteis.getDataJDBC(obj.getVigenciaDe()));
            sqlInserir.setDate(i++, Uteis.getDataJDBC(obj.getVigenciaAte()));
            sqlInserir.setDate(i++, Uteis.getDataJDBC(obj.getIngressoAte()));
            sqlInserir.setInt(i++, obj.getProdutoPadraoGerarParcelasContrato().getCodigo());
            sqlInserir.setInt(i++, obj.getPlanoTextoPadrao().getCodigo());
            sqlInserir.setBoolean(i++, obj.getBolsa());
            sqlInserir.setBoolean(i++, obj.getPermitePagarComBoleto());
            sqlInserir.setDouble(i++, obj.getPercentualMultaCancelamento());
            sqlInserir.setInt(i++, obj.getProdutoTaxaCancelamento().getCodigo());
            if (obj.getReciboTextoPadrao().getCodigo() != 0) {
                sqlInserir.setInt(i++, obj.getReciboTextoPadrao().getCodigo());
            } else {
                sqlInserir.setNull(i++, 0);
            }
            sqlInserir.setBoolean(i++, obj.isProrataObrigatorio());
            sqlInserir.setString(i++, obj.getDiasVencimentoProrata());
            if (obj.getDescontoAntecipado().getCodigo() != 0) {
                sqlInserir.setInt(i++, obj.getDescontoAntecipado().getCodigo());
            } else {
                sqlInserir.setNull(i++, 0);
            }
            sqlInserir.setBoolean(i++, obj.getRegimeRecorrencia());
            sqlInserir.setString(i++, obj.getCorrespondenciaZD());
            sqlInserir.setBoolean(i++, obj.getComissao());
            resolveIntegerNull(sqlInserir, i++, obj.getNrVezesParcelarAdesao());
            sqlInserir.setBoolean(i++, obj.getCobrarAdesaoSeparada());
            sqlInserir.setBoolean(i++, obj.getSite());
            sqlInserir.setBoolean(i++, obj.getPermitirVendaPlanoSiteNoBalcao());
            sqlInserir.setString(i++, obj.getDescricaoEncantamento());
            sqlInserir.setBoolean(i++, obj.getRenovavelAutomaticamente());
            if (obj.getQuantidadeMaximaFrequencia() != null) {
                sqlInserir.setInt(i++, obj.getQuantidadeMaximaFrequencia());
                sqlInserir.setInt(i++, TipoFrequenciaEnum.SEMANAL.getCodigo());
            } else {
                sqlInserir.setNull(i++, Types.NULL);
                sqlInserir.setNull(i++, Types.NULL);
            }
            sqlInserir.setBoolean(i++, obj.isVendaCreditoTreino());

            if (obj.getInicioMinimoContrato() == null) {
                sqlInserir.setNull(i++, Types.DATE);
            } else {
                sqlInserir.setDate(i++, Uteis.getDataJDBC(obj.getInicioMinimoContrato()));
            }
            sqlInserir.setBoolean(i++, obj.getParcelamentoOperadora());
            sqlInserir.setBoolean(i++, obj.getRenovarProdutoObrigatorio());
            sqlInserir.setInt(i++, obj.getDiaDoMesDescontoBoletoPagAntecipado());
            sqlInserir.setDouble(i++, obj.getPorcentagemDescontoBoletoPagAntecipado());
            sqlInserir.setBoolean(i++, obj.isRenovarAnuidadeAutomaticamente());
            sqlInserir.setBoolean(i++, obj.getCobrarProdutoSeparado());
            resolveIntegerNull(sqlInserir, i++, obj.getNrVezesParcelarProduto());
            sqlInserir.setInt(i++, obj.getPontos());
            resolveIntegerNull(sqlInserir, i++, obj.getTermoAceite().getCodigo());
            sqlInserir.setBoolean(i++, obj.getTotem());
            sqlInserir.setInt(i++, obj.getConvenioCobrancaPrivateLabel().getCodigo());
            sqlInserir.setBoolean(i++, obj.isRestringirMarcacaoAulasColetivas());
            sqlInserir.setInt(i++, obj.getRestringirQtdMarcacaoPorDia());
            sqlInserir.setBoolean(i++, obj.isCreditoTreinoNaoCumulativo());
            sqlInserir.setInt(i++, obj.getConvidadosPorMes());
            if (obj.getPlanoTipo().getCodigo() == null) {
                sqlInserir.setNull(i++, Types.NULL);
            } else {
                sqlInserir.setInt(i++, obj.getPlanoTipo().getCodigo());
            }
            sqlInserir.setBoolean(i++, obj.isFaturar());
            sqlInserir.setBoolean(i++, obj.isPlanoPersonal());
            sqlInserir.setBoolean(i++, obj.isCreditoSessao());
            sqlInserir.setBoolean(i++, obj.isRenovarAutomaticamenteComDesconto());
            sqlInserir.setBoolean(i++, obj.isRenovarAutomaticamenteApenasCondicaoPagamentoRecorrencia());
            sqlInserir.setBoolean(i++, obj.getPermiteSituacaoAtestadoContrato());
            sqlInserir.setInt(i++, obj.getQtdSemanasAno());
            sqlInserir.setInt(i++, obj.getMaximoVezesParcelar());
            sqlInserir.setBoolean(i++, obj.isNaoRenovarContratoParcelaVencidaAberto());
            sqlInserir.setBoolean(i++, obj.getDividirManutencaoParcelasEA());
            sqlInserir.setBoolean(i++, obj.getAceitaDescontoExtra());
            sqlInserir.setBoolean(i++, obj.getRestringeVendaPorCategoria());
            sqlInserir.setBoolean(i++, obj.getPermitirVendaPlanoTotemNoBalcao());
            sqlInserir.setInt(i++, obj.getRestringirQtdMarcacaoPorDiaGeral());
            sqlInserir.setBoolean(i++, obj.isParcelamentoOperadoraDuracao());
            sqlInserir.setDouble(i++, obj.getValorDescontoBoletoPagAntecipado());
            sqlInserir.setBoolean(i++, obj.isPermitirAcessoRedeEmpresa());
            sqlInserir.setInt(i++, obj.getQuantidadeCompartilhamentos());
            sqlInserir.setBoolean(i++, obj.getRestringirMarcacaoAulaPorNrVezesModalidade());
            sqlInserir.setBoolean(i++, obj.getRenovarAutomaticamenteUtilizandoValorBaseContrato());
            sqlInserir.setBoolean(i++, obj.isRenovarComDescontoTotem());
            sqlInserir.setBoolean(i++, obj.getBloquearRecompra());
            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);


        PlanoDuracao planoDuracaoDAO = new PlanoDuracao(con);
        PlanoRecorrencia planoRecorrenciaDAO = new PlanoRecorrencia(con);
        PlanoComposicao planoComposicaoDAO = new PlanoComposicao(con);
        PlanoCategoria planoCategoriaDAO = new PlanoCategoria(con);
        PlanoModalidade planoModalidadeDAO = new PlanoModalidade(con);
        PlanoHorario planoHorarioDAO = new PlanoHorario(con);
        PlanoProdutoSugerido planoProdutoSugeridoDAO = new PlanoProdutoSugerido(con);
        PlanoExcecao planoExcecaoDAO = new PlanoExcecao(con);
        PlanoEmpresa planoEmpresaDAO = new PlanoEmpresa(con);


        if (!obj.getRegimeRecorrencia()) {
            planoDuracaoDAO.incluirPlanoDuracaos(obj.getCodigo(), obj.getPlanoDuracaoVOs());
        } else {
            obj.getPlanoRecorrencia().setPlano(obj.getCodigo());
            planoRecorrenciaDAO.incluir(obj.getPlanoRecorrencia());
        }
        planoComposicaoDAO.incluirPlanoComposicaos(obj.getCodigo(), obj.getPlanoComposicaoVOs());
        if(obj.getRestringeVendaPorCategoria()) {
            planoCategoriaDAO.incluirPlanosCategorias(obj.getCodigo(), obj.getPlanoCategoriaVOs());
        }
        planoModalidadeDAO.incluirPlanoModalidades(obj.getCodigo(), obj.getPlanoModalidadeVOs());
        planoHorarioDAO.incluirPlanoHorarios(obj.getCodigo(), obj.getPlanoHorarioVOs());
        planoProdutoSugeridoDAO.incluirPlanoProdutoSugeridos(obj.getCodigo(), obj.getPlanoProdutoSugeridoVOs());
        planoExcecaoDAO.incluirOuAlterarPlanoExcecoes(obj);
        if (obj.getEmpresas().size() > 0) {
            planoEmpresaDAO.incluirEmpresasPlano(obj);
        }

        planoDuracaoDAO = null;
        planoRecorrenciaDAO = null;
        planoComposicaoDAO = null;
        planoModalidadeDAO = null;
        planoHorarioDAO = null;
        planoProdutoSugeridoDAO = null;
        planoExcecaoDAO = null;
        planoEmpresaDAO = null;
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe
     * <code>PlanoVO</code>. Sempre utiliza a chave primária da classe como
     * atributo para localização do registro a ser alterado. Primeiramente
     * valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>alterar</code> da superclasse.
     *
     * @param obj Objeto da classe <code>PlanoVO</code> que será alterada no
     *            banco de dados.
     * @throws Exception Caso haja problemas de conexão, restrição de acesso
     *                   ou validação de dados.
     */
    public void alterar(PlanoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public PlanoVO consultarPorDescricao(String descricao, int nivelMontarDados) throws Exception {
        String sql = "select * from plano where upper(descricao) = ?";
        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            pst.setString(1, descricao.toUpperCase());
            try (ResultSet rs = pst.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs, nivelMontarDados, con);
                }
            }
        }
        return null;
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe
     * <code>PlanoVO</code>. Sempre utiliza a chave primária da classe como
     * atributo para localização do registro a ser alterado. Primeiramente
     * valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>alterar</code> da superclasse.
     *
     * @param obj Objeto da classe <code>PlanoVO</code> que será alterada no
     *            banco de dados.
     * @throws Exception Caso haja problemas de conexão, restrição de acesso
     *                   ou validação de dados.
     */
    public void alterarSemCommit(PlanoVO obj) throws Exception {
        PlanoVO.validarDados(obj);
        alterar(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE Plano set descricao=?, empresa=?, vigenciaDe=?, vigenciaAte=?, "
                + "ingressoAte=?, produtoPadraoGerarParcelasContrato=?, planoTextoPadrao=? , bolsa=?, "
                + "permitepagarcomboleto=?, percentualMultaCancelamento=?, produtoTaxaCancelamento=?, "
                + "reciboTextoPadrao=?, prorataObrigatorio=?, diasVencimentoProrata=?, "
                + "descontoAntecipado=? , recorrencia=?, correspondencia_zd = ?, comissao = ?, "
                + "cobraradesaoseparada=?, NrVezesParcelarAdesao = ?, site = ?, permitirVendaPlanoSiteNoBalcao=?, descricaoencantamento = ?, renovavelAutomaticamente = ?, \n"
                + "quantidadeMaximaFrequencia=?, tipoFrequencia=?, vendaCreditoTreino=?, inicioMinimoContrato = ?, parcelamentoOperadora = ?, renovarprodutoobrigatorio = ?, diaDoMesDescontoBoletoPagAntecipado=?, \n"
                + "porcentagemDescontoBoletoPagAntecipado = ?, renovaranuidadeautomaticamente = ?, cobrarProdutoSeparado=?, nrVezesParcelarProduto=?, pontos = ?, \n"
                + "termoAceite = ?, totem = ?, convenioCobrancaPrivateLabel = ? ,restringirmarcacaoaulascoletivas = ? ,restringirqtdmarcacaoporpia = ?, creditoTreinoNaoCumulativo = ?, \n"
                + "permitiracessosomentenaempresavendeucontrato = ?, convidadosPorMes = ?, planotipo = ?, faturar = ?, planoPersonal = ?, creditoSessao = ?, renovarAutomaticamenteComDesconto = ?, apresentarvendarapida = ?, \n"
                + "renovarAutomaticamenteApenasCondicaoPagamentoRecorrencia = ?, permiteSituacaoAtestadoContrato = ?, qtdSemanasAno = ?, maximoVezesParcelar = ?, naorenovarcontratoparcelavencidaaberto = ?, \n"
                + "dividirmanutencaoparcelasea=?, aceitaDescontoExtra=?,restringeVendaPorCategoria=?, permitirVendaPlanoTotemNoBalcao = ?, restringirqtdmarcacaopordiageral=?, parcelamentoOperadoraDuracao = ?, \n"
                + "valordescontoboletopagantecipado=?, permitirAcessoRedeEmpresa = ?, quantidadeCompartilhamentos = ?, restringirMarcacaoAulaPorNrVezesModalidade = ?, \n"
                + "RenovarAutomaticamenteUtilizandoValorBaseContrato = ?, renovarComDescontoTotem = ?, bloquearRecompra = ? \n"
                + "WHERE codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            int i = 1;
            sqlAlterar.setString(i++, obj.getDescricao().trim());
            if (obj.getEmpresa().getCodigo() != 0) {
                sqlAlterar.setInt(i++, obj.getEmpresa().getCodigo());
            } else {
                sqlAlterar.setNull(i++, 0);
            }
            sqlAlterar.setDate(i++, Uteis.getDataJDBC(obj.getVigenciaDe()));
            sqlAlterar.setDate(i++, Uteis.getDataJDBC(obj.getVigenciaAte()));
            sqlAlterar.setDate(i++, Uteis.getDataJDBC(obj.getIngressoAte()));
            sqlAlterar.setInt(i++, obj.getProdutoPadraoGerarParcelasContrato().getCodigo());
            sqlAlterar.setInt(i++, obj.getPlanoTextoPadrao().getCodigo());
            sqlAlterar.setBoolean(i++, obj.getBolsa());
            sqlAlterar.setBoolean(i++, obj.getPermitePagarComBoleto());
            sqlAlterar.setDouble(i++, obj.getPercentualMultaCancelamento());
            sqlAlterar.setInt(i++, obj.getProdutoTaxaCancelamento().getCodigo());
            if (obj.getReciboTextoPadrao().getCodigo() != 0) {
                sqlAlterar.setInt(i++, obj.getReciboTextoPadrao().getCodigo());
            } else {
                sqlAlterar.setNull(i++, 0);
            }
            sqlAlterar.setBoolean(i++, obj.isProrataObrigatorio());
            sqlAlterar.setString(i++, obj.getDiasVencimentoProrata());
            if (obj.getDescontoAntecipado().getCodigo() != 0) {
                sqlAlterar.setInt(i++, obj.getDescontoAntecipado().getCodigo());
            } else {
                sqlAlterar.setNull(i++, 0);
            }
            sqlAlterar.setBoolean(i++, obj.getRegimeRecorrencia());
            sqlAlterar.setString(i++, obj.getCorrespondenciaZD());
            sqlAlterar.setBoolean(i++, obj.getComissao());

            sqlAlterar.setBoolean(i++, obj.getCobrarAdesaoSeparada());
            resolveIntegerNull(sqlAlterar, i++, obj.getNrVezesParcelarAdesao());

            sqlAlterar.setBoolean(i++, obj.getSite());
            sqlAlterar.setBoolean(i++, obj.getPermitirVendaPlanoSiteNoBalcao());
            sqlAlterar.setString(i++, obj.getDescricaoEncantamento());
            sqlAlterar.setBoolean(i++, obj.getRenovavelAutomaticamente());

            if (obj.getQuantidadeMaximaFrequencia() != null) {
                sqlAlterar.setInt(i++, obj.getQuantidadeMaximaFrequencia());
                sqlAlterar.setInt(i++, TipoFrequenciaEnum.SEMANAL.getCodigo());
            } else {
                sqlAlterar.setNull(i++, Types.NULL);
                sqlAlterar.setNull(i++, Types.NULL);
            }
            sqlAlterar.setBoolean(i++, obj.isVendaCreditoTreino());
            if (obj.getInicioMinimoContrato() == null) {
                sqlAlterar.setNull(i++, Types.DATE);
            } else {
                sqlAlterar.setDate(i++, Uteis.getDataJDBC(obj.getInicioMinimoContrato()));
            }
            sqlAlterar.setBoolean(i++, obj.getParcelamentoOperadora());
            sqlAlterar.setBoolean(i++, obj.getRenovarProdutoObrigatorio());
            sqlAlterar.setInt(i++, obj.getDiaDoMesDescontoBoletoPagAntecipado());
            sqlAlterar.setDouble(i++, obj.getPorcentagemDescontoBoletoPagAntecipado());
            sqlAlterar.setBoolean(i++, obj.isRenovarAnuidadeAutomaticamente());
            sqlAlterar.setBoolean(i++, obj.getCobrarProdutoSeparado());
            resolveIntegerNull(sqlAlterar, i++, obj.getNrVezesParcelarProduto());
            sqlAlterar.setInt(i++, obj.getPontos());
            resolveIntegerNull(sqlAlterar, i++, obj.getTermoAceite().getCodigo());
            sqlAlterar.setBoolean(i++, obj.getTotem());
            sqlAlterar.setInt(i++, obj.getConvenioCobrancaPrivateLabel().getCodigo());
            sqlAlterar.setBoolean(i++, obj.isRestringirMarcacaoAulasColetivas());
            sqlAlterar.setInt(i++, obj.getRestringirQtdMarcacaoPorDia());
            sqlAlterar.setBoolean(i++, obj.isCreditoTreinoNaoCumulativo());
            sqlAlterar.setBoolean(i++, obj.isPermitirAcessoSomenteNaEmpresaVendeuContrato());
            sqlAlterar.setInt(i++, obj.getConvidadosPorMes());
            if (obj.getPlanoTipo().getCodigo() == null) {
                sqlAlterar.setNull(i++, Types.NULL);
            } else {
                sqlAlterar.setInt(i++, obj.getPlanoTipo().getCodigo());
            }
            sqlAlterar.setBoolean(i++, obj.isFaturar());
            sqlAlterar.setBoolean(i++, obj.isPlanoPersonal());
            sqlAlterar.setBoolean(i++, obj.isCreditoSessao());
            sqlAlterar.setBoolean(i++, obj.isRenovarAutomaticamenteComDesconto());
            sqlAlterar.setBoolean(i++, obj.isApresentaVendaRapida());
            sqlAlterar.setBoolean(i++, obj.isRenovarAutomaticamenteApenasCondicaoPagamentoRecorrencia());
            sqlAlterar.setBoolean(i++, obj.getPermiteSituacaoAtestadoContrato());
            sqlAlterar.setInt(i++, obj.getQtdSemanasAno());
            sqlAlterar.setInt(i++, obj.getMaximoVezesParcelar());
            sqlAlterar.setBoolean(i++, obj.isNaoRenovarContratoParcelaVencidaAberto());
            sqlAlterar.setBoolean(i++, obj.getDividirManutencaoParcelasEA());
            sqlAlterar.setBoolean(i++, obj.getAceitaDescontoExtra());
            sqlAlterar.setBoolean(i++, obj.getRestringeVendaPorCategoria());
            sqlAlterar.setBoolean(i++, obj.getPermitirVendaPlanoTotemNoBalcao());
            sqlAlterar.setInt(i++, obj.getRestringirQtdMarcacaoPorDiaGeral());
            sqlAlterar.setBoolean(i++, obj.isParcelamentoOperadoraDuracao());
            sqlAlterar.setDouble(i++, obj.getValorDescontoBoletoPagAntecipado());
            sqlAlterar.setBoolean(i++, obj.isPermitirAcessoRedeEmpresa());
            sqlAlterar.setInt(i++, obj.getQuantidadeCompartilhamentos());
            sqlAlterar.setBoolean(i++, obj.getRestringirMarcacaoAulaPorNrVezesModalidade());
            sqlAlterar.setBoolean(i++, obj.getRenovarAutomaticamenteUtilizandoValorBaseContrato());
            sqlAlterar.setBoolean(i++, obj.isRenovarComDescontoTotem());
            sqlAlterar.setBoolean(i++, obj.getBloquearRecompra());
            sqlAlterar.setInt(i++, obj.getCodigo());
            sqlAlterar.execute();
        }

        getFacade().getPlanoComposicao().alterarPlanoComposicaos(obj.getCodigo(), obj.getPlanoComposicaoVOs());
        if(obj.getRestringeVendaPorCategoria()) {
            getFacade().getPlanoCategoria().alterarPorPlano(obj.getCodigo(), obj.getPlanoCategoriaVOs());
        } else {
            getFacade().getPlanoCategoria().excluirPorPlano(obj.getCodigo());
        }
        getFacade().getPlanoModalidade().alterarPlanoModalidades(obj.getCodigo(), obj.getPlanoModalidadeVOs());
        getFacade().getPlanoHorario().alterarPlanoHorarios(obj.getCodigo(), obj.getPlanoHorarioVOs());
        getFacade().getPlanoProdutoSugerido().alterarPlanoProdutoSugeridos(obj.getCodigo(), obj.getPlanoProdutoSugeridoVOs());
        getFacade().getPlanoDuracao().alterarPlanoDuracaos(obj, obj.getPlanoDuracaoVOs());
        if (!obj.getRegimeRecorrencia()) {
            getFacade().getContrato().alterarRenovarAutomaticamenteContratosAtivos(obj);
        } else {
            if (obj.getPlanoRecorrencia().getCodigo() == null || obj.getPlanoRecorrencia().getCodigo() == 0) {
                obj.getPlanoRecorrencia().setPlano(obj.getCodigo());
                getFacade().getPlanoRecorrencia().incluir(obj.getPlanoRecorrencia());
            } else {
                obj.getPlanoRecorrencia().setPlano(obj.getCodigo());
                getFacade().getPlanoRecorrencia().alterar(obj.getPlanoRecorrencia());
            }
            getFacade().getContratoRecorrencia().alterarRenovacaoAutomaticaContratoRecorrencia(obj, obj.getPlanoRecorrencia().getRenovavelAutomaticamente());
        }
        getFacade().getPlanoExcecao().incluirOuAlterarPlanoExcecoes(obj);
        getFacade().getPlanoEmpresa().atualizarEmpresasPlano(obj);
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe
     * <code>PlanoVO</code>. Sempre localiza o registro a ser excluído através
     * da chave primária da entidade. Primeiramente verifica a conexão com o
     * banco de dados e a permissão do usuário para realizar esta operacão na
     * entidade. Isto, através da operação
     * <code>excluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>PlanoVO</code> que será removido no
     *            banco de dados.
     * @throws Exception Caso haja problemas de conexão ou restrição de
     *                   acesso.
     */
    public void excluir(PlanoVO obj) throws Exception {
        try {
            PlanoRecorrenciaParcela planoRecorrenciaParcelaDAO = new PlanoRecorrenciaParcela(con);
            PlanoComposicao planoComposicaoDAO = new PlanoComposicao(con);
            PlanoCategoria planoCategoriaDAO = new PlanoCategoria(con);
            PlanoHorario planoHorarioDAO = new PlanoHorario(con);
            PlanoRecorrencia planoRecorrenciaDAO = new PlanoRecorrencia(con);
            con.setAutoCommit(false);
            excluir(getIdEntidade());
            if (obj.getRegimeRecorrencia()) {
                planoRecorrenciaParcelaDAO.excluirPorPlanoRecorrencia(obj.getPlanoRecorrencia().getCodigo());
                obj.getPlanoRecorrencia().setPlano(obj.getCodigo());
                planoRecorrenciaDAO.excluir(obj.getPlanoRecorrencia());
            }
            planoComposicaoDAO.excluirPlanoComposicaos(obj.getCodigo());
            planoCategoriaDAO.excluirPorPlano(obj.getCodigo());
            planoHorarioDAO.excluirPlanoHorarios(obj.getCodigo());
            Iterator i = obj.getPlanoModalidadeVOs().iterator();
            while (i.hasNext()) {
                PlanoModalidadeVO planoModalidade = (PlanoModalidadeVO) i.next();
                getFacade().getPlanoModalidadeVezesSemana().excluirPlanoModalidadeVezesSemana(planoModalidade.getCodigo());
            }
            Iterator j = obj.getPlanoDuracaoVOs().iterator();

            while (j.hasNext()) {
                PlanoDuracaoVO planoDuracao = (PlanoDuracaoVO) j.next();
                getFacade().getPlanoCondicaoPagamento().excluirPlanoCondicaoPagamentos(planoDuracao.getCodigo());
                getFacade().getPlanoDuracaoCreditoTreino().excluir(planoDuracao);
                getFacade().getPlanoDuracao().excluir(planoDuracao);
            }
            getFacade().getPlanoModalidade().excluirPlanoModalidades(obj.getCodigo());
            getFacade().getPlanoProdutoSugerido().excluirPlanoProdutoSugeridos(obj.getCodigo());
            getFacade().getPlanoEmpresa().excluirEmpresasPlano(obj);

            String sql = "DELETE FROM Plano WHERE ((codigo = ?))";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo());
                sqlExcluir.execute();
            }

            con.commit();
            planoRecorrenciaParcelaDAO = null;
            planoComposicaoDAO = null;
            planoHorarioDAO = null;
            planoRecorrenciaDAO = null;
            con.setAutoCommit(false);
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public PlanoDuracaoVO calculoDuracaoMensal(PlanoDuracaoVO planoDuracao, Double valorModalidades) throws Exception, ConsistirException {
        if (valorModalidades <= 0.0 && planoDuracao.getValorDesejadoMensal() > 0.0) {
            throw new Exception("O campo VALOR MENSAL DE REFRÊNCIA deve ser informado.");
        }
        PlanoDuracaoVO.validarDados(planoDuracao);
        Double valorMensal = planoDuracao.getValorDesejadoMensal() * planoDuracao.getNumeroMeses();
        Double valorModalidade = planoDuracao.getNumeroMeses() * valorModalidades;
        if (planoDuracao.getValorDesejadoMensal() != 0.0) {
            if (planoDuracao.getValorDesejadoMensal() >= valorModalidades) {
                acrescimo(planoDuracao, valorMensal, valorModalidade);
            } else if (planoDuracao.getValorDesejadoMensal() < valorModalidades) {
                reducao(planoDuracao, valorMensal, valorModalidade);
            }
            planoDuracao.setValorDesejadoParcela(Uteis.arredondarForcando2CasasDecimais(((planoDuracao.getValorDesejadoMensal() * planoDuracao.getNumeroMeses()) / planoDuracao.getNrMaximoParcelasCondPagamento())));
            calculoTipoValor(planoDuracao);
        } else {
            throw new Exception("O campo VALOR DESEJADO MENSAL deve ser informado.");
        }
        return planoDuracao;


    }

    public PlanoDuracaoVO calculoDuracaoParcela(PlanoDuracaoVO planoDuracao, Double valorModalidades) throws Exception, ConsistirException {
        if (valorModalidades <= 0.0 && planoDuracao.getValorDesejadoParcela() > 0.0) {
            throw new Exception("O campo VALOR MENSAL DE REFRÊNCIA deve ser informado.");
        }
        PlanoDuracaoVO.validarDados(planoDuracao);
        Double valorParcela = (planoDuracao.getValorDesejadoParcela() * planoDuracao.getNrMaximoParcelasCondPagamento());
        Double valorModalidade = (planoDuracao.getNumeroMeses() * valorModalidades);
        if (planoDuracao.getValorDesejadoParcela() != 0.0) {
            if (valorParcela >= valorModalidade) {
                acrescimo(planoDuracao, valorParcela, valorModalidade);
            } else if (valorParcela < valorModalidade) {
                reducao(planoDuracao, valorParcela, valorModalidade);
            }
            planoDuracao.setValorDesejadoMensal(Uteis.arredondarForcando2CasasDecimais(((planoDuracao.getValorDesejadoParcela() * planoDuracao.getNrMaximoParcelasCondPagamento()) / planoDuracao.getNumeroMeses())));
            calculoTipoValor(planoDuracao);
        } else {
            throw new Exception("O campo VALOR DESEJADO PARCELA deve ser informado.");
        }


        return planoDuracao;
    }

    public PlanoDuracaoVO calculoDuracao(PlanoDuracaoVO planoDuracao, Double valorModalidades) throws Exception, ConsistirException {

        PlanoDuracaoVO.validarDados(planoDuracao);

        if (planoDuracao.getValorDesejado() == 0.0) {
            throw new Exception("O campo VALOR DESEJADO FORMA de DESCONTO deve ser informado.");
        }
        if (planoDuracao.getTipoOperacao() == null) {
            throw new Exception("O campo TIPO OPERAÇÃO deve ser informado.");
        }
        if (planoDuracao.getTipoOperacao().equals("AC")) {
            if (planoDuracao.getTipoValor().equals("PD")) {
                planoDuracao.setPercentualDesconto(planoDuracao.getValorDesejado());
                planoDuracao.setValorDesejadoMensal((valorModalidades + (valorModalidades * (planoDuracao.getPercentualDesconto() / 100))));
                planoDuracao.setValorDesejadoParcela((((planoDuracao.getNumeroMeses() * planoDuracao.getValorDesejadoMensal()) / planoDuracao.getNrMaximoParcelasCondPagamento())));
            } else {
                planoDuracao.setValorEspecifico(planoDuracao.getValorDesejado());
                planoDuracao.setValorDesejadoMensal((valorModalidades + planoDuracao.getValorEspecifico()));
                planoDuracao.setValorDesejadoParcela((((planoDuracao.getNumeroMeses() * planoDuracao.getValorDesejadoMensal()) / planoDuracao.getNrMaximoParcelasCondPagamento())));
            }

        } else if (planoDuracao.getTipoOperacao().equals("RE")) {

            if (planoDuracao.getTipoValor().equals("PD")) {
                planoDuracao.setPercentualDesconto(planoDuracao.getValorDesejado());
                planoDuracao.setValorDesejadoMensal((valorModalidades - (valorModalidades * (planoDuracao.getPercentualDesconto() / 100))));
                planoDuracao.setValorDesejadoParcela((((planoDuracao.getNumeroMeses() * planoDuracao.getValorDesejadoMensal()) / planoDuracao.getNrMaximoParcelasCondPagamento())));
            } else {
                planoDuracao.setValorEspecifico(planoDuracao.getValorDesejado());
                planoDuracao.setValorDesejadoMensal((valorModalidades - planoDuracao.getValorEspecifico()));
                planoDuracao.setValorDesejadoParcela((((planoDuracao.getNumeroMeses() * planoDuracao.getValorDesejadoMensal()) / planoDuracao.getNrMaximoParcelasCondPagamento())));
            }
        }

        return planoDuracao;
    }

    public void acrescimo(PlanoDuracaoVO planoDuracao, Double valor, Double valorModalidades) {
        double porcentagem = 0.0;
        planoDuracao.setTipoOperacao("AC");
        if (planoDuracao.getTipoValor().equals("PD")) {
            planoDuracao.setTipoValor("PD");
            porcentagem = valor - valorModalidades;
            planoDuracao.setPercentualDesconto(((porcentagem * 100) / valorModalidades));
        } else {
            planoDuracao.setTipoValor("VE");
            planoDuracao.setValorEspecifico((valor - valorModalidades) / planoDuracao.getNumeroMeses());
        }
    }

    public void reducao(PlanoDuracaoVO planoDuracao, Double valor, Double valorModalidades) {
        double porcentagem = 0.0;
        planoDuracao.setTipoOperacao("RE");
        if (planoDuracao.getTipoValor().equals("PD")) {
            planoDuracao.setTipoValor("PD");
            porcentagem = valorModalidades - valor;
            planoDuracao.setPercentualDesconto(((porcentagem * 100) / valorModalidades));
        } else {
            planoDuracao.setTipoValor("VE");
            planoDuracao.setValorEspecifico((valorModalidades - valor) / planoDuracao.getNumeroMeses());
        }
    }

    public void calculoTipoValor(PlanoDuracaoVO planoDuracao) {
        if (planoDuracao.getTipoValor().equals("VE")) {
            planoDuracao.setValorDesejado(Uteis.arredondarForcando2CasasDecimais(planoDuracao.getValorEspecifico()));
        } else {
            planoDuracao.setValorDesejado((planoDuracao.getPercentualDesconto()));
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Plano</code> através do valor do atributo
     * <code>Date vigenciaAte</code>. Retorna os objetos com valores pertecentes
     * ao período informado por parâmetro. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     *                        usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>PlanoVO</code>
     * resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de
     *                   acesso.
     */
    public PlanoVO consultarPorCodigoContrato(Integer codigo, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT \n");
        sql.append("pl.*, \n");
        sql.append("cd.numeromeses as cd_numeromeses \n");
        sql.append("FROM contrato co \n");
        sql.append("INNER JOIN contratoduracao cd on cd.contrato = co.codigo \n");
        sql.append("INNER JOIN plano pl on pl.codigo = co.plano \n");
        sql.append("WHERE co.codigo = ").append(codigo).append(" \n");
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                if (!rs.next()) {
                    return new PlanoVO();
                }
                PlanoVO planoVO = montarDados(rs, nivelMontarDados, this.con);
                if (planoVO.getParcelamentoOperadora() != null &&
                        planoVO.getParcelamentoOperadora() &&
                        planoVO.isParcelamentoOperadoraDuracao()) {
                    //limitar no max de vezes parcelar
                    Integer cd_numeromeses = rs.getInt("cd_numeromeses");
                    if (cd_numeromeses < planoVO.getMaximoVezesParcelar()) {
                        planoVO.setMaximoVezesParcelar(cd_numeromeses);
                    }
                }
                return planoVO;
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Plano</code> através do valor do atributo
     * <code>Date vigenciaAte</code>. Retorna os objetos com valores pertecentes
     * ao período informado por parâmetro. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     *                        usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>PlanoVO</code>
     * resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de
     *                   acesso.
     */
    public List consultarPorComposicao(Integer codigo, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        Date dataHoje = Calendario.getDataComHoraZerada(Calendario.hoje());
        String sqlStr = "SELECT Plano.* FROM PlanoComposicao, Composicao, Plano WHERE Plano.codigo =  PlanoComposicao.plano and PlanoComposicao.composicao = Composicao.codigo "
                + " and Composicao.codigo =" + codigo
                + " and '" + Uteis.getDataJDBC(dataHoje) + "' <= Plano.ingressoAte and '" + Uteis.getDataJDBC(dataHoje) + "' >= Plano.vigenciaDe"
                + " ORDER BY Composicao.descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Plano</code> através do valor do atributo
     * <code>Date vigenciaAte</code>. Retorna os objetos com valores pertecentes
     * ao período informado por parâmetro. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     *                        usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>PlanoVO</code>
     * resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de
     *                   acesso.
     */
    public List consultarPorVigenciaAte(Date prmIni, Date prmFim, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr = "SELECT * FROM Plano WHERE ((vigenciaAte >= '" + Uteis.getDataJDBC(prmIni) + "') and (vigenciaAte <= '" + Uteis.getDataJDBC(prmFim) + "'))  ORDER BY vigenciaAte";
        } else {
            sqlStr = "SELECT * FROM Plano WHERE ((vigenciaAte >= '" + Uteis.getDataJDBC(prmIni) + "') and (vigenciaAte <= '" + Uteis.getDataJDBC(prmFim) + "'))  and empresa = " + empresa.intValue() + "  ORDER BY vigenciaAte";
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Plano</code> através do valor do atributo
     * <code>Date vigenciaDe</code>. Retorna os objetos com valores pertecentes
     * ao período informado por parâmetro. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     *                        usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>PlanoVO</code>
     * resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de
     *                   acesso.
     */
    public List consultarPorVigenciaDe(Date prmIni, Date prmFim, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr = "SELECT * FROM Plano WHERE ((vigenciaDe >= '" + Uteis.getDataJDBC(prmIni) + "') and (vigenciaDe <= '" + Uteis.getDataJDBC(prmFim) + "'))  ORDER BY vigenciaDe";
        } else {
            sqlStr = "SELECT * FROM Plano WHERE ((vigenciaDe >= '" + Uteis.getDataJDBC(prmIni) + "') and (vigenciaDe <= '" + Uteis.getDataJDBC(prmFim) + "')) and empresa = " + empresa.intValue() + "  ORDER BY vigenciaDe";
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Plano</code> através do valor do atributo
     * <code>nome</code> da classe
     * <code>Empresa</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da classe <code>PlanoVO</code>
     * resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de
     *                   acesso.
     */
    public List consultarPorNomeEmpresa(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT Plano.* FROM Plano, Empresa WHERE Plano.empresa = Empresa.codigo and upper( Empresa.nome ) like('" + valorConsulta.toUpperCase() + "%')  ORDER BY Empresa.nome";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Plano</code> através do valor do atributo
     * <code>nome</code> da classe
     * <code>Empresa</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da classe <code>PlanoVO</code>
     * resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de
     *                   acesso.
     */
    public List<PlanoVO> consultarPorCodigoEmpresa(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT * FROM Plano WHERE empresa = " + valorConsulta + "  ORDER BY descricao";

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    /**
     * Filtra planos que a empresa pode vender.
     * Deve filtrar os planos que a empresa tem permissão para vender. Caso o plano não esteja marcado para permitir venda
     * por nenhuma empresa, deve apresentar o plano em todas as empresas.
     *
     * @param valorConsulta código da empresa
     * @param nivelMontarDados nível desejado para o `montarDados`
     * @return lista de planos disponíveis para venda
     * @throws Exception problemas de conexão ou restrição de acesso
     */

    public List<PlanoVO> consultarPlanosPorCodigoEmpresa(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT * FROM Plano p"
                + " WHERE EXISTS(SELECT 1"
                + "                FROM PlanoEmpresa pe"
                + "               WHERE pe.empresa = " + valorConsulta
                + "                 AND pe.venda = true"
                + "                 AND pe.plano = p.codigo)"
                + "    OR NOT EXISTS(SELECT 1"
                + "                    FROM PlanoEmpresa pe"
                + "                   WHERE pe.plano = p.codigo"
                + "                     AND pe.venda = true)"
                + " ORDER BY p.descricao";

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Plano</code> através do valor do atributo
     * <code>String descricao</code>. Retorna os objetos, com início do valor do
     * atributo idêntico ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     *                        usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>PlanoVO</code>
     * resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de
     *                   acesso.
     */
    public List<PlanoVO> consultarPorDescricao(String valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr += "SELECT * FROM Plano WHERE upper( descricao ) like('" + valorConsulta.toUpperCase() + "%')  ORDER BY descricao";
        } else {
            sqlStr += "SELECT * FROM Plano WHERE upper( descricao ) like('" + valorConsulta.toUpperCase() + "%') and empresa = " + empresa.intValue() + "  ORDER BY descricao";
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List<PlanoVO> consultarPorDescricaoBolsaVigente(String valorConsulta, Boolean controlarAcesso, Integer empresa, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append(" SELECT * FROM Plano\n");
        sqlStr.append(" WHERE ");
        sqlStr.append(" UPPER( descricao ) like('").append(valorConsulta.toUpperCase()).append("%')\n");
        if (empresa != 0) {
            sqlStr.append(" AND empresa = ").append(empresa.intValue()).append("\n");
        }
        sqlStr.append(" AND vigenciade  <='").append(Uteis.getData(Calendario.hoje())).append("'\n");
        sqlStr.append(" AND vigenciaate >='").append(Uteis.getData(Calendario.hoje())).append("'\n");
        sqlStr.append("ORDER BY descricao");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Plano</code> através do valor do atributo
     * <code>String descricao</code>. Retorna os objetos, com início do valor do
     * atributo idêntico ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param valorConsulta
     * @param ingressoate
     * @param empresa
     * @param controlarAcesso                       Indica se a aplicação deverá verificar se o
     *                                              usuário possui permissão para esta consulta ou não.
     * @param consultarEmPlanoEmpresaVendaPermitida
     * @param nivelMontarDados
     * @return List Contendo vários objetos da classe <code>PlanoVO</code>
     * resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de
     *                   acesso.
     */
    public List<PlanoVO> consultarPorDescricaoDataIngresso(String valorConsulta,
                                                           Date ingressoate,
                                                           Integer empresa,
                                                           boolean controlarAcesso,
                                                           boolean consultarEmPlanoEmpresaVendaPermitida,
                                                           int nivelMontarDados) throws Exception {
        return consultarPorDescricaoDataIngresso(valorConsulta, ingressoate, empresa, controlarAcesso, consultarEmPlanoEmpresaVendaPermitida, true, nivelMontarDados);
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Plano</code> através do valor do atributo
     * <code>String descricao</code>. Retorna os objetos, com início do valor do
     * atributo idêntico ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     *                        usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>PlanoVO</code>
     * resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de
     *                   acesso.
     */
    public List<PlanoVO> consultarPorDescricaoDataIngresso(String valorConsulta,
                                                           Date ingressoate,
                                                           Integer empresa,
                                                           boolean controlarAcesso,
                                                           boolean consultarEmPlanoEmpresaVendaPermitida,
                                                           boolean planoPersonal,
                                                           int nivelMontarDados) throws Exception{
        return consultarPorDescricaoDataIngresso(valorConsulta, ingressoate, empresa, controlarAcesso, consultarEmPlanoEmpresaVendaPermitida, planoPersonal, 0, nivelMontarDados);
    }

    public List<PlanoVO> consultarPorDescricaoDataIngresso(String valorConsulta,
                                                           Date ingressoate,
                                                           Integer empresa,
                                                           boolean controlarAcesso,
                                                           boolean consultarEmPlanoEmpresaVendaPermitida,
                                                           boolean planoPersonal,
                                                           Integer categoria,
                                                           int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sbSqlConsulta = new StringBuilder();
        sbSqlConsulta.append("SELECT * FROM Plano\n");
        sbSqlConsulta.append("WHERE upper( descricao ) like('").append(valorConsulta.toUpperCase()).append("%')\n");
        sbSqlConsulta.append("AND (NOT site OR (site AND permitirVendaPlanoSiteNoBalcao))\n");
        sbSqlConsulta.append("AND (NOT totem OR (totem AND permitirVendaPlanoTotemNoBalcao))\n");
        if (!planoPersonal) {
            sbSqlConsulta.append(" AND (planoPersonal IS FALSE OR planoPersonal IS NULL) ");
        }
        if (empresa != 0) {
            if (consultarEmPlanoEmpresaVendaPermitida) {
                // Regra: Este plano poderá ser vendido em qualquer unidade onde "Permitir venda" esteja marcado.
                sbSqlConsulta.append("AND ((SELECT COUNT(*) ");
                sbSqlConsulta.append("   FROM planoempresa ");
                sbSqlConsulta.append("   WHERE planoempresa.plano = plano.codigo ");
                sbSqlConsulta.append("       AND planoempresa.empresa = ").append(empresa).append(" ");
                sbSqlConsulta.append("       AND venda IS TRUE");
                sbSqlConsulta.append(") > 0 ");
                //  Regra: Se nenhuma unidade estiver marcada com "Permitir venda", é considerado que todas as unidades poderão vender este plano.
                sbSqlConsulta.append("OR (SELECT COUNT(*) ");
                sbSqlConsulta.append("   FROM planoempresa");
                sbSqlConsulta.append("   WHERE planoempresa.plano = plano.codigo ");
                sbSqlConsulta.append("       AND venda IS TRUE) = 0) \n");
            } else {
                sbSqlConsulta.append("AND empresa = ").append(empresa).append(" \n");
            }
        }
        if(categoria != 0){
            sbSqlConsulta.append(" AND (plano.restringevendaporcategoria AND plano.codigo IN (SELECT plano FROM planocategoria WHERE categoria = ").append(categoria).append(") OR ");
            sbSqlConsulta.append(" (plano.restringevendaporcategoria IS FALSE)) ");
        }else{
            sbSqlConsulta.append(" AND (plano.restringevendaporcategoria IS FALSE) ");
        }

        sbSqlConsulta.append("ORDER BY descricao");

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sbSqlConsulta.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List<PlanoVO> consultarPorDescricaoDataIngressoSite(String valorConsulta,
                                                               Date ingressoate, Integer empresa, boolean controlarAcesso,
                                                               boolean planoSite, Integer codigoPlano, int nivelMontarDados) throws Exception {
        return consultarPorDescricaoDataIngressoSite(valorConsulta, ingressoate, empresa, controlarAcesso,
                planoSite, false, codigoPlano, nivelMontarDados);
    }

    public List<PlanoVO> consultarPorDescricaoDataIngressoSite(String valorConsulta,
                                                               Date ingressoate, Integer empresa, boolean controlarAcesso,
                                                               boolean planoSite, boolean totem, Integer codigoPlano, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sqlStr = new StringBuilder("");
        sqlStr.append("SELECT * FROM Plano\n");
        sqlStr.append("WHERE upper(descricao) like ('").append(valorConsulta.toUpperCase()).append("%')\n");
        if (empresa != 0) {
            sqlStr.append("AND (empresa = ").append(empresa).append("\n");
            sqlStr.append("OR exists(SELECT codigo FROM planoempresa pe WHERE pe.plano = plano.codigo AND pe.empresa = ").append(empresa).append(" AND pe.venda = true ))\n");
            sqlStr.append("AND ingressoate >= '").append(ingressoate).append("'\n");
            sqlStr.append("AND vigenciade <= '").append(ingressoate).append("'\n");
        }

        if (totem) {
            sqlStr.append("AND totem \n");
        } else {
            if (planoSite) {
                sqlStr.append("AND site = true\n");
            } else {
                sqlStr.append("AND site IS NOT true\n");
            }
        }

        if (codigoPlano != 0) {
            sqlStr.append("AND codigo = ").append(codigoPlano).append("\n");
        }

        sqlStr.append("ORDER BY descricao\n");

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Plano</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou
     * superiores ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     *                        usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>PlanoVO</code>
     * resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de
     *                   acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr += "SELECT * FROM Plano WHERE codigo >= " + valorConsulta + "  ORDER BY codigo";
        } else {
            sqlStr += "SELECT * FROM Plano WHERE codigo >= " + valorConsulta + " and empresa = " + empresa + "  ORDER BY codigo";
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Operação responsável por adicionar um objeto da
     * <code>PlanoHorarioVO</code> no Hashtable
     * <code>PlanoHorarios</code>. Neste Hashtable são mantidos todos os objetos
     * de PlanoHorario de uma determinada Plano.
     *
     * @param obj Objeto a ser adicionado no Hashtable.
     */
    public void adicionarObjPlanoHorarios(PlanoHorarioVO obj) throws Exception {
        getPlanoHorarios().put(obj.getHorario().getCodigo() + "", obj);
        //adicionarObjSubordinadoOC
    }

    /**
     * Operação responsável por remover um objeto da classe
     * <code>PlanoHorarioVO</code> do Hashtable
     * <code>PlanoHorarios</code>. Neste Hashtable são mantidos todos os objetos
     * de PlanoHorario de uma determinada Plano.
     *
     * @param Horario Atributo da classe <code>PlanoHorarioVO</code> utilizado
     *                como apelido (key) no Hashtable.
     */
    public void excluirObjPlanoHorarios(Integer Horario) throws Exception {
        getPlanoHorarios().remove(Horario + "");
        //excluirObjSubordinadoOC
    }

    /**
     * Operação responsável por adicionar um objeto da
     * <code>PlanoModalidadeVO</code> no Hashtable
     * <code>PlanoModalidades</code>. Neste Hashtable são mantidos todos os
     * objetos de PlanoModalidade de uma determinada Plano.
     *
     * @param obj Objeto a ser adicionado no Hashtable.
     */
    public void adicionarObjPlanoModalidades(PlanoModalidadeVO obj) throws Exception {
        getPlanoModalidades().put(obj.getModalidade().getCodigo() + "", obj);
        //adicionarObjSubordinadoOC
    }

    /**
     * Operação responsável por remover um objeto da classe
     * <code>PlanoModalidadeVO</code> do Hashtable
     * <code>PlanoModalidades</code>. Neste Hashtable são mantidos todos os
     * objetos de PlanoModalidade de uma determinada Plano.
     *
     * @param Modalidade Atributo da classe <code>PlanoModalidadeVO</code>
     *                   utilizado como apelido (key) no Hashtable.
     */
    public void excluirObjPlanoModalidades(Integer Modalidade) throws Exception {
        getPlanoModalidades().remove(Modalidade + "");
        //excluirObjSubordinadoOC
    }

    /**
     * Operação responsável por adicionar um objeto da
     * <code>PlanoComposicaoVO</code> no Hashtable
     * <code>PlanoComposicaos</code>. Neste Hashtable são mantidos todos os
     * objetos de PlanoComposicao de uma determinada Plano.
     *
     * @param obj Objeto a ser adicionado no Hashtable.
     */
    public void adicionarObjPlanoComposicaos(PlanoComposicaoVO obj) throws Exception {
        getPlanoComposicaos().put(obj.getComposicao().getCodigo() + "", obj);
        //adicionarObjSubordinadoOC
    }

    /**
     * Operação responsável por remover um objeto da classe
     * <code>PlanoComposicaoVO</code> do Hashtable
     * <code>PlanoComposicaos</code>. Neste Hashtable são mantidos todos os
     * objetos de PlanoComposicao de uma determinada Plano.
     *
     * @param Composicao Atributo da classe <code>PlanoComposicaoVO</code>
     *                   utilizado como apelido (key) no Hashtable.
     */
    public void excluirObjPlanoComposicaos(Integer Composicao) throws Exception {
        getPlanoComposicaos().remove(Composicao + "");
        //excluirObjSubordinadoOC
    }

    /**
     * Operação responsável por adicionar um objeto da
     * <code>PlanoCondicaoPagamentoVO</code> no Hashtable
     * <code>PlanoCondicaoPagamentos</code>. Neste Hashtable são mantidos todos
     * os objetos de PlanoCondicaoPagamento de uma determinada Plano.
     *
     * @param obj Objeto a ser adicionado no Hashtable.
     */
    public void adicionarObjPlanoCondicaoPagamentos(PlanoCondicaoPagamentoVO obj) throws Exception {
        getPlanoCondicaoPagamentos().put(obj.getCodigo() + "", obj);
        //adicionarObjSubordinadoOC
    }

    /**
     * Operação responsável por remover um objeto da classe
     * <code>PlanoCondicaoPagamentoVO</code> do Hashtable
     * <code>PlanoCondicaoPagamentos</code>. Neste Hashtable são mantidos todos
     * os objetos de PlanoCondicaoPagamento de uma determinada Plano.
     *
     * @param Codigo Atributo da classe <code>PlanoCondicaoPagamentoVO</code>
     *               utilizado como apelido (key) no Hashtable.
     */
    public void excluirObjPlanoCondicaoPagamentos(Integer Codigo) throws Exception {
        getPlanoCondicaoPagamentos().remove(Codigo + "");
        //excluirObjSubordinadoOC
    }

    /**
     * Operação responsável por adicionar um objeto da
     * <code>PlanoDuracaoVO</code> no Hashtable
     * <code>PlanoDuracaos</code>. Neste Hashtable são mantidos todos os objetos
     * de PlanoDuracao de uma determinada Plano.
     *
     * @param obj Objeto a ser adicionado no Hashtable.
     */
    public void adicionarObjPlanoDuracaos(PlanoDuracaoVO obj) throws Exception {
        getPlanoDuracaos().put(obj.getCodigo() + "", obj);
        //adicionarObjSubordinadoOC
    }

    /**
     * Operação responsável por remover um objeto da classe
     * <code>PlanoDuracaoVO</code> do Hashtable
     * <code>PlanoDuracaos</code>. Neste Hashtable são mantidos todos os objetos
     * de PlanoDuracao de uma determinada Plano.
     *
     * @param Codigo Atributo da classe <code>PlanoDuracaoVO</code> utilizado
     *               como apelido (key) no Hashtable.
     */
    public void excluirObjPlanoDuracaos(Integer Codigo) throws Exception {
        getPlanoDuracaos().remove(Codigo + "");
        //excluirObjSubordinadoOC
    }

    public PlanoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        return consultarPorChavePrimaria(codigoPrm, nivelMontarDados, null);
    }

    public PlanoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados, Boolean site) throws Exception {
        PlanoVO eCache = (PlanoVO) obterFromCache(codigo);
        if (eCache != null){
            return eCache;
        }
        String sql = "SELECT * FROM Plano WHERE codigo = ? ";

        if (site != null) {
            sql += "AND site IS " + site.toString() + " ";
        }

        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, codigo);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    eCache = (montarDados(rs, nivelMontarDados, this.con));
                }
            }
        }
        putToCache(eCache);
        return eCache;
    }

    public PlanoVO consultarPorChavePrimaria(Integer codigoPrm, boolean controleAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controleAcesso);
        String sql = "SELECT * FROM Plano WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return new PlanoVO();
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public Date consultarDataInicioFutura(Integer codigoPrm) throws Exception {
        String sql = "SELECT iniciominimocontrato FROM Plano WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm);
            try (ResultSet rs = sqlConsultar.executeQuery()) {
                if (rs.next()) {
                    return rs.getDate("iniciominimocontrato");
                }
                return null;
            }
        } catch (Exception ex) {
            return null;
        }
    }

    public Hashtable getPlanoHorarios() {
        return (planoHorarios);
    }

    public void setPlanoHorarios(Hashtable planoHorarios) {
        this.planoHorarios = planoHorarios;
    }

    public Hashtable getPlanoModalidades() {
        return (planoModalidades);
    }

    public void setPlanoModalidades(Hashtable planoModalidades) {
        this.planoModalidades = planoModalidades;
    }

    public Hashtable getPlanoComposicaos() {
        return (planoComposicaos);
    }

    public void setPlanoComposicaos(Hashtable planoComposicaos) {
        this.planoComposicaos = planoComposicaos;
    }

    public Hashtable getPlanoCondicaoPagamentos() {
        return (planoCondicaoPagamentos);
    }

    public void setPlanoCondicaoPagamentos(Hashtable planoCondicaoPagamentos) {
        this.planoCondicaoPagamentos = planoCondicaoPagamentos;
    }

    public Hashtable getPlanoDuracaos() {
        return (planoDuracaos);
    }

    public void setPlanoDuracaos(Hashtable planoDuracaos) {
        this.planoDuracaos = planoDuracaos;
    }

    /**
     * Responsável por consultar planos com data de vigenciaate maior ou menor
     * (dependendo da flag passada) do que a data passada como parametro
     *
     * <AUTHOR> 13/04/2011
     */
    @Override
    public List<PlanoVO> consultarIngressoAte(Date data, Boolean ativos, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT * FROM Plano WHERE (");
        if (ativos) {
            sqlStr.append("ingressoAte >= '");
        } else {
            sqlStr.append("ingressoAte < '");
        }
        sqlStr.append(Uteis.getDataJDBC(data)).append("')");
        if (empresa != null && empresa != 0) {
            sqlStr.append("AND empresa = ").append(empresa.intValue());
        }
        sqlStr.append("  ORDER BY vigenciaAte");

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    @Override
    public String consultarDescricaoPorCodigo(Integer codigo) throws Exception {
        ResultSet set = Plano.criarConsulta("SELECT descricao FROM plano WHERE codigo = " + codigo, con);
        if (set.next()) {
            return set.getString("descricao");
        }
        return "";
    }

    /**
     * 1. Pesquisar todos os contratos que foram renovados ou rematriculados,
     * que não estão cancelados e que ainda não tenha o produto do Reajuste; 2.
     * Percorrer os contratos e: 2.1. Verificar se ainda não possui REAJUSTE
     * MONETARIO como produto; 2.2. Calcular a diferença dos produtos de plano
     * mensal em aberto somando o 'valorAcrescentar'; 2.3. Gerar produtos
     * mensais de acordo com as competências restantes ou as competências a
     * partir da data de vencimento informada (opcional) 2.4. Alterar o valor
     * final e valor base calculo do contrato para efeito de cancelamento; 2.5.
     *
     * @param plano plano
     */
    @Override
    public StringBuilder alterarValoresContratos(TipoReajuste tipo, final double valor, final int plano, final Date dataVencimento,
                                                 final UsuarioVO usuario, boolean simular, final String tiposContrato, final Date dataLancamentoInicio,
                                                 final Date dataLancamentoFinal, double valorMensalAntigo, double valorMensalNovo,
                                                 Integer codigoContrato, Integer duracaoContrato, String tipoAlteracao, boolean gerarNovaParcela,
                                                 Date dataLancamentoNovaParcela, Date dataLancamentoParcelaCancelada, Date anoReferencia) throws Exception {

        StringBuilder resultado = new StringBuilder();
        double somaGeral = 0.0;
        double somaDiminuir = 0;
        if (tipoAlteracao.equals(PlanoVO.TIPO_ALTERACAO_DETALHADO)) {
            if (dataVencimento == null) {
                throw new ConsistirException("Informe uma data de vencimento as parcelas, "
                        + "para considerar como início do reajuste.");
            }
            if (valor <= 0.0) {
                throw new ConsistirException("Informe um valor a ser reajustado MAIOR QUE ZERO.");
            }
        }

        final String descricaoReajuste = "REAJUSTE MONETARIO " + (anoReferencia == null ? Calendario.getInstance().get(Calendar.YEAR) : Calendario.getAno(anoReferencia));

        StringBuilder sql = new StringBuilder();
        sql.append("select c.* \n");
        sql.append("from contrato c \n");
        sql.append("inner join contratoDuracao cd on cd.contrato = c.codigo \n");
        sql.append("inner join plano p on p.codigo = c.plano \n");
        sql.append("where p.codigo = ").append(plano).append(" \n");
        if ((codigoContrato != null) && (codigoContrato > 0)) {
            sql.append(" and c.codigo = ").append(codigoContrato).append(" \n");
        }
        if ((duracaoContrato != null) && (duracaoContrato > 0)) {
            sql.append(" and cd.numeroMeses = ").append(duracaoContrato).append(" \n");
        }
        sql.append("AND NOT EXISTS (SELECT co.codigo FROM contratooperacao co WHERE co.contrato = c.codigo AND tipooperacao  = 'CA')\n");
        //sql.append("and c.codigo not in (select contrato from contratooperacao  where tipooperacao  = 'CA') \n");
        sql.append("AND NOT EXISTS (SELECT mp.codigo FROM movproduto mp WHERE mp.contrato = c.codigo AND mp.descricao  like('").append(descricaoReajuste).append("%')) \n ");
        //sql.append("and c.codigo not in (select contrato from movproduto where descricao like('").append(descricaoReajuste).append("%')) \n ");
        if ((tiposContrato != null) && (!tiposContrato.trim().equals("")))
            sql.append("and c.situacaocontrato in (").append(tiposContrato).append(") \n");
        if (dataLancamentoInicio != null) {
            sql.append("and c.datalancamento  >= '").append(Uteis.getDataHoraJDBC(dataLancamentoInicio, "00:00:00.000")).append("' ");
        }
        if (dataLancamentoFinal != null) {
            sql.append("and c.datalancamento  <= '").append(Uteis.getDataHoraJDBC(dataLancamentoFinal, "23:59:59.999")).append("' \n");
        }
        sql.append("and c.codigo in (select contrato from movproduto where movproduto.contrato = c.codigo and situacao = 'EA' limit 1) \n");

        sql.append("and not EXISTS (select pessoa from movproduto where movproduto.descricao like('").append(descricaoReajuste).append("%') ");
        sql.append("And c.plano = (select ct.plano from contrato ct where coalesce(ct.contratoBaseadoRenovacao, 0) = c.codigo or coalesce(ct.contratoBaseadoRematricula, 0) = c.codigo) AND codigo = c.pessoa)");

        //sql.append("and c.pessoa not in (select pessoa from movproduto where movproduto.descricao like('").append(descricaoReajuste).append("%') ");
        //sql.append("                        and c.plano = (select ct.plano from contrato ct where coalesce(ct.contratoBaseadoRenovacao, 0) = c.codigo or coalesce(ct.contratoBaseadoRematricula, 0) = c.codigo)) \n");
        sql.append("order by c.codigo ");

        int count = SuperFacadeJDBC.contar("SELECT COUNT(*) FROM (" + sql + ") as qtd", con);
        int atual = 1;
        int contParcelas = 0;
        double somaParcelasAtuais = 0.0;
        ResultSet contratos = criarConsulta(sql.toString(), con);
        while (contratos.next()) {
            JSONObject jsonLogContrato = new JSONObject();
            StringBuilder msgLogAlteracaoParcela = new StringBuilder();
            if (JSFUtilities.isJSFContext()) {
                JSFUtilities.setManagedBeanValue("PlanoControle.pgrAtual", atual);
                JSFUtilities.setManagedBeanValue("PlanoControle.pgrTotal", count);
            }
            Integer contrato = contratos.getInt("codigo");
            double valorFinal = contratos.getDouble("valorfinal");
            double valorBaseCalculo = contratos.getDouble("valorbasecalculo");

            jsonLogContrato.put("contrato_codigo", contrato);
            jsonLogContrato.put("contrato_valorfinal_anterior", valorFinal);
            jsonLogContrato.put("contrato_valorbasecalculo_anterior", valorBaseCalculo);

            Integer codigoPessoa = contratos.getInt("pessoa");
            StringBuilder condicaoVencimento = new StringBuilder();
            if (dataVencimento != null) {
                condicaoVencimento.append(" and mpc.datavencimento >= '").append(
                        Uteis.getDataFormatoBD(dataVencimento)).append("' ");
            }
            ResultSet movprodutos = criarConsulta(
                    new StringBuilder().append("select mp.* ").
                            append("from movproduto mp ").
                            append("inner join movprodutoparcela mpp on mpp.movproduto = mp.codigo ").
                            append("inner join movparcela mpc on mpc.codigo = mpp.movparcela ").
                            append("inner join produto p on p.codigo = mp.produto ").
                            append("where mp.contrato = ").append(contrato).append(" and p.tipoproduto = 'PM' ").
                            append(condicaoVencimento.toString()).
                            append("and mp.situacao = 'EA' ").
                            append("order by mpc.datavencimento ").toString(), con);

//            Uteis.logar(null, "Reajustar valor do Contrato -> " + contrato);
            MovProdutoModalidade movProdutoModalidadeDAO;
            MovProduto movProdutoDAO;
            RemessaItem remessaItemDAO;
            try {
                con.setAutoCommit(false);
                movProdutoModalidadeDAO = new MovProdutoModalidade(con);
                movProdutoDAO = new MovProduto(con);
                remessaItemDAO = new RemessaItem(con);

                JSONArray jsonLogContratoProdutos = new JSONArray();

                double somaProdutos = 0.0;
                while (movprodutos.next()) {
                    MovProdutoVO mp = MovProduto.montarDados(movprodutos, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, con);

                    JSONObject logMovProduto = new JSONObject();
                    logMovProduto.put("movproduto_codigo", mp.getCodigo());
                    logMovProduto.put("movproduto_totalfinal_anterior", mp.getTotalFinal());
                    logMovProduto.put("movproduto_precounitario_anterior", mp.getPrecoUnitario());
                    logMovProduto.put("movproduto_valordesconto_anterior", mp.getValorDesconto());
                    logMovProduto.put("movproduto_valorfaturado_anterior", mp.getValorFaturado());

                    double valorAcrescentar = 0;
                    double valorDiminuir = 0;
                    if (tipoAlteracao.equals(PlanoVO.TIPO_ALTERACAO_SIMPLES)) {
                        double valorBD = Uteis.arredondarForcando2CasasDecimais(mp.getTotalFinal());
                        if (valorMensalAntigo != valorBD) {
                            continue;
                        }
                        if (valorMensalNovo > valorMensalAntigo) {
                            valorAcrescentar = valorMensalNovo - valorMensalAntigo;
                        } else {
                            valorDiminuir = valorMensalAntigo - valorMensalNovo;
                            somaDiminuir = somaDiminuir + valorDiminuir;
                        }
                    }
                    mp.setMovProdutoModalidades(movProdutoModalidadeDAO.consultarPorMovProduto(mp.getCodigo(), Uteis.NIVELMONTARDADOS_TELACONSULTA));


                    List<MovProdutoModalidadeVO> listaModalidades = mp.getMovProdutoModalidades();
                    if (!listaModalidades.isEmpty()) {
                        boolean incluirNovoMovProduto = tipoAlteracao.equals(PlanoVO.TIPO_ALTERACAO_DETALHADO) || (valorAcrescentar > 0);
                        MovProdutoVO reaj = (MovProdutoVO) mp.getClone(true);
                        if (incluirNovoMovProduto) {
                            reaj.setDescricao(descricaoReajuste + " - " + reaj.getMesReferencia());
                            reaj.setMovProdutoModalidades(listaModalidades);
                            if (tipoAlteracao.equals(PlanoVO.TIPO_ALTERACAO_DETALHADO)) {
                                valorAcrescentar = tipo == TipoReajuste.PORCENTAGEM
                                        ? (mp.getTotalFinal() * valor / 100) : valor;
                                valorAcrescentar = Uteis.arredondarForcando2CasasDecimais(valorAcrescentar);
                            }
                            reaj.setTotalFinal(valorAcrescentar);
                            reaj.setPrecoUnitario(valorAcrescentar);
                            reaj.setResponsavelLancamento(usuario);
                            reaj.setDataLancamento(Calendario.hoje());

                            double valorPorModalidade = valorAcrescentar / listaModalidades.size();
                            for (MovProdutoModalidadeVO movProdutoModalidadeVO : listaModalidades) {
                                movProdutoModalidadeVO.setMovProdutoVO(reaj);
                                movProdutoModalidadeVO.setCodigo(0);
                                movProdutoModalidadeVO.setValor(valorPorModalidade);
                            }
                            if (!simular) {
                                movProdutoDAO.incluirSemCommit(reaj);
                                logMovProduto.put("movproduto_criado_reajuste_codigo", reaj.getCodigo());
                                logMovProduto.put("movproduto_criado_reajuste_totalfinal", reaj.getTotalFinal());
                                logMovProduto.put("movproduto_criado_reajuste_precounitario", reaj.getPrecoUnitario());
                                logMovProduto.put("movproduto_criado_reajuste_valorfaturado", reaj.getValorFaturado());
                                logMovProduto.put("movproduto_criado_reajuste_valordesconto", reaj.getValorDesconto());
                            }
                        } else if ((valorDiminuir > 0) && (!simular)) {
                            try (PreparedStatement pst = con.prepareStatement("update movProduto set totalFinal = totalFinal - ?, valorDesconto = coalesce(valorDesconto,0) + ? where codigo = ?")) {
                                pst.setDouble(1, valorDiminuir);
                                pst.setDouble(2, valorDiminuir);
                                pst.setInt(3, mp.getCodigo());
                                pst.execute();
                            }
                            logMovProduto.put("movproduto_alterado_totalfinal", (mp.getTotalFinal() - valorDiminuir));
                            logMovProduto.put("movproduto_alterado_valordesconto", (mp.getValorDesconto() - valorDiminuir));
                        }

                        StringBuilder s = new StringBuilder();
                        s.append("select mpp.movparcela,mp.valorparcela, mp.dataregistro from movprodutoparcela mpp "
                                + "inner join movparcela mp on mp.codigo = mpp.movparcela "
                                + "where mpp.movproduto = ").append(mp.getCodigo());
                        ResultSet rsMovParcela = criarConsulta(s.toString(), con);
                        if (rsMovParcela.next()) {
                            JSONObject logMovParcela = new JSONObject();
                            int codMovParcela = rsMovParcela.getInt("movparcela");
                            double valorParcela = rsMovParcela.getDouble("valorParcela");
                            Date dataRegistroParcela = rsMovParcela.getTimestamp("dataregistro");

                            logMovParcela.put("movparcela_codigo", codMovParcela);
                            logMovParcela.put("movparcela_valor_anterior", valorParcela);
                            logMovParcela.put("movparcela_dataregistro_anterior", Calendario.getDataAplicandoFormatacao(dataRegistroParcela, "yyyyMMddHHmmss"));

                            if (!remessaItemDAO.existeParcelaEmRemessaGeradaouAguardando(codMovParcela)) {
                                if (codMovParcela != 0 && !simular) {
                                    s = new StringBuilder();

                                    Integer codParcelaAjustar = codMovParcela;
                                    if (gerarNovaParcela && (!simular)) {
                                        MovParcelaVO movParcelaVO = gerarNovaMovParcelaCancelarAnterior(codMovParcela, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada);
                                        codParcelaAjustar = movParcelaVO.getCodigo();

                                        logMovParcela.put("movparcela_criada_codigo", movParcelaVO.getCodigo());
                                        logMovParcela.put("movparcela_criada_valor", (valorParcela + valorAcrescentar));
                                    }

                                    if ((incluirNovoMovProduto) && (!simular)) {
                                        s.append("insert into movprodutoparcela (movproduto,movparcela,valorpago) values(").
                                                append(reaj.getCodigo()).append(",").append(codParcelaAjustar).append(",").append(valorAcrescentar).append(");");
                                        s.append("update movparcela set valorParcela = ").append(Double.toString(valorParcela + valorAcrescentar)).
                                                append(" where codigo = ").append(codParcelaAjustar).append(";");
                                        executarConsulta(s.toString(), con);
                                    } else if ((valorDiminuir > 0) && (!simular)) {
                                        try (PreparedStatement pst = con.prepareStatement("update movparcela set valorParcela = valorParcela - ? where codigo = ?")) {
                                            pst.setDouble(1, valorDiminuir);
                                            pst.setInt(2, codParcelaAjustar);
                                            pst.execute();
                                        }
                                        msgLogAlteracaoParcela.append("Codigo parcela:").append(codMovParcela).append("\n");
                                        msgLogAlteracaoParcela.append("Valor parcela original:").append(Formatador.formatarValorMonetario(valorParcela)).append("\n");
                                        msgLogAlteracaoParcela.append("Valor parcela após alteração:").append(Formatador.formatarValorMonetario(valorParcela - valorDiminuir)).append("\n");
                                    }

                                    if (gerarNovaParcela && (!simular)) {
                                        String update = ("update movprodutoparcela set movparcela = " + codParcelaAjustar + " where movparcela = " + codMovParcela + ";");
                                        executarConsulta(update, con);
                                    }
                                }
                                somaParcelasAtuais = somaParcelasAtuais + valorParcela;
                                contParcelas++;

                                logMovProduto.put("movparcela", logMovParcela);
                            } else {
                                if (!simular) {
                                    movProdutoDAO.excluirSemCommit(reaj);
                                }
                                continue;
                            }
                        }
                        somaProdutos = somaProdutos + valorAcrescentar;
                    }
                    jsonLogContratoProdutos.put(logMovProduto);

                }// for... movprodutos

                jsonLogContrato.put("movprodutos", jsonLogContratoProdutos);

                if (somaProdutos > 0.0) {
                    Double valorFinalNovo = (somaProdutos + valorFinal);
                    Double valorBaseCalculoNovo = (somaProdutos + valorBaseCalculo);

                    jsonLogContrato.put("contrato_valorfinal_somaprodutos", valorFinalNovo);
                    jsonLogContrato.put("contrato_valorbasecalculo_somaprodutos", valorBaseCalculoNovo);

                    StringBuilder s = new StringBuilder();
                    s.append("update contrato ").
                            append("set valorfinal = ").append(Double.toString(valorFinalNovo)).
                            append(", valorbasecalculo = ").append(Double.toString(valorBaseCalculoNovo)).
                            append(" where codigo = ").append(contrato);
                    if (!simular) {
                        executarConsulta(s.toString(), con);
                    }
                }
                if (somaDiminuir > 0.0) {
                    Double valorFinalNovo = (valorFinal - somaDiminuir);
                    Double valorBaseCalculoNovo = (valorBaseCalculo - somaDiminuir);

                    jsonLogContrato.put("contrato_valorfinal_somadiminuir", valorFinalNovo);
                    jsonLogContrato.put("contrato_valorbasecalculo_somadiminuir", valorBaseCalculoNovo);

                    StringBuilder s = new StringBuilder();
                    s.append("update contrato ").
                            append("set valorfinal = ").append(Double.toString(valorFinalNovo)).
                            append(", valorbasecalculo = ").append(Double.toString(valorBaseCalculoNovo)).
                            append(" where codigo = ").append(contrato);
                    if (!simular) {
                        executarConsulta(s.toString(), con);
                    }
                }
                if (msgLogAlteracaoParcela.length() > 0) {
                    String msgContrato = "Codigo Contrato:" + codigoContrato + "\n";
                    msgLogAlteracaoParcela.insert(0, msgContrato);
                    registrarLogAlteracaoParcela(usuario, codigoPessoa, contrato, msgLogAlteracaoParcela.toString());
                }

                if (!simular) {
                    Log logDAO = new Log(con);
                    logDAO.incluirLogGenerico(LogGenericoTipoEnum.REAJUSTE, usuario, jsonLogContrato.toString());
                    logDAO = null;
                }


                con.commit();
                somaGeral = somaGeral + somaProdutos;
//                Uteis.logar(null, "     Valor Acrescentado -> " + somaProdutos);
                atual++;

            } catch (Exception e) {
                con.rollback();
                Uteis.logar(e, Plano.class);
            } finally {
                con.setAutoCommit(true);
                movProdutoModalidadeDAO = null;
                movProdutoDAO = null;
                remessaItemDAO = null;
            }
        }//for... contratos
        if ((count > 0) && ((somaGeral > 0.0) || (somaDiminuir > 0.0))) {
            resultado.append("Contratos: ").append(count).append("<br/>");
            resultado.append("Parcelas atuais: ").append(contParcelas).append(" = ");
            resultado.append(Formatador.formatarValorMonetario(somaParcelasAtuais)).append("<br/>");
            if (somaGeral > 0.0) {
                resultado.append("Reajuste para maior: ").append(Formatador.formatarValorMonetario(somaGeral)).append("<br/>");
                resultado.append("Total após reajuste para maior: ").append(Formatador.formatarValorMonetario(somaGeral + somaParcelasAtuais)).append("<br/>");
            }
            if (somaDiminuir > 0.0) {
                resultado.append("Reajuste para menor: ").append(Formatador.formatarValorMonetario(somaDiminuir)).append("<br/>");
                resultado.append("Total após reajuste para menor: ").append(Formatador.formatarValorMonetario(somaParcelasAtuais - somaDiminuir)).append("<br/>");
            }
        }
        return resultado;
    }

    private MovParcelaVO gerarNovaMovParcelaCancelarAnterior(Integer movParcelaBase,
                                                             Date dataLancamentoNovaParcela,
                                                             Date dataLancamentoParcelaCancelada) throws Exception {
        MovParcela movParcelaDAO;
        try {
            movParcelaDAO = new MovParcela(this.con);
            MovParcelaVO movParcelaVOBase = movParcelaDAO.consultarPorChavePrimaria(movParcelaBase, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            MovParcelaVO movParcelaVONova = (MovParcelaVO) movParcelaVOBase.getClone(true);
            if (dataLancamentoNovaParcela != null) {
                movParcelaVONova.setDataRegistro(dataLancamentoNovaParcela);
            }
            movParcelaDAO.incluirSemCommit(movParcelaVONova);

            String updateParcelaCancelada = ("update movparcela set situacao = 'CA' where codigo = " + movParcelaVOBase.getCodigo() + ";");
            if (dataLancamentoParcelaCancelada != null) {
                updateParcelaCancelada = ("update movparcela set situacao = 'CA', dataregistro = '" + Uteis.getDataFormatoBD(dataLancamentoParcelaCancelada)+ "' where codigo = " + movParcelaVOBase.getCodigo() + ";");
            }
            executarConsulta(updateParcelaCancelada, con);
            return movParcelaVONova;
        } finally {
            movParcelaDAO = null;
        }
    }

    private void registrarLogAlteracaoParcela(UsuarioVO usuarioVO, Integer codigoPessoa, Integer codigoContrato, String msgLog) throws Exception {
        Log logDAO;
        try {
            logDAO = new Log(this.con);
            LogVO obj = new LogVO();
            obj.setPessoa(codigoPessoa);
            obj.setChavePrimaria(String.valueOf(codigoContrato));
            obj.setNomeEntidade("CONTRATO");
            obj.setNomeEntidadeDescricao("Contrato");
            obj.setResponsavelAlteracao(usuarioVO.getNome());
            obj.setUserOAMD(usuarioVO.getUserOamd());
            obj.setNomeCampo("MENSAGEM");
            obj.setValorCampoAlterado("");
            StringBuilder msg = new StringBuilder();
            obj.setOperacao("ALTERAÇÃO");
            msg.append("AJUSTE MONTETÁRIO DE MENSALIDADE PARA UM VALOR MENOR. \n");
            msg.append(msgLog).append("\n");
            obj.setValorCampoAlterado(msg.toString());
            obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            logDAO.incluirSemCommit(obj);
        } finally {
            logDAO = null;
        }
    }

    public String consultarJSON(Integer empresa, final String tipoPlano) throws Exception {
        ResultSet rs = getRS(empresa, tipoPlano);

        StringBuilder json = new StringBuilder();
        json.append("{\"aaData\":[");
        boolean dados = false;
        while (rs.next()) {
            dados = true;
            json.append("[\"").append(rs.getString("codigo")).append("\",");
            json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("descricao")).trim()).append("\",");
            json.append("\"").append(rs.getDate("ingressoate")).append("\",");
            json.append("\"").append(rs.getDate("vigenciade")).append("\",");
            json.append("\"").append(rs.getDate("vigenciaate")).append("\",");
            json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("empresa"))).append("\"],");
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    private ResultSet getRS(Integer empresa, final String tipoPlano) throws Exception {
        StringBuilder sql = new StringBuilder("SELECT plano.codigo, plano.descricao, ingressoate, vigenciade, vigenciaate, empresa.nome AS empresa, ptp.descricao as modelocontrato\n"
                + "FROM plano\n"
                + "  INNER JOIN empresa ON plano.empresa = empresa.codigo\n"
                + "  INNER JOIN planotextopadrao ptp ON plano.planotextopadrao = ptp.codigo\n");
        sql.append(" WHERE 1 = 1\n");
        Date dataHoje = Calendario.getDataComHoraZerada(Calendario.hoje());
        if (empresa != 0) {
            sql.append(" and empresa = ?\n");
        }
        if ("VI".equals(tipoPlano)) {
            sql.append(" and '").append(Uteis.getDataJDBC(dataHoje)).append("' between vigenciade and vigenciaate");
        } else if ("NVI".equals(tipoPlano)) {
            sql.append(" and '").append(Uteis.getDataJDBC(dataHoje)).append("' not between vigenciade and vigenciaate");
        }
        sql.append("  ORDER BY descricao");

        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        if (empresa != 0) {
            sqlConsultar.setInt(1, empresa);
        }

        return sqlConsultar.executeQuery();
    }

    public List consultarParaImpressao(final String filtro, final String ordem, final String campoOrdenacao, int codigoEmpresa, final String tipoPlano) throws Exception {
        ResultSet rs = getRS(codigoEmpresa, tipoPlano);
        List lista = new ArrayList();
        while (rs.next()) {
            PlanoVO planoVO = new PlanoVO();
            String geral = rs.getString("codigo") + rs.getString("descricao") + rs.getString("empresa") + rs.getString("ingressoAte") + rs.getString("vigenciade") + rs.getString("vigenciaate");
            if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                planoVO.setCodigo(rs.getInt("codigo"));
                planoVO.setDescricao(rs.getString("descricao"));
                planoVO.getEmpresa().setNome(rs.getString("empresa"));
                planoVO.setIngressoAte(rs.getDate("ingressoAte"));
                planoVO.setVigenciaDe(rs.getDate("vigenciade"));
                planoVO.setVigenciaAte(rs.getDate("vigenciaate"));
                planoVO.getPlanoTextoPadrao().setDescricao(rs.getString("modelocontrato"));
                lista.add(planoVO);
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Descrição")) {
            Ordenacao.ordenarLista(lista, "descricao");
        } else if (campoOrdenacao.equals("Prazo de Ingresso")) {
            Ordenacao.ordenarLista(lista, "ingressoAte");
        } else if (campoOrdenacao.equals("Início")) {
            Ordenacao.ordenarLista(lista, "vigenciaDe");
        } else if (campoOrdenacao.equals("Fim")) {
            Ordenacao.ordenarLista(lista, "vigenciaAte");
        } else if (campoOrdenacao.equals("Empresa")) {
            Ordenacao.ordenarLista(lista, "empresa");
        }

        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;
    }

    public Boolean planoDentroVigencia(int plano) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT * from plano ");
        sql.append(" WHERE ");
        sql.append(" codigo =").append(plano).append("\n");
        sql.append(" AND vigenciade  <='").append(Uteis.getData(Calendario.hoje())).append("'\n");
        sql.append(" AND vigenciaate >='").append(Uteis.getData(Calendario.hoje())).append("'\n");
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql.toString())) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                return tabelaResultado.next();
            }
        }
    }

    public boolean enviarDependenteFoguete(int plano) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT enviarDependenteFoguete from plano ");
        sql.append(" WHERE ");
        sql.append(" codigo = ").append(plano).append("\n");
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql.toString())) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (tabelaResultado.next()) {
                    return tabelaResultado.getBoolean("enviardependentefoguete");
                }
            }
        }
        return false;
    }

    public boolean consultarPlanoRenovacaoAutomatica(int codigo) throws Exception {
        String sql = "SELECT renovavelautomaticamente FROM Plano WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigo);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                return tabelaResultado.next() && tabelaResultado.getBoolean("renovavelautomaticamente");
            }
        }
    }

    public Integer consultarTotalContratosAlterarQtdDiasCancelamentoAutomatico(Integer codigoPlano) throws Exception {
        alterar(getIdEntidade());
        try (Statement st = con.createStatement()) {
            try (ResultSet rs = st.executeQuery("select count(*) as total from contratorecorrencia  where contrato in (select codigo from contrato where  plano = " + codigoPlano + " and vigenciaateajustada >= CURRENT_DATE)")) {
                if (rs.next()) {
                    return rs.getInt("total");
                }
            }
        }
        return 0;
    }

    public void alterarContratosAlterarQtdDiasCancelamentoAutomatico(Integer codigoPlano, Integer diasCancelamentoAutomatico) throws Exception {
        alterar(getIdEntidade());
        String sql = "update contratorecorrencia set diascancelamentoautomatico = ? where contrato in (select codigo from contrato where  plano = ? and vigenciaateajustada >= CURRENT_DATE)";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql.toString())) {
            sqlAlterar.setInt(1, diasCancelamentoAutomatico);
            sqlAlterar.setInt(2, codigoPlano);
            sqlAlterar.execute();
        }
    }

    public boolean existeContratoRenovavelAutomaticamenteComEssaConfiguracao(final int codigoPlano, final int codigoModalidade, final int nrVezesPorSemana, final int codigoHorario, final int duracao, final int condicaopagamento, final int pacote) throws Exception {
        StringBuilder sql = new StringBuilder();
        StringBuilder where = new StringBuilder(" where con.situacao = 'AT' and (pla.renovavelautomaticamente or con.renovavelautomaticamente or plarec.renovavelautomaticamente or conrec.renovavelautomaticamente) and con.plano = " + codigoPlano);
        sql.append(" select con.codigo from contrato con left join contratorecorrencia conrec on conrec.codigo=con.codigo ");
        sql.append(" inner join plano pla on pla.codigo = con.plano left join planorecorrencia plarec on plarec.plano = pla.codigo  ");
        if (!UteisValidacao.emptyNumber(codigoModalidade)) {
            sql.append(" inner join  contratomodalidade cm on cm.contrato = con.codigo ");
            where.append(" and cm.modalidade = " + codigoModalidade);
        }
        if (!UteisValidacao.emptyNumber(nrVezesPorSemana)) {
            where.append(" and cm.vezessemana = " + nrVezesPorSemana);
        }
        if (!UteisValidacao.emptyNumber(codigoHorario)) {
            sql.append(" inner join contratohorario ch on con.codigo = ch.contrato ");
            where.append(" and ch.horario = " + codigoHorario);
        }
        if (!UteisValidacao.emptyNumber(duracao)) {
            sql.append(" inner join contratoduracao cd on cd.contrato = con.codigo ");
            where.append(" and cd.numeromeses  = " + duracao);
        }
        if (!UteisValidacao.emptyNumber(condicaopagamento)) {
            sql.append(" inner join contratocondicaopagamento cp on cp.contrato = con.codigo ");
            where.append(" and cp.condicaopagamento = " + condicaopagamento);
        }
        if (!UteisValidacao.emptyNumber(pacote)) {
            sql.append(" inner join contratocomposicao cc on cc.contrato = con.codigo ");
            where.append(" and cc.composicao = " + pacote);
        }
        where.append(" limit 1 ");
        int i = 1;
        return existe(sql.toString() + where.toString(), con);
    }

    public void setarContratoTextoPadrao(Integer texto, Integer plano, UsuarioVO usuarioVO, boolean permiteImpressaoContratoMutavel) throws Exception {
        ResultSet rs = con.createStatement().executeQuery("select c.codigo as codigoContrato \n" +
                "from contrato c \n" +
                "inner join contratotextopadrao ct on ct.contrato = c.codigo\n" +
                "where c.plano = " + plano + "\n" +
                "and c.situacao = 'AT'");
        List<Integer> listaContratoAlterar = new ArrayList<>();
        while (rs.next()){
            listaContratoAlterar.add(rs.getInt("codigoContrato"));
        }
        SuperFacadeJDBC.executarConsulta("UPDATE contratotextopadrao SET planotextopadrao = "
                + texto
                + " where contrato in (select codigo from contrato where plano = "
                + plano
                + ")", con);
        // se permite a impressão de contrato de forma mutável, não é necessário atualizar o html dos contratos
        if (!permiteImpressaoContratoMutavel) {
            for (Integer codigoContrato : listaContratoAlterar) {
                getFacade().getContratoTextoPadrao().gravarHtmlContrato(codigoContrato, usuarioVO);
            }
        }
    }

    public void setarPlanoPersonalTextoPadrao(Integer texto, Integer plano) throws Exception {
        StringBuilder sqlUpdate = new StringBuilder();
        sqlUpdate.append("UPDATE planopersonaltextopadrao\n");
        sqlUpdate.append("SET planotextopadrao = ").append(texto).append("\n");
        sqlUpdate.append("WHERE taxapersonal IN (SELECT ctp.codigo\n");
        sqlUpdate.append("\tFROM controletaxapersonal ctp\n");
        sqlUpdate.append("\tINNER JOIN planopersonaltextopadrao ptp ON ptp.taxapersonal = ctp.codigo\n");
        sqlUpdate.append("\tWHERE ctp.plano = ").append(plano).append("\n");
        sqlUpdate.append("\tAND datafimvigenciaplano > '").append(Uteis.getDataJDBC(Calendario.hoje())).append("')");

        SuperFacadeJDBC.executarConsulta(sqlUpdate.toString(), con);
    }

    public void alterarFrequenciaMaximaContratosLancados(Integer quantidadeMaximaPermitida, Integer plano) throws Exception {
        SuperFacadeJDBC.executarConsulta("UPDATE contrato SET quantidademaximafrequencia = " + quantidadeMaximaPermitida + " where plano = " + plano + ";", con);
    }

    public boolean existePlanoComMesmoNome(PlanoVO obj) throws Exception {
        return existe("SELECT codigo" +
                " FROM plano" +
                " WHERE descricao = '" + obj.getDescricao().trim() + "'" +
                " AND empresa = " + obj.getEmpresa().getCodigo() +
                " AND codigo <> " + obj.getCodigo(), this.con);
    }

    public boolean existeContratoDuracaoCondicao(Integer codigoPlano, Integer duracao, boolean validarCondicaoPagamento) throws Exception {
        StringBuilder sql = new StringBuilder();
        if (validarCondicaoPagamento) {
            sql.append(" SELECT ccp.codigo  ");
            sql.append(" FROM contratocondicaopagamento ccp ");
            sql.append(" INNER JOIN contrato  C ON C.codigo = ccp.contrato ");
            sql.append(" WHERE C.plano = ").append(codigoPlano);
            sql.append(" AND   ccp.condicaopagamento = ").append(duracao);
            sql.append(" LIMIT 1 ");
        } else {
            sql.append(" SELECT CD.codigo ");
            sql.append(" FROM contratoduracao CD ");
            sql.append(" INNER JOIN contrato  C ON C.codigo = CD.contrato");
            sql.append(" WHERE C.plano = ").append(codigoPlano);
            sql.append(" AND   CD.numeromeses = ").append(duracao);
            sql.append(" LIMIT 1 ");
        }

        return existe(sql.toString(), this.con);
    }

    @Override
    public List<PlanoVO> consultarPorDescricaoPlanoVigente(String descricaoPlano,
                                                           Integer empresa,
                                                           Integer nivelMontarDados) throws Exception {

        return consultarPorDescricaoPlanoVigente(descricaoPlano, empresa, nivelMontarDados, null, false);
    }

    @Override
    public List<PlanoVO> consultarPorDescricaoPlanoVigente(String descricao,
                                                           Integer empresa,
                                                           int nivelMontarDados,
                                                           Boolean site,
                                                           boolean consultarEmPlanoEmpresaVendaPermitida) throws Exception {
        consultar(getIdEntidade());
        Date dataHoje = Calendario.getDataComHoraZerada(Calendario.hoje());
        String sql = "SELECT * FROM plano " +
                "WHERE upper(descricao) like('%" + descricao.toUpperCase() + "%') " +
                "AND '" + Uteis.getDataJDBC(dataHoje) + "' between vigenciade and vigenciaate ";
        return consulta(sql, empresa, nivelMontarDados, site, consultarEmPlanoEmpresaVendaPermitida, false);
    }

    public List<PlanoVO> consultarPorDescricaoVigenciaDeAte(String descricao,
                                                            Integer empresa,
                                                            int nivelMontarDados,
                                                            Boolean site,
                                                            boolean consultarEmPlanoEmpresaVendaPermitida) throws Exception {
        consultar(getIdEntidade());
        Date dataHoje = Calendario.getDataComHoraZerada(Calendario.hoje());
        String sql = "SELECT * FROM plano " +
                "WHERE upper(descricao) like('%" + descricao.toUpperCase() + "%') " +
                "AND '" + Uteis.getDataJDBC(dataHoje) + "' <= ingressoAte " +
                "AND '" + Uteis.getDataJDBC(dataHoje) + "' >= vigenciaDe ";
        return consulta(sql, empresa, nivelMontarDados, site, consultarEmPlanoEmpresaVendaPermitida, false);
    }

    public List<PlanoVO> consultarVigentesTodasEmpresas(int nivelMontarDados,
                                                        Boolean site) throws Exception {
        consultar(getIdEntidade());
        Date dataHoje = Calendario.getDataComHoraZerada(Calendario.hoje());
        String sql = "SELECT * FROM plano " +
                "WHERE '" + Uteis.getDataJDBC(dataHoje) + "' <= ingressoAte ";

        if (site != null && site) {
            sql += "AND site IS " + site.toString() + " ";
        }

        sql += "ORDER BY descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql)) {
                return (montarDadosConsulta(rs, nivelMontarDados, this.con));
            }
        }
    }

    @Override
    public int contarContratosAtivosPorPlanoCliente(int codigoPlano, Integer codigoCliente) throws Exception {
        String sql = "SELECT COUNT(*) AS total " +
                "FROM contrato c " +
                "JOIN cliente cl ON cl.pessoa = c.pessoa " +
                "WHERE c.plano = ? " +
                "AND cl.codigo = ? " +
                "AND cl.situacao <> 'VI' ";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, codigoPlano);
            ps.setInt(2, codigoCliente);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("total");
                }
            }
        }
        return 0;
    }

    public List<PlanoVO> consultarVigentes(Integer empresa,
                                           int nivelMontarDados,
                                           Boolean site,
                                           boolean consultarEmPlanoEmpresaVendaPermitida, Boolean pactoFlow) throws Exception {
        consultar(getIdEntidade());
        Date dataHoje = Calendario.getDataComHoraZerada(Calendario.hoje());
        String sql = "SELECT * FROM plano " +
                "WHERE '" + Uteis.getDataJDBC(dataHoje) + "' <= ingressoAte and '" + Uteis.getDataJDBC(dataHoje) + "' <= vigenciaAte and '" + Uteis.getDataJDBC(dataHoje) + "' >= vigenciaDe ";
        return consulta(sql, empresa, nivelMontarDados, site, consultarEmPlanoEmpresaVendaPermitida, pactoFlow);
    }

    public List<PlanoVO> consultarVigentesPactoFlow(Integer empresa,
                                           int nivelMontarDados,
                                           Boolean site,
                                           boolean consultarEmPlanoEmpresaVendaPermitida) throws Exception {
        consultar(getIdEntidade());
        Date dataHoje = Calendario.getDataComHoraZerada(Calendario.hoje());
        String sql = "SELECT * FROM plano " +
                "WHERE '" + Uteis.getDataJDBC(dataHoje) + "' <= ingressoAte and '" + Uteis.getDataJDBC(dataHoje) + "' <= vigenciaAte and '" + Uteis.getDataJDBC(dataHoje) + "' >= vigenciaDe ";
        return consultaPactoFlow(sql, empresa, nivelMontarDados, site, consultarEmPlanoEmpresaVendaPermitida);
    }

    private List<PlanoVO> consulta(String sql,
                                   int empresa,
                                   int nivelMontarDados,
                                   Boolean site,
                                   boolean consultarEmPlanoEmpresaVendaPermitida, Boolean planosPactoFlow) throws Exception {
        if(site != null && site && planosPactoFlow != null && planosPactoFlow) {
            sql += "AND (site IS " + site.toString() + " or apresentarpactoflow = true) ";
        } else if (site != null && site) {
            sql += "AND site IS " + site.toString() + " ";
        }
        if (consultarEmPlanoEmpresaVendaPermitida) {
            sql +=  // Regra: Este plano poderá ser vendido em qualquer unidade onde "Permitir venda" esteja marcado.
                    "AND ((SELECT COUNT(*) " +
                            "   FROM planoempresa " +
                            "   WHERE planoempresa.plano = plano.codigo " +
                            (UteisValidacao.notEmptyNumber(empresa) ? "       AND planoempresa.empresa = " + empresa : " ") +
                            "       AND venda IS TRUE" +
                            ") > 0 " +
                            //  Regra: Se nenhuma unidade estiver marcada com "Permitir venda", é considerado que todas as unidades poderão vender este plano.
                            "OR ((SELECT COUNT(*) " +
                            "   FROM planoempresa" +
                            "   WHERE planoempresa.plano = plano.codigo " +
                            "       AND venda IS TRUE) = 0 " + (UteisValidacao.notEmptyNumber(empresa) ? " and empresa = " + empresa : "") + ")) ";
        } else {
            sql += (UteisValidacao.notEmptyNumber(empresa) ? " AND empresa = " + empresa : "") + " ";

        }
        sql += "ORDER BY descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    private List<PlanoVO> consultaPactoFlow(String sql,
                                   int empresa,
                                   int nivelMontarDados,
                                   Boolean site,
                                   boolean consultarEmPlanoEmpresaVendaPermitida) throws Exception {
        if (site != null && site) {
            sql += "AND apresentarpactoflow IS TRUE" + " ";
        }
        if (consultarEmPlanoEmpresaVendaPermitida) {
            sql +=  // Regra: Este plano poderá ser vendido em qualquer unidade onde "Permitir venda" esteja marcado.
                    "AND ((SELECT COUNT(*) " +
                            "   FROM planoempresa " +
                            "   WHERE planoempresa.plano = plano.codigo " +
                            (UteisValidacao.notEmptyNumber(empresa) ? "       AND planoempresa.empresa = " + empresa : " ") +
                            "       AND venda IS TRUE" +
                            ") > 0 " +
                            //  Regra: Se nenhuma unidade estiver marcada com "Permitir venda", é considerado que todas as unidades poderão vender este plano.
                            "OR ((SELECT COUNT(*) " +
                            "   FROM planoempresa" +
                            "   WHERE planoempresa.plano = plano.codigo " +
                            "       AND venda IS TRUE) = 0 " + (UteisValidacao.notEmptyNumber(empresa) ? " and empresa = " + empresa : "") + ")) ";
        } else {
            sql += (UteisValidacao.notEmptyNumber(empresa) ? " AND empresa = " + empresa : "") + " ";

        }
        sql += "ORDER BY descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    @Override
    public List<PlanoVO> consultarPorPlanoVigente(Integer empresa, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        String sqlStr = "";
        Date dataHoje = Calendario.getDataComHoraZerada(Calendario.hoje());
        sqlStr += "SELECT * FROM Plano WHERE empresa = " + empresa.intValue() + " and '" + Uteis.getDataJDBC(dataHoje) + "' between vigenciade and vigenciaate  ORDER BY descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    @Override
    public List<PlanoVO> consultarPlanoVigenteSemCampanhaPaginado(Integer empresa, int nivelMontarDados, ListaPaginadaTO paginadorTO) throws Exception {
        consultar(getIdEntidade());
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append(" FROM Plano p WHERE ");
        if (empresa > 0)
            sqlStr.append(" p.empresa =" + empresa + " AND ");
        sqlStr.append(" '" + Uteis.getDataJDBC(Calendario.getDataComHoraZerada(Calendario.hoje())) + "' between vigenciade and vigenciaate ");
        sqlStr.append(" AND p.codigo NOT IN (" +
                " select distinct chaveestrangeira from (" +
                " select  chaveestrangeira from itemcampanha i WHERE i.pontos>0 " +
                " AND i.tipoitem =" + TipoItemCampanhaEnum.PLANO.getCodigo() +
                (empresa > 0 ? " and i.empresa = " + empresa : "") +
                " union all " +
                " select p.codigo as chaveestrangeira from itemcampanha i " +
                " inner join planoduracao pd on pd.codigo=i.chaveestrangeira " +
                " inner join plano p on p.codigo=pd.plano and p.empresa=i.empresa " +
                " WHERE i.pontos>0 " +
                " AND i.tipoitem  = " + TipoItemCampanhaEnum.PLANODURACAO.getCodigo() +
                (empresa > 0 ? " and i.empresa = " + empresa : "") +
                ") buscaPlanoPorDuracao ) ");
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery("SELECT COUNT(*) totalLinhas " + sqlStr.toString());

        if (tabelaResultado.next())
            paginadorTO.setCount(tabelaResultado.getInt("totalLinhas"));

        sqlStr.append(" ORDER BY descricao");
        sqlStr.append(" LIMIT " + paginadorTO.getLimit());
        sqlStr.append(" OFFSET " + paginadorTO.getOffset());

        tabelaResultado = stm.executeQuery("SELECT * " + sqlStr.toString());
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
    }

    @Override
    public Map<Integer, String> consultarPlanosSimplesVendaRapida(Integer empresa) throws Exception {
        String sqlStr = obterSQLPlanosVendaRapida("codigo, descricao", empresa);
        Map<Integer, String> planos;
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sqlStr)) {
                planos = new HashMap<>();
                while (rs.next()) {
                    planos.put(rs.getInt("codigo"), rs.getString("descricao"));
                }
            }
        }
        return planos;
    }

    @Override
    public Map<Integer, String> consultarPlanosSimplesVendaRapidaClienteSelecionado(Integer empresa, Integer codigoCategoriaCliente) throws Exception {
        StringBuilder sbSqlConsulta = new StringBuilder();
        sbSqlConsulta.append("SELECT codigo, descricao FROM Plano WHERE recorrencia \n and ((empresa = \n");
                sbSqlConsulta.append( empresa).append(" \n");
                sbSqlConsulta.append( " or not exists(select empresa from planoempresa where plano = Plano.codigo) ) \n");
                sbSqlConsulta.append( " or " + empresa + " in (select empresa from planoempresa where venda and plano = Plano.codigo) \n");
                sbSqlConsulta.append(") \n and '" + Uteis.getDataJDBC(Calendario.getDataComHoraZerada(Calendario.hoje())) + "' between vigenciade and vigenciaate  \n");
                sbSqlConsulta.append(" and ingressoate >= '" + Uteis.getDataJDBC(Calendario.getDataComHoraZerada(Calendario.hoje())) + "'  \n");
                sbSqlConsulta.append("and plano.apresentarvendarapida = true  \n");
                if(codigoCategoriaCliente!=null && codigoCategoriaCliente>0){
                    sbSqlConsulta.append(" AND (plano.restringevendaporcategoria \n");
                    sbSqlConsulta.append(" AND plano.codigo IN (SELECT plano FROM planocategoria WHERE categoria = "+codigoCategoriaCliente+" ) \n");
                    sbSqlConsulta.append(" OR  (plano.restringevendaporcategoria IS FALSE))  \n");
                }else{
                    sbSqlConsulta.append(" AND plano.restringevendaporcategoria IS FALSE \n");
                }
            sbSqlConsulta.append(" ORDER BY descricao \n");

        Map<Integer, String> planos;
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sbSqlConsulta.toString())) {
                planos = new HashMap<>();
                while (rs.next()) {
                    planos.put(rs.getInt("codigo"), rs.getString("descricao"));
                }
            }
        }
        return planos;
    }

    private String obterSQLPlanosVendaRapida(String colunas, Integer empresa) {
        // VALIDAÇÃO DE SEGURANÇA: Verificar se colunas é seguro
        if (!Uteis.isValidSqlFields(colunas)) {
            throw new SecurityException("Colunas contêm caracteres não permitidos");
        }

        String dataHoje = Uteis.getDataFormatoBD(Calendario.getDataComHoraZerada(Calendario.hoje()));
        String sqlStr = "SELECT " + colunas + " FROM Plano WHERE recorrencia \n and ((empresa = "
                + empresa
                + " or not exists(select empresa from planoempresa where plano = Plano.codigo) )"
                + " or " + empresa + " in (select empresa from planoempresa where venda and plano = Plano.codigo)"
                + ") \n and '" + dataHoje + "' between vigenciade and vigenciaate \n"
                + " and ingressoate >= '" + dataHoje + "' \n"
                + "and plano.apresentarvendarapida = true \n"
                + "ORDER BY descricao";
        return sqlStr;
    }

    public List<PlanoVO> consultarPlanosSimplesVendaRapida(Integer empresa, int nivelMontarDados) throws Exception {
        String sqlStr = obterSQLPlanosVendaRapida("*", empresa);
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    public List<PlanoVO> consultarPlanosProximosInativar(Integer empresa, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());


        // Obtém o primeiro e último dia do próximo mês
        Date inicioProximoMes = Calendario.primeiroDiaProximoMes();
        Date fimProximoMes = Calendario.ultimoDiaProximoMes();

    /*
      Aqui assumimos que ?ficará inativo no próximo mês?
      significa que vigenciaAte está entre o 1º dia e o
      último dia do mês seguinte.
      de outro critério (ex.: <= fim do próximo mês ou > fim do mês atual etc.).
    */
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM plano ");
        sql.append("WHERE empresa = ").append(empresa).append(" ");
        sql.append("  AND vigenciaAte >= '").append(Uteis.getDataJDBC(inicioProximoMes)).append("' ");
        sql.append("  AND vigenciaAte <= '").append(Uteis.getDataJDBC(fimProximoMes)).append("' ");
        // Ajuste a ordenação conforme desejar
        sql.append("ORDER BY descricao");

        try (Statement stm = con.createStatement();
             ResultSet rs = stm.executeQuery(sql.toString())) {
            return montarDadosConsulta(rs, nivelMontarDados, this.con);
        }
    }


    public List<PlanoVO> consultarPlanosSimplesVendaRapidaClienteSelecionado(Integer empresa, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT codigo, descricao FROM Plano WHERE recorrencia \n and ((empresa = "
                + empresa
                + " or not exists(select empresa from planoempresa where plano = Plano.codigo) )"
                + " or " + empresa + " in (select empresa from planoempresa where venda and plano = Plano.codigo)"
                + ") \n and '" + Uteis.getDataJDBC(Calendario.getDataComHoraZerada(Calendario.hoje())) + "' between vigenciade and vigenciaate \n"
                + " and ingressoate >= '" + Uteis.getDataJDBC(Calendario.getDataComHoraZerada(Calendario.hoje())) + "' \n"
                + "and plano.apresentarvendarapida = true \n"
                + "ORDER BY descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }



    public Integer consultarTotalContratosCancelamentoProporcional(Integer codigoPlano) throws Exception {
        alterar(getIdEntidade());
        StringBuilder sbInner = new StringBuilder();
        sbInner.append("select codigo\n")
                .append("from contrato\n")
                .append("where plano = ?\n")
                .append("and vigenciaateajustada >= CURRENT_DATE\n");

        String sql = "select count(*) as total\n" +
                "from contratorecorrencia\n" +
                "where contrato in (" + sbInner + ")";

        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPlano);
            try (ResultSet rs = sqlConsultar.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("total");
                }
            }
        }
        return 0;
    }

    public void alterarContratosCancelamentoProporcional(Integer codigoPlano, boolean cancelamentoproporcional, Integer qtddiascobrarproximaparcela, Integer qtddiascobraranuidadetotal, boolean somenteContratosRenovados) throws Exception {
        alterar(getIdEntidade());

        StringBuilder sbInner = new StringBuilder();
        sbInner.append("select codigo\n")
                .append("from contrato\n")
                .append("where plano = ?\n")
                .append("and vigenciaateajustada >= CURRENT_DATE\n");
        if (somenteContratosRenovados) {
            sbInner.append("and situacaocontrato = 'RN'\n");
        }

        String sql = "update contratorecorrencia " +
                "set cancelamentoproporcional = ?, " +
                "qtddiascobrarproximaparcela = ?, " +
                "qtddiascobraranuidadetotal = ? " +
                "where contrato in (" + sbInner + ")";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setBoolean(1, cancelamentoproporcional);
            sqlAlterar.setInt(2, qtddiascobrarproximaparcela);
            sqlAlterar.setInt(3, qtddiascobraranuidadetotal);
            sqlAlterar.setInt(4, codigoPlano);
            sqlAlterar.execute();
        }

        if (somenteContratosRenovados) {
            sbInner = new StringBuilder();
            sbInner.append("select codigo\n")
                    .append("from contrato\n")
                    .append("where plano = ?\n")
                    .append("and vigenciaateajustada >= CURRENT_DATE\n")
                    .append("and situacaocontrato <> 'RN'\n");

            sql = "update contratorecorrencia " +
                    "set cancelamentoproporcional = ?, " +
                    "qtddiascobrarproximaparcela = ?, " +
                    "qtddiascobraranuidadetotal = ? " +
                    "where contrato in (" + sbInner + ")";

            try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
                sqlAlterar.setBoolean(1, false);
                sqlAlterar.setInt(2, qtddiascobrarproximaparcela);
                sqlAlterar.setInt(3, qtddiascobraranuidadetotal);
                sqlAlterar.setInt(4, codigoPlano);
                sqlAlterar.execute();
            }
        }
    }

    public List<PlanoVO> consultarTodos(Date dataReferencia, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT \n");
        sql.append("* \n");
        sql.append("FROM Plano \n");
        sql.append("WHERE 1 = 1 \n");
        if (dataReferencia != null) {
            Date dataHoje = Calendario.getDataComHoraZerada(dataReferencia);
            sql.append("AND '").append(Uteis.getDataJDBC(dataHoje)).append("' between vigenciade and vigenciaate \n");
        }
        sql.append("ORDER BY descricao");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    @Override
    public void alterarPontos(Integer codigo, Integer pontos, Integer tipoitem) throws Exception {
        //alterar(getIdEntidade());
        String sql = "";
        if (tipoitem == TipoItemCampanhaEnum.PLANO.getCodigo()) {
            sql = "update plano set pontos = ? where codigo = ?";
        } else if (tipoitem == TipoItemCampanhaEnum.PLANODURACAO.getCodigo()) {
            sql = "update planoduracao set pontos = ? where codigo = ?";
        }
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setInt(1, pontos);
            sqlAlterar.setInt(2, codigo);
            sqlAlterar.execute();
        }
    }

    @Override
    public List<Integer> consultarTodosCodigos() throws Exception {
        String sql = "SELECT codigo FROM plano ORDER BY vigenciade DESC";
        List<Integer> codigos = new ArrayList<Integer>();
        ResultSet rs = con.prepareStatement(sql).executeQuery();
        while (rs.next()) {
            codigos.add(rs.getInt("codigo"));
        }
        return codigos;
    }

    @Override
    public void alterarDescricaoPlano(Integer codigo, String descricao) throws Exception {
        String sql = "UPDATE plano SET descricao = ? WHERE codigo = ?";
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setString(1, descricao);
        ps.setInt(2, codigo);
        ps.executeUpdate();
    }

    @Override
    public Integer consultarPlanoDataMaiorQueZero(Integer codigoPlano) throws Exception {
        Integer obj = 0;
        StringBuilder query = new StringBuilder();
        query.append("select p.quantidademaximafrequencia as dias from plano p\n");
        query.append("where p.codigo = ?\n");
        PreparedStatement ps = con.prepareStatement(query.toString());
        ps.setInt(1, codigoPlano);
        ResultSet rs = ps.executeQuery();
        while (rs.next()) {
            obj = rs.getInt("dias");
        }
        return obj;
    }

    public List<PlanoVO> consultarTodosOsPlanosVendas(Integer empresa) throws Exception {
        List<PlanoVO> planos = new ArrayList<>();

        String sqlStr = "SELECT DISTINCT p.codigo, p.descricao FROM plano p"
                + " LEFT JOIN planoempresa p2 on p2.plano = p.codigo"
                + " WHERE p.empresa = " + empresa
                + " OR p2.venda IS TRUE AND p2.empresa = " + empresa;

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                while (tabelaResultado.next()) {
                    PlanoVO plano = new PlanoVO();
                    plano.setCodigo(tabelaResultado.getInt("codigo"));
                    plano.setDescricao(tabelaResultado.getString("descricao"));
                    planos.add(plano);
                }
            }
        }

        return planos;
    }

    public List<PlanoVO> consultarPorPermissaoVenda(Integer empresa, String filtroSelecionado, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select distinct p.* from plano p\n")
                .append("inner join planoempresa pe on pe.plano = pe.plano \n")
                .append("where pe.venda is true\n");
        if (empresa > 0) {
            sql.append("and pe.empresa = ").append(empresa);
        }

        switch (filtroSelecionado) {
            case "ATIVO":
                sql.append("AND p.ingressoAte >= '").append(Uteis.getDataJDBC(Calendario.hoje())).append("'\n");
                break;
            case "INATIVO":
                sql.append("AND p.ingressoAte < '").append(Uteis.getDataJDBC(Calendario.hoje())).append("'\n");
                break;
        }

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    public List<PlanoVO> consultarPlanosComBolsa(Integer empresa) throws Exception {
        List<PlanoVO> planos = new ArrayList<>();

        String sql = "SELECT * FROM plano WHERE bolsa = true and empresa = " + empresa;

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql)) {
                while (tabelaResultado.next()) {
                    PlanoVO plano = new PlanoVO();
                    plano.setCodigo(tabelaResultado.getInt("codigo"));
                    plano.setDescricao(tabelaResultado.getString("descricao"));
                    planos.add(plano);
                }
            }
        }

        return planos;
    }

    public PlanoVO consultaPlanoDelSoft(Integer empresa, Integer codigoPlano) throws Exception {
        if(codigoPlano != 0) {
            String sql = "SELECT * FROM plano WHERE codigo = " + codigoPlano;

            try (Statement stm = con.createStatement()) {
                try (ResultSet tabelaResultado = stm.executeQuery(sql)) {
                    if (tabelaResultado.next()) {
                        PlanoVO plano = new PlanoVO();
                        plano.setCodigo(tabelaResultado.getInt("codigo"));
                        plano.setDescricao(tabelaResultado.getString("descricao"));
                        return plano;
                    }
                }
            }
        }
        return null;
    }

    public List<PlanoVO> consultarPlanoParaTransferencia(String descricao, boolean controleAcessoMultiplasEmpresasPorPlano,
                                                         EmpresaVO empresaVO) throws Exception {

        List<PlanoVO> planos = consultarPorDescricaoVigenciaDeAte("", empresaVO.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS , false, controleAcessoMultiplasEmpresasPorPlano);
        List<PlanoVO> planosListagem = new ArrayList<>();
        for (PlanoVO plano : planos) {
            boolean modalidaComTurma = false;
            if (!plano.isPlanoPersonal() && plano.getRecorrencia() && (!plano.getSite() || plano.getPermitirVendaPlanoSiteNoBalcao())) {
                for (PlanoModalidadeVO planoModalidade : plano.getPlanoModalidadeVOs()) {
                    if (planoModalidade.getModalidade().getUtilizarTurma()) {
                        modalidaComTurma = true;
                        break;
                    }
                }
                if (!modalidaComTurma) {
                    planosListagem.add(plano);
                }
            }
        }
        return planosListagem;
    }

    public List<String> obterChavesEmpresasPermiteAcesso(Integer codigoPlano) throws Exception {
        List<String> keys = new ArrayList<>();
        ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT chave, codigoEmpresa FROM planoempresaredeacesso \n" +
                "WHERE plano = " + codigoPlano, con);
        while (rs.next()) {
            keys.add(String.format("%s-%s", rs.getString("chave"), rs.getString("codigoEmpresa")));
        }
        return keys;
    }

    public PlanoVO findByIdExterno(String idExterno, int codigoEmpresa, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM plano \n" +
                "WHERE idexterno = '" + idExterno + "'\n";
        if (UteisValidacao.emptyNumber(codigoEmpresa)) {
            sql += "AND empresa = " + codigoEmpresa + "\n";
        }
        try (ResultSet rs = criarConsulta(sql, con)) {
            if (rs.next()) {
                return montarDados(rs, nivelMontarDados, this.con);
            }
        }
        return new PlanoVO();
    }
}

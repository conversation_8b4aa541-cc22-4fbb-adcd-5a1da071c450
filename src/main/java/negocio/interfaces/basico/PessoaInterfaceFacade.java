package negocio.interfaces.basico;

import br.com.pactosolucoes.ce.comuns.to.NegociacaoEventoContratoTO;
import br.com.pactosolucoes.ce.comuns.to.PessoaTO;
import com.sun.org.apache.xpath.internal.operations.Bool;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.ManutencaoAjusteGeralTO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.enumerador.TipoAssinaturaBiometricaEnum;
import negocio.comuns.basico.enumerador.TipoPessoa;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.PessoaBloqueioCobrancaTO;
import negocio.comuns.financeiro.PessoaCPFTO;
import negocio.comuns.financeiro.enumerador.TipoBloqueioCobrancaEnum;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface 
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface PessoaInterfaceFacade extends SuperInterface {

    public PessoaVO novo() throws Exception;

    public void incluir(PessoaVO obj) throws Exception;

    public void incluirSemComit(PessoaVO obj) throws Exception;

    public void alterar(PessoaVO obj) throws Exception;

    public void alterarSemComit(PessoaVO obj) throws Exception;

    public void alterarPessoaSiteSemCommit(Integer codigoPessoa,
                                           String endereco,
                                           String complemento,
                                           String numero,
                                           String bairro,
                                           String cep,
                                           final Integer codigoCidade,
                                           final Integer codigoEstado,
                                           String telCelular,
                                           String telResidencial,
                                           Connection con) throws Exception;

    public void excluir(PessoaVO obj) throws Exception;

    public PessoaVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public PessoaVO consultarPorCodigo(Integer valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorTipoPessoa(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorDescricaoProfissao(String valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorNome(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    List<PessoaVO> consultarPorCfp(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public PessoaVO consultarPorCPF(String cpf, int nivelMontarDados) throws Exception;

    public PessoaVO consultarPorCnpj(String cnpj, int nivelMontarDados) throws Exception;

    public List consultarPorNomeCidade(String valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorNomeEmpresa(String valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorMatricula(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);
    
    public byte[] obterFotoBanco(final Integer codigoPessoa) throws SQLException;

    public byte[] obterFoto(final String chave, final Integer codigoPessoa) throws Exception;

    public String obterFotoKey(final Integer codigoPessoa) throws Exception;

    public void incluirConexaoInicializada(PessoaVO obj, Connection con) throws Exception;

    public void incluirConexaoInicializada(PessoaVO obj, Connection con, boolean validarPermissao) throws Exception;

    public void alterarConexaoInicializada(PessoaVO obj, Connection con) throws Exception;

    public void alterarConexaoInicializada(PessoaVO obj, Connection con,boolean validarPermissao) throws Exception;

    public void alterarConexaoInicializadaSemPermissao(PessoaVO obj, Connection con) throws Exception;

    public void excluirConexaoInicializada(PessoaVO obj, Connection con) throws Exception;

    public PessoaVO consultarPorNomePessoa(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorNomePessoaComLimite(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarTodosPessoaComLimite(boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public PessoaVO consultarPorCPF(String valorConsulta, Integer codigoPessoa, boolean controlarAcesso) throws Exception;

    public void removerFoto(final String key, final int codigoPessoa) throws Exception;

    public void alterarSenhaAcesso(int codigoPessoa, String senha) throws Exception;

    public void definirCPFComoSenhaAcesso(int codigoPessoa, int codigoEmpresa) throws Exception;

    public void alterarSenhaAcesso(int codigoPessoa, String senha,boolean atualizarBaseOffline) throws Exception;

    public void liberarRemoverSenhaAcesso(PessoaVO pessoaVO) throws Exception;

    public List<PessoaVO> consultar(String sql, final int nivelMontarDados) throws Exception;

    public List<PessoaVO> consultarPorNomePessoaComLimiteFinanceiro(int codigoEmpresa, String valorConsulta, boolean controlarAcesso, boolean consultarQuaquerParteDoNome, int nivelMontarDados) throws Exception;

    public List<PessoaVO> consultarPorNomePessoaComLimiteFinanceiro(int codigoEmpresa, String valorConsulta, boolean controlarAcesso, int limit, boolean consultarQuaquerParteDoNome, int nivelMontarDados) throws Exception;

    public List<PessoaVO> consultarPorNomePessoaComLimiteFinanceiro(int codigoEmpresa, String valorConsulta, boolean controlarAcesso, boolean consultarQuaquerParteDoNome, int nivelMontarDados, boolean consultarColabInativo) throws Exception;

    public List consultarTodosPessoaComLimiteFinanceiro(int codigoEmpresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void incluirPessoaSimplificado(PessoaVO obj) throws Exception;

    public PessoaVO incluirPessoaResponsavelAluno(PessoaVO obj) throws Exception;

    public void alterarNomePessoaResponsavelAluno(PessoaVO obj) throws Exception;

    public List<Integer> consultarPorDataNascECPF(String cpf, java.sql.Date dataNasc) throws SQLException, Exception;

    public List<PessoaVO> consultarPessoaColaboradorComLimite(int codigoEmpresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<PessoaVO> consultarPessoaPorNomeColaboradorComLimite(int codigoEmpresa, String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<PessoaVO> consultarPessoaClienteComLimite(int codigoEmpresa, boolean clienteVisitante, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<PessoaVO> consultarPessoaPorNomeClienteComLimite(int codigoEmpresa, String valorConsulta, boolean clienteVisitante,
            boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<PessoaVO> consultarClientes(int codigoEmpresa, String nomePessoa, int nivelMontarDados) throws Exception;

    public List<PessoaVO> consultarPessoaClienteVisitantePorDataBVComLimite(int codigoEmpresa, Date dataInicial, Date dataFinal, String horarioInicial, String horarioFinal,
            int nivelMontarDados) throws Exception;

    public List<PessoaVO> consultarPessoaPorNomeClienteVisitantePorDataBVComLimite(int codigoEmpresa, String valorConsulta,
            Date dataInicial, Date dataFinal, String horarioInicial, String horarioFinal, int nivelMontarDados) throws Exception;

    public List<PessoaVO> consultarPessoaComLimite(int codigoEmpresa,
            int nivelMontarDados) throws Exception;

    public List<PessoaVO> consultarPessoaPorNomeComLimite(int codigoEmpresa, String valorConsulta,
            int nivelMontarDados) throws Exception;

    public List<PessoaVO> consultarPorNomeAlunoProfessorComLimite(String valorConsulta, int professor) throws Exception;

    public PessoaVO consultarPessoaEmpresaFinan(EmpresaVO empresa) throws Exception;

    public void atualizarDadosCadastraisPessoaEmpresa (PessoaVO pessoaVO, EmpresaVO empresaVO)throws Exception;

    public Integer obterCodigoColaboradorComCodigoAlternativo(final String codAcessoAlternativo) throws Exception;

    public Integer obterCodigoClienteComCodigoAlternativo(final String codAcessoAlternativo) throws Exception;

    void alterarCPF(Integer codigoPessoa, String cpf, String descricaoLog, UsuarioVO usuarioVO, boolean controlaTransacao) throws Exception;

    public String consultarJSON(Integer empresa) throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa) throws SQLException;

    public List<PessoaVO> consultarTodosPessoaComLimiteCRM(int codigoEmpresa, String valorConsulta, boolean controlarAcesso) throws Exception;

    Integer consultarCodigoPessoaPorCodigoContrato(Integer codigo) throws Exception;

    public List<NegociacaoEventoContratoTO> consultarEventosPessoa(Integer pessoa, Integer limit) throws Exception;

    public List<PessoaTO> consultarPessoaColaboradorAtivo(Integer codigoEmpresa) throws Exception;

    /**
     * Realiza a alteraçao do atributo idVindi da pessoa.
     * @param pessoa
     * @throws Exception
     */
    void alterarIdVindi(PessoaVO pessoa) throws Exception;

    void alterarIdVindiComLog(PessoaVO pessoa, UsuarioVO usuarioVO) throws Exception;
    
    public void atualizaFotoKey(Integer codigo,String fotokey)throws Exception;

    public void deletarFoto(final String chave, final Integer codigoPessoa) throws Exception;

    public void alterarIdMaxiPago(PessoaVO pessoa) throws Exception;

    public PessoaVO consultarPorCodigoEEmpresa(Integer codigo, Integer codigoEmpresa, int nivelMontarDados)throws Exception;
    
     public boolean senhaAcessoJaUtilizada(int codigoAutorizacao,int codigoPessoa, String senhaEncriptada) throws Exception;
     
    public PessoaVO consultarPessoaAvulsaPorCPF(String cpf, int nivelMontarDados) throws Exception;

    PessoaVO consultarPessoaAvulsaPorRNE(String rne, int nivelMontarDados) throws Exception;

    void alterarIdGetNet(PessoaVO pessoa) throws Exception;

    void atualizarAssinaturaBiometriaDigital(Integer codigo, String assinatura) throws SQLException;

    void atualizarAssinaturaBiometriaFacial(Integer codigo, String assinatura) throws SQLException;

    String obterAssinaturaBiometriaDigital(Integer pessoa) throws SQLException;

    boolean verificaAssinaturaBiometriaDigital(Integer pessoa) throws SQLException;

    String obterAssinaturaBiometriaFacial(Integer pessoa) throws SQLException;

    boolean verificaAssinaturaBiometriaFacial(Integer pessoa) throws SQLException;

    void excluirAssinaturaBiometriaComLog(PessoaVO pessoaVO, TipoAssinaturaBiometricaEnum tipoAssinaturaBiometricaEnum, UsuarioVO usuarioVO) throws SQLException;

    /**
     * @return retorna a pessoa do usuário PACTOBR.
     */
    PessoaVO getPessoaPactoBR() throws Exception;

    TipoPessoa consultarTipoPessoa(int codigoPessoa) throws Exception;

    List<PessoaTO> consultarNomeSimples(int codigoEmpresa, String nomePessoa) throws Exception;

    public List<Integer> getCodigosPessoaEmbaralhar() throws  Exception;

    public void atualizarNomeFotoKey(Integer codigo, String nome, String fotokey) throws  Exception;

    void gravarModalAtualizacaoCadastral(PessoaVO obj, UsuarioVO usuarioVO) throws Exception;

    public void alterarSpiviClientID(Integer codigoPessoa, final Integer spiviClientID) throws Exception;

    void alterarIdMundiPagg(PessoaVO pessoa) throws Exception;

    void alterarIdPagarMe(PessoaVO pessoa) throws Exception;

    void obterInformacoesDeBloqueioCobrancaAutomatica(PessoaVO pessoaVO) throws Exception;

    void alterarDataBloqueioCobrancaAutomatica(Date dataBloqueioCobrancaAutomatica, TipoBloqueioCobrancaEnum tipoBloqueioCobrancaEnum,
                                               Integer pessoa, UsuarioVO usuarioVO, boolean controlaTransacao, String mensagemAdicionalLog) throws Exception;

    Map<Integer, PessoaVO> obterMapaPessoasBloqueioCobrancaAutomatica() throws Exception;

    boolean podeCobrarParcelaBloqueioCobrancaAutomatica(MovParcelaVO movParcelaVO, Map<Integer, PessoaVO> mapaPessoaBloqueio);

    Integer descobrirCodigoPessoa(Integer codigoCliente, Integer codigoColaborador, Integer codigoMovParcela, Integer codigoContrato) throws Exception;

    List<PessoaBloqueioCobrancaTO> consultarPessoasComCobrancaBloqueada(Integer empresa, Date inicio, Date fim) throws Exception;

    List<PessoaBloqueioCobrancaTO> consultarPessoasParaBloqueio(boolean desbloquear, ManutencaoAjusteGeralTO obj) throws Exception;

    Integer obterPessoaCliente(Integer cliente) throws Exception;

    Integer obterPessoaColaborador(Integer colaborador) throws Exception;

    void atualizarDados(Boolean atualizarDados, Integer codPessoa) throws SQLException;

    List<String> nomesColaboradores(List<Integer> codigosColaboradores) throws SQLException;

    PessoaCPFTO obterCpfValidandoIdade(PessoaVO pessoaVO) throws Exception;

    int obterPessoaPorIdExternoIntegracao(String idExternoIntegracao) throws Exception;

    String uploadTemplateFotoPessoa(String key, int codigoPessoa, String templateFoto) throws Exception;

    Integer codigoPessoaResponsavel(String nome) throws Exception;

    String obtemNumeroTelefone(Integer pessoa) throws Exception;

    String obterCodigoViteo (Integer pessoa) throws Exception;

    Boolean existeContratoParaPessoa (Integer pessoa) throws Exception;

    public PessoaVO consultarPorMatriculaExterna(Integer matriculaExterna, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public PessoaVO consultarPessoaEmpresaFinanSemAlterar(EmpresaVO empresa) throws Exception;

    public void separarPessoaColaborador(Integer codigoColaborador, PessoaVO pessoaColaborador, PessoaVO pessoaAluno) throws Exception;

    int contarPessoas() throws Exception;

    public List<PessoaVO> consultarPorPassaporte(String passaporte, int nivelMontarDados) throws Exception;
}

package negocio.interfaces.contrato;

import br.com.pactosolucoes.enumeradores.TipoContratoEnum;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.plano.ConsultarAlunosTurmaVO;
import negocio.comuns.plano.ModalidadeVO;
import org.json.JSONArray;
import controle.contrato.ContratoControle;
import negocio.comuns.basico.ClienteSimplificadoTO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.contrato.ContratoModalidadeVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.CaixaVO;
import negocio.comuns.financeiro.EstornoReciboVO;
import negocio.comuns.plano.PlanoVO;
import negocio.interfaces.basico.SuperInterface;
import org.json.JSONObject;
import relatorio.negocio.comuns.basico.ItemRelatorioTO;
import relatorio.negocio.comuns.basico.PendenciaResumoPessoaRelVO;
import relatorio.negocio.comuns.sad.RenovacaoSinteticoVO;
import relatorio.negocio.comuns.sad.RotatividadeAnaliticoDWVO;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

import negocio.comuns.arquitetura.UsuarioVO;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface 
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface ContratoInterfaceFacade extends SuperInterface {

    public ContratoVO novo() throws Exception;

    public void alterar(ContratoVO obj) throws Exception;

    public void excluir(ContratoVO obj) throws Exception;

    public void alterarSituacaoContrato(ContratoVO obj) throws Exception;

    public void alterarSituacaoContrato(ContratoVO obj, Boolean controleAcesso) throws Exception;

    public void alterarDatasVigenciaContrato(ContratoVO obj) throws Exception;

    public void alterarDatasVigenciaContrato(ContratoVO obj, Boolean controleAcesso) throws Exception;

    public void estornoContrato(ContratoVO obj, ClienteVO cliente, CaixaVO caixaAberto, final String observacao) throws Exception;

    public Double calcularValorContrato(ContratoVO obj, boolean vendaContrato, boolean addArrendondamento) throws Exception;

    public void calcularProdutoSugeridoPlano(ContratoVO obj) throws Exception;

    public ContratoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    Map<Integer, ContratoVO> mapearContratoPorCodigo(Set<Integer> ids) throws Exception;

    public ContratoVO consultarPorChavePrimariaComModalidade(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public ContratoVO consultarPorCodigo(Integer valorConsulta, int nivelMontarDados) throws Exception;
    public String consultaOrigemSistema(Integer valorConsulta) throws Exception;

    public List<ContratoVO> consultarPorCodigos(String valorConsulta, int nivelMontarDados) throws Exception;

    public List<ContratoVO> consultarContratosAtivosNaoAssinados(int nivelMontarDados) throws Exception;

    public boolean contratoExiste(Integer codigo) throws Exception;

    public List consultarPorNomeEmpresa(String valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorNomePessoa(String valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorCodigoPessoa(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public ContratoVO consultarPorCodigoPessoa(Integer codigoPrm, int nivelMontarDados) throws Exception;

    public List consultarPorDescricaoPlano(String valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorSituacao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorDescricaoHorario(String valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorDescricaoCondicaoPagamento(String valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorSituacaoColaborador(String valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorParcelaPessoaData(String valorConsulta, Integer empresa,
            Date dataInicio, Date dataTermino, boolean incluirParcelasRegimeRecorrencia,
            int nivelMontarDados) throws Exception;

    public List consultarPorParcelaPessoa(String valorConsulta, Integer empresa, int nivelMontarDados,
            boolean incluirParcelasRegimeRecorrencia) throws Exception;

    public List<ContratoVO> consultarPorSituacaoContratoECodigoPessoa(String valorConsulta, Integer pessoa, int nivelMontarDados) throws Exception;
    
    public List consultarPorSituacaoContratoECodigoPessoaSemBolsa(String valorConsulta, Integer pessoa, int nivelMontarDados) throws Exception;

    public List consultarPorSituacaoContratoECodigoPessoa(String valorConsulta, Integer pessoa, Boolean verificarPermissao, int nivelMontarDados) throws Exception;

    public List consultarContratoNaoRenovadoPorContratoResponsavelRenovacaoMatriculaSituacaoContrato(Integer empresa, String situacao, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarContratoNaoRenovadoPorContratoResponsavelRenovacaoMatricula(Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarContratoRenovadoPorContratoResponsavelRenovacaoMatriculaSituacaoContrato(Integer empresa, String situacao, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarContratoRenovadoPorContratoResponsavelRenovacaoMatricula(Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarContratoPorDataInicioOperacao(Date data, Integer empresa, Boolean controleAcesso, int nivelMontarDados) throws Exception;

    public List consultarContratoQueIniciaHoje(Date data, Integer empresa, Boolean controleAcesso, int nivelMontarDados) throws Exception;

    public Integer consultaQuantidadeRematriculaPorDataEmpresa(Date prmIni, Date prmFim, Integer empresa, boolean controlarAcesso) throws Exception;

    public Integer consultaQuantidadeMatriculaPorDataEmpresa(Date prmIni, Date prmFim, Integer empresa, boolean controlarAcesso) throws Exception;

    public Integer consultaQuantidadeMatriculaPorDataEmpresaColaborador(Date prmIni, Date prmFim, Integer empresa, String codigoColaboradores, boolean controlarAcesso) throws Exception;
    
    public ContratoVO consultarPorSituacaoCodigoPessoa(Integer valorConsulta, String situacao, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public ContratoVO consultarUltimoContratoVigerntePorCodigoPessoa(Integer valorConsulta, String situacao, boolean ignorarContratoCompartilhado, boolean controlarAcesso, int nivelMontarDados, Boolean ignorarRenovadoRematriculados) throws Exception;

    public ContratoVO consultarContratoPorContratoBaseadoRenovacao(Integer contrato, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public Integer obterQuantidadeDeContratoPorCliente(Integer pessoa, Integer empresa, Date data, Boolean contrelAcesso, int nivelMontarDados) throws Exception;

    public ContratoVO obterContratoVigenteConcomitante(Integer pessoa, Integer empresa, Date data, Boolean contrelAcesso, int nivelMontarDados) throws Exception;

    public ContratoVO consultarContratoVigentePorPessoaEData(Integer pessoa, Date data, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarContratoVigenteData(Date data, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorSituacaoCodigoEmpresa(Integer empresa, String situacao, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<Integer> consultarCodigoPotenciaisAVencer(Date data, Integer empresa, Boolean contrelAcesso, final int nrDiasAVencer) throws Exception;

    public List<Integer> consultarCodigoPotenciaisVencidos(Date data, Integer empresa, Boolean contrelAcesso) throws Exception;

    public List<Integer> consultarCodigoPotenciaisDesistentes(Date data, Integer empresa, Boolean contrelAcesso) throws Exception;

    public List<ContratoVO> consultarPorHistoricoComVinculoDiferenteDeConsultor(Date data, String tipoHistorico, Integer codigoEmpresa, int nivelMontarDados) throws Exception;

    public List<ContratoVO> consultarPorHistoricoContratoAndContrato(Date data, String tipoHistorico, Integer codigoEmpresa, List<ContratoVO> listaContrato, int nivelMontarDados) throws Exception;

    public List<ContratoVO> consultarContratosVigerntePorDataPessoa(Integer pessoa, Date dataAnterior, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarContratosVigernteCanceladoPorCodigoPessoa(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public ContratoVO consultarUltimoContratoVigernteCanceladoPorCodigoPessoa(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public ContratoVO obterContratoResponsavelPorContratoBase(Integer contrato, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public Boolean consultarPorCodigoPessoaSemExcecao(Integer codigoPrm, int nivelMontarDados) throws Exception;

    public ContratoVO consultarContratoVigentePorPessoa(Integer pessoa, boolean controlarAcesso, boolean somenteAtivo, int nivelMontarDados) throws Exception;

    public boolean possuiContratoVigentePessoa(int codigoPessoa,int contratoDesconsiderar) throws Exception;

    public List consultarPorCodigoPessoaOrdenadoPelaDataVencimentoContrato(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados, Integer limit) throws Exception;

    public Boolean consultarContratoSeEstaVirgente(Integer codigo, Date data, boolean controlarAcesso) throws Exception;

    public void alterarContratoHorario(ContratoVO obj) throws Exception;

    public void alterarContratoManutencaoModalidade(List<ContratoModalidadeVO> listaModalidadeAdicionada, List<ContratoModalidadeVO> listaModalidadeExcluida, List<ContratoModalidadeVO> listaModalidadeAlterada, ContratoVO contratoNovo, ContratoVO contratoAntigo, UsuarioVO usuario) throws Exception;

    public Date consultarUltimoVencimento(Integer codigoPessoa) throws Exception;

    /**
     * Alterar o valor base do contrato e valor final.
     *
     * @param obj
     * @throws Exception
     */
    void alterarValoresContrato(ContratoVO obj) throws Exception;

    public void alterarValorBaseContrato(ContratoVO obj, Boolean controleAcesso) throws Exception ;
    
    /**
     * conta a qtde de contratos previstos para renovar no periodo informado por parametro
     * @param empresa
     * @param dataInicio
     * @param dataFim
     * @param lista
     * @param apenasRenovados
     * @param condicaoDuracao
     * @return qtde
     * @throws Exception
     */
    public int contarQtdeContratoPrevistoPeriodo(Integer empresa, Date dataInicio, 
            Date dataFim, List<ColaboradorVO> lista, boolean apenasRenovados, 
            String condicaoDuracao, String condicaoEspontaneoAgendado) throws Exception;

    public  int contarQtdeContratoPrevistoPeriodo(Integer empresa,
            Date dataInicio, Date dataFim,Date dataRenovacao, List<ColaboradorVO> lista,
            boolean apenasRenovados, String condicaoDuracao, String condicaoEspontaneoAgendado, Boolean realizadoDoMes)
            throws Exception ;

    public String clientesComContratosPrevistosPeriodoJSON
            (Integer empresa, Date dataInicio, Date dataFim, List<ColaboradorVO> lista,
             String tipoConsulta, String condicaoDuracao, String condicaoEspontaneoAgendado,
             List<ClienteSimplificadoTO> alunosComMuitosProfessores) throws Exception;

    public List<RenovacaoSinteticoVO> clientesComContratosPrevistosPeriodoExportar
            (Integer empresa, Date dataInicio, Date dataFim, List<ColaboradorVO> lista,
             String tipoConsulta, String condicaoDuracao, String condicaoEspontaneoAgendado,
             String campoOrdenacao, String ordem, String filtro, List<ClienteSimplificadoTO> alunosComMuitosProfessores) throws Exception;

    public List<ClienteSimplificadoTO> clientesComContratosPrevistosPeriodo(Integer empresa,
                                                                            Date dataInicio, Date dataFim, List<ColaboradorVO> lista, boolean apenasRenovados,
                                                                            String condicaoDuracao, String condicaoEspontaneoAgendado) throws Exception;

    public Integer obterQuantidadeContratosAtivos(Integer empresa, Date data) throws Exception;

    public int contarQtdeContratoPrevistoPeriodo(Integer empresa,
            Date dataInicio, Date dataFim, String condicaoConsultores,
            boolean apenasRenovados, String condicaoDuracao, String condicaoEspontaneoAgendado)
            throws Exception;

    /**
     * conta a qtde de contratos previstos para renovar no periodo informado por parametro
     * @param empresa
     * @param dataInicio
     * @param dataFim
     * @return qtde
     * @throws Exception
     */
    public int contarQtdeContratoPrevistoRenovadoPeriodo(Integer empresa, Date dataInicio, Date dataFim, Date dataRenovar, List<ColaboradorVO> lista) throws Exception;

    /**
     *
     * @param empresa       0 para consultar de todas as empresas
     * @param dataInicio
     * @param dataFim
     * @param dataRenovar
     * @param lista
     * @return
     * @throws Exception
     */
    List<ClienteSimplificadoTO> clientesComContratoPrevistoRenovadoPeriodo(Integer empresa, Date dataInicio, Date dataFim, Date dataRenovar, List<ColaboradorVO> lista) throws Exception;

    public List consultarContratoPrevistoRenovacao(Date dataInicio, Date dataFim, Integer empresa, Boolean bolsa, Boolean trancado, Boolean cancelado, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    /**
     * consulta os contratos previstos para renovar no periodo informado por parametro
     *
     * @param empresa           0 para consultar de todas as empresas
     * @param dataInicio
     * @param dataFim
     * @return qtde
     * @throws Exception
     */
    public List<Date> consultarContratoDataPrevistoPeriodo(Integer empresa,
            Date dataInicio, Date dataFim, List<ColaboradorVO> lista)
            throws Exception;

    public Boolean consultarContratoCancelado(Integer codContrato)
            throws SQLException;

    public ContratoVO consultarUltimoContratoClientePorSituacaoContrato(
            Integer pessoa, String situacaoContrato,
            boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public Optional<ContratoVO> consultarUltimoContratoClienteComPlanoCredito(Integer pessoa, int nivelMontarDados) throws Exception;

    public void gerarListaContratosInconformes() throws Exception;

    public void gerarSituacoesTemporaisContratos(Integer codigoPrimeiroContrato, Integer codigoEmpresa) throws Exception;

    public List<ContratoVO> consultarContratosVigentes() throws Exception;

    public List<ContratoVO> consultar(String tabelasFrom,
            String condicao, final int nivelMontarDados)
            throws SQLException, Exception;

    /**
     * Retorna verdadeiro se existe um contrato com o código passado iniciado antes
     * de dataBase informada
     * @param codigoContrato
     * @param dataBase
     * @return
     */
    public boolean existeOutroContratoComecandoNaData(int codigoContrato,
            Date dataBase) throws SQLException, Exception;

    List<ContratoVO> consultar(String sql, final int nivelMontarDados) throws Exception;

    List<ContratoVO> consultar(String sql, boolean montarDadosAgregadores, final int nivelMontarDados) throws Exception;

    public Integer contar(String sql) throws SQLException, Exception;

    public List consultarPorCodigoPessoaOrdenadoPelaDataVencimentoContratoPorPlano(Integer pessoa, Integer plano, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void incluirContrato(ContratoVO obj) throws Exception;

    public void alterarContratoResponsavelRenovacaoMatriculaSemCommit(ContratoVO contrato) throws Exception;

    public Date consultarDataRematricula(Integer codigo) throws Exception;

    public List<ContratoVO> consultarContratosEstornoAutomatico(Date dia,
            int toleranciaDias) throws Exception;

    public List<ContratoVO> consultaContratosPorCodigoRecibo(int recibo, int nivelMontarDados) throws Exception;

     public int contarContratoAlteracaoManual(Date inicio, Date fim, Integer empresa, List<ColaboradorVO> lista) throws Exception;

    public List<ContratoVO> consultarPorIntervaloDatasEmpresaDataAlteracaoManual(Date inicio, Date fim, int empresa,List<ColaboradorVO> lista,final int nivelMontarDados) throws Exception;
    
    public void alteraTipoContrato(ContratoVO contratoVO, TipoContratoEnum valorAntesAlteracao, UsuarioVO usuarioResponsavelVO) throws Exception;

    public List<ContratoVO> consultarPorHorarioTurma(int codigoHorario, int nivelMontarDados) throws Exception;

    public List<ContratoVO> consultarPorClientePeriodoDataLancamento(ClienteVO clienteVO, Date dataInicial, Date dataFinal, int nivelMontarDados) throws Exception;

    public ContratoVO consultarContratoVigentePorPessoaSintetico(final int pessoa,Date dataAtual) throws Exception;

    public Object consultarValorColunaContrato(final String nomeColuna, final String condicao) throws Exception;

    public void alterarConsultorContrato(ContratoVO obj) throws Exception;

    public String nomePlano(Integer contrato) throws Exception;

    public int duracao(Integer contrato) throws Exception;

    public void lancarSaidaEstornoRecibo(EstornoReciboVO estorno, ContratoVO obj, CaixaVO caixaAberto) throws Exception;
    
    public void alterarApenasDadosBasicos(ContratoVO obj) throws Exception;
    
    public int percentualContratosLancadosPorDuracao(Integer duracaoDesejada, Date inicio, Date fim, Integer empresa) throws Exception;
    
    public RotatividadeAnaliticoDWVO montarRotatividadeResumido(Integer empresa, Date data) throws Exception;
    
    public Integer consultaPrevistosRenovacaoResumido(Date data, Integer empresa) throws Exception;
    
    public Integer consultaIndiceRenovacaoResumido(Date data, Integer empresa) throws Exception;
    
    public List <ContratoVO> consultarListaPorSituacaoCodigoPessoa(Integer valorConsulta, String situacao, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void  consultaPlanoDuracao(int pessoa, ContratoVO obj) throws Exception;
    
    public List<ContratoVO> consultarContratosVencendo(Date dia,
            Integer carenciaRenovacao,
            Integer nrDiasRenovacaoAntecipadaAutomatica,
            Integer empresa,
            int nivelMontarDados, Connection con) throws Exception;

    public List<ContratoVO> consultarContratosPrimeiraParcelaVencidaEstornoAutomatico(Date dia,
                                                                                      int toleranciaDias) throws Exception;

    public List<ContratoVO> consultarContratosAtivosPorPlano(Integer codigoPlano, Integer codigoEmpresa) throws Exception;

    public void alterarRenovarAutomaticamenteContratosAtivos(PlanoVO planoVO) throws Exception;
    
    public List<Integer> obterContratosAVencer(Integer professor, Date inicio, Date fim, Integer empresa) throws Exception;

    public List<Integer> obterDadosContratosBITreino(Integer professor, Date inicio, Date fim, Integer empresa, boolean cancelados) throws Exception;
    
    public JSONArray obterListaContratosBITreino(Integer professor, Date inicio, Date fim, Integer empresa, boolean naoRenovado) throws Exception;

    void montarListasParaEstorno(ContratoVO contratoVO) throws Exception;
    
    public Date retornarDataInicioNovoContratoRenovacao(ContratoVO contratoVO)throws Exception;
    
    public Integer consultarTotalContratos(Integer codigoPlano)throws Exception;
    
    public void alterarCarenciaTodosContratoParaCarenciaQueEstaNoPlano(Integer codigoPlano)throws Exception;
    
    public List<ContratoVO> consultarListaTela(Integer pessoa, Integer limit)throws Exception;
    
    public ContratoVO consultarContratoDetalhe(Integer contrato) throws Exception;

    public void validarContratoConcomitante(ContratoVO novoContrato)throws Exception;
    
    public List consultarContratoMudancaDeSituacao(Date data, Integer empresa,String tipoOperacao, Boolean controleAcesso, int nivelMontarDados) throws Exception;

    public List<ContratoVO> consultarContratoCancelado(Integer codPessoa,int nivelMontarDados) throws Exception;

    public ContratoControle obterContratoControle(int plano, int cliente);

    public List<Integer> clientesComContratosPrevistosPeriodo(Integer empresa,
                                                                           Date dataInicio, Date dataFim,
                                                                           Integer professor,
                                                                           boolean apenasRenovados,
                                                                           String condicaoDuracao,
                                                                           String condicaoEspontaneoAgendado) throws Exception ;

    Long consultaQuantidadePorPlano(Integer codigoPlano) throws Exception;

    int quantidadeContratosNaoRecorrentesVigentes(Integer codigoPessoa) throws Exception;

    int quantidadeContratosRecorrentesVigentes(Integer codigoPessoa) throws Exception;

    List<ContratoVO>consultarContratosVigentesPorPessoa(Integer codigoPessoa, int nivelMontarDados) throws Exception;

    List<ContratoVO>consultarContratosVigentesPorCliente(Integer codigoCliente, int nivelMontarDados) throws Exception;

    public ContratoVO obterRenovacaoContrato(Integer contratoAtual, boolean controlarAcesso, int nivelMontarDados) throws Exception;
    public boolean existeRenovacaoContrato(Integer contratoAtual) throws Exception;

    Date consultarDataUltimaAulaPrevistaContratoCredito(ContratoVO contratoVO, Date dataInicio)throws Exception;

    void alterarPermiteRenovacaoAutomatica(ContratoVO contratoVO, boolean renovarAutomaticamente,
                                           UsuarioVO usuarioVO, ClienteVO clienteVO) throws Exception;

    public Integer obterQuantidadeDeContratoRenovadoAutomaticamente(String ano,String mes) throws Exception;
    public void gravarInformacoesAssinaturaContratoEmail(ContratoVO contrato) throws Exception;
    void propagarAssinaturaEletronicaContrato(ContratoVO obj) throws Exception;
    public boolean existeContratoConcomitantePessoaNaData(Integer pessoa, Date dataPesquisa) throws Exception;

    public boolean existeAlgumContratoConcomitanteVigentePessoaNaData(Integer pessoa, Integer contrato, Date dataPesquisa) throws Exception;

    public boolean existeAlgumContratoConcomitanteVigenteOuVencidoPessoaNaData(Integer pessoa, Integer contrato, Date dataPesquisa) throws Exception;

    public List<ContratoVO>consultarContratosRenovar(Integer codigoPessoa, int nivelMontarDados) throws Exception;

    public List<ContratoVO>consultarContratosRecorrenciaRenovar(Integer codigoPessoa, Integer carenciaRenovacao, boolean totem, int nivelMontarDados) throws Exception;

    public List<ContratoVO> consultarContratosRematricular(Integer codigoPessoa, int nivelMontarDados) throws Exception;

    public List<ContratoVO> consultarContratoPorDataVigenciaBV(Integer pessoa,Date dataInicial,Date dataFinal,Integer mesReferencia, Integer codigoConsultor,int nivelMontarDados)throws Exception;

    public ContratoVO simularContratoSite(String key, int plano, int cliente, int empresa,
                                          int nrParcelasAdesao, int nrParcelasProduto, String numeroCupomDesconto, boolean gerarAdesao,
                                          boolean gerarAnuidade, int nrParcelasPagamento, int contratoRenovar) throws Exception;

    public ContratoVO gravarContratoSite(String key,int plano,int cliente,int nrParcelasAdesao, int nrParcelasProduto, String numeroCupomDesconto, boolean gerarAdesao, boolean gerarAnuidade, int nrParcelasPagamento)throws Exception;

    public ContratoVO gravarContratoSite(String key, int plano, int cliente,
                                          int nrParcelasAdesao, int nrParcelasProduto, String numeroCupomDesconto, boolean gerarAdesao,
                                          boolean gerarAnuidade, int nrParcelasPagamento, int contratoRenovar) throws Exception;

    public ContratoVO gravarContratoSite(String key, int plano, int cliente,
                                         int nrParcelasAdesao, int nrParcelasProduto, String numeroCupomDesconto, boolean gerarAdesao,
                                         boolean gerarAnuidade, int nrParcelasPagamento, boolean simular, int contratoRenovar,
                                         int empresacod,
                                         boolean jsf,
                                         UsuarioVO usuario,
                                         Integer diaVencimentoCartaoRecorrencia, Integer consultor) throws Exception;

    ContratoVO gravarContratoSite(String key,
                                  int plano,
                                  int cliente,
                                  int nrParcelasAdesao,
                                  int nrParcelasProduto,
                                  String numeroCupomDesconto,
                                  boolean gerarAdesao,
                                  boolean gerarAnuidade,
                                  int nrParcelasPagamento,
                                  boolean simular,
                                  int contratoRenovar,
                                  int empresacod,
                                  boolean jsf,
                                  UsuarioVO usuario,
                                  Integer diaVencimentoCartaoRecorrencia,
                                  ClienteVO indicadoPor,
                                  boolean validarInclusaoConcomitante,
                                  Integer contratoRematricula, Integer consultor, Boolean parcelamentoOperadora, Integer codigoEvento, boolean lancarConcomitante) throws Exception;

    ContratoVO gravarContratoSite(String key,
                                         int plano,
                                         int cliente,
                                         int nrParcelasAdesao,
                                         int nrParcelasProduto,
                                         String numeroCupomDesconto,
                                         boolean gerarAdesao,
                                         boolean gerarAnuidade,
                                         boolean gerarValorMatricula,
                                         int nrParcelasPagamento,
                                         boolean simular,
                                         int contratoRenovar,
                                         int empresacod,
                                         boolean jsf,
                                         UsuarioVO usuarioLogado,
                                         Integer diaVencimentoCartaoRecorrencia,
                                         ClienteVO indicadoPor,
                                         boolean validarInclusaoConcomitante,
                                         Integer contratoRematricula,
                                         Date dataVencimentoParcelaAnuidade, Integer consultor, ContratoVO contrato, boolean trocaPlano,
                                         Date dataPrimeiraPacela, Boolean parcelamentoOperadora, Integer codigoEvento, boolean lancarConcomitante) throws Exception;

    Integer contarComAutorizacaoContratoRenovavel(Integer codigoEmpresa, Date dataBase, List<ColaboradorVO> consultores) throws Exception;

    Integer contarComAutorizacaoNaoRenovavel(Integer codigoEmpresa, Date dataBase, List<ColaboradorVO> consultores) throws Exception;

    List<PendenciaResumoPessoaRelVO> obterAlunosAutorizacaoContratos(Integer codigoEmpresa, Date dataBase, List<ColaboradorVO> consultores, ConfPaginacao paginacao,
                                                                     boolean count, boolean comAutorizacaoNaoRenovavel, boolean comAutorizacaoContratoRenovavel);

    ResultSet consultaGeralContratosAutorizacao(Integer codigoEmpresa, Date dataBase, List<ColaboradorVO> consultores, ConfPaginacao paginacao,
                                                boolean count, boolean comAutorizacaoNaoRenovavel, boolean comAutorizacaoContratoRenovavel) throws Exception;

    Date consultarUltimaDataVigenciaPorCliente(ClienteVO cliente) throws SQLException;

    int consultarDiasVigenciaContratoAtivo(ClienteVO cliente) throws SQLException;

    int consultarTotalContratosIniciamApos(Date inicioMinimoContrato, Integer codigoPlano) throws SQLException;

    List<ContratoVO> consultarContratosIniciamApos(Date inicioMinimoContrato, Integer codigoPlano, int nivelMontarDados) throws Exception;

    ContratoVO consultarUltimoContratoPorPessoa(Integer pessoa, int nivelMontarDados) throws Exception;

    ContratoVO consultarContratoVigentePorPessoaEDataMaisVigencia(Integer pessoa, Date data, boolean controlarAcesso, int tolerancia, int nivelMontarDados) throws Exception;

    Integer atualizarDataContratos(Integer codigo, Integer diasPlano) throws Exception;

    Map<String, Integer> vendasOnlinePlano(Integer empresa, Date dataInicial, Date dataFinal) throws Exception;

    Integer vendasOnlineProdutoTotal(Integer empresa, Date dataInicial, Date dataFinal) throws Exception;

    Integer vendasOnlinePlanoTotal(Integer empresa, Date dataInicial, Date dataFinal) throws Exception;

    List<ItemRelatorioTO> vendasOnlinePlanoLista(Integer empresa, Date dataInicial, Date dataFinal) throws Exception;

    List<ItemRelatorioTO> vendasOnlineProdutoLista(Integer empresa, Date dataInicial, Date dataFinal) throws Exception;

    List<ConsultarAlunosTurmaVO> contarEntradaPorModalidadeNoPeriodo(Date dataInicio, Date dataFim, ModalidadeVO modalidadeVO, EmpresaVO empresaVO) throws Exception;

    List<String> getContratoModalidades(Integer codigoPessoa, boolean exibirTodasModalidades) throws Exception;

    int countModalidadesContrato(Integer pessoa) throws Exception;

    void gerarMatriculaAlunoTurma(ContratoVO contratoVO) throws Exception;

    Double consultarValorPagoContrato(Integer codigoContrato) throws Exception;

    Boolean verificaSeContratoFoiAssinado(Integer codigoContrato) throws Exception;

    public Integer consultarUltimaDuracao(Integer codigoPessoa) throws Exception;

    ContratoVO consultarContratoTransferido(Integer codigoPessoa, String matriculaTitular, int nivelMontarDados) throws Exception;

    List<Integer> consultarIdContratosVencendoNaoRecorrente(Date dia, Integer carenciaRenovacao, Integer nrDiasRenovacaoAntecipadaAutomatica, Integer empresa, Connection con) throws Exception;

    boolean existeContratosVigentesPorPessoaOuPessoaOriginal(Integer codigoPessoa) throws Exception;

    List<ContratoVO> consultarContratosVigentesAtivosPorPessoaOriginal(Integer codigoPessoa, int nivelMontarDados) throws Exception;

    int consultarQuantidadeContratosAtivosPorPessoa(Integer codigoPessoa) throws Exception;

    void alterarConsultorContratoGeral(ContratoVO contratoVO, ClienteVO clienteVO,
                                       UsuarioVO usuarioResponsavel) throws Exception;

    Map<String, Boolean> apresentarOperacaoNoMenuCliente(ContratoVO contratoSelecionadoVO, ClienteVO clienteVO,
                                                         EmpresaVO empresaLogadoVO, UsuarioVO usuarioLogadoVO,
                                                         List<ContratoVO> listaContratosCliente) throws Exception;

    boolean validarPermissaoParaEstornarContrato(ContratoVO contratoSelecionadoVO, ClienteVO clienteVO,
                                                 EmpresaVO empresaLogadoVO, UsuarioVO usuarioLogadoVO);

    public String consultarLifeTimeContratosVigentesJSON(Boolean configLtvRealizado, Integer empresa, Date dataPesquisa) throws Exception;

    public List<RenovacaoSinteticoVO> consultarLifeTimeContratosVigentesExportar(Boolean configLtvRealizado, Integer empresa, Date dataInicio, String campoOrdenacao, String ordem, String filtro) throws Exception ;

    public ContratoVO consultarUltimoContratoNaoRenovadoRematriculadoPorCodigoPessoa(Integer codigoPessoa, boolean controlarAcesso, int nivelMontarDados, boolean ignorarContratoCompartilhado, Integer empresa) throws Exception;

    public Integer obterCodigoPrimeiroContratoBaseadoRenovacao(Integer codigoContrato) throws Exception;

    public List consultarPorCodigoPessoaEPessoaOriginalOrdenadoPelaDataVencimentoContrato(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados, Integer limit) throws Exception;

    public void acrescentarDiasContrato(ContratoVO contratoVO, int qtdDiasAcrescentar) throws Exception;

    JSONObject calcularProRataDeAlteracaoVencimentoParcelas(ClienteVO clienteVO, ContratoVO contratoVO,
                                                            Integer diaVencimento, EmpresaVO empresaVO,
                                                            UsuarioVO usuarioVO) throws Exception;

    void alterarXnumpro(Integer codigo, String xnumproSesice) throws SQLException;
    void alterarIdExterno(Integer codigoContrato, Integer idMatriculaSesice) throws SQLException;

    public boolean existeMatriculaRematriculaTotemSemPagamento(Integer pessoa) throws Exception;

    Integer contarContratosAtivosTipoPlano(Integer plano, Integer tipoPlano) throws Exception;

    boolean verificarContratoEmBanco(Integer codigo) throws Exception;

    void atualizarDataSincronizacaoIntegracaoFoguete(Integer codigoContrato, Date dataSincronizacao) throws Exception;

}

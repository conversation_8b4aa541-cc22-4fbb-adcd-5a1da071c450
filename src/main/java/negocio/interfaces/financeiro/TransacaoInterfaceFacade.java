/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.interfaces.financeiro;

import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaVO;
import br.com.pactosolucoes.comuns.to.GrupoParcelasTO;
import br.com.pactosolucoes.comuns.to.GrupoTransacoesErrosTO;
import br.com.pactosolucoes.comuns.to.GrupoTransacoesTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.contrato.ContratoRecorrenciaVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.CodigoRetornoPactoEnum;
import negocio.comuns.financeiro.enumerador.OrigemCobrancaEnum;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.interfaces.basico.SuperInterface;
import org.json.JSONObject;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface TransacaoInterfaceFacade extends SuperInterface {

    void alterar(TransacaoVO obj) throws Exception;

    List<TransacaoVO> consultar(Date dataInicio, Date dataFim, SituacaoTransacaoEnum situacao, Integer empresa, TipoTransacaoEnum tipoTransacaoEnum,
                                CodigoRetornoPactoEnum codigoRetornoPactoEnum, boolean apresentarCobrancaVerificarCartao) throws Exception;

    List<TransacaoVO> consultar(Date dataInicio, Date dataFim, SituacaoTransacaoEnum situacao, Integer empresa, TipoTransacaoEnum tipoTransacaoEnum,
                                CodigoRetornoPactoEnum codigoRetornoPactoEnum, String filtro, Integer limit, Integer offset, boolean apresentarCobrancaVerificarCartao) throws Exception;

    List<TransacaoVO> consultar(Date dataInicio, Date dataFim, SituacaoTransacaoEnum situacao, Integer empresa, TipoTransacaoEnum tipoTransacaoEnum,
                                CodigoRetornoPactoEnum codigoRetornoPactoEnum, String filtro, Integer limit, Integer offset,
                                boolean apresentarCobrancaVerificarCartao, int nivelMontarDados) throws Exception;

    Integer obterCountTransacoes(Date dataInicio, Date dataFim, SituacaoTransacaoEnum situacao, Integer empresa, TipoTransacaoEnum tipoTransacaoEnum,
                                 CodigoRetornoPactoEnum codigoRetornoPactoEnum, String filtro, boolean apresentarCobrancaVerificarCartao) throws Exception;

    List<TransacaoVO> consultar(String sql, final int nivelMontarDados) throws SQLException, Exception;

    TransacaoVO consultarPorChavePrimaria(final int codigo) throws Exception;

    TransacaoVO consultarPorChavePrimaria(final int codigo, int nivelMontarDados) throws Exception;

    String consultarParamsRespostaPorChavePrimaria(final int codigo) throws Exception;

    String consultarCodigoExterno2PorChavePrimaria(final int codigo) throws Exception;

    boolean possuiTransacaoAprovadaCodigoAutenticacao01NosUltimos2Dias(String codigoAutenticacao01) throws Exception;

    List<TransacaoVO> consultarGeradasAgoraPelaRecorrencia(final int codigoEmpresa, final int usuarioRecorrencia) throws Exception;

    TransacaoVO consultarPorCodigoExterno(final String codigo) throws Exception;

    Connection getCon();

    List<TransacaoVO> consultarPorRemessa(final int codigoRemessa) throws Exception;

    List<GrupoTransacoesTO> consultarQuantidadeAgrupandoPorSituacao(Date dataInicio, Date dataFim, SituacaoTransacaoEnum situacao, Integer empresa,
                                                                    TipoTransacaoEnum tipoTransacaoEnum, CodigoRetornoPactoEnum codigoRetornoPactoEnum,
                                                                    String filtro, boolean apresentarCobrancaVerificarCartao) throws Exception;

    Integer consultarQuantidadeTransacoesASeremCobradas(Date dataInicio, Date dataFim, Integer empresa) throws Exception;

    Integer consultarQuantidadeTransacoesASeremCobradas(Date dataInicio, Date dataFim, SituacaoTransacaoEnum situacao,
                                                        Integer empresa, TipoTransacaoEnum tipoTransacaoEnum, CodigoRetornoPactoEnum codigoRetornoPactoEnum,
                                                        String filtro) throws Exception;

    List<GrupoTransacoesTO> consultarValoresAgrupandoPorSituacao(Date dataInicio, Date dataFim, SituacaoTransacaoEnum situacao, Integer empresa) throws Exception;

    List<GrupoTransacoesTO> consultarValoresAgrupandoPorSituacao(Date dataInicio, Date dataFim, SituacaoTransacaoEnum situacao, Integer empresa, TipoTransacaoEnum tipoTransacaoEnum,
                                                                 CodigoRetornoPactoEnum codigoRetornoPactoEnum, String filtro) throws Exception;

    GrupoParcelasTO consultarValoresParcelasAgrupandoPorSituacaoEmAberto(Date dataInicio, Date dataFim, Integer empresa) throws Exception;

    List<GrupoParcelasTO> consultarValoresParcelasAgrupandoPorSituacaoParcela(Date dataInicio, Date dataFim, SituacaoTransacaoEnum situacao, Integer empresa,
                                                                              TipoTransacaoEnum tipoTransacaoEnum, CodigoRetornoPactoEnum codigoRetornoPactoEnum,
                                                                              String filtro, boolean apresentarCobrancaVerificarCartao) throws Exception;

    List<ParcelaTransacaoVO> consultarParcelasTransacao(Date dataInicio, Date dataFim, SituacaoTransacaoEnum situacao, Integer empresa,
                                                                              TipoTransacaoEnum tipoTransacaoEnum, CodigoRetornoPactoEnum codigoRetornoPactoEnum, String filtro) throws Exception;

    GrupoParcelasTO consultarValoresParcelasAgrupandoPorSituacaoPago(Date dataInicio, Date dataFim, Integer empresa) throws Exception;

    GrupoParcelasTO consultarValoresParcelasAgrupandoPorSituacaoPago(Date dataInicio, Date dataFim, SituacaoTransacaoEnum situacao,
                                                                     Integer empresa, TipoTransacaoEnum tipoTransacaoEnum, CodigoRetornoPactoEnum codigoRetornoPactoEnum,
                                                                     String filtro) throws Exception;

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ContratoComposicaoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>ContratoComposicaoVO</code> que será removido no banco de dados.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    void excluir(TransacaoVO obj) throws Exception;

    /**
     * Método inclui uma transação esperando que esteja dentro de um bloco transacional, omitindo os métodos de 'commit' e 'rollback'
     *
     * @param obj
     * @throws Exception
     */
    void incluir(TransacaoVO obj) throws Exception;

    void incluirTransacaoMovParcelas(TransacaoVO transacaoVO) throws Exception;

    ClienteVO obterClientePorTransacao(TransacaoVO transacao) throws Exception;

    ContratoRecorrenciaVO obterContratoRecorrenciaPorTransacao(TransacaoVO transacao) throws Exception;

    List<MovParcelaVO> obterParcelasDaTransacao(TransacaoVO transacao) throws Exception;

    void preencherAtributoEmpresaTodasTransacoes() throws Exception;

    public List<TransacaoVO> consultarPorPessoa(final int codigoPessoa) throws Exception;

    String consultarParamsRespostaPorPessoaCartaoEmpresaSituacao(final int codigoPessoa, final String cartaoMascarado, final int empresa, final Integer[] situacoes) throws Exception;

    public List<TransacaoVO> consultarTelaCliente(final int codigoPessoa, int limit, int offset) throws Exception;

    public List<TransacaoVO> consultarTelaClienteTransacaoVerificacao(final int codigoPessoa, int limit, int offset) throws Exception;

    List<CancelamentoGetCardTO> consultarPorPessoaTelaCliente(Integer pessoa, int limit, int offset) throws Exception;

    Integer quantidadePorPessoa(Integer pessoa) throws SQLException;

    public Integer obterCountTransacaoCliente(Integer pessoa) throws Exception;

    public Integer obterCountTransacaoVerificacaoCliente(Integer pessoa) throws Exception;
    /**
     * Verifica se a última {@link TransacaoVO} gerada para a movparcela esta com a {@link SituacaoTransacaoEnum} informada.
     *
     * @param movParcela
     * @param aprovada
     * @return
     * @throws Exception
     */
    Boolean parcelaEstaEmTransacaoComSituacao(Integer movParcela, SituacaoTransacaoEnum aprovada) throws Exception;

    /**
     * Retorna as {@link TransacaoVO} de um determinado tipo e situacao.
     *
     * @param tipo
     * @param situacao
     * @return
     * @throws Exception
     */
    List<TransacaoVO> consultarPorTipoESituacao(TipoTransacaoEnum tipo, SituacaoTransacaoEnum situacao,
                                                Integer convenioCobranca, Integer empresa) throws Exception;

    /**
     * Retorna as {@link TransacaoVO} de um determinado tipo e situacao e pelo seu atributo aguardandoConfirmacao.
     *
     * @param tipo
     * @param situacao
     * @return
     * @throws Exception
     */
    List<TransacaoVO> consultarPorTipoESituacao(TipoTransacaoEnum tipo, SituacaoTransacaoEnum situacao, Boolean aguardandoConfirmacao,
                                                Integer convenioCobranca, Integer empresa, boolean semTransacaoAtualizadaHoje,
                                                boolean validarProximaTentativa, Date dataInicialProcessamento) throws Exception;

    TransacaoVO consultarPorCodigoExternoETipo(String codigoExterno, TipoTransacaoEnum tipo) throws Exception;

    void totalizadorPorConvenio(TotalizadorRemessaTO totalizador, Integer codigoEmpresa, Date dataInicio, Date dataFim, TipoTransacaoEnum tipoTransacao) throws Exception;

    void totalizadorBIResultadoDCC(TotalizadorRemessaTO totalizador, Integer codigoEmpresa,
                                   Date dataInicio, Date dataFim,
                                   String nrTentativas, String situacaoParcela, String somenteConvenio, String situacaoRemessa,
                                   boolean somenteMes, boolean somenteForaMes, List<Integer> convenios, String nrTentativasFiltrar) throws Exception;

    void atualizarCodigoRetorno(TransacaoVO transacaoVO);

    void excluirParcelaMultaJurosTransacao(TransacaoVO transacaoVO, MovParcelaVO parcelaMultaJuros);

    void incluirHistoricoRetorno(TransacaoVO obj, final String retorno,
                                 final String metodo, final String origemSincronizacao) throws Exception;

    void incluirHistoricoRetorno(TransacaoVO obj, final String retorno,
                                 final String metodo, final String origemSincronizacao, int statusServer, long tempoRequisicao) throws Exception;

    public void alterarMessagemErro(TransacaoVO obj, final String erro) throws Exception;

    JSONObject gerarReciboTransacoesSemRecibo();

    String cancelarTransacao(TransacaoVO transacaoVO, boolean estornarRecibo, UsuarioVO usuarioVO, String chave) throws Exception;

    ConvenioCobrancaVO obterConvenioCobranca(TransacaoVO transacaoVO) throws Exception;

    void validarMovimentacoesFinanceiras(TransacaoVO transacaoVO) throws Exception;

    void gravarLogTransacao(String operacao, TransacaoVO transacaoAnteriorVO, TransacaoVO transacaoAtualVO, UsuarioVO usuarioVO);

    List<GrupoTransacoesErrosTO> consultarErrosAgrupandoPorTipoTransacao(Date dataInicio, Date dataFim,
                                                                         SituacaoTransacaoEnum situacao, Integer empresa,
                                                                         TipoTransacaoEnum tipoTransacaoEnum, CodigoRetornoPactoEnum codigoRetornoPactoEnum,
                                                                         String filtro) throws Exception;

    List<GrupoTransacoesErrosTO> consultarTotalizadorTransacaoPortipo(Date dataInicio, Date dataFim,
                                                                      SituacaoTransacaoEnum situacao, Integer empresa,
                                                                      TipoTransacaoEnum tipoTransacaoEnum, CodigoRetornoPactoEnum codigoRetornoPactoEnum,
                                                                      String filtro, boolean apresentarCobrancaVerificarCartao) throws Exception;

    void excluirCartaoVindi(String codigoExterno, ConvenioCobrancaVO obj);

    void incluirTransacaoWebhook(Integer transacao, String webhook) throws Exception;

    String sincronizarTransacao(TransacaoVO transacaoVO, UsuarioVO usuarioVO, boolean validarSincronizar) throws Exception;

    List<TransacaoVO> consultarPorParcela(Integer codigoParcela) throws Exception;

    List<TransacaoVO> consultarPorParcela(Integer codigoParcela, int nivelMontarDados) throws Exception;

    List<ObjetoGenerico> obterTransacoesDuplicadas() throws Exception;

    void alterarDataAtualizacao(final TransacaoVO obj, boolean atualizarParamsResposta) throws Exception;

    String retentativaTransacao(TransacaoVO transacaoVO, UsuarioVO usuarioVO, Integer convenioCobranca, String ipCliente, OrigemCobrancaEnum origemCobrancaEnum) throws Exception;

    JSONObject realizaCobrancaVerificarCartao(ClienteVO clienteVO, ColaboradorVO colaboradorVO,
                                              AutorizacaoCobrancaVO autoVO, UsuarioVO usuarioVO, String ipOrigem);

    List<TransacaoVO> consultarTransacaoVerificacaoParaCancelar(TipoTransacaoEnum tipo, Integer convenioCobranca, Integer empresa) throws Exception;

    void alterarCodigoExterno(final TransacaoVO obj) throws Exception;

    List<TransacaoVO> obterListaTransacoesEstorno(List<MovParcelaVO> listParc) throws Exception;

    void enviarEmailComprovanteCancelamento(String emailEnviar, TransacaoVO transacaoVO) throws Exception;

    void atualizarRetornoCancelamento(String retornoCancelamento, Integer codigo);

    TransacaoVO consultarPorGatewayId(final Long gateway_id) throws Exception;
}

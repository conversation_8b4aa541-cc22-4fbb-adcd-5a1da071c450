package servicos;

import br.com.pactosolucoes.comuns.json.JSONMapper;
import br.com.pactosolucoes.integracao.financeiro.IntegracaoFinanceiroJSON;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.RoboControle;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.arquitetura.RoboVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONObject;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.sql.Connection;
import java.sql.ResultSet;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;

import static negocio.facade.jdbc.arquitetura.SuperFacadeJDBC.criarConsulta;

/**
 * <AUTHOR>
 */
public class AdministrativoRunnerFINANCEIRO {

    private static final SimpleDateFormat FORMATADOR_DATA = new SimpleDateFormat("dd/MM/yyyy");

    private static class RetornoAtivosVencidos {
        public int ativos;
        public int vencidos;
        public int agregadores;
        public int agregadores_2acessos;
        public int agregadores_3acessos;
        public int agregadores_4acessos;
        public int agregadores_5_ou_mais_acessos;
        public int checkinsAgregadores;

        public RetornoAtivosVencidos(int ativos, int vencidos, int agregadores, int agregadores_2acessos,
                                     int agregadores_3acessos, int agregadores_4acessos, int agregadores_5_ou_mais_acessos, int checkinsAgregadores) {
            this.ativos = ativos;
            this.vencidos = vencidos;
            this.agregadores = agregadores;
            this.agregadores_2acessos = agregadores_2acessos;
            this.agregadores_3acessos = agregadores_3acessos;
            this.agregadores_4acessos = agregadores_4acessos;
            this.agregadores_5_ou_mais_acessos = agregadores_5_ou_mais_acessos;
            this.checkinsAgregadores = checkinsAgregadores;
        }
    }

    public static void main(String[] args) {
        Uteis.debug = true;
        Uteis.logar("Entrou no Administrativo Runner");
        if (args.length == 0) {
            args = new String[]{"bdzillyonlivefitnessintegracaoacesso-2025-04-07"};
        }
        if (args.length >= 1) {
            String chave = args[0];
            Connection con = null;
            try {
                DAO dao = new DAO();
                con = dao.obterConexaoEspecifica(chave);
                Conexao.guardarConexaoForJ2SE(con);
                //atualizar banco de dados, se defasado
                LoginControle control = new LoginControle();
                control.setUsername("RECOR");
                control.setSenha(PropsService.getPropertyValue(PropsService.RECORRENCIA_USER_PASSWORD));
                control.setUserOamd("adm");
                control.login();
                control.atualizarBD();

                RoboControle roboControle = new RoboControle();
                roboControle.inicializarRobo();
                RoboVO robo = roboControle.getRobo();
                if (args.length >= 2) {
                    robo.setDia(FORMATADOR_DATA.parse(args[1]));
                    Calendario.dia = FORMATADOR_DATA.parse(args[1]);
                }

                processarAtualizacaoAtivosVencidosFinanceiro(chave, con);

                Logger.getLogger(AdministrativoRunnerFINANCEIRO.class.getName()).log(
                        Level.INFO, " Finalizando AdministrativoRunner em: {0}", new Object[]{Uteis.getDataComHora(Calendario.hoje())});

            } catch (Exception ex) {
                Uteis.logarDebug("Erro: " + ex.getMessage());
                ex.printStackTrace();
                Logger.getLogger(AdministrativoRunnerFINANCEIRO.class.getName()).log(Level.SEVERE,
                        "Erro ao obter conexao especifica com a chave " + chave, ex);
            } finally {
                Uteis.finalizarExecutor(1);
            }
        }
    }

    private static void processarAtualizacaoAtivosVencidosFinanceiro(String chave, Connection con) {
        String comandoSQL = "SELECT codigo FROM empresa";
        try(ResultSet rs = criarConsulta(comandoSQL, con)) {
            while (rs.next()) {
                processarAtualizacaoAtivosVencidosFinanceiro(chave, rs.getInt("codigo"), con);
            }
        } catch (Exception ex) {
            Logger.getLogger(AdministrativoRunner.class.getName()).log(Level.INFO, ex.getMessage(), ex);
            Uteis.logar(null, ex.getMessage());
        }
    }

    private static void processarAtualizacaoAtivosVencidosFinanceiro(String chave, Integer codigoEmpresa, Connection con) {
        Uteis.logar(null, "INICIALIZA Processamento Ativos Vencidos Financeiro - em: " + Uteis.getDataComHora(Calendario.hoje()));
        try {
            String url = String.format("%s/prest/adm/%s/consultarEmpresaFinanceiro?codigoEmpresa=%s", PropsService.getPropertyValue(PropsService.urlOamd), chave, codigoEmpresa.toString());
            String result = ExecuteRequestHttpService.executeRequestGET(url, null);
            JSONObject jsonObject = new JSONObject(result);
            if (!jsonObject.has("return")) {
                throw new Exception(String.format("Empresa não encontrada: %s / %s / %s", chave, codigoEmpresa, result));
            }

            IntegracaoFinanceiroJSON ifp = JSONMapper.getObject(jsonObject.getJSONObject("return"), IntegracaoFinanceiroJSON.class);
            if (ifp == null || ifp.getChave() == null || ifp.getChave().isEmpty()) {
                throw new Exception(String.format("Empresa não encontrada: %s / %s / %s", chave, codigoEmpresa, result));
            }

            Date hoje = Calendario.getDataComHoraZerada(Calendario.hoje());

            // Pega a próxima data para atualização
            Date dataAtu = Calendario.getInstance(Calendario.getAno(hoje), Calendario.getMes(hoje), ifp.getDiaAtualizarAtivosVencidos()).getTime();
            // Se a data da atualização já passou, é calculada a data do mês posterior
            if (Calendario.menor(dataAtu, hoje) && ifp.getUaAtivosVencidos() != null) {
                // Próximo mês
                dataAtu = Calendario.somarMeses(dataAtu, 1);
                // Garantir que o dia do mês permaneça o mesmo
                dataAtu = Calendario.getInstance(Calendario.getAno(dataAtu), Calendario.getMes(dataAtu), ifp.getDiaAtualizarAtivosVencidos()).getTime();
            }

            // Atualiza se nenhuma atualização foi feita ainda
            boolean atualizar = ifp.getUaAtivosVencidos() == null || Calendario.getAno(ifp.getUaAtivosVencidos()) < 2000;
            if (!atualizar) {
                // Atualiza se já estiver a 5 dias ou menos, da data de atualizar
                atualizar = Calendario.diferencaEmDias(hoje, dataAtu) <= 5;
            }


            if (!atualizar) {
                // Log?
                return;
            }

            if (Calendario.menorOuIgual(hoje, dataAtu)) {
                dataAtu = hoje;
            }

            AdministrativoRunnerFINANCEIRO.RetornoAtivosVencidos ret = consultarAtivosVencidos(con, codigoEmpresa, dataAtu);

            if (ret == null) {
                throw new Exception(String.format("Ativos/vencidos da empresa não encontrados: %s / %s / %s", chave, codigoEmpresa, result));
            }

            Uteis.logar("RetornoAtivosVencidos => %s Ativos: %s, Vencidos: %s, Agregadores %s, Check-ins dos Agregadores: %s", chave, ret.ativos, ret.vencidos, ret.agregadores, ret.checkinsAgregadores);

            url = String.format("%s/prest/adm/%s/atualizarAtivosVencidos?codigoEmpresa=%s&ativos=%s&vencidos=%s&agregadores=%s&agregadores2=%s&agregadores3=%s&agregadores4=%s&agregadores5=%s&checkinsAgregadores=%s",
                    PropsService.getPropertyValue(PropsService.urlOamd), chave, codigoEmpresa, ret.ativos, ret.vencidos, ret.agregadores, ret.agregadores_2acessos, ret.agregadores_3acessos, ret.agregadores_4acessos, ret.agregadores_5_ou_mais_acessos, ret.checkinsAgregadores);
            result = ExecuteRequestHttpService.executeRequest(url, null, false, "utf-8");
            jsonObject = new JSONObject(result);
            if (jsonObject.length() == 0 || !jsonObject.has("return") && !jsonObject.has("erro")) {
                throw new Exception(String.format("atualizarAtivosVencidos - retorno inesperado: %s", result));
            }
            if (jsonObject.has("erro")) {
                throw new Exception(String.format("atualizarAtivosVencidos - erro: %s", jsonObject.getString("erro")));
            }
        } catch (Exception ex) {
            Logger.getLogger(AdministrativoRunner.class.getName()).log(Level.INFO, ex.getMessage(), ex);
            Uteis.logar(null, ex.getMessage());
        }
        Uteis.logar(null, "Processamento Ativos Vencidos Financeiro - em: " + Uteis.getDataComHora(Calendario.hoje()));
    }

    private static AdministrativoRunnerFINANCEIRO.RetornoAtivosVencidos consultarAtivosVencidos(Connection con, Integer codigoEmpresa, Date data) throws Exception {
        String comandoSQL = "SELECT\n"
                + "  sum(qtdAtivos) AS ativos,\n"
                + "  sum(qtdVencidos) AS vencidos,\n"
                + "  sum(qtdAgregador) as agregadores,\n"
                + "  sum(qtdAgregador_2acessos) as agregadores_2acessos,\n"
                + "  sum(qtdAgregador_3acessos) as agregadores_3acessos,\n"
                + "  sum(qtdAgregador_4acessos) as agregadores_4acessos,\n"
                + "  sum(qtdAgregador_5_ou_mais_acessos) as agregadores_5_ou_mais_acessos,\n"
                + "  sum(qtdCheckinAgregador) as qtdCheckinsAgregador\n"
                + "FROM (SELECT\n"
                + "        'A' AS tipo,\n"
                + "        count(*) AS qtdAtivos,\n"
                + "        0 AS qtdVencidos,\n"
                + "        0 as qtdAgregador,\n"
                + "        0 as qtdAgregador_2acessos,\n"
                + "        0 as qtdAgregador_3acessos,\n"
                + "        0 as qtdAgregador_4acessos,\n"
                + "        0 as qtdAgregador_5_ou_mais_acessos,\n"
                + "        0 as qtdCheckinAgregador,\n"
                + "        c.empresa,\n"
                + "        c.codigo\n"
                + "      FROM contrato c\n"
                + "      WHERE current_date >= c.vigenciaDe\n"
                + "            AND current_date <= c.vigenciaAteAjustada\n"
                + "            AND c.codigo NOT IN\n"
                + "                (SELECT\n"
                + "                   h.contrato\n"
                + "                 FROM historicocontrato h\n"
                + "                 WHERE h.tipohistorico IN ('CA', 'DE')\n"
                + "                       AND current_date >= h.datainiciosituacao \n"
                + "                       AND current_date <= h.datafinalsituacao\n"
                + "                 )\n"
                + "            AND c.codigo NOT IN\n"
                + "                (SELECT\n"
                + "                   o.contrato\n"
                + "                 FROM contratooperacao o\n"
                + "                 WHERE o.tipooperacao IN ('TR', 'TV')\n"
                + "                       AND current_date >= o.datainicioefetivacaooperacao\n"
                + "                       AND current_date <= o.datafimefetivacaooperacao)\n"
                + "            AND c.codigo NOT IN\n"
                + "                (SELECT\n"
                + "                   contratobaseadorenovacao\n"
                + "                 FROM contrato ct\n"
                + "                 WHERE ct.pessoa = c.pessoa\n"
                + "                       AND current_date >= ct.vigenciaDe \n"
                + "                       AND current_date <= ct.vigenciaAteAjustada \n"
                + "                 )\n"
                + "      GROUP BY c.empresa, c.codigo\n"
                + "      UNION\n"
                + "      SELECT\n"
                + "        'V' AS tipo,\n"
                + "        0 AS qtdAtivos,\n"
                + "        count(*) AS qtdVencidos,\n"
                + "        0 as qtdAgregador,\n"
                + "        0 as qtdAgregador_2acessos,\n"
                + "        0 as qtdAgregador_3acessos,\n"
                + "        0 as qtdAgregador_4acessos,\n"
                + "        0 as qtdAgregador_5_ou_mais_acessos,\n"
                + "        0 as qtdCheckinAgregador,\n"
                + "        c.empresa,\n"
                + "        c.codigo\n"
                + "      FROM contrato c\n"
                + "      WHERE c.contratoresponsavelrenovacaomatricula = 0\n"
                + "            AND c.codigo IN\n"
                + "                (SELECT\n"
                + "                   h.contrato\n"
                + "                 FROM historicocontrato h\n"
                + "                 WHERE h.tipohistorico IN ('VE')\n"
                + "                       AND current_date >= h.datainiciosituacao\n"
                + "                       AND current_date <= h.datafinalsituacao\n"
                + "                 )\n"
                + "            AND c.codigo NOT IN\n"
                + "                (SELECT\n"
                + "                   h.contrato\n"
                + "                 FROM historicocontrato h\n"
                + "                 WHERE h.tipohistorico IN ('DE')\n"
                + "                       AND h.datainiciosituacao >= '" + Uteis.getDataJDBC(Uteis.obterPrimeiroDiaMes(data)) + "' \n"
                + "                       AND h.datainiciosituacao <= current_date\n"
                + "                )\n"
                + "      GROUP BY c.empresa, c.codigo\n"
                + "      UNION\n"
                + "      SELECT\n"
                + "        'G' AS tipo,\n"
                + "        0 AS qtdAtivos,\n"
                + "        0 as qtdVencidos,\n"
                + "        1 as qtdAgregador,\n"
                + "        0 as qtdAgregador_2acessos,\n"
                + "        0 as qtdAgregador_3acessos,\n"
                + "        0 as qtdAgregador_4acessos,\n"
                + "        0 as qtdAgregador_5_ou_mais_acessos,\n"
                + "        count(*) as qtdCheckinAgregador,\n"
                + "        c.empresa,\n"
                + "        p.pessoa\n"
                + "      FROM periodoacessocliente p\n"
                + "      INNER JOIN cliente c ON p.pessoa = c.pessoa\n"
                + "      WHERE (p.datainicioacesso between month_ago and current_date)\n"
                + "            AND (COALESCE(p.tokengympass, '') <> ''\n"
                + "                 OR COALESCE(p.tokengogood, '') <> ''\n"
                + "                 OR p.tipototalpass)\n"
                + "      GROUP BY c.empresa, p.pessoa\n"
                + "      having count(*) = 1 \n"
                + "      UNION\n"
                + "      SELECT\n"
                + "        'G2' AS tipo,\n"
                + "        0 AS qtdAtivos,\n"
                + "        0 as qtdVencidos,\n"
                + "        0 as qtdAgregador,\n"
                + "        1 as qtdAgregador_2acessos,\n"
                + "        0 as qtdAgregador_3acessos,\n"
                + "        0 as qtdAgregador_4acessos,\n"
                + "        0 as qtdAgregador_5_ou_mais_acessos,\n"
                + "        0 as qtdCheckinAgregador,\n"
                + "        c.empresa,\n"
                + "        p.pessoa\n"
                + "      FROM periodoacessocliente p\n"
                + "      INNER JOIN cliente c ON p.pessoa = c.pessoa\n"
                + "      WHERE (p.datainicioacesso BETWEEN month_ago AND current_date)\n"
                + "        AND (\n"
                + "          COALESCE(p.tokengympass, '') <> ''\n"
                + "          OR COALESCE(p.tokengogood, '') <> ''\n"
                + "          OR p.tipototalpass\n"
                + "        )\n"
                + "      GROUP BY c.empresa, p.pessoa\n"
                + "      having count(*) = 2 \n"
                + "      UNION\n"
                + "      SELECT\n"
                + "        'G3' AS tipo,\n"
                + "        0 AS qtdAtivos,\n"
                + "        0 as qtdVencidos,\n"
                + "        0 as qtdAgregador,\n"
                + "        0 as qtdAgregador_2acessos,\n"
                + "        1 as qtdAgregador_3acessos,\n"
                + "        0 as qtdAgregador_4acessos,\n"
                + "        0 as qtdAgregador_5_ou_mais_acessos,\n"
                + "        0 as qtdCheckinAgregador,\n"
                + "        c.empresa,\n"
                + "        p.pessoa\n"
                + "      FROM periodoacessocliente p\n"
                + "      INNER JOIN cliente c ON p.pessoa = c.pessoa\n"
                + "      WHERE (p.datainicioacesso BETWEEN month_ago AND current_date)\n"
                + "        AND (\n"
                + "          COALESCE(p.tokengympass, '') <> ''\n"
                + "          OR COALESCE(p.tokengogood, '') <> ''\n"
                + "          OR p.tipototalpass\n"
                + "        )\n"
                + "      GROUP BY c.empresa, p.pessoa\n"
                + "      having count(*) = 3 \n"
                + "      UNION\n"
                + "      SELECT\n"
                + "        'G4' AS tipo,\n"
                + "        0 AS qtdAtivos,\n"
                + "        0 as qtdVencidos,\n"
                + "        0 as qtdAgregador,\n"
                + "        0 as qtdAgregador_2acessos,\n"
                + "        0 as qtdAgregador_3acessos,\n"
                + "        1 as qtdAgregador_4acessos,\n"
                + "        0 as qtdAgregador_5_ou_mais_acessos,\n"
                + "        0 as qtdCheckinAgregador,\n"
                + "        c.empresa,\n"
                + "        p.pessoa\n"
                + "      FROM periodoacessocliente p\n"
                + "      INNER JOIN cliente c ON p.pessoa = c.pessoa\n"
                + "      WHERE (p.datainicioacesso BETWEEN month_ago AND current_date)\n"
                + "        AND (\n"
                + "          COALESCE(p.tokengympass, '') <> ''\n"
                + "          OR COALESCE(p.tokengogood, '') <> ''\n"
                + "          OR p.tipototalpass\n"
                + "        )\n"
                + "      GROUP BY c.empresa, p.pessoa\n"
                + "      having count(*) = 4 \n"
                + "      UNION\n"
                + "      SELECT\n"
                + "        'G5' AS tipo,\n"
                + "        0 AS qtdAtivos,\n"
                + "        0 as qtdVencidos,\n"
                + "        0 as qtdAgregador,\n"
                + "        0 as qtdAgregador_2acessos,\n"
                + "        0 as qtdAgregador_3acessos,\n"
                + "        0 as qtdAgregador_4acessos,\n"
                + "        1 as qtdAgregador_5_ou_mais_acessos,\n"
                + "        0 as qtdCheckinAgregador,\n"
                + "        c.empresa,\n"
                + "        p.pessoa\n"
                + "      FROM periodoacessocliente p\n"
                + "      INNER JOIN cliente c ON p.pessoa = c.pessoa\n"
                + "      WHERE (p.datainicioacesso BETWEEN month_ago AND current_date)\n"
                + "        AND (\n"
                + "          COALESCE(p.tokengympass, '') <> ''\n"
                + "          OR COALESCE(p.tokengogood, '') <> ''\n"
                + "          OR p.tipototalpass\n"
                + "        )\n"
                + "      GROUP BY c.empresa, p.pessoa\n"
                + "      having count(*) > 4) AS foo\n"
                + "  INNER JOIN empresa emp ON foo.empresa = emp.codigo AND emp.codigo = " + codigoEmpresa.toString();

        comandoSQL = comandoSQL.replaceAll("current_date", "'" + Uteis.getDataJDBC(data) + "'");
        comandoSQL = comandoSQL.replaceAll("month_ago", "'" + Uteis.getDataJDBC(Uteis.somarMeses(data, -1)) + "'");
        try(ResultSet rs = criarConsulta(comandoSQL, con)) {
            if (rs.next()) {
                return new AdministrativoRunnerFINANCEIRO.RetornoAtivosVencidos(rs.getInt("ativos"),
                        rs.getInt("vencidos"),
                        rs.getInt("agregadores"),
                        rs.getInt("agregadores_2acessos"),
                        rs.getInt("agregadores_3acessos"),
                        rs.getInt("agregadores_4acessos"),
                        rs.getInt("agregadores_5_ou_mais_acessos"),
                        rs.getInt("qtdCheckinsAgregador"));
            }
        }
        return null;
    }


}

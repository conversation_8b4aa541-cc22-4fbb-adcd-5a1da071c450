package servicos.adm;

import com.fasterxml.jackson.databind.ObjectMapper;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.ContratoDependenteVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.ConfiguracaoSistema;
import negocio.facade.jdbc.basico.ContratoDependente;
import negocio.facade.jdbc.basico.Convite;
import negocio.facade.jdbc.contrato.Contrato;
import servicos.adm.dto.ConviteDTO;
import servicos.propriedades.PropsService;

import java.net.URLEncoder;
import java.sql.Connection;
import java.util.List;

public class ConviteService {

    private Connection con;

    public ConviteService(Connection con) throws Exception {
        this.con = con;
    }

    public String gerarLinkConvite(String chave, String matricula, Integer empresa, boolean utilizarConfigConviteAdm) throws Exception {
        Cliente clienteDAO = new Cliente(con);
        Convite conviteDAO = new Convite(con);
        Contrato contratoDAO = new Contrato(con);
        ContratoDependente contratoDependenteDAO = new ContratoDependente(con);
        ConfiguracaoSistema configuracaoSistemaDAO = new ConfiguracaoSistema(con);
        try {
            String url = PropsService.getPropertyValue(PropsService.urlVendasOnline);
            Integer convitesDireito = 0;
            Integer convitesUsados = 0;
            ClienteVO cliente = clienteDAO.consultarPorMatricula(matricula, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            convitesUsados = conviteDAO.totalizarMes(cliente.getCodigo());

            List<ContratoVO> listaContratosCliente = contratoDAO.consultarListaTela(cliente.getPessoa().getCodigo(), 3);
            List<ContratoDependenteVO> listaContratosDependente = contratoDependenteDAO.findAllByCliente(cliente, 3);

            if (!UteisValidacao.emptyList(listaContratosDependente)) {
                for (ContratoDependenteVO contratoDep : listaContratosDependente) {
                    listaContratosCliente.add(contratoDep.getContrato());
                }
            }

            if (!UteisValidacao.emptyList(listaContratosCliente)) {
                for (ContratoVO contratoVO : listaContratosCliente) {
                    convitesDireito += conviteDAO.convitesDireito(contratoVO.getCodigo());
                }
            }

            if (convitesDireito > 0) {
                if (convitesUsados < convitesDireito) {
                    String urlConvite = url + "/cadastro?un=" + empresa
                            + "&k=" + URLEncoder.encode(chave, "UTF-8")
                            + "&rcvm=" + URLEncoder.encode(matricula, "UTF-8")
                            + "&rcv=" + URLEncoder.encode(Uteis.getPrimeiroNome(cliente.getPessoa().getNome()), "UTF-8");

                    if (utilizarConfigConviteAdm) {
                        ConfiguracaoSistemaVO config = configuracaoSistemaDAO.buscarConfiguracaoSistema();
                        return new ObjectMapper()
                                .writeValueAsString(
                                        new ConviteDTO(config.getTituloConvite(), config.getDescricaoConvite(),
                                                config.getTempoReabilitacaoExAluno(), urlConvite));
                    } else {
                        return urlConvite;
                    }
                } else {
                    return "Todos os convites disponíveis para este mês já foram utilizados!";
                }
            } else {
                return "Não foi possivel gerar link: O plano do aluno não possui convites.";
            }
        } catch (Exception ex) {
            Uteis.logar(ex, ConviteService.class);
            return "ERRO:Algo inesperado ocorreu, o link não poderá ser gerado.";
        }
    }
}

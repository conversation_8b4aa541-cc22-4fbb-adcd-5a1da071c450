package servicos.impl.admCoreMs;

import br.com.pactosolucoes.comuns.json.JSONMapper;
import br.com.pactosolucoes.enumeradores.TipoClienteRestricaoEnum;
import controle.arquitetura.exceptions.SecretException;
import negocio.oamd.RedeEmpresaVO;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.impl.autenticacaoMs.AutenticacaoMsService;
import servicos.impl.microsservice.SuperMSService;
import servicos.impl.planoMs.PlanoMsException;
import servicos.util.ExecuteRequestHttpService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AdmCoreMsService extends SuperMSService {

    public static JSONObject replicarPerfilAcesso(JSONObject planoJSON, String admCoreMsUrl, String chaveDestino) throws Exception {
        String url = admCoreMsUrl + "/perfil-acesso/replicar";
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", AutenticacaoMsService.token(chaveDestino));

        try {
            String response = ExecuteRequestHttpService.post(url, planoJSON.toString(), headers);

            return new JSONObject(new JSONObject(response).getString("content"));
        } catch (Exception e) {
            e.printStackTrace();
            throw new PlanoMsException(messageError(e.getMessage()));
        }
    }

    public static JSONObject clonarPerfilAcesso(Integer codigo, String admCoreMsUrl, String chaveOrigem) throws Exception {
        String url = admCoreMsUrl + "/perfil-acesso/" + codigo;
        return get(chaveOrigem, url);
    }


    public static JSONObject obterPerfilAcesso(Integer codigo, String admCoreMsUrl, String chave) throws Exception {
        String url = admCoreMsUrl + "/perfil-acesso/" + codigo;
        return get(chave, url);
    }

    private static JSONObject get(String chave, String url) throws Exception {
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", AutenticacaoMsService.token(chave));

        try {
            String response = ExecuteRequestHttpService.get(url, headers);
            return new JSONObject(response).getJSONObject("content");
        } catch (Exception e) {
            e.printStackTrace();
            throw new PlanoMsException(messageError(e.getMessage()));
        }
    }

    private static Map<String, String> getHeaders(RedeEmpresaVO redeEmpresaVO) throws SecretException {
        Map<String, String> headers = new HashMap<>();
        headers.put(ZW_AUTHORIZATION_HEADER, getTokenZW(redeEmpresaVO.getChaveFranqueadora(), redeEmpresaVO.getCodigoUnidadeFranqueadora(), null));
        return headers;
    }

    public static List<ClienteRestricaoDTO> obterClienteRestricoes(RedeEmpresaVO redeEmpresaVO, String cpf) throws Exception {
        String url = String.format("%s/cliente-restricao/%s", redeEmpresaVO.getServiceMap().getAdmCoreUrl(), cpf);
        try {
            String response = ExecuteRequestHttpService.get(url, getHeaders(redeEmpresaVO));
            return JSONMapper.getList(new JSONObject(response).getJSONArray(CONTENT_RESPONSE), ClienteRestricaoDTO.class);
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception(messageError(e.getMessage()));
        }
    }

    public static void incluirClienteRestricao(RedeEmpresaVO redeEmpresaVO, ClienteRestricaoDTO clienteRestricaoDTO) throws Exception {
        try {
            ExecuteRequestHttpService.post(redeEmpresaVO.getServiceMap().getAdmCoreUrl() + "/cliente-restricao", new JSONObject(clienteRestricaoDTO).toString(), getHeaders(redeEmpresaVO));
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception(messageError(e.getMessage()));
        }
    }

    public static void retirarClienteRestricoes(RedeEmpresaVO redeEmpresaVO, String chave, String cpf, TipoClienteRestricaoEnum tipoClienteRestricaoEnum) throws Exception {
        String url = String.format("%s/cliente-restricao/%s/%s/%s", redeEmpresaVO.getServiceMap().getAdmCoreUrl(), cpf, chave, tipoClienteRestricaoEnum.getSigla());
        try {
            ExecuteRequestHttpService.delete(url, getHeaders(redeEmpresaVO));
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception(messageError(e.getMessage()));
        }
    }

    public static void retirarClienteRestricoesCpfs(RedeEmpresaVO redeEmpresaVO, String chave, List<String> cpfs, TipoClienteRestricaoEnum tipoClienteRestricaoEnum, Integer empresa) throws Exception {
        String url = String.format("%s/cliente-restricao/%s/%s/%s", redeEmpresaVO.getServiceMap().getAdmCoreUrl(), chave, empresa, tipoClienteRestricaoEnum.getSigla());
        try {
            ExecuteRequestHttpService.post(url, new JSONArray(cpfs).toString(), getHeaders(redeEmpresaVO));
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception(messageError(e.getMessage()));
        }
    }

    public static void salvarClienteRedeEmpresa(RedeEmpresaVO redeEmpresaVO, ClienteRedeEmpresaDTO clienteRedeEmpresaDTO) throws Exception {
        try {
            ExecuteRequestHttpService.post(redeEmpresaVO.getServiceMap().getAdmCoreUrl() + "/cliente-rede-empresa", new JSONObject(clienteRedeEmpresaDTO).toString(), getHeaders(redeEmpresaVO));
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception(messageError(e.getMessage()));
        }
    }


}

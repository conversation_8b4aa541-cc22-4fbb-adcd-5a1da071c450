/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.integracao.adm.beans;

import negocio.comuns.basico.EmpresaVO;

/**
 * <AUTHOR>
 */
public class EmpresaWS {

    private Integer codigo;
    private String nome;
    private String tokenSMS;
    private String endereco;
    private String setor;
    private String numero;
    private String complemento;
    private String CEP;
    private String latitude;
    private String longitude;
    private String cidade;
    private String estado;
    private String siglaEstado;
    private boolean usarGestaoCreditosPersonal = false;
    private boolean alterarDataHoraCheckGestaoPersonal = true;
    private boolean obrigatorioAssociarAlunoAoCheckIn = false;
    private boolean bloquearAcessoPersonalSemCredito = false;
    private boolean mostrarFotosAlunosMonitor = false;
    private int duracaoCredito = 0;
    private int diasBloqueioParcelaEmAberto = 0;
    private int tempoCheckOutAutomatica = 0;
    private boolean consumirCreditoPorAlunoVinculado = false;
    private boolean usarFotoPersonal = false;
    private String timeZoneDefault;
    private Integer codigoColaborador;
    private String descricaoPerfil;
    private String site;
    private boolean bloqueioTemporario = false;
    private boolean bvObrigatorio = false;
    private String pais;
    private String telefone;
    private String email;
    private boolean usarNFSe = false;
    private boolean usarNFCe = false;
    private boolean usarENotas = false;
    private String chaveNFSe = "";
    private String idExterno;
    private int totalDiasExtras;
    private boolean empresaSuspensa = false;

    private boolean integracaoSpiviHabilitada;
    private String integracaoSpiviSourceName;
    private Integer integracaoSpiviSiteID;
    private String integracaoSpiviPassword;

    private Integer codigoFinanceiro;
    private String cnpj;
    private String tokenShortcode;
    private boolean usarSistemaInternacional = false;
    private boolean permiteContratosConcomitante ;

    public EmpresaWS() {
    }

    public EmpresaWS(Integer codigo, String nome) {
        this.codigo = codigo;
        this.nome = nome;
    }

    public EmpresaWS(Integer codigo, String nome, String tokenSMS, String estado, String pais, String cnpj, Integer codigoFinanceiro, String tokenShortcode) {
        this.codigo = codigo;
        this.nome = nome;
        this.tokenSMS = tokenSMS;
        this.estado = estado;
        this.pais = pais;
        this.cnpj = cnpj;
        this.codigoFinanceiro = codigoFinanceiro;
        this.tokenShortcode = tokenShortcode;
    }

    public EmpresaWS(Integer codigo, String nome, String tokenSMS, String estado, String pais, boolean usarNFSe, boolean usarNFCe, String chaveNFSe, boolean usarEnota) {
        this.codigo = codigo;
        this.nome = nome;
        this.tokenSMS = tokenSMS;
        this.estado = estado;
        this.pais = pais;
        this.usarNFSe = usarNFSe;
        this.usarNFCe = usarNFCe;
        this.chaveNFSe = chaveNFSe;
        this.usarENotas = usarEnota;
    }

    public EmpresaWS(Integer codigo, String nome, String tokenSMS, String estado, String pais, boolean usarNFSe, boolean usarNFCe, String chaveNFSe, boolean usarEnota, int totalDiasExtras) {
        this.codigo = codigo;
        this.nome = nome;
        this.tokenSMS = tokenSMS;
        this.estado = estado;
        this.pais = pais;
        this.usarNFSe = usarNFSe;
        this.usarNFCe = usarNFCe;
        this.chaveNFSe = chaveNFSe;
        this.usarENotas = usarEnota;
        this.totalDiasExtras = totalDiasExtras;
    }

    public EmpresaWS(Integer codigo, String nome, String tokenSMS, String estado, String siglaEstado, String pais, boolean usarNFSe, boolean usarNFCe, String chaveNFSe, boolean usarEnota, int totalDiasExtras) {
        this.codigo = codigo;
        this.nome = nome;
        this.tokenSMS = tokenSMS;
        this.estado = estado;
        this.siglaEstado = siglaEstado;
        this.pais = pais;
        this.usarNFSe = usarNFSe;
        this.usarNFCe = usarNFCe;
        this.chaveNFSe = chaveNFSe;
        this.usarENotas = usarEnota;
        this.totalDiasExtras = totalDiasExtras;
    }

    public EmpresaWS(EmpresaVO empresa) {
        this.codigo = empresa.getCodigo();
        this.nome = empresa.getNome();
        this.tokenSMS = empresa.getTokenSMS();
        this.timeZoneDefault = empresa.getTimeZoneDefault();

        this.usarGestaoCreditosPersonal = empresa.isUsarGestaoCreditosPersonal();
        this.alterarDataHoraCheckGestaoPersonal = empresa.isAlterarDataHoraCheckGestaoPersonal();
        this.usarFotoPersonal = empresa.getConfigsPersonal().isUsarFotoPersonal();
        this.obrigatorioAssociarAlunoAoCheckIn = empresa.getConfigsPersonal().isObrigatorioAssociarAlunoAoCheckIn();
        this.bloquearAcessoPersonalSemCredito = empresa.getConfigsPersonal().isBloquearAcessoPersonalSemCredito();
        this.duracaoCredito = empresa.getConfigsPersonal().getDuracaoCredito();
        this.diasBloqueioParcelaEmAberto = empresa.getConfigsPersonal().getDiasBloqueioParcelaEmAberto();
        this.consumirCreditoPorAlunoVinculado = empresa.getConfigsPersonal().isConsumirCreditoPorAlunoVinculado();
        this.mostrarFotosAlunosMonitor = empresa.getConfigsPersonal().isMostrarFotosAlunosMonitor();
        this.tempoCheckOutAutomatica = empresa.getConfigsPersonal().getTempoCheckOutAutomatico();
        this.endereco = empresa.getEndereco();
        this.setor = empresa.getSetor();
        this.numero = empresa.getNumero();
        this.complemento = empresa.getComplemento();
        this.CEP = empresa.getCEP();
        this.latitude = empresa.getLatitude();
        this.longitude = empresa.getLongitude();
        this.cidade = empresa.getCidade_Apresentar();
        this.estado = empresa.getEstado().getDescricao();
        this.site = empresa.getSite();
        this.bloqueioTemporario = empresa.isBloqueioTemporario();
        this.pais = empresa.getPais().getNome();
        this.bvObrigatorio = empresa.isBvObrigatorio();
        this.telefone = empresa.getTelComercial1();
        this.email = empresa.getEmail();
        this.idExterno = empresa.getIdExterno();
        this.integracaoSpiviHabilitada = empresa.isIntegracaoSpiviHabilitada();
        this.integracaoSpiviSourceName = empresa.getIntegracaoSpiviSourceName();
        this.integracaoSpiviSiteID = empresa.getIntegracaoSpiviSiteID();
        this.integracaoSpiviPassword = empresa.getIntegracaoSpiviPassword();

        this.codigoFinanceiro = empresa.getCodEmpresaFinanceiro();
        this.cnpj = empresa.getCNPJ();
        this.tokenShortcode = empresa.getTokenSMSShortCode();
        this.usarSistemaInternacional = empresa.isUsarSistemaInternacional();
        this.permiteContratosConcomitante = empresa.isPermiteContratosConcomintante();    }

    public int getTempoCheckOutAutomatica() {
        return tempoCheckOutAutomatica;
    }

    public void setTempoCheckOutAutomatica(int tempoCheckOutAutomatica) {
        this.tempoCheckOutAutomatica = tempoCheckOutAutomatica;
    }

    public int getDuracaoCredito() {
        return duracaoCredito;
    }

    public void setDuracaoCredito(int duracaoCredito) {
        this.duracaoCredito = duracaoCredito;
    }

    public int getDiasBloqueioParcelaEmAberto() {
        return diasBloqueioParcelaEmAberto;
    }

    public void setDiasBloqueioParcelaEmAberto(int diasBloqueioParcelaEmAberto) {
        this.diasBloqueioParcelaEmAberto = diasBloqueioParcelaEmAberto;
    }

    public boolean isConsumirCreditoPorAlunoVinculado() {
        return consumirCreditoPorAlunoVinculado;
    }

    public void setConsumirCreditoPorAlunoVinculado(boolean consumirCreditoPorAlunoVinculado) {
        this.consumirCreditoPorAlunoVinculado = consumirCreditoPorAlunoVinculado;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getTokenSMS() {
        return tokenSMS;
    }

    public void setTokenSMS(String tokenSMS) {
        this.tokenSMS = tokenSMS;
    }

    public boolean isUsarGestaoCreditosPersonal() {
        return usarGestaoCreditosPersonal;
    }

    public void setUsarGestaoCreditosPersonal(boolean usarGestaoCreditosPersonal) {
        this.usarGestaoCreditosPersonal = usarGestaoCreditosPersonal;
    }

    public boolean isObrigatorioAssociarAlunoAoCheckIn() {
        return obrigatorioAssociarAlunoAoCheckIn;
    }

    public void setObrigatorioAssociarAlunoAoCheckIn(boolean obrigatorioAssociarAlunoAoCheckIn) {
        this.obrigatorioAssociarAlunoAoCheckIn = obrigatorioAssociarAlunoAoCheckIn;
    }

    public boolean isBloquearAcessoPersonalSemCredito() {
        return bloquearAcessoPersonalSemCredito;
    }

    public void setBloquearAcessoPersonalSemCredito(boolean bloquearAcessoPersonalSemCredito) {
        this.bloquearAcessoPersonalSemCredito = bloquearAcessoPersonalSemCredito;
    }

    public boolean isMostrarFotosAlunosMonitor() {
        return mostrarFotosAlunosMonitor;
    }

    public void setMostrarFotosAlunosMonitor(boolean mostrarFotosAlunosMonitor) {
        this.mostrarFotosAlunosMonitor = mostrarFotosAlunosMonitor;
    }

    public String getEndereco() {
        return endereco;
    }

    public void setEndereco(String endereco) {
        this.endereco = endereco;
    }

    public String getSetor() {
        return setor;
    }

    public void setSetor(String setor) {
        this.setor = setor;
    }

    public String getNumero() {
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public String getComplemento() {
        return complemento;
    }

    public void setComplemento(String complemento) {
        this.complemento = complemento;
    }

    public String getCEP() {
        return CEP;
    }

    public void setCEP(String CEP) {
        this.CEP = CEP;
    }

    public String getCidade() {
        return cidade;
    }

    public void setCidade(String cidade) {
        this.cidade = cidade;
    }

    public String getEstado() {
        return estado;
    }

    public void setEstado(String estado) {
        this.estado = estado;
    }

    public String getSiglaEstado() {
        return this.siglaEstado;
    }

    public void setSiglaEstado(final String siglaEstado) {
        this.siglaEstado = siglaEstado;
    }

    @Override
    public String toString() {
        return String.format("%s - %s", this.codigo, this.nome);
    }

    public boolean isUsarFotoPersonal() {
        return usarFotoPersonal;
    }

    public void setUsarFotoPersonal(boolean usarFotoPersonal) {
        this.usarFotoPersonal = usarFotoPersonal;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getTimeZoneDefault() {
        return timeZoneDefault;
    }

    public void setTimeZoneDefault(String timeZoneDefault) {
        this.timeZoneDefault = timeZoneDefault;
    }

    public Integer getCodigoColaborador() {
        return codigoColaborador;
    }

    public void setCodigoColaborador(Integer codigoColaborador) {
        this.codigoColaborador = codigoColaborador;
    }

    public String getDescricaoPerfil() {
        return descricaoPerfil;
    }

    public void setDescricaoPerfil(String descricaoPerfil) {
        this.descricaoPerfil = descricaoPerfil;
    }

    public String getSite() {
        return site;
    }

    public void setSite(String site) {
        this.site = site;
    }

    public boolean isAlterarDataHoraCheckGestaoPersonal() {
        return alterarDataHoraCheckGestaoPersonal;
    }

    public void setAlterarDataHoraCheckGestaoPersonal(boolean alterarDataHoraCheckGestaoPersonal) {
        this.alterarDataHoraCheckGestaoPersonal = alterarDataHoraCheckGestaoPersonal;
    }

    public boolean isBloqueioTemporario() {
        return bloqueioTemporario;
    }

    public void setBloqueioTemporario(boolean bloqueioTemporario) {
        this.bloqueioTemporario = bloqueioTemporario;
    }

    public String getPais() {
        return pais;
    }

    public void setPais(String pais) {
        this.pais = pais;
    }

    public String getTelefone() {
        return telefone;
    }

    public void setTelefone(String telefone) {
        this.telefone = telefone;
    }

    public boolean isBvObrigatorio() {
        return bvObrigatorio;
    }

    public void setBvObrigatorio(boolean bvObrigatorio) {
        this.bvObrigatorio = bvObrigatorio;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public boolean isUsarNFSe() {
        return usarNFSe;
    }

    public void setUsarNFSe(boolean usarNFSe) {
        this.usarNFSe = usarNFSe;
    }

    public boolean isUsarNFCe() {
        return usarNFCe;
    }

    public void setUsarNFCe(boolean usarNFCe) {
        this.usarNFCe = usarNFCe;
    }

    public String getChaveNFSe() {
        return chaveNFSe;
    }

    public void setChaveNFSe(String chaveNFSe) {
        this.chaveNFSe = chaveNFSe;
    }

    public String getIdExterno() {
        return idExterno;
    }

    public void setIdExterno(String idExterno) {
        this.idExterno = idExterno;
    }

    public boolean isIntegracaoSpiviHabilitada() {
        return integracaoSpiviHabilitada;
    }

    public void setIntegracaoSpiviHabilitada(final boolean integracaoSpiviHabilitada) {
        this.integracaoSpiviHabilitada = integracaoSpiviHabilitada;
    }

    public String getIntegracaoSpiviSourceName() {
        return integracaoSpiviSourceName;
    }

    public void setIntegracaoSpiviSourceName(final String integracaoSpiviSourceName) {
        this.integracaoSpiviSourceName = integracaoSpiviSourceName;
    }

    public Integer getIntegracaoSpiviSiteID() {
        return integracaoSpiviSiteID;
    }

    public void setIntegracaoSpiviSiteID(final Integer integracaoSpiviSiteID) {
        this.integracaoSpiviSiteID = integracaoSpiviSiteID;
    }

    public String getIntegracaoSpiviPassword() {
        return integracaoSpiviPassword;
    }

    public void setIntegracaoSpiviPassword(final String integracaoSpiviPassword) {
        this.integracaoSpiviPassword = integracaoSpiviPassword;
    }

    public boolean isUsarENotas() {
        return usarENotas;
    }

    public void setUsarENotas(boolean usarENotas) {
        this.usarENotas = usarENotas;
    }

    public Integer getCodigoFinanceiro() {
        return codigoFinanceiro;
    }

    public void setCodigoFinanceiro(Integer codigoFinanceiro) {
        this.codigoFinanceiro = codigoFinanceiro;
    }

    public String getCnpj() {
        return cnpj;
    }

    public void setCnpj(String cnpj) {
        this.cnpj = cnpj;
    }

    public int getTotalDiasExtras() {
        return totalDiasExtras;
    }

    public void setTotalDiasExtras(int totalDiasExtras) {
        this.totalDiasExtras = totalDiasExtras;
    }

    public boolean isEmpresaSuspensa() {
        return empresaSuspensa;
    }

    public void setEmpresaSuspensa(boolean empresaSuspensa) {
        this.empresaSuspensa = empresaSuspensa;
    }

    public String getTokenShortcode() {
        return tokenShortcode;
    }

    public void setTokenShortcode(String tokenShortcode) {
        this.tokenShortcode = tokenShortcode;
    }

    public boolean isUsarSistemaInternacional() {
        return usarSistemaInternacional;
    }

    public void setUsarSistemaInternacional(boolean usarSistemaInternacional) {
        this.usarSistemaInternacional = usarSistemaInternacional;
    }

    public boolean isPermiteContratosConcomitante() {
        return permiteContratosConcomitante;
    }

    public void setPermiteContratosConcomitante(boolean permiteContratosConcomitantes) {
        this.permiteContratosConcomitante = permiteContratosConcomitantes;
    }

}

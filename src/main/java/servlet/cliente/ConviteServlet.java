package servlet.cliente;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import servicos.adm.ConviteService;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;

public class ConviteServlet extends SuperServlet {

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Allow-Methods", "GET,POST");
        response.setContentType("application/json");

        String path = request.getServletPath();
        String[] pathParts = path.split("/");
        String recurso = pathParts[3];

        String chave = request.getParameter("chave");
        Integer empresa = Integer.valueOf(request.getParameter("empresa"));

        try {
            switch (recurso){
                case "gerar-link-convite":
                    try (Connection con = new DAO().obterConexaoEspecifica(chave)) {
                        String matricula = request.getParameter("matricula");
                        boolean utilizarConfigConviteAdm = request.getParameter("utilizarConfigConviteAdm") != null
                                && request.getParameter("utilizarConfigConviteAdm").equalsIgnoreCase("true");
                        response.getWriter().append(new ConviteService(con).gerarLinkConvite(chave, matricula, empresa, utilizarConfigConviteAdm));
                    }
                    break;
            }
        }catch (Exception e ){
            System.out.println("Erro na api rest "+recurso+". Message: "+e.getMessage());
            processarErro(e, request, response, null);
        }
    }

}

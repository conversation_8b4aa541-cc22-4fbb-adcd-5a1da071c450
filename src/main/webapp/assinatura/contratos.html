<!--
To change this template, choose Tools | Templates
and open the template in the editor.
-->
<!DOCTYPE html>
<html>
    <head>
        <title>ZW Assinatura Digital</title>

        <script type="text/javascript" src="jquery.min.js"></script>
        <link href="../css/signature-pad/signature-pad.css" rel="stylesheet" type="text/css">
        <link href="../css/guilhotina/jquery.guillotine.css" rel="stylesheet" type="text/css">
        <link href="../beta/css/font-awesome.css" rel="stylesheet" type="text/css">
        <link href="canvasCrop.css" rel="stylesheet" type="text/css"/>
        <link href="assinaturas_v11.css?" rel="stylesheet" type="text/css">
        <link href="facial2.css" rel="stylesheet" type="text/css">
        <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
        <meta name="mobile-web-app-capable" content="yes">
        <meta name="apple-mobile-web-app-capable" content="yes">
        <link rel="manifest" href="manifest.json">
        <link href="capturaFotoHTML5.css" rel="stylesheet" type="text/css">
        <script src="jquery.canvasCrop.js" type="text/javascript"></script>

        <link rel="icon" sizes="152x152" href="../imagens/assinaturadigital.png"/>
    </head>

    <body>
        <style>
            .fa-icon-crop:before {
                content: "\f125";
            }
            .caixa.btn{
                width: 97vw;
                margin: 0 auto;
                padding: 0;
                margin-bottom: 2vh;
            }
            .preview.foto{
                max-width: 98% !important;
            }
        </style>
        <div id="painelmenu">
            <div class="caixaMenu" style="overflow-y: auto;">
                <header>
                    <a class="voltar" onclick="closeMenu();">
                        <i class="fa-icon-chevron-left"></i>
                    </a>
                    <span class="texto-header">
                        PAINEL DE AÇÕES
                    </span>
                </header>

                <div id="username" class="texto-header">
                </div>

                <div id="empresas" class="texto-header">
                </div>

                <span class="infoAssinatura texto-header"
                  style="width: calc(100% - 8vh); text-align: center; font-weight: bold; font-style: normal; padding: 4vh; font-size: 2vh;">
                O QUE VOCÊ DESEJA FAZER?
            </span>

                <div class="itemMenu">
                    <a onclick="assinarUmContrato();">
                        <div class="caixaAzul">
                            <img src="../imagens/icon-contrato.svg" class="icones" style="width: 10vh; margin-top: 4vh;"/>
                        </div>
                    </a>
                    <span class="infoAssinatura"
                          style="width: calc(100% - 8vh); text-align: center; font-weight: bold; font-style: normal; padding: 2vh 4vh 4vh 4vh; font-size: 2vh; color: #094771;">
                        ASSINAR UM CONTRATO
                    </span>
                </div>

                <div class="itemMenu">
                    <a onclick="assinarUmContratoCancelado();">
                        <div class="caixaAzul">
                            <img src="../imagens/icon-contrato.svg" class="icones" style="width: 10vh; margin-top: 4vh;"/>
                        </div>
                    </a>
                    <span class="infoAssinatura"
                          style="width: calc(100% - 8vh); text-align: center; font-weight: bold; font-style: normal; padding: 2vh 4vh 4vh 4vh; font-size: 2vh; color: #094771;">
                        ASSINAR UM CANCELAMENTO DE CONTRATO
                    </span>
                </div>

                <div class="itemMenu">
                    <a onclick="assinarUmContratoProduto();">
                        <div class="caixaAzul">
                            <img src="../imagens/icon-contrato.svg" class="icones" style="width: 10vh; margin-top: 4vh;"/>
                        </div>
                    </a>
                    <span class="infoAssinatura"
                          style="width: calc(100% - 8vh); text-align: center; font-weight: bold; font-style: normal; padding: 2vh 4vh 4vh 4vh; font-size: 2vh; color: #094771;">
                        ASSINAR UM CONTRATO DE PRODUTO
                    </span>
                </div>

                <div class="itemMenu">
                    <a onclick="adicionarUmCartaoVacina()">
                        <div class="caixaAzul">
                            <img src="../imagens/icon_cartao_vacinacao_branco.svg" class="icones" style="width: 10vh; margin-top: 4vh;"/>
                        </div>
                    </a>
                    <span class="infoAssinatura"
                          style="width: calc(100% - 8vh); text-align: center; font-weight: bold; font-style: normal; padding: 2vh 4vh 4vh 4vh; font-size: 2vh; color: #094771;">
                        CADASTRAR CARTÃO DE VACINA
                    </span>
                </div>

                <div class="itemMenu">
                    <a onclick="assinarParQ();">
                        <div class="caixaAzul">
                            <img src="../imagens/icon-contrato.svg" class="icones" style="width: 10vh; margin-top: 4vh;"/>
                        </div>
                    </a>
                    <span class="infoAssinatura"
                          style="width: calc(100% - 8vh); text-align: center; font-weight: bold; font-style: normal; padding: 2vh 4vh 4vh 4vh; font-size: 2vh; color: #094771;">
                        ASSINAR PAR-Q
                    </span>
                </div>

                <div class="itemMenu" id="termoResponsabilidadeMenuLateral">
                    <a onclick="assinarTermoResponsabilidade();">
                        <div class="caixaAzul">
                            <img src="../imagens/icon-contrato.svg" class="icones" style="width: 10vh; margin-top: 4vh;"/>
                        </div>
                    </a>
                    <span class="infoAssinatura"
                          style="width: calc(100% - 8vh); text-align: center; font-weight: bold; font-style: normal; padding: 2vh 4vh 4vh 4vh; font-size: 2vh; color: #094771;">
                        TERMO RESPONSABILIDADE
                    </span>
                </div>

                <div class="itemMenu">
                    <a onclick="assinarUmPlano();">
                        <div class="caixaAzul">
                            <img src="../imagens/icon-contrato.svg" class="icones" style="width: 10vh; margin-top: 4vh;"/>
                        </div>
                    </a>
                    <span class="infoAssinatura"
                          style="width: calc(100% - 8vh); text-align: center; font-weight: bold; font-style: normal; padding: 2vh 4vh 4vh 4vh; font-size: 2vh; color: #094771;">
                        ASSINAR UM CONTRATO PERSONAL
                    </span>
                </div>

                <div class="itemMenu">
                    <a onclick="adicionarUmaFoto()">
                        <div class="caixaAzul">
                            <img src="../imagens/icon-camera-outline.svg" class="icones" style="width: 10vh; margin-top: 4vh;"/>
                        </div>
                    </a>
                    <span class="infoAssinatura"
                          style="width: calc(100% - 8vh); text-align: center; font-weight: bold; font-style: normal; padding: 2vh 4vh 4vh 4vh; font-size: 2vh; color: #094771;">
                        TIRAR UMA FOTO DE PERFIL
                    </span>
                </div>
            </div>
        </div>

        <header id="mainHeader">
            <a class="menu" id="idbtnmenu">
                <i class="fa-icon-reorder"></i>
            </a>
            <a class="voltar" onclick="voltar()">
                <i class="fa-icon-chevron-left"></i>
            </a>
            <span id="tituloHeader">
                OPÇÕES
            </span>
            <input type="text" class="campoPesquisa" id="valorFiltro" />
            <a class="search" onclick="habilitarPesquisa()">
                <i class="fa-icon-search"></i>
            </a>
            <a class="removesearch" onclick="desabilitarPesquisa()">
                <i class="fa-icon-remove"></i>
            </a>
        </header>

        <div id="main">
            <div style="margin-top: 15vh">
                <div class="itemMenu principal">
                    <a onclick="assinarUmContrato();">
                        <div class="caixaAzul principal">
                            <img src="../imagens/icon-contrato-dark.svg" class="icones" style="width: 10vh; margin-top: 4vh;"/>
                        </div>
                    </a>
                    <span class="infoAssinatura"
                          style="width: calc(100% - 8vh); text-align: center; font-weight: bold; font-style: normal; padding: 2vh 4vh 4vh 4vh; font-size: 2vh; color: #094771;">
                        ASSINAR UM CONTRATO
                    </span>
                </div>

                <div class="itemMenu principal">
                    <a onclick="assinarUmContratoCancelado();">
                        <div class="caixaAzul principal">
                            <img src="../imagens/icon-contrato-dark.svg" class="icones" style="width: 10vh; margin-top: 4vh;"/>
                        </div>
                    </a>
                    <span class="infoAssinatura"
                          style="width: calc(100% - 8vh); text-align: center; font-weight: bold; font-style: normal; padding: 2vh 4vh 4vh 4vh; font-size: 2vh; color: #094771;">
                        ASSINAR UM CANCELAMENTO DE CONTRATO
                    </span>
                </div>

                <div class="itemMenu principal">
                    <a onclick="assinarUmContratoProduto();">
                        <div class="caixaAzul principal">
                            <img src="../imagens/icon-contrato-dark.svg" class="icones" style="width: 10vh; margin-top: 4vh;"/>
                        </div>
                    </a>
                    <span class="infoAssinatura"
                          style="width: calc(100% - 8vh); text-align: center; font-weight: bold; font-style: normal; padding: 2vh 4vh 4vh 4vh; font-size: 2vh; color: #094771;">
                        ASSINAR UM CONTRATO DE PRODUTO
                    </span>
                </div>

                <div class="itemMenu principal" id="termoResponsabilidadePrincipal">
                    <a onclick="assinarTermoResponsabilidade()">
                        <div class="caixaAzul principal">
                            <img src="../imagens/icon-contrato-dark.svg" class="icones" style="width: 10vh; margin-top: 4vh;"/>
                        </div>
                    </a>
                    <span class="infoAssinatura"
                          style="width: calc(100% - 8vh); text-align: center; font-weight: bold; font-style: normal; padding: 2vh 4vh 4vh 4vh; font-size: 2vh; color: #094771;">
                        TERMO RESPONSABILIDADE
                    </span>
                </div>

                <div class="itemMenu principal">
                    <a onclick="adicionarUmCartaoVacina()">
                        <div class="caixaAzul principal ">
                            <img src="../imagens/icon_cartao_vacinacao.svg" class="icones" style="width: 10vh; margin-top: 4vh;"/>
                        </div>
                    </a>
                    <span class="infoAssinatura"
                          style="width: calc(100% - 8vh); text-align: center; font-weight: bold; font-style: normal; padding: 2vh 4vh 4vh 4vh; font-size: 2vh; color: #094771;">
                        CADASTRAR CARTÃO DE VACINA
                    </span>
                </div>

                <div class="itemMenu principal">
                    <a onclick="assinarParQ();">
                        <div class="caixaAzul principal">
                            <img src="../imagens/icon-contrato-dark.svg" class="icones" style="width: 10vh; margin-top: 4vh;"/>
                        </div>
                    </a>
                    <span class="infoAssinatura"
                          style="width: calc(100% - 8vh); text-align: center; font-weight: bold; font-style: normal; padding: 2vh 4vh 4vh 4vh; font-size: 2vh; color: #094771;">
                        ASSINAR PAR-Q
                    </span>
                </div>

                <div class="itemMenu principal">
                    <a onclick="assinarUmPlano();">
                        <div class="caixaAzul principal">
                            <img src="../imagens/icon-contrato-dark.svg" class="icones" style="width: 10vh; margin-top: 4vh;"/>
                        </div>
                    </a>
                    <span class="infoAssinatura"
                          style="width: calc(100% - 8vh); text-align: center; font-weight: bold; font-style: normal; padding: 2vh 4vh 4vh 4vh; font-size: 2vh; color: #094771;">
                        ASSINAR UM CONTRATO PERSONAL
                    </span>
                </div>


                <div class="itemMenu principal">
                    <a onclick="adicionarUmaFoto()">
                        <div class="caixaAzul principal">
                            <img src="../imagens/icon-camera-outline-dark.svg" class="icones" style="width: 10vh; margin-top: 4vh;"/>
                        </div>
                    </a>
                    <span class="infoAssinatura"
                          style="width: calc(100% - 8vh); text-align: center; font-weight: bold; font-style: normal; padding: 2vh 4vh 4vh 4vh; font-size: 2vh; color: #094771;">
                        TIRAR UMA FOTO DE PERFIL
                    </span>
                </div>
            </div>
        </div>

        <div class="caixaConcluir" onclick="concluir();" >
            <i class="fa-icon-ok-circle"></i>
            <span id="msgConcluir"></span>
            <a onclick="concluir();" class="ok">
                <div class="caixa btn"  style="margin-left: 1.5vw">
                    CONCLUIR
                </div>
            </a>
        </div>

        <div class="caixaAssinatura">
            <div style="position: absolute;
    top: 17%;
    width: 95%;
    text-align: center;
    font-weight: bold;
    color: #777777;
    font-size: 7vw;
    border-top: #777777 1px solid;
    margin-left: 2.5%;">
                Assine aqui
            </div>

            <div id="signature-pad" class="m-signature-pad">
                <div class="m-signature-pad--body">
                    <canvas></canvas>
                </div>
                <div class="m-signature-pad--footer">

                    <div class="right" style="display: none;">
                        <button type="button" class="button clear" data-action="clear">Limpar assinatura</button>
                    </div>
                    <div class="right" style="display: none;">
                        <button type="button" class="button save" data-action="save-png">Save as PNG</button>
                        <button type="button" class="button save" data-action="save-svg">Save as SVG</button>
                    </div>
                </div>
            </div>
            <div class="caixa assinaturaResponsavel lblTitulo" style="padding-top: 10px;">
                Assinatura do responsável
            </div>
            <a onclick="signaturePad.clear();" id="idbtnlimparassinatura">
                <div class="caixa btn" style="margin-bottom: 2vh; background-color: #ededed; color: #373737;">
                    LIMPAR ASSINATURA
                </div>
            </a>
            <div id="assFinAssinar" class="caixaAssinatura">
                <div style="position: absolute;
    top: 37%;
    width: 95%;
    text-align: center;
    font-weight: bold;
    color: #777777;
    font-size: 7vw;
    border-top: #777777 1px solid;
    margin-left: 2.5%;">
                    Assine aqui
                </div>

                <div id="signature-pad2" class="m-signature-pad">
                    <div class="m-signature-pad--body">
                        <canvas></canvas>
                    </div>

                    <div class="m-signature-pad--footer">
                        <div class="right" style="display: none;">
                            <button type="button" class="button clear" data-action="clear">Limpar assinatura</button>
                        </div>
                        <div class="right" style="display: none;">
                            <button type="button" class="button save" data-action="save-png">Save as PNG</button>
                            <button type="button" class="button save" data-action="save-svg">Save as SVG</button>
                        </div>
                    </div>
                </div>

                <div class="caixa assinaturaResponsavel lblTitulo" style="padding-top: 10px;">
                    Assinatura do responsável financeiro
                </div>

                <a onclick="signaturePad2.clear();" id="idbtnlimparassinatura2">
                    <div class="caixa btn" style="margin-bottom: 2vh; background-color: #ededed; color: #373737;">
                        LIMPAR ASSINATURA
                    </div>
                </a>
            </div>
            <a onclick="prosseguirFotoPerfil();">
                <div class="caixa btn" >
                    PROSSEGUIR
                </div>
            </a>
        </div>

        <div class="caixaDocumentos">
            <span class="infoAssinatura"
                  style="width: calc(100% - 8vh); text-align: center; font-weight: bold; font-style: normal; padding: 4vh; font-size: 2vh;">
                ANEXE OS DOCUMENTOS DE IDENTIFICAÇÃO E ENDEREÇO REFERENTES AO RESPONSÁVEL
            </span>

            <div class="labelPequena" style="margin-top: 5vh;">Documento de identificação <i class="fa-icon-remove" title="Remover" style="font-size: 24px; cursor: pointer; margin: 0px 0px 0px 8px;" onclick="limparDocumentos()"></i></div>
            <img id="uploadPreview" class="preview" onclick="document.getElementById('uploadImage').click();" style="cursor: pointer;"/>
            <input id="uploadImage" type="file" name="myPhoto" onchange="PreviewImage('uploadImage', 'uploadPreview', 'docs', 800);"
                   accept="image/*" style="display: none;"/>

            <div class="labelPequena">Comprovante de Endereço <i class="fa-icon-remove" title="Remover" style="font-size: 24px; cursor: pointer; margin: 0px 0px 0px 8px;" onclick="limparEndereco()"></i></div>
            <img id="uploadPreviewEND" class="preview" onclick="document.getElementById('uploadImageEND').click();" style="cursor: pointer;"/>
            <input id="uploadImageEND" type="file" name="myPhoto" onchange="PreviewImage('uploadImageEND', 'uploadPreviewEND', 'endereco', 800);"
                   accept="image/*" style="display: none;"/>

            <div class="labelPequena">Atestado de Aptidão Física <i class="fa-icon-remove" title="Remover" style="font-size: 24px; cursor: pointer; margin: 0px 0px 0px 8px;" onclick="limparAtestado()"></i></div>
            <img id="uploadPreviewATESTADO" class="preview" onclick="document.getElementById('uploadImageATESTADO').click();" style="cursor: pointer;"/>
            <input id="uploadImageATESTADO" type="file" name="myPhoto" onchange="PreviewImage('uploadImageATESTADO', 'uploadPreviewATESTADO', 'atestado', 800);"
                   accept="image/*" style="display: none;"/>

            <div class="labelPequena">Anexo 1 <i class="fa-icon-remove" title="Remover" style="font-size: 24px; cursor: pointer; margin: 0px 0px 0px 8px;" onclick="limparAnexo1()"></i></div>
            <img id="uploadPreviewAnexo1" class="preview" onclick="document.getElementById('uploadImageAnexo1').click();" style="cursor: pointer;"/>
            <input id="uploadImageAnexo1" type="file" name="myPhoto" onchange="PreviewImage('uploadImageAnexo1', 'uploadPreviewAnexo1', 'anexo1', 800);"
                   accept="image/*" style="display: none;"/>

            <div class="labelPequena">Anexo 2 <i class="fa-icon-remove" title="Remover" style="font-size: 24px; cursor: pointer; margin: 0px 0px 0px 8px;" onclick="limparAnexo2()"></i></div>
            <img id="uploadPreviewAnexo2" class="preview" onclick="document.getElementById('uploadImageAnexo2').click();" style="cursor: pointer;"/>
            <input id="uploadImageAnexo2" type="file" name="myPhoto" onchange="PreviewImage('uploadImageAnexo2', 'uploadPreviewAnexo2', 'anexo2', 800);"
                   accept="image/*" style="display: none;"/>

            <a onclick="prosseguirValidar();">
                <div class="caixa btn" >
                    PROSSEGUIR
                </div>
            </a>
        </div>

        <div class="caixaAtestado">
            <span class="infoAssinatura"
                  style="width: calc(100% - 8vh); text-align: center; font-weight: bold; font-style: normal; padding: 4vh; font-size: 2vh;">
                ANEXE A IMAGEM DO ATESTADO DE APTIDÃO FÍSICA
            </span>

            <div class="labelPequena" id="idnomeparaatestado" style="text-transform: uppercase;"></div>
            <img id="uploadPreviewAtestado1" class="preview" src="../imagens/image_icon.jpg" onclick="document.getElementById('uploadImageAtestado1').click();"/>
            <input id="uploadImageAtestado1" type="file" name="myPhoto" onchange="PreviewImage('uploadImageAtestado1', 'uploadPreviewAtestado1', true);"
                   accept="image/*" style="display: none; cursor: pointer;"/>

            <a onclick="prosseguirValidar();">
                <div class="caixa btn" >
                    CONCLUIR
                </div>
            </a>
        </div>

        <div class="caixaFoto">
            <span class="infoAssinatura" id="headerAluno"
                  style="width: calc(100% - 8vh); text-align: center; font-weight: bold; font-style: normal; padding: 4vh; font-size: 2vh;">
                TIRE UMA FOTO PARA O PERFIL DO ALUNO NO ZW
            </span>
            <div class="labelPequena" id="idnomeparafoto" style="text-transform: uppercase;"></div>

            <div id="containerimagebox" >
                <div class="imageBox" >
                    <div class="mask"></div>
                    <div class="thumbBox"></div>
                    <div class="spinner" style="display: none"></div>
                </div>
                <div style="width: 100%;">
                    <div class="tools clearfix">
                        <span id="rotateLeft" ><i class="fa-icon-undo"></i></span>
                        <span id="rotateRight" ><i class="fa-icon-repeat"></i></span>
                        <span id="zoomIn" ><i class="fa-icon-zoom-out"></i></span>
                        <span id="zoomOut" ><i class="fa-icon-zoom-in"></i></span>
                    </div>
                </div>
            </div>

            <div style="display: flex; align-items: center; justify-content: center; gap: 2vh; margin-top: 4vh;">
                <div style="border: 2px #094771 solid;
        border-radius: 50%;
        width: 10vh;
        height: 10vh;
        color: #094771;
        line-height: 10vh;
        text-align: center;
        cursor: pointer;
        margin-top: 4vh;
        display: block;
        font-size: 4vh;"
                     onclick="document.getElementById('upload-file').click();">
                    <i class="fa-icon-picture"></i>
                </div>
                <input type="file" id="upload-file" accept="image/*" style="display: none;"/>

                <script type="text/javascript">
                    var CanvasCrop;
                    $(function () {
                        var rot = 0, ratio = 1;
                        CanvasCrop = $.CanvasCrop({
                            cropBox: ".imageBox",
                            imgSrc: "images/avatar.jpg",
                            limitOver: 2
                        });


                        $('#upload-file').on('change', function () {
                            fotoAlterada = true;
                            var reader = new FileReader();
                            reader.onload = function (e) {
                                CanvasCrop = $.CanvasCrop({
                                    cropBox: ".imageBox",
                                    imgSrc: e.target.result,
                                    limitOver: 2
                                });
                                rot = 0;
                                ratio = 1;
                            };

                            var arquivo = this.files[0];
                            reader.onloadend = function (e) {
                                getOrientation(arquivo, function(orientation) {
                                    switch(orientation) {
                                        case 6:
                                            rot += 90;
                                            rot = rot > 360 ? 90 : rot;
                                            CanvasCrop.rotate(rot);
                                            break;
                                        case 8:
                                            rot -= 90;
                                            rot = rot < 0 ? 270 : rot;
                                            CanvasCrop.rotate(rot);
                                            break;
                                    }
                                });
                            };

                            reader.readAsDataURL(this.files[0]);
                            this.files = null;
                        });

                        $("#rotateLeft").on("click", function () {
                            rot -= 90;
                            rot = rot < 0 ? 270 : rot;
                            CanvasCrop.rotate(rot);
                            fotoAlterada = true;
                        });
                        $("#rotateRight").on("click", function () {
                            rot += 90;
                            rot = rot > 360 ? 90 : rot;
                            CanvasCrop.rotate(rot);
                            fotoAlterada = true;
                        });
                        $("#zoomIn").on("click", function () {
                            ratio = ratio * 0.9;
                            CanvasCrop.scale(ratio);
                            fotoAlterada = true;
                        });
                        $("#zoomOut").on("click", function () {
                            ratio = ratio * 1.1;
                            CanvasCrop.scale(ratio);
                            fotoAlterada = true;
                        });
                    });
                </script>
                <div style="border: 2px #094771 solid;
        border-radius: 50%;
        width: 10vh;
        height: 10vh;
        color: #094771;
        line-height: 10vh;
        text-align: center;
        cursor: pointer;
        margin-top: 4vh;
        display: block;
        font-size: 4vh;" onclick="capturarFoto()">
                    <i class="fa-icon-camera"></i>
                </div>
            </div>
        </div>

        <div id="modalCapturaFoto" class="modal" style="display: none;">
            <!-- Boto de fechar -->
            <a href="#" id="closeModalBtn" class="closeBtn pure-button pure-button-danger">
                <i>X</i>
            </a>

            <div id="capturaFotoDiv" class="uploadBox">
                <i id="loading_webcam" class="fa-icon-camera loadingWebcam"></i>
                <span id="loading_webcam_lbl" class="background_message centered">
                    Carregando...
                </span>
                <div id="video_container" class="uploadBox videoContainer">
                    <video id="webcamVideo"></video>
                </div>
            </div>
            <div id="imageDiv" class="uploadBox imageDiv">
                <canvas id="canvas_img" alt="Imagem enviada" class="imageUploaded pure-button-"></canvas>
            </div>
            <div style="display: flex; flex-direction: row; justify-content: center; gap: 1rem;">
                <a href="#" id="snapshot_btn" class="capFotoButton hiddenBtn">
                    <i class="capFotoButton icon fa-icon-camera"></i>
                    Capturar Foto
                </a>
                <a href="#" id="toggle_camera_btn" class="capFotoButton">
                    <i class="capFotoButton icon fa-icon-refresh"></i>
                    Trocar Câmera
                </a>
            </div>
        </div>

        <div class="caixaFacial">
            <div style="text-align: right"></div>

            <span onclick="sairCapturaFacial();" class="infoAssinatura cabecalhofacial">
                <i class="fa-icon-caret-left"></i>
                VOLTAR
            </span>

            <div class="containervideo">
                <div class="amostra" style="">
                    <img id="fotofacial1"/>
                    <img id="fotofacial2"/>
                </div>

                <video autoplay class="hidden" ></video>
            </div>

            <div  class="containerbtsvideo">
                <a onclick="trocarCamera();"
                   class="btnfacial virar">
                    <i class="fa-icon-refresh"></i>
                </a>

                <a onclick="enviarFotoFacial();"
                   class="btnfacial avancar">
                    <i class="fa-icon-arrow-right"></i>
                </a>

                <div onclick="takePhoto()"
                        class="bck-take">
                    <div class="frt-take">
                    </div>
                </div>
            </div>

<!--            <a onclick="limparFotos()" class="limparfotos" style="position: relative; display: none">-->
<!--                <div class="caixa btn" style="margin-top: 40px; background-color: #ededed; color: #373737;margin-bottom: 20px; ">-->
<!--                    LIMPAR FOTOS-->
<!--                </div>-->
<!--            </a>-->

            <div class="caixaConcluir" id="msgfacial" style="position: fixed" onclick="$('#msgfacial').hide()">
                <i class="fa-icon-warning-sign"></i>
                <span id="msgFacialtxt"></span>

                <a onclick="$('#msgfacial').hide()" class="ok" style="display: block">
                    <div class="caixa btn" style="background-color: #ffffff; color: #094771; margin-top: 40px; margin-left: 1vw;">
                        OK
                    </div>
                </a>
            </div>
        </div>

        <a onclick="prosseguirDocumentos()" class="enviarFotoAssinando" style="position: relative; ">
            <div class="caixa btn" style="margin-top: 4vh; background-color: #ededed; color: #373737;">
                PULAR
            </div>
        </a>

        <a onclick="subirFoto();" class="caixaFoto" style="position: relative; width: 100%;  bottom: 2vh;">
            <div class="caixa btn concluir" style="margin-top: 4vh;">
                ENVIAR FOTO
            </div>
        </a>

        <div class="caixaValidarTermoResponsabilidade" style="text-align-last: center;">
            <table style="width: 100%;">
                <tr valign="top">
                    <td class="caixaMenor2" style="text-align: left; margin-left: 15px; margin-right: 15px;">
                        <div class="labelPequena">Assinatura</div>

                        <div class="caixaPreview">
                            <img id="previewAssinaturaTermoResponsabilidade"
                                 class="preview" style="height: auto;" />
                        </div>
                    </td>
                </tr>
            </table>

            <a onclick="editar()" class="ok">
                <div class="caixa btn" style="margin-bottom: 2vh; background-color: #ededed; color: #373737;">
                    REENVIAR ASSINATURA
                </div>
            </a>

            <a onclick="voltarInicio()" class="ok">
                <div class="caixa btn" >
                    OK
                </div>
            </a>
        </div>

        <div class="caixaValidar" id="divAssinaturaDigital">
            <table style="width: 100%;">
                <tr valign="top">
                    <td class="caixaMenor2" style="text-align: left; margin-left: 30px; margin-right: 10px;">
                        <div class="labelPequena">Documento de identificação</div>
                        <div class="caixaPreview">
                            <img id="previewDocs"
                                 class="preview" onclick="abrirPreviewGrande(this)" style="cursor: pointer;"/>
                        </div>
                    </td>

                    <td class="caixaMenor2" style="text-align: right; margin-left: 15px; margin-right: 15px;">
                        <div class="labelPequena">Comprovante de Endereço</div>
                        <div class="caixaPreview">
                            <img id="previewEndereco"
                                 class="preview" onclick="abrirPreviewGrande(this)" style="cursor: pointer;"/>
                        </div>
                    </td>

                    <td class="caixaMenor2" style="text-align: right; margin-left: 15px; margin-right: 15px;">
                        <div class="labelPequena">Atestado</div>

                        <div class="caixaPreview">
                            <img id="previewAtestado"
                                 class="preview" onclick="abrirPreviewGrande(this)" style="cursor: pointer;"/>
                        </div>
                    </td>
                </tr>

                <tr valign="top">
                    <td class="caixaMenor2" style="text-align: right; margin-left: 30px; margin-right: 10px;">
                        <div class="labelPequena">Anexo 1</div>

                        <div class="caixaPreview">
                            <img id="previewAnexo1"
                                 class="preview" onclick="abrirPreviewGrande(this)" style="height: auto;"/>
                        </div>
                    </td>

                    <td class="caixaMenor2" style="text-align: right; margin-left: 15px; margin-right: 15px;">
                        <div class="labelPequena">Anexo 2</div>

                        <div class="caixaPreview">
                            <img id="previewAnexo2"
                                 class="preview" onclick="abrirPreviewGrande(this)" style="height: auto;"/>
                        </div>
                    </td>

                    <td class="caixaMenor2" style="text-align: left; margin-left: 15px; margin-right: 15px;">
                        <div class="labelPequena">Assinatura</div>

                        <div class="caixaPreview">
                            <img id="previewAssinatura"
                                 class="preview" style="height: auto;" />
                            <button type="button" id="abrirModal" onclick="abrirModal('assinaturaDigital')">Remover Assinatura</button>
                        </div>
                    </td>

                    <td id="assFinApresentar" class="caixaMenor2" style="text-align: left; margin-left: 15px; margin-right: 15px;">
                        <div class="labelPequena">Assinatura Resp. Financeiro</div>

                        <div class="caixaPreview">
                            <img id="previewAssinatura2"
                                 class="preview" style="height: auto;" />
                            <button type="button" id="abrirModal2" onclick="abrirModal('assinaturaDigital2')">Remover Assinatura</button>
                        </div>
                    </td>
                </tr>
            </table>

            <a onclick="validar();" id="concluirValidar">
                <div class="caixa btn" >
                    VALIDAR
                </div>
            </a>
            <a onclick="editar()" class="ok">
                <div class="caixa btn" style="margin-bottom: 2vh; background-color: #ededed; color: #373737;">
                    REENVIAR DOCUMENTOS
                </div>
            </a>
            <a onclick="voltarInicio()" class="ok">
                <div class="caixa btn" >
                    OK
                </div>
            </a>
        </div>
        <div id="modalAssinatura" class="modal">
            <div class="modal-content">
                <h3 class="lblTitulo">Deseja remover a assinatura?</h3>
                <div class="modal-options">
                    <button type="button" class="buttonRemover" onclick="removerAssinatura('assinaturaDigital', false)">Sim</button>
                    <button type="button" class="buttonFechar" onclick="fecharModal()">Não</button>
                </div>
            </div>
        </div>
        <div id="modalAssinatura2" class="modal">
            <div class="modal-content">
                <h3 class="lblTitulo">Deseja remover a assinatura?</h3>
                <div class="modal-options">
                    <button type="button" class="buttonRemover" onclick="removerAssinatura('assinaturaDigital2', true)">Sim</button>
                    <button type="button" class="buttonFechar" onclick="fecharModal()">Não</button>
                </div>
            </div>
        </div>
        <div id="modalAssinaturaProduto" class="modal">
            <div class="modal-content">
                <h3 class="lblTitulo">Deseja remover a assinatura?</h3>
                <div class="modal-options">
                    <button type="button" class="buttonRemover" onclick="removerAssinaturaProduto()">Sim</button>
                    <button type="button" class="buttonFechar" onclick="fecharModal()">Não</button>
                </div>
            </div>
        </div>
        <div id="modalRemoverAssinaturaEletronica" class="modal">
            <div class="modal-content">
                <h3 class="lblTitulo">Deseja remover a assinatura?</h3>
                <div class="modal-options">
                    <button type="button" class="buttonRemover" style="cursor:pointer;" onclick="removerAssinatura('assinaturaEletronica', false)">Sim</button>
                    <button type="button" class="buttonFechar" style="cursor:pointer;"  onclick="fecharModal()">Não</button>
                </div>
            </div>
        </div>
        <div id="permissaoNegadaRemoverAssinatura" class="modal">
            <div class="modal-content">
                <h3 class="lblTitulo">Você não tem permissão para remover a assinatura.</h3>
                <h3 style="text-align: center">Permissão (9.70)</h3>
                <div class="modal-options" style="justify-content: center">
                    <button type="button" style="margin-top: 30px" class="buttonFechar" onclick="fecharModal()">Fechar</button>
                </div>
            </div>
        </div>

        <div id="modalAssinaturaEletronica" class="modal">
            <div class="modal-content" >
                <p><label style="color: #777777; font-weight: bold;">Assinado via email em </label><label id="labelAssinadoEm" style="color: #777777;"/></p>
                <p><label style="color: #777777; font-weight: bold;">Pelo IP: </label><label id="labelAssinadoIp" style="color: #777777;"/></p>
                <p><label style="color: #777777; font-weight: bold;">CPF confirmado: </label><label id="labelAssinadoCpf" style="color: #777777;"/></p>
                <p><label style="color: #777777; font-weight: bold;">Email confirmado: </label><label id="labelAssinadoEmail" style="color: #777777;"/></p>
                <table style="width: 100%;">
                    <tr valign="top">
                        <td class="caixaMenor" style="text-align: left; margin-left: 15px; margin-right: 15px;">
                            <button type="button"  style="cursor:pointer;background-color: #094771;padding: 10px;border: none;text-align: center;font-size: 12px;color: #ffffff; font-weight: bold;"  onclick="abrirModal('assinaturaEletronica'),fecharModalAssinaturaEletronica();">Remover Assinatura</button>
                        </td>
                        <td class="caixaMenor" style="width: 30%;text-align: left; margin-left: 15px; margin-right: 15px;">
                            <button type="button"  style="cursor:pointer; padding: 10px;border: none;text-align: center; font-size: 12px; font-weight: bold;" onclick="fecharModalAssinaturaEletronica();">Fechar</button>
                        </td>
                    </tr>
                </table>
            </div>
        </div>

        <div class="caixaValidar" id="caixaValidarParq">
            <table style="width: 100%;">
                <tr valign="top">

                    <td class="caixaMenor2" style="text-align: left; margin-left: 15px; margin-right: 15px;; margin-bottom: 15px">
                        <div class="labelPequena">Assinatura</div>

                        <div class="caixaPreview" style="margin-left: 15px; margin-right: 15px">
                            <img id="previewAssinaturaParq"
                                 class="preview" style="height: 200px;"/>
                            <button type="button" id="abrirModalParq" onclick="abrirModalParq()">Editar Assinatura</button>
                        </div>
                    </td>

                    <td class="caixaMenor2" style="text-align: left; margin-left: 15px; margin-right: 15px; margin-bottom: 15px">
                        <div class="labelPequena">PDF</div>

                        <a id="pdfParqImp" onclick="downloadPdfParq()">
                        <div class="caixaPreview" style="text-align: center; height: 292px;">
                                <img class="preview" src="../imagens/arquivo.png" style="margin-top: 15%;"/>
                        </div>
                        </a>
                    </td>
                </tr>
            </table>

            <div id="permissaoNegadaRemoverAssinaturaParq" class="modal">
                <div class="modal-content">
                    <h3 class="lblTitulo">Você não tem permissão para editar a assinatura.</h3>
                    <h3 style="text-align: center">Permissão (9.70)</h3>
                    <div class="modal-options" style="justify-content: center">
                        <button type="button" style="margin-top: 30px" class="buttonFechar" onclick="fecharModalParq()">Fechar</button>
                    </div>
                </div>
            </div>

            <div id="modalAssinaturaParq" class="modal">
                <div class="modal-content">
                    <h3 class="lblTitulo">Deseja editar a assinatura?</h3>
                    <div class="modal-options">
                        <button type="button" class="buttonRemover" onclick="editarAssinaturaParq(this)">Sim</button>
                        <button type="button" class="buttonFechar" onclick="fecharModalParq()">Não</button>
                    </div>
                </div>
            </div>

            <a onclick="voltarInicio()" class="ok">
                <div class="caixa btn" >
                    OK
                </div>
            </a>
        </div>

        <div class="caixaCartaoVacina">
            <span class="infoAssinatura"
                  style="width: calc(100% - 8vh); text-align: center; font-weight: bold; font-style: normal; padding: 4vh; font-size: 2vh;">
                ANEXE O CARTÃO DE VACINAS
            </span>

            <div class="labelPequena" id="idnomeparacartao" style="text-transform: uppercase;"></div>
            <img style="margin-bottom: 10px;" id="uploadPreviewAnexo1Cartao" class="preview" src="../imagens/image_icon.jpg" onclick="document.getElementById('uploadImageAnexo1Cartao').click();"/>
            <input  id="uploadImageAnexo1Cartao" type="file" name="myPhoto" onchange="PreviewImage('uploadImageAnexo1Cartao', 'uploadPreviewAnexo1Cartao', 'cartaovacina', 800);"
                   accept="image/*" style="display: none; cursor: pointer;"/>
            <div class="labelPequena">Cartão de vacina <i class="fa-icon-remove" title="Remover" style="font-size: 24px; cursor: pointer; margin: 0px 0px 0px 8px;" onclick="limparAnexo1Cartao()"></i></div>
            <div class="labelPequena">
                <label style="margin: 20px;" class="selectPequeno">
                    <input style="margin: 4px;border: 1px solid #B4B7BB;" type="checkbox" class="radio" value="1" name="tipoanexo" onclick="selecionarTipoAnexo(this)" />1ª dose</label>
                <label style="margin: 20px;"  class="selectPequeno">
                    <input style="margin: 4px;border: 1px solid #B4B7BB;" type="checkbox" class="radio" value="2" name="tipoanexo" onclick="selecionarTipoAnexo(this)" />2ª dose / Dose única</label>
            </div>

            <a onclick="prosseguirValidar();">
                <div class="caixa btn" >
                    PROSSEGIR
                </div>
            </a>

            <div class="caixaConcluir" id="msgcartao" style="position: fixed" onclick="$('#msgcartao').hide()">
                <i class="fa-icon-warning-sign"></i>
                <span id="msgcartaotxt"></span>

                <a onclick="$('#msgcartao').hide()" class="ok" style="display: block">
                    <div class="caixa btn" style="background-color: #ffffff; color: #094771; margin-top: 40px; margin-left: 1vw;">
                        OK
                    </div>
                </a>
            </div>
        </div>

        <div class="caixaCartaoVisualizar">
            <table style="width: 100%;  text-align: center; margin-bottom: 10px;">
                <div class="labelPequena" id="idnomeparacartao1" style="text-transform: uppercase;"></div>
                <tr valign="top">
                    <td class="caixaMenor2" style="text-align: left; margin-left: 30px; margin-right: 10px;">
                        <div class="labelPequena">Cartão de Vacina</div>
                        <div class="caixaPreview">
                            <img id="previewCartao"
                                 class="preview" onclick="abrirPreviewGrande(this)" style="cursor: pointer;"/></div>
                    </td>
                </tr>
            </table>

            <div class="labelPequena">
                <label style="margin: 20px;" class="selectPequeno">
                    <input disabled="true" style="margin: 4px;border: 1px solid #B4B7BB;" type="checkbox" class="radio" value="1" name="tipoanexo1"  />1ª dose</label>
                <label style="margin: 20px;"  class="selectPequeno">
                    <input disabled="true" style="margin: 4px;border: 1px solid #B4B7BB;" type="checkbox" class="radio" value="2" name="tipoanexo1"  />2ª dose / Dose única</label>
            </div>

            <a onclick="editarCartao()" class="ok">
                <div class="caixa btn" style="margin-bottom: 2vh; background-color: #ededed; color: #373737;">
                    REENVIAR CARTÃO DE VACINA
                </div>
            </a>

            <a onclick="voltarInicio()" class="ok">
                <div class="caixa btn" >
                    VOLTAR
                </div>
            </a>
        </div>

        <div class="formularioParQ">
            <span id="titleFormularioParQ" class="infoAssinatura"
                  style="width: calc(100% - 8vh); text-align: center; font-weight: bold; font-style: normal; padding: 4vh; font-size: 2vh;">
                Preencha o formulário Par-Q
            </span>

            <div class="labelPequena" id="idParQ" style="text-transform: uppercase;"></div>

            <div id="perguntasParQ"></div>

            <div class="termoDeAceite">
                <div class="titleTermoAceite">
                    TERMO DE RESPONSABILIDADE PARA PRÁTICA DE ATIVIDADE FÍSICA
                </div>
                <!--            Lei Estadual 6.725/2014 - Ticket: TW-470-->
                <div style="font-size: 10px" id="leiParqRJ">
                    <div class="titleTermoAceite">LEI Nº 6765 DE 05 DE MAIO DE 2014.</div>
                    <div>DISPÕE SOBRE A PRÁTICA DE ATIVIDADES FÍSICAS E ESPORTIVAS EM CLUBES, ACADEMIAS E ESTABELECIMENTOS SIMILARES, E DÁ OUTRAS PROVIDÊNCIAS. </div>
                    <div> O GOVERNADOR DO ESTADO DO RIO DE JANEIRO </div>
                    <div> Faço saber que a Assembleia Legislativa do Estado do Rio de Janeiro decreta e eu sanciono a seguinte Lei: </div>
                    <div style="margin-top: 10px"> Art. 1º Considera-se obrigatório e imprescindível, para a prática de qualquer atividade física e esportiva, em clubes, academias e estabelecimentos similares, o preenchimento, pelo interessado, do Questionário de Prontidão para Atividade Física constante do Anexo I e do Termo de Responsabilidade para a Prática de Atividade Física constante do Anexo II desta Lei. </div>
                    <div>  Parágrafo único. Se o interessado for menor de idade, o Questionário e o Termo de Responsabilidade deverão ser preenchidos e assinados pelo responsável legal, juntamente com sua autorização por escrito. </div>
                    <div style="margin-top: 10px">  Art. 2º Fica dispensada a apresentação de atestado médico ou a obrigatoriedade de qualquer outro exame de aptidão física aos interessados que responderem negativamente a todas as perguntas do Questionário de Prontidão para Atividade Física. </div>
                    <div>  Parágrafo único. Aos que responderem positivamente a qualquer uma das perguntas do Questionário, será exigida a apresentação de atestado médico de aptidão física, na forma das Leis Estaduais nº 2.014, de 15 de julho de 1992, e 2.835, de 17 de novembro de 1997, o qual deverá ser anotado e arquivado junto ao prontuário do interessado. </div>
                    <div style="margin-top: 10px">  Art. 3º Esta lei entra em vigor na data de sua publicação. </div>
                    <div style="margin-top: 15px">  Rio de Janeiro, 05 de maio de 2014. </div>
                    <div>   LUIZ FERNANDO DE SOUZA </div>
                    <div style="margin-bottom: 20px">   Governador </div>
                </div>
                <!--            Lei Estadual 20630/2018 - Ticket: TW-1777-->
                <div style="font-size: 10px" id="leiParqGO">
                    <div class="titleTermoAceite">LEI N 20.630, DE 08 DE NOVEMBRO DE 2019.</div>
                    <div>
                        Obriga, para a prática de qualquer atividade física e esportiva, o preenchimento do
                        documento que especifica e dá outras providências.
                    </div>
                    <div>O GOVERNADOR DO ESTADO DE GOIÁS</div>
                    <div>
                        A ASSEMBLEIA LEGISLATIVA DO ESTADO DE GOIÁS, nos termos do art. 10 da Constituição Estadual, decreta e eu
                        sanciono a seguinte Lei:
                    </div>
                    <div style="margin-top: 10px">
                        Art. 1º É obrigatório, para a prática de qualquer atividade física e esportiva, em clubes, academias e estabelecimentos
                        similares, o preenchimento, pelo interessado, do Questionário de Prontidão para Atividade Física constante do Anexo Único desta Lei.
                    </div>
                    <div>
                        Parágrafo único. Se o interessado for menor de idade, o Questionário de Prontidão para Atividade Física deverá ser
                        preenchido e assinado pelo responsável legal, juntamente com sua autorização por escrito.
                    </div>
                    <div style="margin-top: 10px">
                        Art. 2° Somente aos que responderem positivamente a qualquer uma das perguntas do Questionário será exigida a
                        apresentação de atestado médico de aptidão física.
                    </div>
                    <div style="margin-top: 10px">
                        Art. 3° Fica revogada a Lei nº 12.881, de 03 de junho de 1996.
                    </div>
                    <div style="margin-top: 10px">
                        Art. 4º Esta Lei entra em vigor na data de sua publicação
                    </div>
                    <div style="margin-top: 15px">
                        Goiânia, 08 de novembro de 2019.
                    </div>
                    <div>
                        RONALDO RAMOS CAIADO
                    </div>
                    <div style="margin-bottom: 20px">
                        Governador
                    </div>
                </div>
                <div class="textoTermoAceite">
                    <input type="checkbox" id="checkTermoDeAceite" name="termoDeAceite">
                    <label id="texto_obrigatorio_termo" for="checkTermoDeAceite">
                        Declaro que estou ciente de que é recomendável conversar com um médico, antes de iniciar ou aumentar o nível
                        de atividade física pretendido, assumindo plena responsabilidade pela realização de qualquer atividade física
                        sem o atendimento desta recomendação.
                    </label>
                </div>
            </div>
        </div>

        <div class="caixaAssinaturaTermoResponsabilidade">
            <div class="assine-aqui">
                Assine aqui
            </div>

            <div id="signature-pad-termoresponsabilidade" class="m-signature-pad">
                <div class="m-signature-pad--body">
                    <canvas></canvas>
                </div>
                <div class="m-signature-pad--footer">

                    <div class="right" style="display: none;">
                        <button type="button" class="button clear" data-action="clear">Limpar assinatura</button>
                    </div>
                    <div class="right" style="display: none;">
                        <button type="button" class="button save" data-action="save-png">Save as PNG</button>
                        <button type="button" class="button save" data-action="save-svg">Save as SVG</button>
                    </div>
                </div>
            </div>
            <div class="caixa assinaturaResponsavel lblTitulo" style="padding-top: 10px;">
                Assinatura do responsável
            </div>
            <a onclick="signaturePad.clear();" id="idbtnlimparassinaturatermoresponsabilidade">
                <div class="caixa btn" style="margin-bottom: 2vh; background-color: #ededed; color: #373737;">
                    LIMPAR ASSINATURA
                </div>
            </a>
            <a  onclick="prosseguirValidar();">
                <div class="caixa btn" >
                    PROSSEGUIR
                </div>
            </a>
        </div>

        <div class="caixaAssinaturaParQ">
            <div class="assine-aqui">
                Assine aqui
            </div>

            <div id="signature-pad-parq" class="m-signature-pad">
                <div class="m-signature-pad--body">
                    <canvas></canvas>
                </div>
                <div class="">

                    <div class="right" style="display: none;">
                        <button type="button" class="button clear" data-action="clear">Limpar assinatura</button>
                    </div>
                    <div class="right" style="display: none;m-signature-pad--footer">
                        <button type="button" class="button save" data-action="save-png">Save as PNG</button>
                        <button type="button" class="button save" data-action="save-svg">Save as SVG</button>
                    </div>
                </div>
            </div>
            <div class="caixa assinaturaResponsavel lblTitulo" style="padding-top: 10px;">
                Assinatura do responsável
            </div>
            <a onclick="signaturePad.clear();" id="idbtnlimparassinaturaparq">
                <div class="caixa btn" style="margin-bottom: 2vh; background-color: #ededed; color: #373737;">
                    LIMPAR ASSINATURA
                </div>
            </a>
            <a  onclick="prosseguirValidar();">
                <div class="caixa btn" >
                    PROSSEGUIR
                </div>
            </a>
        </div>

        <div class="previewGrande" onclick="fecharPreviewGrande();" style="z-index: 999; left: 0;">
                <img id="idpreviewGrande" class="preview" style="margin-top: 10vh; width: 70%; margin-left: 15%;"/>
            </div>

        <div class="spinnerCarregando" id="loadingDiv">
            <div class="bounce1"></div>
            <div class="bounce2"></div>
            <div class="bounce3"></div>
        </div>

        <script type="text/javascript" src="../script/capturaFoto/Webcam.js/webcam.zwmod.js"></script>
        <script type="text/javascript" src="../script/signature-pad/signature_pad.js"></script>
        <script type="text/javascript" src="../script/signature-pad/app.js"></script>
        <script type="text/javascript" src="../script/guilhotina/jquery.guillotine.min.js"></script>
        <script type="text/javascript" src="../script/guilhotina/html2canvas.js"></script>
        <script type="text/javascript" src="capturaFotoHTML5.2.js"></script>
        <script type="text/javascript" charset="ISO-8859-1" src="assinaturas_v58.js"></script>
        <script type="text/javascript" src="facial.js"></script>
        <script type="text/javascript" src="imageCapture3.js"></script>
        <canvas class="pseudocanvas" id="resize_canvas"></canvas>
        <canvas class="pseudocanvas" id="crop_canvas"></canvas>
        <canvas class="pseudocanvas" id="rotation_canvas"></canvas>
        <canvas class="pseudocanvas" id="canvasprofile"></canvas>

        <img id="pseudoprofile" crossOrigin="Anonymous" style="display: none;"/>
    </body>

    <script>
        utilizaTermoResponsabilidade();
        verificaConfigSescEAtualizaMensagemParq();
        if ('serviceWorker' in navigator) {
            console.log("Will the service worker register?");
            navigator.serviceWorker.register('service-worker.js')
                .then(function(reg){
                    console.log("Yes, it did.");
                }).catch(function(err) {
                    console.log("No it didn't. This happened: ", err)
                });
        }
    </script>
</html>

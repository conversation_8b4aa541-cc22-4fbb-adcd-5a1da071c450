<%@ taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@include file="../includes/imports.jsp" %>

<rich:modalPanel id="modalGymPass" domElementAttachment="parent"
                 autosized="true" shadowOpacity="false" width="510"
                 styleClass="novaModal">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText id="lblModalGymPass" value="Token Wellhub"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                    id="hidelinkGymPass"/>
            <rich:componentControl for="modalGymPass" attachTo="hidelinkGymPass"
                                   operation="hide" event="onclick">
            </rich:componentControl>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formGymPass" styleClass="pure-form" ajaxSubmit="true">
        <div style="width: 100%">
            <fieldset>
                <legend style="font-size: 14px !important; color: #074871;border: none;">1� Etapa - Cadastro do Token</legend>
                <div style="width: auto;border-left: 02px solid #b1b1bd;">
                    <h:panelGrid columns="1" width="100%"
                                 footerClass="colunaCentralizada" headerClass="subordinado"
                                 styleClass="paginaFontResponsiva">

                        <h:panelGrid columns="2" styleClass="paginaFontResponsiva" cellpadding="8"
                                     columnClasses="colunaCentralizada"
                                     width="100%">
                            <h:selectOneMenu id="tipoGymPass" styleClass="font14 cinza"
                                             value="#{TelaClienteControle.cliente.gympassTypeNumber}">
                                <f:selectItems value="#{TelaClienteControle.listaTipoGymPass}"/>
                            </h:selectOneMenu>
                            <h:inputText id="tokenGymPass" styleClass="font14 cinza" title="Informe o Token" maxlength="15"
                                         value="#{TelaClienteControle.cliente.gympasUniqueToken}"/>

                        </h:panelGrid>

                        <h:panelGroup layout="block" styleClass="container-botoes">
                            <a4j:commandLink reRender="formGymPass"
                                             id="excluirGymPass"
                                             rendered="#{LoginControle.permissaoAcessoMenuVO.excluirTokemGympass}"
                                             action="#{TelaClienteControle.excluirTokenGymPass}"
                                             oncomplete="#{TelaClienteControle.mensagemNotificar};#{TelaClienteControle.onCompleteGymPass};adicionarPlaceHolderGymPass();"
                                             styleClass="botaoPrimario texto-size-14" style="margin-left: 10px">
                                <h:outputText style="font-size: 14px;" value="Excluir"/>
                            </a4j:commandLink>

                            <a4j:commandLink reRender="formGymPass"
                                             id="salvarGymPassa"
                                             action="#{TelaClienteControle.salvarTokenGymPass}"
                                             oncomplete="#{TelaClienteControle.mensagemNotificar};#{TelaClienteControle.onCompleteGymPass};adicionarPlaceHolderGymPass();"
                                             styleClass="botaoPrimario texto-size-14" style="margin-left: 10px">
                                <h:outputText style="font-size: 14px" value="Salvar"/>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                </div>
            </fieldset>
        </div>
        <div style="width: 100%;">
            <fieldset>
                <legend style="font-size: 14px !important; color: #074871;border: none;" >2� Etapa - Foi feito check-in do Usu�rio do Token?</legend>
                <h:panelGroup layout="block" styleClass="container-botoes" >
                    <a4j:commandLink reRender="formGymPass"
                                     id="validarGymPassa"
                                     action="#{TelaClienteControle.validarTokenGymPass}"
                                     oncomplete="#{TelaClienteControle.mensagemNotificar};#{TelaClienteControle.onCompleteGymPass};adicionarPlaceHolderGymPass();"
                                     styleClass="botaoPrimario texto-size-14" style="margin-top: 10px">
                        <h:outputText style="font-size: 14px;" value="Autorizar Acesso"/>
                    </a4j:commandLink>
                </h:panelGroup>
            </fieldset>
        </div>
    </a4j:form>

    <script type="text/javascript">
        window.onload = adicionarPlaceHolderGymPass();

        function adicionarPlaceHolderGymPass() {
            if (document.getElementById("formGymPass:tokenGymPass") != null) {
                document.getElementById("formGymPass:tokenGymPass").setAttribute("placeholder", "Informe Token GymPass");
            }
        }
    </script>

</rich:modalPanel>

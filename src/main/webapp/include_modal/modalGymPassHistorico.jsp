<%@ taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@include file="../includes/imports.jsp" %>

<rich:modalPanel id="modalGymPassHistorico" domElementAttachment="parent"
                 autosized="true" shadowOpacity="false" width="700"
                 styleClass="novaModal">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText id="lblModalGymPassHistorico" value="Hist�rico Wellhub"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                    id="hidelinkGymPassHistorico"/>
            <rich:componentControl for="modalGymPassHistorico" attachTo="hidelinkGymPassHistorico"
                                   operation="hide" event="onclick">
            </rich:componentControl>
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formGymPassHisto" ajaxSubmit="true">
        <h:panelGroup layout="block" styleClass="paginaFontResponsiva"
                      style="max-width: 700px; text-align: center; max-height: 250px; overflow: auto">

            <table class="tblHeaderLeft semZebra" style="margin-bottom: 5px;">
                <thead>
                <tr>
                    <th>
                        <h:outputText styleClass="texto-size-14 cinza negrito" style="color: #777"
                                      value="C�DIGO"/>
                    </th>
                    <th>
                        <h:outputText styleClass="texto-size-14 cinza negrito" style="color: #777"
                                      value="DATA DO ACESSO"/>
                    </th>
                    <th>
                        <h:outputText styleClass="texto-size-14 cinza negrito" style="color: #777"
                                      value="TOKEN"/>
                    </th>
                    <th>
                        <h:outputText styleClass="texto-size-14 cinza negrito" style="color: #777"
                                      value="LEGENDA"/>
                    </th>
                    <th>
                        <h:outputText styleClass="texto-size-14 cinza negrito" style="color: #777"
                                      value="VALOR"/>
                    </th>
                    <th>
                        <h:outputText styleClass="texto-size-14 cinza negrito" style="color: #777"
                                      value="PRODUTO"/>
                    </th>
                </tr>
                </thead>

                <tbody>
                <a4j:repeat var="gym" value="#{TelaClienteControle.listaGymPass}">
                    <tr>
                        <td>
                            <h:outputText
                                    value="#{gym.codigo}"
                                    style="text-decoration: none; text-align: left; font-size: 14px; color: #29AAE2;"/>
                        </td>
                        <td>
                            <h:outputText
                                    value="#{gym.dataInicioAcesso_Apresentar}"
                                    style="text-decoration: none; text-align: left; font-size: 14px; color: #29AAE2;"/>
                        </td>
                        <td>
                            <h:outputText
                                    value="#{gym.tokenGymPass}"
                                    style="text-decoration: none; text-align: left; font-size: 14px; color: #29AAE2;"/>
                        </td>
                        <td>
                            <h:outputText rendered="#{gym.permiteAcesso}">
                                <i class="fa-icon-circle" style="color: #2BAF50"></i>
                            </h:outputText>
                            <h:outputText rendered="#{!gym.permiteAcesso}">
                                <i class="fa-icon-circle" style="color: #FF5555"></i>
                            </h:outputText>
                        </td>
                        <td>
                            <h:outputText
                                    value="#{gym.valorGympass_ApresentarReal}"
                                    style="text-decoration: none; text-align: left; font-size: 14px; color: #29AAE2;"/>
                        </td>
                        <td>
                            <h:outputText
                                    value="#{gym.produtoGymPassApresentar}"
                                    style="text-decoration: none; text-align: left; font-size: 14px; color: #29AAE2;"/>
                        </td>
                    </tr>
                </a4j:repeat>
                </tbody>
            </table>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>

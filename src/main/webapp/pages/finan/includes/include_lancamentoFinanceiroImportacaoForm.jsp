<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<link href="css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="css/tooltipster/tooltipster.css" rel="stylesheet" type="text/css"/>
<link href="css/tooltipster/tooltipster-light.css" rel="stylesheet" type="text/css"/>

<style type="text/css">
    .tituloImportacao {
        font-family: "Segoe UI", "Trebuchet MS", sans-serif;
        font-size: 16px;
        font-weight: bold;
        color: #2c3e50;
        padding: 8px 0;
        margin-top: 5px;
        margin-bottom: 5px;
    }

    .textoInfo {
        font-family: "Segoe UI", Arial, sans-serif;
        font-size: 13px;
        color: #555;
        margin-bottom: 14px;
        line-height: 1.5;
        margin-top: 5px;
    }

    .botaoCentralizado {
        text-align: center;
        margin-top: 16px;
        margin-bottom: 10px;
    }

    .botaoImportacao {
        padding: 10px 24px;
        background-color: #3498db;
        color: #fff;
        border: none;
        border-radius: 6px;
        font-weight: bold;
        font-size: 13px;
        cursor: pointer;
        transition: background-color 0.3s ease;
        height: 12px;
    }

    .botaoDownload {
        padding: 10px 24px;
        height: 18px;
        margin-top: 5px;
        margin-bottom: 5px;
    }

    .botaoImportacao:hover {
        background-color: #2980b9;
    }

    .importacaoPainel {
        margin-top: 5px;
    }

    .resultadoImportacao {
        background-color: #ecf0f1;
        padding: 15px;
        border-radius: 6px;
        border: 1px solid #bdc3c7;
        font-family: "Segoe UI", Arial, sans-serif;
        font-size: 13px;
        color: #333;
        margin-top: 20px;
    }

    .tabelaHistorico {
        width: 100%;
        margin-top: 20px;
        border-collapse: collapse;
        font-size: 13px;
    }

    .tabelaHistorico th, .tabelaHistorico td {
        border: 1px solid #ccc;
        padding: 8px;
        text-align: left;
    }

    .tabelaHistorico th {
        background-color: #f9f9f9;
        font-weight: bold;
    }

    .botaoDetalhes {
        background-color: #7f8c8d;
        color: #fff;
        border: none;
        padding: 6px 12px;
        font-size: 12px;
        border-radius: 4px;
        cursor: pointer;
    }

    .botaoDetalhes:hover {
        background-color: #636e72;
    }
</style>

<h:panelGroup style="width:100%; vertical-align: top;">

    <table width="100%">
        <tr>
            <td align="left">
                <img src="${root}/images/arrow2.gif" width="16" height="16"
                     style="vertical-align: middle; margin-right: 6px;"/>
                <h:outputText styleClass="tituloImportacao" value="Importação de Contas (Planilha)"/>
            </td>
        </tr>
    </table>

    <div class="sep"><img src="${root}/images/shim.gif"/></div>


    <h:panelGrid columns="1" width="100%" styleClass="" columnClasses="">

        <h:commandButton value=" Baixar modelo de planilha"
                         action="#{MovContaControle.downloadModeloPlanilhaImportacao}"
                         styleClass="botaoDownload"
                         title="Clique para baixar o modelo de importação"
                         onclick="Richfaces.showModalPanel('panelStatus1'); monitorarDownload();"/>


        <h:outputText styleClass="textoInfo"
                      value="Utilize o modelo de planilha para importar contas a pagar e a receber.
                      A planilha contém abas auxiliares com códigos válidos para facilitar o preenchimento."/>

    </h:panelGrid>

    <!--antigo -->

    <h:panelGrid columns="1" width="100%" styleClass="" columnClasses="">

        <rich:fileUpload
                id="uploadPlanilha"
                fileUploadListener="#{MovContaControle.processarUploadPlanilhaImportacao}"
                maxFilesQuantity="1"
                immediateUpload="true"
                acceptedTypes="xls,xlsx"
                addButtonClass=""
                addControlLabel="Enviar planilha preenchida"
                doneLabel="Upload concluído"
                ontyperejected="alert('Tipo de arquivo não permitido. Apenas .xls ou .xlsx');"
                style="width: 100%;">


            <a4j:support event="onuploadcomplete" reRender="uploadPlanilha,painelImportacoes,panelMensagemImportacao"/>


            <a4j:support event="onclear"
                         action="#{MovContaControle.removerArquivo}"
                         oncomplete="limparUploadComponente()"
                         reRender="uploadPlanilha, painelImportacoes,panelMensagemImportacao"/>

            <!-- Caso o usuário cancele manualmente o upload -->
            <a4j:support event="onuploadcanceled"
                         action="#{MovContaControle.removerArquivo}"
                         reRender="uploadPlanilha, painelImportacoes,panelMensagemImportacao"/>
        </rich:fileUpload>


    </h:panelGrid>
    <h:panelGrid id="panelMensagemImportacao" columns="1" width="100%" styleClass="tabMensagens">
        <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
            <h:panelGrid columns="1" width="100%">
                <h:outputText id="msgContaPagarImportacao" styleClass="mensagem" value="#{MovContaControle.mensagem}"/>
                <h:outputText styleClass="mensagemDetalhada" value="#{MovContaControle.mensagemDetalhada}"/>
            </h:panelGrid>
        </h:panelGrid>
    </h:panelGrid>

    <!-- Seção: Histórico de Importações -->
    <h:panelGroup rendered="#{not empty MovContaControle.historicoImportacoes}" layout="block" styleClass="secao" id="painelImportacoes">
        <h:outputText styleClass="tituloImportacao texto-font texto-size-18-real texto-bold texto-cor-cinza" value="Histórico de Importações"/>

        <rich:dataTable id="tabelaHistoricoImportacoes"
                        value="#{MovContaControle.historicoImportacoes}"
                        var="item"
                        rows="10"
                        rowKeyVar="index"
                        width="100%"
                        styleClass="tabelaSimplesCustom">

            <rich:column sortBy="#{item.dataImportacao}" styleClass="col-text-align-left" headerClass="col-text-align-left">
                <f:facet name="header">
                    <h:outputText value="Data" styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"/>
                </f:facet>
                <h:outputText value="#{item.dataImportacao}" styleClass="texto-font texto-size-14-real texto-cor-cinza">
                    <f:convertDateTime pattern="dd/MM/yyyy HH:mm"/>
                </h:outputText>
            </rich:column>

            <rich:column sortBy="#{item.usuario}" styleClass="col-text-align-left" headerClass="col-text-align-left">
                <f:facet name="header">
                    <h:outputText value="Usuário" styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"/>
                </f:facet>
                <h:outputText value="#{item.usuario}" styleClass="texto-font texto-size-14-real texto-cor-cinza"/>
            </rich:column>

            <rich:column sortBy="#{item.totalImportados}" styleClass="col-text-align-left" headerClass="col-text-align-left">
                <f:facet name="header">
                    <h:outputText value="Sucesso" styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"/>
                </f:facet>
                <h:outputText value="#{item.totalImportados}" styleClass="texto-font texto-size-14-real texto-cor-cinza"/>
            </rich:column>

            <rich:column sortBy="#{item.totalErros}" styleClass="col-text-align-left" headerClass="col-text-align-left">
                <f:facet name="header">
                    <h:outputText value="Erros" styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"/>
                </f:facet>
                <h:outputText value="#{item.totalErros}" styleClass="texto-font texto-size-14-real texto-cor-cinza"/>
            </rich:column>

            <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                <f:facet name="header">
                    <h:outputText value="Status" styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"/>
                </f:facet>
                <h:outputText value="#{item.totalErros == 0 ? 'Sucesso' : 'Parcial/Falha'}"
                              styleClass="texto-font texto-size-14-real texto-cor-cinza"/>
            </rich:column>

            <rich:column styleClass="col-text-align-center" headerClass="col-text-align-center">
                <f:facet name="header">
                    <h:outputText value="Detalhes" styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"/>
                </f:facet>
                <a4j:commandButton value="Ver Detalhes"
                                   action="#{MovContaControle.selecionarHistorico(item)}"
                                   reRender="modalConteudoImportacao"
                                   oncomplete="Richfaces.showModalPanel('modalDetalhesImportacao'); return false;"
                                   styleClass="botaoDetalhes texto-font texto-size-14-real" />
            </rich:column>
        </rich:dataTable>

        <rich:datascroller
                styleClass="scrollPureCustom"
                renderIfSinglePage="false"
                align="center"
                for="tabelaHistoricoImportacoes"
                maxPages="10"
                id="sctabelaHistoricoImportacoes"/>
    </h:panelGroup>




    <rich:modalPanel id="modalDetalhesImportacao" width="600" height="600" moveable="true" resizeable="false">
        <f:facet name="header">
            <h:outputText value=" Detalhes da Importação"/>
        </f:facet>
        <f:facet name="controls">
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer"
                            onclick="Richfaces.hideModalPanel('modalDetalhesImportacao')"/>
        </f:facet>

        <a4j:outputPanel id="modalConteudoImportacao" layout="block">
            <h:panelGrid columns="1" style="width: 100%">
                <h:outputText value="Usuário: #{MovContaControle.historicoSelecionado.usuario}" />

                <!-- Cabeþalho de informações -->
                <h:panelGrid columns="2" columnClasses="coluna-label,coluna-valor" style="width: 100%; margin-bottom: 10px;">
                    <h:outputText value="Usuário:" styleClass="texto-bold"/>
                    <h:outputText value="#{MovContaControle.historicoSelecionado.usuario}" />

                    <h:outputText value="Data:" styleClass="texto-bold"/>
                    <h:outputText value="#{MovContaControle.historicoSelecionado.dataImportacao}">
                        <f:convertDateTime pattern="dd/MM/yyyy HH:mm" timeZone="America/Sao_Paulo"/>
                    </h:outputText>

                    <h:outputText value="Importados:" styleClass="texto-bold"/>
                    <h:outputText value="#{MovContaControle.historicoSelecionado.totalImportados}" />

                    <h:outputText value="Erros:" styleClass="texto-bold"/>
                    <h:outputText value="#{MovContaControle.historicoSelecionado.totalErros}" />
                </h:panelGrid>

                <h:outputText value="Resultado:" style="margin-top: 10px; font-weight: bold" />

                <!-- Tabela de mensagens -->
                <h:panelGroup rendered="#{not empty MovContaControle.historicoSelecionado.mensagensResultado}">
                    <h:panelGroup layout="block" style="max-height: 250px; overflow-y: auto; overflow-x: hidden; border: 1px solid #ccc; padding: 5px; width: 100%;">
                        <rich:dataTable value="#{MovContaControle.historicoSelecionado.paginaMensagensComIndice}"
                                        var="linha"
                                        rows="10"
                                        styleClass="tabelaSimplesCustom"
                                        style="width: 100%;">

                            <!-- Coluna do número (fixa e estreita) -->
                            <rich:column style="width: 20px;" styleClass="col-text-align-left" headerClass="col-text-align-left">
                                <f:facet name="header">
                                    <h:outputText value="N?" style="font-size: 11px;" />
                                </f:facet>
                                <h:outputText value="#{linha.numero}" style="font-size: 10px;" />
                            </rich:column>

                            <!-- Coluna da mensagem (ocupa o restante do espaço) -->
                            <rich:column style="width: auto;" styleClass="col-text-align-left" headerClass="col-text-align-left">
                                <f:facet name="header">
                                    <h:outputText value="Mensagem" />
                                </f:facet>
                                <h:outputText value="#{linha.mensagem}" escape="false"
                                              style="white-space: pre-wrap; overflow-wrap: break-word;" />
                            </rich:column>
                        </rich:dataTable>

                    </h:panelGroup>

                    <!-- PaginaþÒo -->
                    <h:panelGroup layout="block" style="margin-top:10px; text-align: center;">
                        <a4j:commandButton value="Anterior"
                                           action="#{MovContaControle.historicoSelecionado.paginaAnterior}"
                                           reRender="modalConteudoImportacao"
                                           disabled="#{MovContaControle.historicoSelecionado.pagina eq 1}"
                                           styleClass="botaoDetalhes texto-font texto-size-14-real"/>

                        <h:outputText value=" Página #{MovContaControle.historicoSelecionado.pagina} "
                                      styleClass="texto-font texto-size-14-real texto-cor-cinza"/>

                        <a4j:commandButton value="Próxima"
                                           action="#{MovContaControle.historicoSelecionado.proximaPagina}"
                                           reRender="modalConteudoImportacao"
                                           disabled="#{MovContaControle.historicoSelecionado.pagina * MovContaControle.historicoSelecionado.tamanhoPagina ge MovContaControle.historicoSelecionado.mensagensResultado.size()}"
                                           styleClass="botaoDetalhes texto-font texto-size-14-real"/>
                    </h:panelGroup>
                </h:panelGroup>

            </h:panelGrid>
        </a4j:outputPanel>
    </rich:modalPanel>


</h:panelGroup>

<!-- Script para forçar o reset visual do RichFaces uploader -->
<script>
    function limparUploadComponente() {
        var uploader = RichFaces.$("formulario:uploadPlanilha");
        if (uploader) {
            uploader.clearItems();
        }
    }
</script>

<rich:modalPanel id="panelStatus1" autosized="true" styleClass="modal-carregando-ripple">
    <h:graphicImage url="/images/loader-small.gif" />

    <div class="textoCarregando" id="idtextoCarregando"
         style="font-size: 20px; background-color: #fff; margin-top: 10px;">
        Carregando...
    </div>
</rich:modalPanel>
<script>
    function monitorarDownload() {
        let interval = setInterval(function () {
            if (document.cookie.indexOf("fileDownload=true") !== -1) {
                Richfaces.hideModalPanel('panelStatus1');
                clearInterval(interval);
                // Apaga o cookie (opcional)
                document.cookie = "fileDownload=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
            }
        }, 1000);
    }
</script>

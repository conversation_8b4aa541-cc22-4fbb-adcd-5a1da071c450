/**
 * Created by <PERSON> on 27/02/2016.
 */

var isTouchDevice = ("ontouchstart" in window || window.DocumentTouch && document instanceof DocumentTouch);
var arrayOrdemOriginalBI;


montarTips();

function montarTips() {
    jQuery('.tooltipster').tooltipster({
        theme: 'tooltipster-light',
        position: 'bottom',
        animation: 'grow',
        contentAsHTML: true,
        axis: "y",
        scroll: "true"
    });
}

verificarGrupoColaboradorCheck();

function alterarInputHiddenOrdem(e) {
    document.getElementById('form:arrayOrdemBI').value = e;
}

function preencherArrayOrdemOriginal() {
    arrayOrdemOriginalBI = document.getElementById('form:arrayOrdemBI').value
}

function cancelarAlteracoesArrayOrdem() {
    document.getElementById('form:arrayOrdemBI').value = arrayOrdemOriginalBI
}

var $elems = jQuery("html, body");
var delta = 0;

function carregarSortable() {

    jQuery('.colunaSortable .col-sortable').sortable({
        connectWith: "div",
        handle: ".bi-container-bi-block ,.bi-container-bi-block .fa-icon-move",
        zIndex: 12,
        disabled: false,
        dropOnEmpty: true,
        receive: function (event, ui) {
            delta = undefined;
            if ((jQuery(ui.sender).hasClass('col-sortable-1') || jQuery(ui.sender).hasClass('col-sortable-2')) && jQuery(this).hasClass('col-sortable-3')) {
                moverLixeira(jQuery(ui.item));
            } else if (jQuery(ui.sender).hasClass('col-sortable-3')) {
                removerLixeira(jQuery(ui.item))
            }
        },
        sort: function (e, ui) {
            console.log('Tentando adicionar');
            jQuery(ui.placeholder).height(jQuery(ui.item).height());
            jQuery(ui.placeholder).removeClass("bi-container-item-lixeira");
            jQuery(ui.placeholder).addClass("container-bi");
            //     jQuery(ui.placeholder).clone().appendTo(jQuery('.col-sortable-1'));
            jQuery(ui.placeholder).html('<i class="fa-icon-eye-open"></i>');
            var x = jQuery('.ui-sortable-helper').css('left');
            x = parseInt(x) * 1.3;
            jQuery('.ui-sortable-helper').css('left', x);
            jQuery('.ui-sortable-helper').css('top', ((e.pageY - 250)) * 1.3);
            var scrollposition = document.body.scrollTop;
            var heightClient = document.body.clientHeight;
            if (((e.pageY) - scrollposition >= heightClient - 100)) {
                delta = 10;
            } else if (((e.pageY) - scrollposition <= 200)) {
                delta = -10;
            } else {
                delta = undefined;
            }
        },
        stop: function (e) {
            var arrayItens = "";
            jQuery(".bi-container-bis .col-sortable").each(function (i) {
                jQuery(this).children(".container-bi,.bi-container-item-lixeira").each(function (e) {
                    var id = jQuery(this).attr('data-target') ? jQuery(this).attr('data-target') : jQuery(this).attr('id');
                    arrayItens = arrayItens + obterIndiceBI(id) + "-" + i + ",";
                });
            });
            arrayItens = arrayItens.substring(0, arrayItens.length - 1);
            document.getElementById('form:arrayOrdemBI').value = arrayItens;
            delta = undefined;
        }
    });
}

(function f() {
    if (delta) {
        $elems.scrollTop(function (i, v) {
            return v + delta;
        });
    }
    webkitRequestAnimationFrame(f);
})();

function moverLixeira(item) {
    var divLixeira = '<div class="bi-container-item-lixeira" data-target="' + item.attr('id') + '">' +
        '<div style="display: table-cell;vertical-align: middle"><span class="lixeira-text">' + obterNomeBI(item.attr('id')) + '</span></div><i class="fa-icon-question-sign"></i></div>';
    item.fadeOut(function () {
        jQuery(item).clone().appendTo('.col-sortable-hidden');
        jQuery(item).replaceWith(divLixeira).fadeIn();
        carregarHandler();
        if (isTouchDevice) {
            jQuery('.bi-container-bi-block').remove();
            jQuery('.bi-block-container-body .container-bi,.bi-container-item-lixeira').append('<div class = "bi-container-bi-block"></div>');
            jQuery('.bi-container-bi-block').append('<i class="fa-icon-move" ></i>');
        }
    });

}

function removerLixeira(item) {
    var id = jQuery(item).attr('data-target');
    var bi = jQuery(".col-sortable-hidden [id*=" + id.substring(5, id.length) + "]");
    jQuery(item).fadeOut(function () {
        item.replaceWith(bi).fadeIn(function () {
            jQuery(bi).find('.btn-atualizar-bi').trigger('click');
            jQuery(bi).css('display', 'block');
            carregarHandler();
        });
    });
}

function carregarHandler() {
    if (!isTouchDevice) {
        jQuery('.bi-block-container-body .container-bi,.bi-container-item-lixeira').mouseenter(function () {
            if (!sortableBtn) {
                jQuery('.bi-container-bi-block').remove();
                jQuery(this).append('<div class = "bi-container-bi-block"></div>');
                jQuery('.bi-container-bi-block').append('<i class="fa-icon-move" ></i>');
                carregarSortable();
            }
        });
        jQuery('.bi-block-container-body .container-bi,.bi-container-item-lixeira').mouseleave(function () {
            if (!sortableBtn) {
                jQuery('.bi-container-bi-block').remove();
                carregarSortable();
            }
        });
    }

}

function abrirCarregandoPoBI(e) {
    jQuery(e).parent().find('.bi-container-bi-block').remove();
    jQuery(e).parent().append('<div class = "bi-container-bi-block"></div>');
}

function hideCarregandoPorBI(e) {
    jQuery(e).parent().find('.bi-container-bi-block').remove();

}

sortableBtn = true;

function fecharSortable() {
    var fundoBlock = jQuery('.fundo-block-container');
    jQuery('.fundoCinza,.fundoBranco').toggle();

    fundoBlock.animate({"left": "-100%"}, 300, function () {
        jQuery(this).css('position', 'absolute');
        jQuery('.fundoCinza,.fundoBranco').fadeTo("slow", 1);
    });

    fundoBlock.removeClass('open');
    jQuery('.colunaSortable .col-sortable').sortable('disable');
    jQuery('.bi-container-bis').removeClass('container-bi-50');
    jQuery('.bi-container-bis').children('col-sortable').removeClass('ui-sortable');
    jQuery('.fundo-block').removeClass('bi-fundo-block');
    jQuery('.btn-icon-toggle-sortable').addClass('fa-icon-cubes');
    jQuery('.btn-icon-toggle-sortable').removeClass('fa-icon-remove-sign');
    jQuery('.bi-icon-sortable-handle').removeClass('opacity-1');
    jQuery('.bi-btn-filtroCol,.bi-combo-empresa,.fundoCinza,.fundoBranco').css('display', 'block');
    jQuery('.fa-icon-save.bi-icon-sortable-handle,.container-header-meuBI').css('display', 'none');
    jQuery('.container-bi').css('z-index', 0);
    jQuery('.bi-container-bis,.bi-block-container-body').removeClass('colunaSortable');
    var containerBI = jQuery('.bi-block-container-body .bi-container-bis').clone();
    jQuery('.bi-container-bis').remove();
    containerBI.appendTo('.caixaMenuLatel > div:first-child');
    carregarHandler();
    sortableBtn = true;
    try {
        carregarGragicoICV();
    } catch (ignored) {
    }
    try {
        carregarGragicoICVSS();
    } catch (ignored) {
    }

    try {
        carregarGraficoTM();
    } catch (ignored) {
    }
    if (isTouchDevice) {
        jQuery('.bi-container-bi-block').remove();
    }
}

function toggleSortable() {
    if (sortableBtn) {
        var fundoBlock = jQuery('.fundo-block-container');
        fundoBlock.addClass('open');
        fundoBlock.height(jQuery('body').height());
        jQuery('.bi-container-bis').addClass('container-bi-50');
        jQuery('.btn-icon-toggle-sortable').addClass('fa-icon-remove-sign');
        jQuery('.btn-icon-toggle-sortable').removeClass('fa-icon-cubes');
        jQuery('.bi-icon-sortable-handle').addClass('opacity-1');
        jQuery('.fa-icon-save.bi-icon-sortable-handle').css('display', 'inline');
        jQuery('.container-bi').css('z-index', 12);
        jQuery('.bi-btn-filtroCol,.bi-combo-empresa').css('display', 'none');
        jQuery('.container-header-meuBI').css('display', 'block');
        var containerBI = jQuery('.caixaMenuLatel .bi-container-bis').clone();
        jQuery('.bi-container-bis').remove();
        containerBI.appendTo('.bi-block-container-body');
        carregarHandler();
        sortableBtn = false;
        jQuery('.bi-block-container-body').addClass('colunaSortable');
        carregarSortable();
        fundoBlock.animate({"left": "0"}, 300, function () {
            jQuery(this).css('position', 'relative');

        });
        jQuery('.fundoCinza,.fundoBranco').fadeTo("slow", 0.4, function () {
            jQuery('.fundoCinza,.fundoBranco').toggle();
        });

        jQuery('.fundo-block-container .col-sortable,.fundo-block-container').height(5575);
        if (isTouchDevice) {
            jQuery('.bi-container-bi-block').remove();
            jQuery('.bi-block-container-body .container-bi,.bi-container-item-lixeira').append('<div class = "bi-container-bi-block"></div>');
            jQuery('.bi-container-bi-block').append('<i class="fa-icon-move" ></i>');
        }
    } else {
        fecharSortable();
    }
}

function organizarSortable() {

    var coluna1 = jQuery(".bi-container-bis .col-sortable-1");
    var coluna2 = jQuery(".bi-container-bis .col-sortable-2");
    var coluna3 = jQuery(".col-sortable-3");
    var colunaHidden = jQuery(".col-sortable-hidden");

    //Clones
    var coluna1Clone = coluna1.clone();
    var coluna2Clone = coluna2.clone();

    jQuery(coluna1Clone).children('.container-bi').remove();
    jQuery(coluna2Clone).children('.container-bi').remove();
    var arrayItens = document.getElementById('form:arrayOrdemBI').value;
    arrayItens = arrayItens.split(',');
    for (var i = 0; i < arrayItens.length; i++) {

        var posicaoEl = arrayItens[i].split('-')[0];
        var coluna = arrayItens[i].split('-')[1];
        var el;

        if (posicaoEl > 4) {
            el = jQuery("[id*=" + obterIdPorIndicie(posicaoEl) + "]");
        } else {
            el = jQuery("[id*=" + obterIdPorIndicie(posicaoEl) + "]");

        }
        var clone = jQuery(el);
        if (coluna == 0) {
            clone.appendTo(coluna1Clone);
        } else if (coluna == 1) {
            clone.appendTo(coluna2Clone);
        } else if (coluna == 2) {
            clone.appendTo(coluna3);
            moverLixeira(jQuery(clone));
        }
    }
    coluna1.replaceWith(coluna1Clone);
    coluna2.replaceWith(coluna2Clone);
    jQuery('.container-bi').css('display', 'block');

}

function obterNomeBI(id) {
    if (id === 'form:bi-pendencia') {
        return 'Pend�ncias de Clientes';
    } else if (id === 'form:bi-indicieconversao') {
        return 'Convers�o de vendas';
    } else if (id === 'form:bi-idcsessao') {
        return 'Convers�o de vendas Sess�o';
    } else if (id === 'form:panelBIMetasFinan') {
        return 'Metas Financeiras';
    } else if (id === 'form:biTicket') {
        return 'Ticket M�dio de Planos';
    } else if (id === 'form:bi-grupo-risco') {
        return 'Grupo de Risco';
    } else if (id === 'form:biIndiceRenovacao') {
        return '�ndice Renova��o';
    } else if (id === 'form:bi-movimentacao-contrato') {
        return 'Movimenta��o de contratos';
    } else if (id === 'form:panelCR') {
        return 'D�bito em cart�o(DCC)';
    } else if (id === 'form:biContOp') {
        return 'Controle de Opera��es';
    } else if (id === 'form:biProbEvas') {
        return 'Pacto IA';
    } else if (id === 'form:biConvite') {
        return 'Convites de Aula Experimental';
    } else if (id === 'form:bi-clientesverificados') {
        return 'Clientes Verificados';
    } else if (id === 'form:bi-aulaexperimental') {
        return 'Aula Experimental';
    } else if (id === 'form:bi-gestaoAcesso') {
        return 'Gest�o de Acesso'
    } else if (id === 'form:bi-inadimplencia') {
        return 'Inadimpl�ncia'
    } else if (id === 'form:bi-ltv') {
        return 'M�tricas LTV';
    } else if (id == 'form:bi-gympass') {
        return 'Wellhub'
    }
}

function obterIndiceBI(id) {
    if (id === 'form:bi-pendencia') {
        return 0;
    } else if (id === 'form:bi-indicieconversao') {
        return 1;
    } else if (id === 'form:bi-idcsessao') {
        return 2;
    } else if (id === 'form:panelBIMetasFinan') {
        return 3;
    } else if (id === 'form:biTicket') {
        return 4;
    } else if (id === 'form:bi-grupo-risco') {
        return 5;
    } else if (id === 'form:biIndiceRenovacao') {
        return 6;
    } else if (id === 'form:bi-movimentacao-contrato') {
        return 7;
    } else if (id === 'form:panelCR') {
        return 8;
    } else if (id === 'form:biContOp') {
        return 9;
    } else if (id === 'form:biConvite') {
        return 10;
    } else if (id === 'form:bi-clientesverificados') {
        return 11;
    } else if (id === 'form:bi-aulaexperimental') {
        return 12;
    } else if (id === 'form:bi-gestaoAcesso') {
        return 13;
    } else if (id === 'form:bi-inadimplencia') {
        return 14;
    } else if (id === 'form:biProbEvas') {
        return 15;
    } else if (id === 'form:bi-ltv') {
        return 16;
    } else if (id == 'form:bi-gympass') {
        return 17;
    }
}

jQuery('body').click(function (e) {
    if (e.target.id == 'fundo-block') {
        fecharFiltroColaborador();
    }
});


function checkColaborador(checkIrmao) {
    checkIrmao = jQuery(checkIrmao).parent().parent().find('.filtro-colaborador-grupo-item-nome');
    var selecionado = checkIrmao.parent().find('.checkBox-filtro-col').is(":checked");
    var container = jQuery('[data-col=' + checkIrmao.attr('data-col') + "]").not(checkIrmao).parent();
    if (selecionado) {
        if (!container.hasClass('filtro-colaborador-grupo-item-click')) {
            container.addClass('filtro-colaborador-grupo-item-click');
        }
    } else {
        container.removeClass('filtro-colaborador-grupo-item-click');
    }
    container.find('.filtro-colaborador-img .checkBox-filtro-col').prop("checked", checkIrmao.parent().find('.checkBox-filtro-col').is(":checked"));
}

function abrirFiltroCol() {
    var div = jQuery('<div id="fundo-block" class = "bi-fundo-block fundo-filtro-col"></div>');
    //var t = jQuery(window).scrollTop();
    var t = (jQuery('.container-colaboradores').height() + t) > jQuery('body').height() ? jQuery('body').height() - jQuery('.container-colaboradores').height() : t;
    jQuery('.container-colaboradores').css("top", t).toggle(100, function () {
        jQuery(this).animate({"right": "0"}, function () {
            div.height(jQuery('body').height());
            jQuery('body').append(div);
        });
    });
}

function abrirFiltroSemBlock() {
    var t = jQuery(window).scrollTop();
    t = (jQuery('.container-colaboradores').height() + t) > jQuery('body').height() ? jQuery('body').height() - jQuery('.container-colaboradores').height() : t;
    jQuery('.container-colaboradores').css("top", t).toggle(100, function () {
        jQuery(this).animate({"right": "0"});

    });
}

function fecharFiltroSemBlock() {
    jQuery('.container-colaboradores').css('display', 'block').animate({"right": "-440"});
}

function fecharFiltroColaborador() {
    jQuery('.container-colaboradores').css('display', 'block').animate({"right": "-440"}, function () {
        jQuery('.bi-fundo-block.bi-fundo-block').remove();
        jQuery(this).css('display', 'none');
    });
    if (selecionouColaboradorFiltro) {
        carregarBITodos();
        selecionouColaboradorFiltro = false;
    }
}

selecionouColaboradorFiltro = false;

function carregarHandleFiltroColaborador() {
    jQuery('.btn-toggle-grupo').click(function () {
        var attr = jQuery(this).attr('opened');
        var grupo = jQuery(this);
        if (attr == null || attr == 'false') {

            jQuery(this).parent().parent().find('.filtro-colaborador-grupo-item').each(function (i) {
                jQuery(this).addClass('filtro-colaborador-grupo-item-click');
                grupo.attr('opened', 'true');
                var check = jQuery(this).find('.checkBox-filtro-col');
                check.prop("checked", true);
                checkColaborador(check);
            });

        } else {
            jQuery(this).parent().parent().find('.filtro-colaborador-grupo-item').each(function (i) {
                jQuery(this).removeClass('filtro-colaborador-grupo-item-click');
                grupo.attr('opened', 'false');
                var check = jQuery(this).find('.checkBox-filtro-col');
                check.prop("checked", false);
                checkColaborador(check);
            });
        }
        selecionarDoGrupo();
        selecionouColaboradorFiltro = true;
    });
    jQuery('.filtro-colaborador-grupo-item').click(function () {
        selecionarItemFiltro(jQuery(this));
    });
    jQuery('.desmarcarTodosFiltro').click(function () {
        desmarcarTodosItemFiltro();
    });
    jQuery('.marcarTodosItemFiltro').click(function () {
        marcarTodosItemFiltro();
    });
}

function selecionarItemFiltro(el) {
    var checkBox;
    jQuery(el).toggleClass('filtro-colaborador-grupo-item-click');
    if (jQuery(el).find('.filtro-colaborador-img .checkBox-filtro-col')) {
        checkBox = jQuery(el).find('.filtro-colaborador-img .checkBox-filtro-col');
    } else {
        checkBox = jQuery(el).parent().find('.filtro-colaborador-img .checkBox-filtro-col')
    }
    if (jQuery(el).hasClass('col-icv')) {
        Notifier.info('O colaborador ' + jQuery(el).find('.filtro-colaborador-grupo-item-nome').text() + " n�o est� presente em nenhum grupo colaborador", "Informa��o:");
    }
    var check = checkBox.is(":checked");
    jQuery(checkBox).prop("checked", !check);
    checkColaborador(checkBox);
    selecionarDoGrupo();
    selecionouColaboradorFiltro = true;
}

function marcarTodosItemFiltro() {
    var checkBox;
    var item = jQuery('.filtro-colaborador-grupo-item');
    jQuery(item).addClass('filtro-colaborador-grupo-item-click');
    if (item.find('.filtro-colaborador-img .checkBox-filtro-col')) {
        checkBox = item.find('.filtro-colaborador-img .checkBox-filtro-col');
    } else {
        checkBox = item.parent().find('.filtro-colaborador-img .checkBox-filtro-col')
    }
    var check = true;
    jQuery(checkBox).prop("checked", check);
    checkColaborador(checkBox);
    selecionarDoGrupo();
    selecionouColaboradorFiltro = true;
}

function desmarcarTodosItemFiltro() {
    var checkBox;
    var item = jQuery('.filtro-colaborador-grupo-item');
    jQuery(item).removeClass('filtro-colaborador-grupo-item-click');
    if (item.find('.filtro-colaborador-img .checkBox-filtro-col')) {
        checkBox = item.find('.filtro-colaborador-img .checkBox-filtro-col');
    } else {
        checkBox = item.parent().find('.filtro-colaborador-img .checkBox-filtro-col')
    }
    var check = false;
    jQuery(checkBox).prop("checked", check);
    checkColaborador(checkBox);
    selecionarDoGrupo();
    selecionouColaboradorFiltro = true;
}

function marcarTodosItemFiltroFC() {
    var checkBox;
    var item = jQuery('.marcar-item');
    jQuery(item).addClass('filtro-colaborador-grupo-item-click');
    if (item.find('.marcar-item')) {
        checkBox = item.find('.marcar-item');
    } else {
        checkBox = item.parent().find('.marcar-item')
    }
    var check = true;
    jQuery(checkBox).prop("checked", check);
    checkColaborador(checkBox);
    selecionarDoGrupo();
    selecionouColaboradorFiltro = true;
}

function desmarcarTodosItemFiltroFC() {
    var checkBox;
    var item = jQuery('.marcar-item');
    jQuery(item).removeClass('filtro-colaborador-grupo-item-click');
    if (item.find('.marcar-item')) {
        checkBox = item.find('.marcar-item');
    } else {
        checkBox = item.parent().find('.marcar-item')
    }
    var check = false;
    jQuery(checkBox).prop("checked", check);
    checkColaborador(checkBox);
    selecionarDoGrupo();
    selecionouColaboradorFiltro = true;
}

function selecionarDoGrupo() {
    var tooltipColaborador = "";
    var valorTooltip = "";
    var qntSelecionados = 0;
    var todosColSelecionados = true;
    var qntTodosCol = 0;
    jQuery('.filtro-container-grupo').each(function (i) {

        var itemsGrupo = jQuery(this).find('.checkBox-filtro-col');
        var todosSelecionados = true;
        var algumSelecionado = false;

        itemsGrupo.each(function (index) {
            var item = jQuery(this);
            var nome = jQuery(this).parent().parent().children('.filtro-colaborador-grupo-item-nome');
            var dataCol = nome.attr('data-col');
            qntTodosCol++;
            if (item.is(":checked")) {
                if (tooltipColaborador === "" || tooltipColaborador.indexOf('"' + dataCol + '"') < 0) {

                    tooltipColaborador = tooltipColaborador + ' , <span data-col="' + dataCol + '">' + nome.text() + '</span>';
                    valorTooltip = valorTooltip + ' , ' + nome.text();
                }
                qntSelecionados++;
                algumSelecionado = true;
            } else {
                todosSelecionados = false;
                todosColSelecionados = false;
            }
        });
        if (todosSelecionados) {
            jQuery(this).find('.btn-toggle-grupo').addClass('fa-icon-check');
            jQuery(this).find('.btn-toggle-grupo').removeClass('fa-icon-check-empty');
            jQuery(this).find('.btn-toggle-grupo').attr('opened', 'true');
            jQuery(this).find('.btn-toggle-grupo').removeClass('fa-icon-minus-square-o');
        } else {
            if (algumSelecionado) {
                jQuery(this).find('.btn-toggle-grupo').addClass('fa-icon-minus-square-o');
                jQuery(this).find('.btn-toggle-grupo').removeClass('fa-icon-check');
                jQuery(this).find('.btn-toggle-grupo').attr('opened', 'false');
                jQuery(this).find('.btn-toggle-grupo').removeClass('fa-icon-check-empty');
            } else {
                jQuery(this).find('.btn-toggle-grupo').addClass('fa-icon-check-empty');
                jQuery(this).find('.btn-toggle-grupo').attr('opened', 'false');
                jQuery(this).find('.btn-toggle-grupo').removeClass('fa-icon-minus-square-o');
                jQuery(this).find('.btn-toggle-grupo').removeClass('fa-icon-check');

            }
        }
    });
    jQuery('.btn-open-filtro-col').attr('title', tooltipColaborador);

    jQuery('.btn-open-filtro-col').addClass('badgeItem');
    if (qntSelecionados <= 2 && qntSelecionados > 0 && !todosColSelecionados) {
        jQuery('.btn-open-filtro-col').text(valorTooltip.substring(2, valorTooltip.length));
        jQuery('.btn-open-filtro-col').tooltipster('content', 'Apresentando o resultado de ' + tooltipColaborador.substring(2, tooltipColaborador.length));
        jQuery('.btn-open-filtro-col').attr("data-bagde", qntSelecionados);
    } else if (qntSelecionados > 2 && !todosColSelecionados) {
        jQuery('.btn-open-filtro-col').text(valorTooltip.substring(2, 15) + "...");
        jQuery('.btn-open-filtro-col').tooltipster('content', 'Apresentando o resultado de ' + tooltipColaborador.substring(2, tooltipColaborador.length));
        jQuery('.btn-open-filtro-col').attr("data-bagde", qntSelecionados);
    } else if (qntSelecionados <= 0 || todosColSelecionados) {
        if (qntSelecionados <= 0) {
            jQuery('.btn-open-filtro-col').tooltipster('content', 'Apresentando o resultado de todos os colaboradores cadastrados no sistema');
            jQuery('.btn-open-filtro-col').attr("data-bagde", 0);
            jQuery('.btn-open-filtro-col').removeClass('badgeItem');
        } else {
            jQuery('.btn-open-filtro-col').tooltipster('content', 'Apresentando o resultado de todos os colaboradores que est�o em grupos');
            jQuery('.btn-open-filtro-col').attr("data-bagde", qntTodosCol);
        }
        jQuery('.btn-open-filtro-col').text("Todos Colaboradores");

    }

    jQuery('.btn-open-filtro-col').attr('title', '');
}

function verificarGrupoColaboradorCheck() {
    var tooltipColaborador = "";
    var valorTooltip = "";
    var qntSelecionados = 0;
    var todosColSelecionados = true;
    var contTodosSelecionados = 0;
    var qntTodosCol = 0;
    jQuery('.filtro-container-grupo').each(function (i) {
        var itemsGrupo = jQuery(this).find('.checkBox-filtro-col');
        var todosSelecionados = true;
        var algumSelecionado = false;

        itemsGrupo.each(function (index) {
            var item = jQuery(this);
            var nome = jQuery(this).parent().parent().children('.filtro-colaborador-grupo-item-nome');
            var dataCol = nome.attr('data-col');
            qntTodosCol++;
            if (dataCol == null) {
                var dataCol = nome.text().split('<>')[1];
                if (jQuery('.filtro-container-grupo').find('[data-col=' + dataCol + ']').html() == undefined) {
                    contTodosSelecionados++;
                }
                nome.attr('data-col', dataCol);
                nome.text(nome.text().split('<>')[0]);


            }

            contTodosSelecionados++;
            if (item.is(":checked")) {
                checkColaborador(item);
                if (tooltipColaborador === "" || tooltipColaborador.indexOf('"' + dataCol + '"') < 0) {

                    tooltipColaborador = tooltipColaborador + ' , <span data-col="' + dataCol + '">' + nome.text() + '</span>';
                    valorTooltip = valorTooltip + ' , ' + nome.text();
                }
                qntSelecionados++;
                jQuery(this).parent().parent().addClass('filtro-colaborador-grupo-item-click');
                algumSelecionado = true;
            } else {
                todosSelecionados = false;
                todosColSelecionados = false;
            }
        });
        if (todosSelecionados) {
            jQuery(this).find('.btn-toggle-grupo').addClass('fa-icon-check');
            jQuery(this).find('.btn-toggle-grupo').removeClass('fa-icon-check-empty');
            jQuery(this).find('.btn-toggle-grupo').attr('opened', 'true');
            jQuery(this).find('.btn-toggle-grupo').removeClass('fa-icon-minus-square-o');
        } else {
            if (algumSelecionado) {
                jQuery(this).find('.btn-toggle-grupo').addClass('fa-icon-minus-square-o');
                jQuery(this).find('.btn-toggle-grupo').removeClass('fa-icon-check');
                jQuery(this).find('.btn-toggle-grupo').attr('opened', 'false');
                jQuery(this).find('.btn-toggle-grupo').removeClass('fa-icon-check-empty');
            } else {
                jQuery(this).find('.btn-toggle-grupo').addClass('fa-icon-check-empty');
                jQuery(this).find('.btn-toggle-grupo').attr('opened', 'false');
                jQuery(this).find('.btn-toggle-grupo').removeClass('fa-icon-minus-square-o');
                jQuery(this).find('.btn-toggle-grupo').removeClass('fa-icon-check');

            }
        }
    });
    jQuery('.btn-open-filtro-col').attr('title', tooltipColaborador);
    jQuery('.btn-open-filtro-col').addClass('badgeItem');
    if (qntSelecionados <= 2 && qntSelecionados > 0 && !todosColSelecionados) {
        jQuery('.btn-open-filtro-col').text(valorTooltip.substring(2, valorTooltip.length));
        jQuery('.btn-open-filtro-col').tooltipster('content', 'Apresentando o resultado de ' + tooltipColaborador.substring(2, tooltipColaborador.length));
        jQuery('.btn-open-filtro-col').attr("data-bagde", qntSelecionados);
    } else if (qntSelecionados > 2 && !todosColSelecionados) {
        jQuery('.btn-open-filtro-col').text(valorTooltip.substring(2, 15) + "...");
        jQuery('.btn-open-filtro-col').tooltipster('content', 'Apresentando o resultado de ' + tooltipColaborador.substring(2, tooltipColaborador.length));
        jQuery('.btn-open-filtro-col').attr("data-bagde", qntSelecionados);
    } else if (qntSelecionados <= 0 || todosColSelecionados) {
        if (qntSelecionados <= 0) {
            jQuery('.btn-open-filtro-col').tooltipster('content', 'Apresentando o resultado de todos os colaboradores cadastrados no sistema');
            jQuery('.btn-open-filtro-col').attr("data-bagde", 0);
            jQuery('.btn-open-filtro-col').removeClass('badgeItem');
        } else {
            jQuery('.btn-open-filtro-col').tooltipster('content', 'Apresentando o resultado de todos os colaboradores que est�o em grupos');
            jQuery('.btn-open-filtro-col').attr("data-bagde", qntTodosCol);
        }
        jQuery('.btn-open-filtro-col').text("Todos Colaboradores");

    }
    jQuery('.btn-open-filtro-col').attr('title', '');
    carregarHandleFiltroColaborador();
}

function obterIdPorIndicie(id) {
    if (id == 0) {
        return 'bi-pendencia';
    } else if (id == 1) {
        return 'bi-indicieconversao';
    } else if (id == 2) {
        return 'bi-idcsessao';
    } else if (id == 3) {
        return 'panelBIMetasFinan';
    } else if (id == 4) {
        return 'biTicket';
    } else if (id == 5) {
        return 'bi-grupo-risco';
    } else if (id == 6) {
        return 'biIndiceRenovacao';
    } else if (id == 7) {
        return 'bi-movimentacao-contrato';
    } else if (id == 8) {
        return 'panelCR';
    } else if (id == 9) {
        return 'biContOp';
    } else if (id == 10) {
        return 'biConvite';
    } else if (id == 11) {
        return 'bi-clientesverificados';
    } else if (id == 12) {
        return 'bi-aulaexperimental';
    } else if (id == 13) {
        return 'bi-gestaoAcesso'
    } else if (id == 14) {
        return 'bi-inadimplencia'
    } else if (id == 15) {
        return 'biProbEvas';
    } else if (id == 16) {
        return 'bi-ltv';
    } else if (id == 17) {
        return 'bi-gympass'
    }
}

requisicao = false;

function carregarBIsVisiveis(topAtual) {
    if (requisicao) {
        return;
    }
    var topElemento = 0;
    var bisCarregar = '';
    jQuery('.bi-container-bis .col-sortable .container-bi').each(function (i) {
        var elem = jQuery(this);
        if (elem.find('.bi-unloaded').length > 0) {
            topElemento = elem.offset().top;
            if (topElemento + 75 < (topAtual + window.innerHeight)) {
                bisCarregar += ((bisCarregar != '' ? '-' : '') + obterIndiceBI(elem.attr('id')));
            }
        }
    });
    if (bisCarregar != '') {
        requisicao = true;
        carregarBIScroll(bisCarregar);
    }
}

jQuery.fn.scrollEnd = function (callback, timeout) {
    jQuery(this).scroll(function () {
        var $this = jQuery(this);
        if ($this.data('scrollTimeout')) {
            clearTimeout($this.data('scrollTimeout'));
        }
        $this.data('scrollTimeout', setTimeout(callback, timeout));
    });
};

// carregarScrollBI();
function carregarScrollBI() {
    jQuery(window).scroll(function () {
        topAtual = jQuery(window).scrollTop();
        if (topAtual > 106) {
            jQuery('.container-filtro-bi').addClass('filtros-bi-fixo');
        } else if (topAtual <= 106) {
            jQuery('.container-filtro-bi').removeClass('filtros-bi-fixo');
        }
        jQuery(window).scrollEnd(function () {
            carregarBIsVisiveis(topAtual)
        }, 600);

    });
}

function fecharCarregando() {
    Richfaces.hideModalPanel('modalCarregando');
}

function abrirCarregando() {
    jQuery(".mensagemBICarregando").text("");
    Richfaces.showModalPanel('modalCarregando');
    carregarBIPorDemanda();

}
